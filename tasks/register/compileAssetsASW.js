module.exports = function (grunt) {
	grunt.registerTask('compileAssetsASW', [
        /* Remove content of the .tmp folder */
		'clean:dev',
        /* Compile es6 -> es5 and copy all the processed files to .tmp/public/scripts/babel-output */
        'babel:asw',
        /* Concatenate all the files to .tmp/public/scripts/main.js */
		'concat:frontend_admin',
        /* Remove .tmp/public/scripts/babel-output */ 
        'clean:babel',
        /* minify, combine, and automatically cache HTML templates with $templateCache */
		'ngtemplates:AdminSportWrench',
        /* .sass files -> .css */
		'sass:server',
        /* copy files from assets/ to .tmp/public/ */
		'copy:dev',
        /* copy ./frontend_admin/index.html to .tmp/public/ */
		'copy:asw'
	]);
};

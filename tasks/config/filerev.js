module.exports = function(grunt) {
    var ROOT_JS     = '.tmp/public/scripts/',
        ROOT_CSS    = '.tmp/public/styles/';
        
    grunt.config.set('filerev', {
        options: {
            encoding: 'utf8',
            algorithm: 'md5',
            length: 8
        },
        js: {
            src: ['main.js', 'vendor_base.js'].map(f => { // 'vendor_main.js', 'xlsx.js', 'gmaps.js'
                return ROOT_JS + f;
            }),
            dest: ROOT_JS
        },
        css: {
            src: ['main.css', 'vendor.css'].map(f => {
                return ROOT_CSS + f;
            }),
            dest: ROOT_CSS
        }
    });

    grunt.loadNpmTasks('grunt-filerev');
};
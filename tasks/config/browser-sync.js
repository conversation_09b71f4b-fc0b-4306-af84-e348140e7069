var proxyMiddleware = require('http-proxy-middleware');

var proxy = proxyMiddleware('/', {
    target: 'http://localhost:3000',
    changeOrigin: true,
    cors: true,
    https: true,
    headers: {
        host: 'localhost:3000'
    }
});

module.exports = function(grunt) {
    grunt.config.set('browserSync', {
        frontend_event: {
            bsFiles: {
                src : [
                    '.tmp/public/stylesEsw/**/*.css',
                    '.tmp/public/**/*.html',
                    '.tmp/public/**/*.js',
                ]
            },
            options: {
                watchTask: true,
                server: {
                    baseDir: ".tmp/public",
                    port: 3100,
                    middleware: [proxy]
                }
            }
        },

        frontend_admin: {
            bsFiles: {
                src : [
                    '.tmp/public/styles/**/*.css',
                    '.tmp/public/**/*.html',
                    '.tmp/public/**/*.js',
                ]
            },
            options: {
                watchTask: true,
                server: {
                    baseDir: ".tmp/public",
                    port: 3200,
                    middleware: [proxy]
                }
            }
        },

        frontend: {
            bsFiles: {
                src : [
                    '.tmp/public/styles/**/*.css',
                    '.tmp/public/**/*.html',
                    '.tmp/public/**/*.js',
                ]
            },
            options: {
                watchTask: true,
                server: {
                    baseDir: ".tmp/public",
                    port: 3300,
                    middleware: [proxy]
                }
            }
        },
    });

    grunt.loadNpmTasks('grunt-browser-sync');

};

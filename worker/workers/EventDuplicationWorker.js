const AbstractWorker = require('../AbstractWorker');
const { EVENT_DUPLICATION_QUEUE } = require('../../api/constants/workers-queue');

class EventDuplicationWorker extends AbstractWorker {
    static queueName() {
        return EVENT_DUPLICATION_QUEUE;
    }
    
    async doJob(job) {
        const webhookData = job.data;

        await EventService.duplication.process(webhookData);
    }
}

module.exports = EventDuplicationWorker;

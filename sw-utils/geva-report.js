'use strict';

const 
	argv 	= require('optimist').argv,
	xlsx 	= require('xlsx'),
	pg      = require('pg'),
	Logger 	= require('./log'),
	log 	= new Logger();
const { formatSheetName } = require('./export-helper');
const
	EVENT_ID 			= Number(argv.event),
	REGION 				= argv.region || (void 0),
	DB_CONNECTION_STR 	= argv.connection,
	OUTPUT_FILEPATH 	= argv.path,
	SHEET_TITLES 		= [
		'Event', 'Date', 'Div', 'First team', 'First code', 
		'Second team', 'Second code', 'Outcome', 'Scores'
	];

let connectionParams;

try {
	connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));
} catch (e) {
	console.error(e);
	process.exit(1)
}

log.debug('Connecting to the DB');
let dbClient = new pg.Client(connectionParams);
dbClient.connect();

new Promise((resolve, reject) => {
	let sqlParams = [EVENT_ID];
	if(REGION) {
		sqlParams.push(`%${REGION}`);
	}
	dbClient.query(
		`SELECT 
             e.name "event_name", 
             TO_CHAR(m.secs_start, 'mm/dd/YYYY') "date", 
             d.short_name "division_name",
             rt1.team_name "first_team", 
             rt1.organization_code first_code,
             rt2.team_name "second_team", 
             rt2.organization_code second_code, (
             CASE 
                 WHEN (m.results::JSON->>'winner')::INT = 1 THEN 'First team won'
                 WHEN (m.results::JSON->>'winner')::INT = 2 THEN 'Second team won'
                 ELSE 'Unknown result' END
             ) "outcome",
             SUBSTR(m.results::JSON->'team1'->>'scores', 7, LENGTH(m.results::JSON->'team1'->>'scores')-7) "scores"
         FROM "event" e
         INNER JOIN division d 
             ON d.event_id = e.event_id
         INNER JOIN matches m 
             ON m.division_id = d.division_id
         INNER JOIN roster_team rt1 
             ON rt1.roster_team_id = m.team1_roster_id 
             AND rt1.deleted IS NULL 
             AND rt1.status_entry = 12
             ${(sqlParams.length === 2)?'AND rt1.organization_code LIKE $2':''}
         INNER JOIN roster_team rt2 
             ON rt2.roster_team_id = m.team2_roster_id 
             AND rt2.deleted IS NULL 
             AND rt2.status_entry = 12
             ${(sqlParams.length === 2)?'AND rt2.organization_code LIKE $2':''}
         WHERE e.event_id = $1
         ORDER BY DATE(m.secs_start), d.short_name, m.secs_start`,
        sqlParams,
        (err, result) => {
        	if(err) {
        		reject(err);
        	} else {
        		resolve(result.rows || []);
        	}
        }
    )
}).then(resultRows => {
	log.debug('Got', resultRows.length, 'result rows from DB.');
	let sheet = {};

	SHEET_TITLES.forEach(function (title, index) {
		sheet[xlsx.utils.encode_cell({ c: index, r: 0 })] = { v: title, t: 's' };
	})

	resultRows.forEach(function (row, index) {
		let sheetLine = (index + 1);

		sheet[xlsx.utils.encode_cell({ c: 0, r: sheetLine })] = { v: row.event_name 	|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 1, r: sheetLine })] = { v: row.date 			|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 2, r: sheetLine })] = { v: row.division_name 	|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 3, r: sheetLine })] = { v: row.first_team 	|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 4, r: sheetLine })] = { v: row.first_code 	|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 5, r: sheetLine })] = { v: row.second_team 	|| '', 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 6, r: sheetLine })] = { v: row.second_code 	|| '',  t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 7, r: sheetLine })] = { v: row.outcome 		|| '',  t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 8, r: sheetLine })] = { v: row.scores 		|| '',  t: 's' };
	})

	sheet['!ref'] = xlsx.utils.encode_range({
		s: { c: 0, r: 0 }, 
		e: { c: SHEET_TITLES.length, r: (resultRows.length + 1) }
	})
	let event_name = (resultRows[0] && resultRows[0].event_name) || ''

	return {
		name 		: formatSheetName(`${event_name} $1`, ['results for GEVA']),
		content 	: sheet
	};
}).then(sheetData => {
	log.debug('Sheet Generated')
	let workbook = {
		SheetNames 	: [sheetData.name],
		Sheets 		: {}
	};

	workbook.Sheets[sheetData.name] = sheetData.content;
	// writing option used in xlsx is 'w' 
	// 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
	xlsx.writeFile(workbook, OUTPUT_FILEPATH,  { font: { name: "Verdana" }, bookSST: true });
	log.debug('WorkBook written')
}).then(() => {
	process.exit(0)
}).catch(err => {
	log.error(err);
	process.exit(1);
})







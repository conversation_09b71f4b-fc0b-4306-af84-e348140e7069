const pg                = require('pg');
const _                 = require('lodash');
const clubService       = require('../api/services/ClubService.geo');
const GoogleMapsUtils   = require('../api/lib/GoogleMapsUtils');

const DB_CONNECTION_STRING  = process.argv[2];
const SECRET_KEY            = process.argv[3];
const EVENT_ID              = Number(process.argv[4]);
const LIMIT                 = 20;


if (!DB_CONNECTION_STRING) {
    error('Missed DB Connection String!', false)
}

if (!SECRET_KEY) {
    error('Missed Secret Key!', false)
}

if (process.argv[4] && !EVENT_ID) {
    error('Event ID should be a Number!', false);
}

const Db            = new pg.Client(DB_CONNECTION_STRING);
const ClubService   = new clubService();

let clubs                           = [];
let cacheMasterClubCoordinates      = {};
let cacheEventCoordinates           = {};
let errorEvents                     = [];
let numberOfCalculatedDistances     = 0;
let numberOfClubsFound              = 0;

(async () => {
    try {
        await Db.connect();

        GoogleMapsUtils._init(SECRET_KEY);

        if (EVENT_ID) {
            await calculateDistanceByEventID(EVENT_ID);
        } else {
            await calculateDistanceForAllUpcomingEvents();
        }
    } catch (e) {
        console.error(e);
    } finally {
        showInfo();

        process.exit();
    }
})();

function error(message, showInformation = true) {
    console.error(message);

    if (showInformation) {
        showInfo();
    }

    process.exit();
}

async function calculateDistanceForAllUpcomingEvents() {
    do {
        const clubRows = await getClubsWithoutDistanceForAllUpcomingEvents(LIMIT);

        if (!clubRows.clubs) {
            showInfo();

            process.exit();
        }

        clubs = clubRows.clubs;

        increaseNumberOfClubsFound(clubs.length);

        for (let i = 0; i < clubs.length; i++) {
            const {
                event_id,
                roster_club_id,
                master_club_id,
                master_club_coordinates,
                event_coordinates
            } = clubs[i];

            if (errorEvents.includes(event_id)) {
                continue;
            }

            if (_.isEmpty(event_coordinates) && _.isEmpty(cacheEventCoordinates[event_id])) {
                const address = await getEventAddress(event_id);

                cacheEventCoordinates[event_id] = await findLocation('', address);

                if (_.isEmpty(cacheEventCoordinates[event_id])) {
                    errorEvents.push(event_id);

                    continue;
                }

                await saveEventCoordinates(cacheEventCoordinates[event_id], event_id);
            }

            const eventCoordinates = getCoordinates(event_coordinates, cacheEventCoordinates[event_id]);

            await checkMasterClubCoordinates(
                master_club_coordinates,
                cacheMasterClubCoordinates[master_club_id],
                master_club_id
            );

            const masterClubCoordinates = getCoordinates(master_club_coordinates, cacheMasterClubCoordinates[master_club_id]);

            if (!_.isEmpty(masterClubCoordinates) && !_.isEmpty(eventCoordinates)) {
                await calculateDistance(eventCoordinates, masterClubCoordinates, roster_club_id);
            }
        }

    } while(clubs.length);
}

/**
 * @param {Number} eventID
 */
async function calculateDistanceByEventID(eventID) {
    const eventCoordinatesRows = await getEventCoordinates(eventID);

    if (!eventCoordinatesRows.length) {
        error(`Event not found!`);
    }

    let eventCoordinates = eventCoordinatesRows[0].coordinates;

    if (_.isEmpty(eventCoordinates)) {
        const address = await getEventAddress(eventID);

        eventCoordinates = await findLocation('', address);

        if (_.isEmpty(eventCoordinates)) {
            process.exit();
        }

        await saveEventCoordinates(eventCoordinates, eventID);
    }

    do {
        const clubsRows = await getClubsWithoutDistanceByEventID(eventID, LIMIT);

        if (!clubsRows.clubs) {
            showInfo();

            process.exit();
        }

        clubs = clubsRows.clubs;

        increaseNumberOfClubsFound(clubs.length);

        for (let i = 0; i < clubs.length; i++) {
            const {
                roster_club_id,
                master_club_id,
                master_club_coordinates
            } = clubs[i];

            await checkMasterClubCoordinates(
                master_club_coordinates,
                cacheMasterClubCoordinates[master_club_id],
                master_club_id
            );

            const masterClubCoordinates = getCoordinates(master_club_coordinates, cacheMasterClubCoordinates[master_club_id]);

            if (!_.isEmpty(masterClubCoordinates) && !_.isEmpty(eventCoordinates)) {
                await calculateDistance(eventCoordinates, masterClubCoordinates, roster_club_id);
            }
        }

    } while(clubs.length);
}

/**
 * @param {Number} limit
 * @returns {Object}
 */
async function getClubsWithoutDistanceForAllUpcomingEvents(limit) {
    const queryResult =  await Db.query(GET_CLUBS_SQL(), [limit]);

    return queryResult.rows[0];
}

/**
 *
 * @param {Number} id - event id
 * @param {Number} limit
 * @returns {Object}
 */
async function getClubsWithoutDistanceByEventID(id, limit) {
    const queryResult = await Db.query(GET_CLUBS_SQL(id), [limit, id]);

    return queryResult.rows[0];
}

/**
 * @param {Number} id - event id
 * @returns {Object}
 */
async function getEventCoordinates(id) {
    const queryResult = await Db.query(`
        SELECT coordinates FROM event WHERE event_id = $1
    `, [id]);

    return queryResult.rows;
}

/**
 * @param {Number} id - event id
 * @returns {String}
 */
async function getEventAddress(id) {
    const queryResult = await Db.query(`
        SELECT concat_ws(' ', address, city, state, zip) AS address 
        FROM event
        WHERE event_id = $1
    `, [id]);

    return queryResult.rows[0].address;
}

/**
 * @param {Number} id - master club id
 * @returns {String}
 */
async function getMasterClubAddress(id) {
    const queryResult = await Db.query(`
        SELECT concat_ws(' ', address, city, state, zip) AS address 
        FROM master_club
        WHERE master_club_id = $1
    `, [id]);

    return queryResult.rows[0].address;
}

/**
 * @param {Object} coordinates
 * @param {Number} eventID
 */
async function saveEventCoordinates(coordinates, eventID) {
    await Db.query(`
        UPDATE event SET coordinates = $1 WHERE event_id = $2
    `, [coordinates, eventID]);
}

/**
 * @param {Object} coordinates
 * @param {Number} masterClubID
 */
async function saveMasterClubCoordinates(coordinates, masterClubID) {
    await Db.query(`
        UPDATE master_club SET coordinates = $1 WHERE master_club_id = $2
    `, [coordinates, masterClubID]);
}

/**
 * @param {String} zip
 * @param {String} address + city + state + zip
 * @returns {Object} coordinates
 */
function findLocation(zip, address) {
    return new Promise((resolve) => {
        let _address = `${ zip }${ (!!address) ? (' ' + address) :'' }`;

        GoogleMapsUtils.getAPIInstance().geocode({
            address     : _address,
            language    : 'en'
        }, (err, result) => {
            if(err) {
                error(err);
            } else {

                let zipLocation = result.results && result.results[0];

                if (_.isEmpty(zipLocation)) {
                    console.error(`No Location found for address ${_address}. REASON: ${result.error_message}`);
                }

                resolve((zipLocation && zipLocation.geometry && zipLocation.geometry.location) || {})
            }
        });
    })
}

/**
 * @param {Number} distance
 * @param {Number} rosterClubID
 */
async function saveDistance(distance, rosterClubID) {
    await Db.query(`
        UPDATE roster_club SET distance_to_event = $1 WHERE roster_club_id = $2
    `, [distance, rosterClubID]);
}

/**
 * @param {Object} ob1
 * @param {Object} ob2
 * @returns {Object}
 */
function getCoordinates(ob1, ob2) {
    return _.isEmpty(ob1) ? ob2 : ob1;
}

/**
 * @param {Object} eventCoordinates
 * @param {Object} masterClubCoordinates
 * @param {Number} rosterClubID
 */
async function calculateDistance(eventCoordinates, masterClubCoordinates, rosterClubID) {
    if (!_.isEmpty(eventCoordinates) && !_.isEmpty(masterClubCoordinates)) {
        const distance = ClubService.generateDistance(eventCoordinates, masterClubCoordinates);

        await saveDistance(distance, rosterClubID);

        console.info(`Distance for club ${rosterClubID} calculated successfully: ${distance} miles`);
        increaseNumberOfCalculatedDistance();
    }
}

function showInfo() {
    console.log('Process finished:');
    console.log(` - Found ${numberOfClubsFound} clubs`);
    console.log(` - Calculated distance for ${numberOfCalculatedDistances} clubs`);
}

/**
 * @param {Number} value
 */
function increaseNumberOfClubsFound(value) {
    numberOfClubsFound += value;
}

function increaseNumberOfCalculatedDistance() {
    numberOfCalculatedDistances += 1;
}

/**
 * @param {Object} fromDB - coordinates from DB
 * @param {Object} fromCache - coordinates from cache
 * @param {Number} masterClubID
 */
async function checkMasterClubCoordinates(fromDB, fromCache, masterClubID) {
    if (_.isEmpty(fromDB) && _.isEmpty(fromCache)) {
        const address = await getMasterClubAddress(masterClubID);

        cacheMasterClubCoordinates[masterClubID] = await findLocation('', address);

        if (!_.isEmpty(cacheMasterClubCoordinates[masterClubID])) {
            await saveMasterClubCoordinates(cacheMasterClubCoordinates[masterClubID], masterClubID);
        }
    }
}

/**
 * @param {Number} [eventID]
 * @return {String}
 */
function GET_CLUBS_SQL(eventID) {
    return `
        SELECT ARRAY_TO_JSON(ARRAY_AGG(data)) as clubs
            FROM (
              SELECT
                rc.roster_club_id,
                rc.master_club_id,
                e.event_id,
                mc.coordinates master_club_coordinates,
                e.coordinates event_coordinates
              FROM roster_club rc
                JOIN master_club mc ON mc.master_club_id = rc.master_club_id
                JOIN event e ON e.event_id = rc.event_id
                WHERE rc."deleted" IS NULL
                    ${eventID ? '' : 'AND e."date_start"::DATE > CURRENT_DATE'}
                    AND e."has_status_housing" IS TRUE
                    AND rc."distance_to_event" IS NULL
                    ${eventID ? 'AND e."event_id" = $2' : ''}
                    ORDER BY rc."roster_club_id"
                    LIMIT $1
              ) AS data
    `
}

'use strict';

const argv  = require('optimist').argv;
const fs    = require('fs').promises;
const xlsx  = require('xlsx');
const _  = require('lodash');
global._ = require('lodash');
global.squel = require('../api/services/squel');
global.knex = require('knex')({client: 'pg'});
global.sails = {
    config: {
        sw_season: require('../config/sw_season').sw_season,
    },
};
global.EventTeamService = require('../api/services/EventTeamService');

const {
    event: EVENT_ID,
    statusEntry: STATUS_ENTRY,
    path: PATH_TO_FILE,
    conn: DB_CONNECTION,
} = argv;

const DOCUMENT_HEADING = {
    TEAM_NAME: 'Team Name',
    DIVISIONS: 'Divisions',
    CLUB_NAME: 'Club Name (Not required)',
    TEAM_CODE: 'Team Code (Not required)',
};

const connectionConfig = JSON.parse(Buffer.from(DB_CONNECTION, 'base64').toString('utf-8'));
const connectionParams = _.isObject(connectionConfig)
    ? _.omit(connectionConfig, 'module')
    : {connectionString: connectionConfig};
const db = require('../api/lib/db.js');
global.Db = new db(connectionParams, {}, {error: console.error});
Db.utils = require('../api/lib/db_utils');

async function processImport() {
    try {
        const divisionIdMap = await buildDivisionIdMap(EVENT_ID);

        const file = await fs.readFile(PATH_TO_FILE);
        let parsedRows = parseFile(file, divisionIdMap);

        if(!parsedRows.length) {
            throw { error: 'Empty list passed' };
        }
        const parsedCount = parsedRows.length;

        await EventTeamService.manual_teams_addition.addTeamsManually(
            EVENT_ID,
            {
                statusEntry: STATUS_ENTRY,
                teams: parsedRows,
            },
            {
                allowUpdating: true,
            }
        );

        return { parsed: parsedCount };
    } catch (err) {
        throw err;
    }
}

async function buildDivisionIdMap(eventID) {
    const { rows } = await Db.query(
        knex('division')
            .select(['division_id', 'name'])
            .where('event_id', eventID)
    );
    const divisionIdMap = new Map();
    for(const {division_id, name} of rows) {
        if(divisionIdMap.has(name)) {
            throw { validation: `Event has multiple divisions with name "${name}"` };
        }
        divisionIdMap.set(name, division_id);
    }

    return divisionIdMap;
}

function parseFile(file, divisionIdMap) {
    let workbook = xlsx.read(file, { raw: true });

    let workingSheet = workbook.Sheets[workbook.SheetNames[0]];
    let rawJSON      = xlsx.utils.sheet_to_json(workingSheet, { header: 1 });

    return formatRows(rawJSON, divisionIdMap);
}

function formatRows (parsedFile, divisionIdMap) {
    let headings = parsedFile[0];

    const columnsConfig = [
        {name: 'teamName', title: DOCUMENT_HEADING.TEAM_NAME, required: true},
        {name: 'divisionName', title: DOCUMENT_HEADING.DIVISIONS, required: true},
        {name: 'clubName', title: DOCUMENT_HEADING.CLUB_NAME, required: false},
        {name: 'teamCode', title: DOCUMENT_HEADING.TEAM_CODE, required: false},
    ].reduce((config, columnConfig) => {
        const index = headings.indexOf(columnConfig.title);
        if(index === -1) {
            if(columnConfig.required) {
                throw { error: `"${columnConfig.title}" column not found` };
            }
            else {
                return config;
            }
        }
        config.push({
            ...columnConfig,
            index,
        });
        return config;
    }, []);

    const parseRow = (row, index) => {
        return columnsConfig.reduce((result, columnConfig) => {
            let value = row[columnConfig.index];
            if(!value) {
                if(columnConfig.required) {
                    throw {validation: `"${columnConfig.title}" column value is required on row ${index+1}`};
                }
                else {
                    result[columnConfig.name] = null;
                    return result;
                }
            }
            result[columnConfig.name] = value.toString().trim();

            return result;
        }, {});
    };

    return parsedFile.reduce((all, row, index) => {
        const isHeadings = index === 0;
        const rowHasValues = row.some(v => Boolean(v));
        if(!isHeadings && rowHasValues) {
            const {
                teamName,
                divisionName,
                clubName,
                teamCode
            } = parseRow(row, index);
            const divisionId = divisionIdMap.get(divisionName);
            if(!divisionId) {
                throw { validation: `Division "${divisionName}" not found on event` };
            }

            all.push({
                team_name: teamName,
                division_id: divisionId,
                manual_club_name: clubName,
                team_code: teamCode
            });
        }

        return all;
    }, [])
}

processImport()
    .then(result => {
        process.stdout.write(JSON.stringify(result));
        process.exit(0);
    })
    .catch(err => {
        Db.end();
        console.error(JSON.stringify(err));

        process.exit(1);
    });

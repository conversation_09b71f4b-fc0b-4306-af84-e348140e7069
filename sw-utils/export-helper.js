"use strict";

const xlsx = require('xlsx');
const moment = require('moment');
const _ = require('lodash');
const swUtils = require('../api/lib/swUtils');

const DEFAULT_WRITE_OPTIONS = { font: { name: "<PERSON><PERSON><PERSON>" }, bookSST: true };

const FORMAT_VARIABLE_REGEX = /\$(\d+)/g;
const MAX_SHEET_NAME_LENGTH = 31;

const capitalize = s => _.capitalize(s);

function _validateFormat(format) {
    const varIndexesInFormat = format.match(FORMAT_VARIABLE_REGEX).map(v => Number(v.substring(1))).sort();

    if(varIndexesInFormat.length === 0) {
        return;
    }

    if(varIndexesInFormat[0] !== 1) {
        throw new Error('Variable indexes should start from 1');
    }
    for(let i = 1; i < varIndexesInFormat.length; i++) {
        if(varIndexesInFormat[i-1] !== varIndexesInFormat[i] + 1) {
            if(varIndexesInFormat[i-1] === varIndexesInFormat[i]) {
                throw new Error(`Variable can't be used multiple times`);
            }
            else {
                throw new Error(`Variables shouldn't skip indexes`);
            }
        }
    }
}

const _createFieldObject = (title, transformation = v => v) => ({
    title,
    transformation,
});

const _transformations = {
    bool: v => v ? 'Yes' : 'No',
    capitalize,
    hotelNightsRequired(hotel_nights_required, {need_hotel_room}) {
        if(!hotel_nights_required || !need_hotel_room) {
            return '';
        }
        const nights = hotel_nights_required.dates;
        return Object.keys(nights).map(
            night => nights[night] && moment(night, 'MMDDYYYY').format('dd')
        ).filter(
            v => !!v
        ).join(', ');
    },
    time(date) {
        if(!date) return '';

        return moment(date).format('ddd h:mm A');
    },
    clothesSize(size) {
        if(!size) {
            return '-';
        }

        return size;
    }
};

const _cell = (c, r) => ({c, r});
const _range = (c, r, w, h) => ({s: {c, r}, e: {c: c + w, r: r + h}});
const _getSheetRange = (sheet) => (sheet['!ref'] ? xlsx.utils.decode_range(sheet['!ref']) : _range(0, 0, 0, 0));

const _getDataType = (data) => {
    if(data instanceof Number) {
        return 'n';
    }
    if(data instanceof Date) {
        return 'd';
    }
    if(_.isBoolean(data)) {
        return 'b';
    }
    return 's';
};


const parseParameterObject = (connectionStr) => {
    return Promise.resolve(JSON.parse(Buffer.from(connectionStr, 'base64').toString('utf-8')));
};

const showError = (message) => {
    process.stderr.write(message);
    process.exit(1);
};

const fieldMaps = {
    officialTravelInfo: new Map([
        ['first', _createFieldObject('First', _transformations.capitalize)],
        ['last', _createFieldObject('Last', _transformations.capitalize)],
        ['travel_method', _createFieldObject('Travel Method', _transformations.capitalize)],
        ['need_hotel_room', _createFieldObject('Hotel', _transformations.bool)],
        ['hotel_nights_required', _createFieldObject('Nights Required', _transformations.hotelNightsRequired)],
        ['roommate_preference', _createFieldObject('Roommate')],
        ['departure_datetime', _createFieldObject('Departure Date/Time', _transformations.time)],
        ['additional_restrictions', _createFieldObject('Conflicts')],
    ]),
    staffTravelInfo: new Map([
        ['first', _createFieldObject('First', _transformations.capitalize)],
        ['last', _createFieldObject('Last', _transformations.capitalize)],
        ['travel_method', _createFieldObject('Travel Method', _transformations.capitalize)],
        ['need_hotel_room', _createFieldObject('Hotel', _transformations.bool)],
        ['hotel_nights_required', _createFieldObject('Nights Required', _transformations.hotelNightsRequired)],
        ['roommate_preference', _createFieldObject('Roommate')],
        ['arrival_time', _createFieldObject('Arrival Date/Time', _transformations.time)],
        ['departure_datetime', _createFieldObject('Departure Date/Time', _transformations.time)],
        ['additional_restrictions', _createFieldObject('Conflicts')],
    ]),
    officialClothingSizes(eventClothes) {
        let result = new Map([
            ['first', _createFieldObject('First', _transformations.capitalize)],
            ['last', _createFieldObject('Last', _transformations.capitalize)],
        ]);
        eventClothes.forEach(({common_item_id, title}) => {
            result.set(common_item_id, _createFieldObject(title, _transformations.clothesSize));
        });
        result.set('special_sizing_requests', _createFieldObject('Notes'));

        return result;
    },
    teamsResults: new Map([
        ['event_name', _createFieldObject('Event Name')],
        ['Opponent name', _createFieldObject('Opponent name')],
        ['Opponent code', _createFieldObject('Opponent code')],
        ['Start Date', _createFieldObject('Date')],
        ['Result', _createFieldObject('Result')],
        ['set1', _createFieldObject('Set 1')],
        ['set2', _createFieldObject('Set 2')],
        ['set3', _createFieldObject('Set 3')],
    ]),
};

const formatTable = (data, map) => {
    const sheet = [
        Array.from(map, (([, {title}]) => title))
    ].concat(
        data.map(row => {
            return Array.from(map, (([key, {transformation}]) => {
                const val = transformation(row[key], row);
                if(_.isObject(val)) {
                    throw new Error('Object is not a valid cell value type. Got: ' + JSON.stringify(val));
                }
                return val;
            }));
        })
    );

    return sheet;
};

const sheet_add_merged_cells = (sheet, data, {origin = {r: -1, c: 0}, width = 1, height = 1}) => {
    let range = _getSheetRange(sheet);
    let {c, r} = origin;
    if(r === -1) {
        r = range.e.r + 1;
    }
    range.e.c = Math.max(range.e.c, c + width - 1);
    range.e.r = Math.max(range.e.r, r + height - 1);
    sheet['!ref'] = xlsx.utils.encode_range(range);

    if(width !== 1 || height !== 1) {
        if (!sheet['!merges']) {
            sheet['!merges'] = [];
        }
        sheet['!merges'].push(_range(c, r, width - 1, height - 1));
    }

    sheet[xlsx.utils.encode_cell(_cell(c, r))] = {
        v: data,
        t: _getDataType(data),
    };

    return _cell(c, r);
};

const sheet_add_new_lines = (sheet, lines = 1) => {
    let range = _getSheetRange(sheet);
    range.e.r += lines;
    sheet['!ref'] = xlsx.utils.encode_range(range);
};

const formatSheetName = function(format, variables) {
    _validateFormat(format);

    const formatLength = format.replace(FORMAT_VARIABLE_REGEX, '').length;
    let usedLength = 0;
    const formatVariables = [];
    for(const variable of variables) {
        if(formatLength + usedLength >= MAX_SHEET_NAME_LENGTH) {
            break;
        }
        const variableValuePart = variable.substring(0, MAX_SHEET_NAME_LENGTH - formatLength - usedLength);
        usedLength += variableValuePart.length;
        formatVariables.push(variableValuePart);
    }

    let formattedSheetName = format.replace(FORMAT_VARIABLE_REGEX, (match, varId) => {
        return formatVariables[Number(varId) - 1] || '';
    }).substring(0, MAX_SHEET_NAME_LENGTH);

    return swUtils.getSafeFileName(formattedSheetName);
}

// set dynamically column width to fit content
function getColumnWidths(sheet, rows, columns) {
    const columnWidths = Array(columns).fill(0);

    _.range(0, rows).forEach((row) => {
        _.range(0, columns).forEach((col) => {
            const cell = sheet[xlsx.utils.encode_cell({ c: col, r: row })];

            if (cell && columnWidths[col] < cell.v.length) {
                columnWidths[col] = cell.v.length;
            }
        });
    });

    return columnWidths.map((value) => ({ wch: value }));
}

module.exports = {
    DEFAULT_WRITE_OPTIONS,
    parseParameterObject,
    showError,

    fieldMaps,
    formatTable,
    sheet_add_merged_cells,
    sheet_add_new_lines,
    formatSheetName,
    getColumnWidths,
};

/* jshint node:true */
/* jshint esversion:6 */
const SW_ACC_SK = '********************************';
const stripe 	= require('stripe')(SW_ACC_SK);
const co 		= require('co');
const pg 		= require('pg');

// Docs: https://nodejs.org/docs/latest/api/process.html#process_process_argv
const DB_CONN 			= process.argv[2];
const YEAR 				= parseInt(process.argv[3], 10); 

if (!DB_CONN) {
	throw new Error('No Connection String passed!');
}

if (!YEAR || (YEAR < 2014)) {
	throw new Error('Invalid year passed!');
}

// Docs: https://github.com/brianc/node-postgres/wiki/Client
const pgClient = new pg.Client(DB_CONN);

return co(function* () {
    let createdFrom 		= new Date(YEAR, 0, 1);
    let createdFromUnix 	= ((createdFrom.getTime() / 1000) | 0);

    yield (clientConnect(pgClient));

    console.log('Loading collected fees list from Stripe ...');
	let feesListQty = yield (findFeesList(createdFromUnix, pgClient));

	console.log('Disconnecting from DB ...');
	yield (clientDisconnect(pgClient));

	console.log('Total:', feesListQty, 'fee objects found from', createdFrom);
})
.catch(err => {
	console.error(err);
	process.exit(1);
})

// Replace single quote for PG
function prepareJSONObject (destination, obj) {
	Object.keys(obj).forEach(key => {
		let val = obj[key];

		if (Object.prototype.toString.call(val) === '[object Object]') {
			destination[key] = prepareJSONObject({}, val);
		} else if (Object.prototype.toString.call(val) === '[object String]') {
			destination[key] = val.replace(/'/g, '\'\'');
		} else {
			destination[key] = val;
		}
	})

	return destination;
}

function prepareJSONForSQL (list) {
	let prepared = [];

	list.forEach(listItem => {

		let preparedItem = prepareJSONObject({}, listItem);

		prepared.push(preparedItem)
	})
	
	return JSON.stringify(prepared);
}

function findFeesList (gte, dbClient) {
	return findAndUpsertStripeCharges(0, { gte }, dbClient);
}

function getPlatfromCollectedFees (params = {}) {
	return stripe.applicationFees.list({
		created 		: {
			gte: params.gte || (void 0)
		},
		starting_after  : params.after || (void 0),
		limit 			: 100,
		expand 			: [
			/* Originating transaction contains a charge object (with ch_*** id) */
			/* Balance transaction contains the Stripe processing fee amount */
			'data.originating_transaction.balance_transaction', 
			/* To retrieve current amount and fee values, we need to get all the refunds, associated with the charge */
			// 'data.originating_transaction.refunds.data.balance_transaction' 
		]
	})
	.then(resp => resp.data)
	.then(retrieveRefundBalanceTransaction.bind(null, stripe))
}

function retrieveRefundBalanceTransaction (stripe, feesList) {
	return Promise.all(feesList.map(fee => {

		if (fee.originating_transaction.refunds.data.length > 0) {
			/** 
			* To get the refunded amount (and fee amount too) we need to 
			* retrieve a balance_transaction object for every refund, existing on the charge 
			**/
			return stripe.refunds.list({ 
				charge: fee.originating_transaction.id, 
				expand: ['data.balance_transaction'] 
			}).then(resp => {
				fee.originating_transaction.refunds.data = resp.data;

				return fee;
			})

		} else {
			return fee;
		}

	}))
}

function findAndUpsertStripeCharges (resultQty, params = {}, dbClient = null) {
	if (resultQty > 0) {
		console.log('  ===', resultQty, 'fee rows already processed ===  ');
	}
	
	return getPlatfromCollectedFees(params)
	.then(list => list.map(formatApplicationFeeObject))
	.then(expandedChargesList => {

		console.log('  > Got:', expandedChargesList.length, 'fee objects');

		if (expandedChargesList.length === 0) {
			return resultQty;
		}

		return beginTransaction(pgClient)
		.then(() => {
			return prcessDBRows(dbClient, expandedChargesList)
			.catch(err => {
				return rollbackTransaction(pgClient)
				.then(() => Promise.reject(err));
			})
		})
		.then(res => {

			console.log(
				' * Db results:', 
				res.sch_upd, '/', res.sch_ins, '"stripe_charge" upd/ins, ', 
				res.p_upd, '"purchase" updated'
			);

			return commitTransaction(pgClient)
		})
		.then(() => expandedChargesList)
		.then(() => {
			let lastItem 	= expandedChargesList[expandedChargesList.length - 1];
			let lastItemID 	= lastItem && lastItem.id;	

			params.after 	= lastItemID;	

			resultQty += expandedChargesList.length;	

			return findAndUpsertStripeCharges(resultQty, params, dbClient);
		})
	})
}

function formatApplicationFeeObject (fee) {
	let chargeAmount 			= fee.originating_transaction.amount,
		chargeRefundedAmount  	= fee.originating_transaction.amount_refunded || 0;

		/* NOTE: stripe does not decrease the amount of charge when refund occures */
		chargeAmount 			= approxNum((chargeAmount - chargeRefundedAmount) / 100);

	let stripeProcessingFee 	= fee.originating_transaction.balance_transaction.fee || 0;

	let chargeRefundsList 		= fee.originating_transaction.refunds.data;

	let stripeProcessingFeeRefunded = chargeRefundsList.reduce((sum, refund) => {
        if (refund.status === 'succeeded') {
        	/* we have negative fee for refunds */
            sum += refund.balance_transaction.fee || 0
        }

        return sum;
    }, 0);

	stripeProcessingFee = approxNum((stripeProcessingFee + stripeProcessingFeeRefunded) / 100);

	let res =  {
		id 						: fee.id,
		amount 					: chargeAmount, 
		fee 					: stripeProcessingFee,
		balance_transaction 	: fee.originating_transaction.balance_transaction,
		/* NOTE: stripe does not decrease the amount of collected fee when refund occures */
		collected_fee 			: approxNum((fee.amount - fee.amount_refunded) / 100),
		stripe_charge_id 		: fee.originating_transaction.id
	}

	return res;
}

function approxNum (n) {
	return (Math.round(parseFloat(n) * 100) / 100) || 0;
}

function prcessDBRows (dbClient, chargesList) {
	let qtyResult = {};

	return Promise.resolve().then(() => {
		let updSQLQuery = genUpdSQLQuery(chargesList);

		console.log('    > Updating "stripe_charge" rows ...');
		return runSQLQuery(dbClient, updSQLQuery)
		.then(res => res.rows.map(charge => charge.stripe_charge_id));
	}).then(updatedCharges => {

		qtyResult.sch_upd = updatedCharges.length;

		let rowsToInsert;

		if (updatedCharges.length > 0) {
			rowsToInsert = chargesList.filter(charge => {
				return (updatedCharges.indexOf(charge.stripe_charge_id) === -1);
			});
		} else {
			rowsToInsert = chargesList;
		}

		let insSQLQuery = getInsSQLQuery(rowsToInsert);

		console.log('    > Inserting "stripe_charge" rows ...');

		return runSQLQuery(dbClient, insSQLQuery)
		.then(res => {
			qtyResult.sch_ins = res.rows.length;
		})
	})
}

function genUpdSQLQuery (feesList) {

	let feesStringified = prepareJSONForSQL(feesList);

	return (
		`UPDATE "stripe_charge" sch 
		 SET "amount" 				= "d"."amount",
		 	 "fee"					= "d"."fee",
		 	 "balance_transaction" 	= "d"."balance_transaction",
		 	 "collected_fee" 		= "d"."collected_fee" 
		 FROM (
			SELECT 
			 	"d"->>'stripe_charge_id' "stripe_charge_id",
			 	("d"->>'amount')::NUMERIC "amount",
			 	("d"->>'fee')::NUMERIC "fee",
			 	("d"->>'balance_transaction')::JSON "balance_transaction",
			 	("d"->>'collected_fee')::NUMERIC "collected_fee"
			 FROM JSON_ARRAY_ELEMENTS('${feesStringified}') "d"
		 ) "d"
		 WHERE sch."stripe_charge_id" = d."stripe_charge_id"
		 	AND sch."type" = 'connect'
		 RETURNING sch."stripe_charge_id"`
	)
}

function getInsSQLQuery (feesListToInsert) {
	let feesStringified = prepareJSONForSQL(feesListToInsert);

	return (
		`INSERT INTO "stripe_charge" (
			"stripe_charge_id", "amount", "fee", "balance_transaction", "collected_fee", "type"
		 )
		 SELECT 
		 	"d"->>'stripe_charge_id' "stripe_charge_id",
		 	("d"->>'amount')::NUMERIC "amount",
		 	("d"->>'fee')::NUMERIC "fee",
		 	("d"->'balance_transaction')::JSON "balance_transaction",
		 	("d"->>'collected_fee')::NUMERIC "collected_fee",
		 	'connect'::TEXT "type"
		 FROM JSON_ARRAY_ELEMENTS('${feesStringified}') "d"
		 RETURNING "stripe_charge_id"`
	)
}

/* === HACKS FOR THE OLD PG MODULE === */

function clientConnect (client) {
	return new Promise((resolve, reject) => {
		client.connect(err => {
  			if (err) {
  				reject(err);
  			} else {
  				resolve();
  			}
  		})
	})
}

function runSQLQuery (client, query) {
	return new Promise((resolve, reject) => {
		client.query(query, (err, result) => {
			if (err) {
				err.query = query;
				reject(err);
			} else {
				resolve(result);
			}
		})
	});
}

function beginTransaction (client) {
	return runSQLQuery(client, 'BEGIN;');
}

function commitTransaction (client) {
	return runSQLQuery(client, 'COMMIT;');
}

function rollbackTransaction (client) {
	return runSQLQuery(client, 'ROLLBACK;');
}

function clientDisconnect (client) {
	return new Promise((resolve, reject) => {
		client.end(err => {
			if (err) {
				reject(err);
			} else {
				resolve();
			}
		})
	});	
}

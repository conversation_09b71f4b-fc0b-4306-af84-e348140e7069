'use strict';

const
	caller 		= require('../api/services/callerDataService'),
	LINE_BREAK 	= require('os').EOL;


function Log () {}

Log.prototype.debug = function () {
	let at 		= caller.getCallers(1),
		args 	= Array.prototype.slice.call(arguments);
	args.push(at);
	args.push(LINE_BREAK);
	console.log.apply(this, args)
}

Log.prototype.error = function () {
	let at 		= caller.getCallers(1),
		args 	= Array.prototype.slice.call(arguments);
	args.push(at);
	args.push(LINE_BREAK);
	console.error.apply(this, args);
}


module.exports = Log;
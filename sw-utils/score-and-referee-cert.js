'use strict';

const argv          = require('optimist').argv
const co            = require('co');
const request       = require('request-promise');
const { Client }    = require('pg');
const XML           = require('nodexml')

const SW_WP_USERNAME    = 'SportWrench';
const SW_WP_PSWD        = 'xm1@usavWebp0int';

const DB_CONN_STR = argv.db_conn;
const TOTAL_CLUBS_LIMIT = Number(argv.limit);
const LIMIT = 10;

const REFCERTNAME   = 'REFCERTNAME';
const REFENDDATE    = 'REFENDDATE';
const SCORECERTNAME = 'SCORECERTNAME';
const SCOREENDDATE  = 'SCOREENDDATE';

const CERT_PROPS = [REFCERTNAME, REFENDDATE, SCORECERTNAME, SCOREENDDATE];

function findClubs (client, offset = 0) {
    const FIND_CLUBS_QUERY = 
        `SELECT 
            mc."master_club_id" "id",
            mc."webpoint_username",
            mc."webpoint_password"
        FROM "master_club" mc
        WHERE mc."webpoint_username" IS NOT NULL
            AND mc."webpoint_password" IS NOT NULL
            AND EXISTS (
                SELECT 1 
                FROM "master_athlete" ma 
                WHERE ma."master_club_id" = mc."master_club_id"
            )
        ORDER BY mc."created" DESC
        LIMIT ${LIMIT}
        OFFSET ${offset}`;

    return new Promise((resolve, reject) => {
        client.query(FIND_CLUBS_QUERY, null, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve(res.rows);
            }
        })
    })
}

function getClubMembersFromWP (club) {
    const CODE_PROP         = 'Code';
    const ERROR_MSG_PROP    = 'Msg';
    const CONTEXT_PROP      = 'Context';
    const MEMBER_PROP       = 'MEMBER';

    const WP_SUCCESS_CODE   = 300;



    let {webpoint_username: clubLogin, webpoint_password: clubPswd } = club;

    return request({
        method  : 'POST',
        uri     : 'https://webpoint.usavolleyball.org/wp15/Contacts/ClubMbrData.wp',
        /* 'Content-Type'  : 'application/x-www-form-urlencoded' is set automatically */
        form    : {
            Username            : SW_WP_USERNAME,
            Passcode            : SW_WP_PSWD,
            ClubAdminUsername   : clubLogin,
            ClubAdminPassword   : clubPswd
        }
    })
    .then(respBody => XML.xml2obj(respBody))
    .then(respJSON => {
        let { RESPONSE: wpResp, CLUBMEMBERS_DATA: membersData } = respJSON;

        if (wpResp && Number(wpResp[CODE_PROP]) !== WP_SUCCESS_CODE) {
            return Promise.reject(new Error(wpResp[ERROR_MSG_PROP]))
        }

        if (membersData && membersData.RESPONSE && Number(membersData.RESPONSE[CODE_PROP]) !== WP_SUCCESS_CODE) {
            return Promise.reject(new Error(membersData.RESPONSE[CONTEXT_PROP]));
        }

        return membersData && membersData[MEMBER_PROP] || [];
    })
}

co(function* () {
    const client = new Client(DB_CONN_STR);

    client.connect();

    let result = {};

    for (let prop  of CERT_PROPS) {
        result[prop] = new Set();
    }

    let offset = 0;
    let clubsQty = 0;

    while (true) {
        
        let clubs = yield (findClubs(client, offset));

        if (clubs.length === 0 || (TOTAL_CLUBS_LIMIT && clubsQty >= TOTAL_CLUBS_LIMIT)) {
            break;
        }

        clubsQty += clubs.length;

        for (let club of clubs) {

            console.log('Processing club #' + club.id);

            try {
                let clubMembers = yield (getClubMembersFromWP(club));

                if (Array.isArray(clubMembers)) {

                    console.log('Members:', clubMembers.length);

                    for (let member of clubMembers) {
                        for (let prop  of CERT_PROPS) {
                            if (member[prop]) {
                                result[prop].add(member[prop]);
                                console.log('   ', `${prop}.size =`, result[prop].size);
                            }
                        }
                    }
                } else {
                    throw new Error('Invalid Club Members Array!' + clubMembers)
                }
            } catch (err) {
                console.error('Error:', err.message);
            }

            console.log();
        }

        offset += LIMIT;
    }

    client.end();

    return result;
})
.then(result => {

    console.log('Finished!');
    console.log('Results:');

    for (let prop  of CERT_PROPS) {

        console.log(prop);

        /* jshint loopfunc:true */
        result[prop].forEach(value => console.log('   ', value));
    }

    process.exit(0);
})
.catch(err => {
    console.error(err);
    process.exit(1);
});

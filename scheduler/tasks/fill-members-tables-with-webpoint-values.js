'use strict';

/**
 * covered 😄👍
 * 
 * The task updates Webpoint data of team members (master_staff/athete rows) on upcoming events
 */

const co = require('co');

/* TODO: rename the Service */
const SafeSportService  = require('../../api/lib/SafeSportService');

const ROWS_PER_ITERATION = 20;

module.exports._findAndSetMemberData = function (errors, member) {
    return this._WebpointService.processMember(member)
    .then(res => res.rowCount)
    .catch(err => {
        err.memberID = member.usav_number;
        errors.push(err);

        return 0;
    });
}

module.exports.run = function run (eventID, totalRowsLimit) { 
    this._WebpointService = this._WebpointService || new SafeSportService(global.Db);

    const findMembers = TeamMembersService.findMembersForWebpointSync
                                            .bind(TeamMembersService, eventID, ROWS_PER_ITERATION);

    let hasRowsLimit = Number.isInteger(totalRowsLimit) && (totalRowsLimit > 0);
    
    return co(function* () {
        let errors = [];

        let offset              = 0;
        let totalMembersQty     = 0;
        let totalUpdatedRows    = 0;

        while (true) {
            let members = yield findMembers(offset); /* jshint ignore:line */

            if (members.length === 0) {
                break;
            }

            let res = yield Promise.all(members.map(m => this._findAndSetMemberData(errors, m))); /* jshint ignore:line */

            /* [n, n, n] -> ∑n */
            let updatedRows = res.reduce((sum, updQty) => (sum + updQty), 0);

            totalUpdatedRows += updatedRows;
            totalMembersQty += members.length;

            if (hasRowsLimit && (totalMembersQty >= totalRowsLimit)) {
                break;
            }

            offset += ROWS_PER_ITERATION;
        }

        return { updatedRowsCount: totalUpdatedRows, errors };
    }.bind(this));
}

'use strict';

const QRGenerator = require('./../../api/lib/QRTicketsGenerator');

class SendPaidPendingPayments {
    constructor () {}

    get PROCESS_LIMIT () {
        return 50;
    }

    get TICKETS_SALES_TYPE () {
        return 'tickets';
    }

    get SENT_RECEIPT_ACTION () {
        return 'pending-payment.receipt.sent.success';
    }

    get SENT_WITH_ERROR_ACTION () {
        return 'pending-payment.receipt.sent.error';
    }

    get FEE_PAYER () {
        return {
            BUYER   : 'buyer',
            SELLER  : 'seller'
        }
    }

    async processSend () {
        let paymentIDsToSend = await SWTAPIService.receipt_sending.getPaymentIDsToSend(this.PROCESS_LIMIT);

        if(!paymentIDsToSend.length) {
            return null;
        }

        for(let paymentID of paymentIDsToSend) {
            let payment, eventData;

            try {
                let receiptData = await SWTAPIService.receipt_sending.getReceiptDataFromRedis(paymentID);

                if(!receiptData) {
                    continue;
                }

                ({eventData, paymentData: payment} = JSON.parse(receiptData));

                if(!payment || !payment.tickets || !payment.tickets.length || !eventData) {
                    continue;
                }

                this.__generateHashes(payment.tickets);

                await (this.__sendTicketsData(eventData, payment));

                let historyData = await this.__sendPaymentInfoNotification(eventData.event_id, paymentID);

                //delete all redis data for receipt
                await this.__clearReceiptRedisData(payment.id);
            } catch (err) {
                loggers.errors_log.error(err);

                await this.__sendError({payment, error: err.message});
            }
        }

        return this.__checkRemainingReceipts();
    }

    __checkRemainingReceipts () {
        return SWTAPIService.receipt_sending.getRemainingReceipts()
            .then(receipts => {
                if(receipts && receipts.length) {
                    let error = {
                        message: `Can't send receipts for payments ${receipts.join(', ')}`
                    };

                    return this.__sendError(error)
                }
            })
    }

    async __sendTicketsData (eventData, payment) {
        for(let id = 0; id < payment.tickets.length; id++) {
            let ticket = payment.tickets[id];

            await this.__generateQRCodeImage(ticket, eventData);

            let formattedBarcode = this.__formatBarcode(ticket.barcode);

            const purchasedEventTicket = eventData.ticket_types.find((tt) => tt.event_ticket_id === ticket.event_ticket_id);

            let [emailData] = await (this.__sendNotifications({
                event_name           : eventData.name,
                short_event_name     : eventData.short_name,
                event_email          : eventData.email,
                additional_fields    : eventData.additional_fields,
                require_tickets_names: eventData.require_tickets_names,
                barcode_number       : ticket.barcode,
                barcode              : formattedBarcode,
                description          : eventData.tickets_receipt_descr,
                first                : ticket.first,
                last                 : ticket.last,
                total                : ticket.amount,
                receipt              : ticket,
                hash                 : ticket.hash,
                sales_type           : this.TICKETS_SALES_TYPE,
                social_links         : eventData.social_links,
                payment_method       : payment.method,
                phone                : payment.phone,
                email                : payment.email,
                purchase_id          : ticket.purchase_id,
                event_id             : ticket.event_id,
                user_id              : ticket.user_id,
                method               : payment.type,
                cardholder           : { first: payment.first, last: payment.last },
                allPurchases         : payment.tickets,
                sw_fee_payer         : eventData.sw_fee_payer       || this.FEE_PAYER.SELLER,
                stripe_fee_payer     : eventData.stripe_fee_payer   || this.FEE_PAYER.SELLER,
                user_zip             : payment.user_zip,
                purchased_at         : payment.purchased_at,
                isAppleDevice        : false,
                event_logo           : eventData.event_logo,
                tickets_receipt_descr: eventData.tickets_receipt_descr,
                valid_dates          : purchasedEventTicket && purchasedEventTicket.valid_dates,
                ticket_type          : purchasedEventTicket && purchasedEventTicket.ticket_type,
                border_colour        : null,
                city                 : eventData.city,
                state                : eventData.state,
            }));

            if(!eventData.require_tickets_names) {
                await this.__saveToHistory(emailData, ticket);
            }
        }
    }

    async __saveToHistory (emailData, { user_id, event_id, purchase_id }) {
        let eventEmailID = await this.__saveToEventEmail(emailData, user_id, event_id);

        return this.__saveToEventChange(eventEmailID, purchase_id, user_id, event_id);
    }

    __clearReceiptRedisData (purchaseID) {
        return SWTAPIService.receipt_sending.removeSentReceiptFromRedis(purchaseID);
    }

    __saveToEventEmail ({ email, subject, to, from }, userID, eventID) {
        let query = squel.insert().into('event_email')
            .set('email_from', from)
            .set('email_to', to)
            .set('recipient_type', 'ticket buyer')
            .set('email_subject', subject)
            .set('email_text', email.text)
            .set('email_html', email.html)
            .set('reason_type', 'receipt sent')
            .set('tickets_customer_user_id', userID)
            .set('event_id', eventID)
            .returning('event_email_id');

        return Db.query(query).then(result => result.rows[0] && result.rows[0].event_email_id);
    }

    __saveToEventChange (eventEmailID, purchaseID, userID, eventID) {
        let query = squel.insert().into('event_change')
            .set('event_email_id', eventEmailID)
            .set('action', 'purchase.email.sent')
            .set('purchase_id', purchaseID)
            .set('user_id', userID)
            .set('event_id', eventID);

        return Db.query(query);
    }

    __sendNotifications (data) {
        let notifiers = [];
        let receiptNotifier;

        if(data.require_tickets_names) {
            receiptNotifier = SWTReceiptService.sendTicketNotification(
                data.event_id, data.barcode_number, data.ticket_type, data.isAppleDevice
            ).catch((...args) => loggers.errors_log.error(...args));
        } else {
            receiptNotifier = SWTReceiptService.sendReceipt(data.email, data)
                .catch((...args) => loggers.errors_log.error(...args));
        }

        notifiers.push(receiptNotifier);

        if(data.phone) {
            let textNotifier = SWTReceiptService.sendText({
                receiver        : data.phone,
                purchaseId      : data.purchase_id,
                eventId         : data.event_id,
                userId          : data.user_id,
                salesType       : data.sales_type,
                eventName       : data.short_event_name
            }).catch(console.error.bind(console));

            notifiers.push(textNotifier);
        }

        return Promise.all(notifiers);
    }

    __generateHashes(purchases) {
        for (let i = 0; i < purchases.length; i++) {
            const purchase = purchases[i];

            purchase.hash = QRGenerator.generateHash({
                ticket_barcode  : purchase.barcode,
                purchase_id     : purchase.purchase_id,
                user_id         : purchase.user_id,
                event_id        : purchase.event_id
            }, true);
        }
    }

    __generateQRCodeImage (purchase, settings) {
        return Promise.resolve().then(() => {

            let itemsStr = QRGenerator.generateQRCodeItemsString(
                settings.ticket_types, [purchase]);

            let qrContent = QRGenerator.generateContent({
                event_id            : purchase.event_id,
                ticket_barcode      : purchase.barcode,
                purchase_timestamp  : purchase.created,
                tickets             : itemsStr,
                purchaser_first     : purchase.first,
                purchaser_last      : purchase.last,
            }, settings.qrcode_version||undefined, settings.require_tickets_names);

            return QRGenerator.generate({
                qr_content      : qrContent,
                imageName       : purchase.hash
            })
        });
    }

    __sendError (errorData) {
        this.__sendErrorToDevelopers(errorData).catch(err => console.log(err));
    }

    __formatBarcode (barcode) {
        return ('' + barcode).replace(/(\d{3})(\d{3})(\d{3})/g, '$1-$2-$3');
    }

    __sendPaymentInfoNotification(eventID, paymentID) {
        return SWTReceiptService.sendPaymentInfo(eventID, paymentID);
    }

    __sendErrorToDevelopers (data) {
        return EmailService.sendEmail({
            from        : '"SportWrench" <<EMAIL>>',
            to          : '"sw debug" <<EMAIL>>, "Baranov Yevhenii" <<EMAIL>>',
            subject     : 'Error in pending-payment receipt sender',
            text        : JSON.stringify(data)
        });
    }
}

module.exports = new SendPaidPendingPayments();

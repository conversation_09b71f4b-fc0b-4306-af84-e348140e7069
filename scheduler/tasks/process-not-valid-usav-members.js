
const AthletesProcessor = require('./process-not-valid-usav-members/athletes-processor');
const StaffersProcessor = require('./process-not-valid-usav-members/staffers-processor');

const {
    MEMBER_TYPE,
} = require('../../api/lib/SEUtilsService');


class ProcessNotValidUSAVMembers {
    #processor = {
        [MEMBER_TYPE.ATHLETE]: AthletesProcessor,
        [MEMBER_TYPE.STAFF]: StaffersProcessor,
    }

    async process(memberType, season) {
        if(![MEMBER_TYPE.ATHLETE, MEMBER_TYPE.STAFF].includes(memberType)) {
            throw new Error(`Invalid member type: ${memberType}`);
        }

        if(!season) {
            throw new Error(`Invalid season: ${season}`);
        }

        const members = await this.#processor[memberType].getMembers(season);

        if(_.isEmpty(members)) {
            return;
        }

        for(const member of members) {
            await this.#processor[memberType].processMember(member);
        }
    }
}

module.exports = new ProcessNotValidUSAVMembers();

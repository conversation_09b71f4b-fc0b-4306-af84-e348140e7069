# Config

This folder is the same as "config" **BUT**
this folder is used to make a convenient way to split configurations into files, because SailsJS does not support usage of subfolders 
(it loads the subfolders' content to sails.config variable by default) and there is no way to change this behaviour.

So, the files from this folders have to be manually loaded in the **config** folder

Proofs:
https://github.com/balderdashy/sails/blob/c1306e5bc24560e068dcb518f2fb7853305cd5a4/lib/hooks/moduleloader/index.js#L238 - 
We can't pass any options to skip some subfolders while lifting SailsJS app

The options: https://github.com/balderdashy/include-all#options 


v0.10 behaviour is deprecated: https://github.com/balderdashy/sails/commit/65ff83409bfe71b545e70a3efdd720bdc59d4756 
More Info: http://sailsjs.org/version-notes/0point10-to-0point11-migration-guide#?config-files-in-subfolders
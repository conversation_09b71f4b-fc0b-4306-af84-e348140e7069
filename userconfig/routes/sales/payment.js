

module.exports = {
    /**
     *
     * @api {get} /api/sales/payments Payments List
     * @apiDescription Returns payments list
     * @apiGroup Sales Payments
     *
     */
    'GET /api/sales/payments': 'SalesManager/PaymentController.get_payments',

    /**
     *
     * @api {post} /api/event/:event/sales/booths/pay/:type/change Change Payment Type
     * @apiDescription Changes payment type
     * @apiGroup Sales Payments
     *
     */
    'POST /api/event/:event/sales/booths/pay/:type/change': 'SalesManager/PaymentController.changePaymentType',

    /**
     *
     * @api {post} /api/event/:event/sales/payment/:payment/receive Receive Check
     * @apiDescription Updates check payment
     * @apiGroup Sales Payments
     *
     */
    'POST /api/event/:event/sales/payment/:payment/receive': 'SalesManager/PaymentController.receive',

    /**
     *
     * @api {post} /api/event/:event/sales/payment/:payment/void Void Purchase
     * @apiDescription Cancels booths payment
     * @apiGroup Sales Payments
     *
     */
    'POST /api/event/:event/sales/payment/:payment/void': 'SalesManager/PaymentController.void',

    /**
     *
     * @api {post} /api/event/:event/sales/payment/:payment/refund Purchase Full Refund
     * @apiDescription Refunds payment fully
     * @apiGroup Sales Payments
     *
     */
    'POST /api/event/:event/sales/payment/:payment/refund': 'SalesManager/PaymentController.refund',

    /**
     *
     * @api {put} /api/event/:event/sales/invoice/:invoice Update Invoice
     * @apiDescription Updates a specific booths payment invoice
     * @apiGroup Sales Payments
     *
     */
    'GET /api/sales/payments/:payment': 'SalesManager/PaymentController.get_booths_payments',
}

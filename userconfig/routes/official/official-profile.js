

module.exports = {
    /**
     *
     * @api {post} /api/official/create Create Profile
     * @apiDescription Creates official profile
     * @apiGroup Official Profile
     *
     */
    'post /api/official/create': 'Official/ProfileController.create',

    /**
     *
     * @api {get} /api/official/find Profile Data
     * @apiDescription Returns official profile data
     * @apiGroup Official Profile
     *
     */
    'get /api/official/find': 'Official/ProfileController.find',

    /**
     *
     * @api {put} /api/official/update Profile Update
     * @apiDescription Updates official profile
     * @apiGroup Official Profile
     *
     */
    'put /api/official/update': 'Official/ProfileController.update',

    /**
     *
     * @api {get} /api/official/clothes Clothes Data
     * @apiDescription Returns officials clothes data
     * @apiGroup Official Profile
     *
     */
    'get /api/official/clothes': 'Official/ProfileController.getClothes',

    /**
     *
     * @api {get} /api/official/update-webpoint Update Webpoint Data (deprecated)
     * @apiDescription Updates officials USAV data from Webpoint (deprecated)
     * @apiGroup Official Profile
     *
     */
    'get /api/official/update-webpoint': 'Official/ProfileController.updateWebpointInfo',
}

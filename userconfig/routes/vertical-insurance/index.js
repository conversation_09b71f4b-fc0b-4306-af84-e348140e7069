module.exports = {

    

     /**
     *
     * @api {post} /api/vertical-insurance/quotes/:quote/pay Pays a quote
     * @apiDescription Pay Vertical Insurance quote
     * @apiGroup Vertical Insurance
     *
     */
     'POST /api/vertical-insurance/quotes/:quote/pay': {
        action: 'v2/vertical-insurance/quote/pay',
        csrf: false
    },

    /**
     *
     * @api {get} /api/vertical-insurance/customer-client-secret/purchase/:purchase Create a customer client secret for purchase
     * @apiDescription Create a Vertical Insurance Customer Client Secret
     * @apiGroup Vertical Insurance
     *
     */
    'GET /api/vertical-insurance/customer-client-secret/purchase/:purchase': {
        action: 'v2/vertical-insurance/customer-client-secret/create-for-purchase',
    },

    /**
     *
     * @api {post} /api/vertical-insurance/team-registration/event/:event/quote Create a quote
     * @apiDescription Create a Vertical Insurance quote
     * @apiGroup Vertical Insurance
     *
     */
    'POST /api/vertical-insurance/team-registration/event/:event/quote': {
        action: 'v2/vertical-insurance/quote/team-registration/create',
    },

    /**
     *
     * @api {put} /api/vertical-insurance/team-registration/event/:event/quote/:quote Update a quote
     * @apiDescription Updates a Vertical Insurance specific quote
     * @apiGroup Vertical Insurance
     *
     */
    'PUT /api/vertical-insurance/team-registration/event/:event/quote/:quote': {
        action: 'v2/vertical-insurance/quote/team-registration/update',
    },

    /**
     *
     * @api {post} /api/vertical-insurance/webhook Vertical Insurance Webhooks Handler
     * @apiDescription Handles Vertical Insurance webhooks
     * @apiGroup Vertical Insurance
     *
     */
    'POST /api/vertical-insurance/webhook': {
        action: 'v2/vertical-insurance/webhook',
        csrf: false,
    },

    /**
     *
     * @api {get} /api/vertical-insurance/team-registration/event/:event/link quote Quote Request Link Getter
     * @apiDescription Gets Data for Vertical Insurance Quote Link
     * @apiGroup Vertical Insurance
     *
     */
    'GET /api/vertical-insurance/team-registration/event/:event/link': {
        action: 'v2/vertical-insurance/quote/team-registration/link',
    },
};

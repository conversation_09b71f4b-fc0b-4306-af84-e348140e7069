'use strict';

const _ 	= require('lodash');

const 
	eventRoutes 	    = require('./event'),
	checkinRoutes 	    = require('./online-checkin-routes'),
	paymentRoutes 	    = require('./payment'),
    webpointRoutes      = require('./webpoint'),
    teamsRoutes         = require('./teams'),
    eventTeamsRoutes    = require('./event-teams'),
    eventStaffRoutes    = require('./event-staff'),
    eventAthleteRoutes  = require('./event-athlete'),
    staffRoutes         = require('./staff'),
    comeEvent           = require('./come-event'),
    athleteRoutes       = require('./athlete'),
    membersImport       = require('./members-import'),
    divisions           = require('./divisions'),
    club                = require('./club'),
    paymentCard = require('./payment-card'),
    personalInfo        = require('./personal-info'),
    paymentV2Routes     = require('./payment_v2'),
    paymentHubRoutes     = require('./payment-hub'),
    justifiRoutes     = require('./justifi'),
    bulkRegistrationV2  = require('./bulk-registration_v2'),
    clubInvoice         = require('./club-invoice');

module.exports = _.defaults({},
    eventRoutes, checkinRoutes, paymentRoutes, webpointRoutes, teamsRoutes, eventTeamsRoutes, eventStaffRoutes,
    eventAthleteRoutes, staffRoutes, comeEvent, athleteRoutes, membersImport, club, personalInfo, divisions,
    paymentV2Routes, bulkRegistrationV2, paymentHubRoutes, paymentCard, clubInvoice, justifiRoutes
);

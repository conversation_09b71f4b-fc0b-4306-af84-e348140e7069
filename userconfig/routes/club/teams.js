

module.exports = {

    /**
     *
     * @api {get} /api/club/teams/:event/list Assign Teams List
     * @apiDescription Teams list allowed to assign on the specific event
     * @apiGroup Master Team
     *
     */
    'get /api/club/teams/:event/list': 'Club/MasterTeamController.assignList',

    /**
     *
     * @api {post} /api/club/teams/season-duplicate Copy Teams to Current Season
     * @apiDescription Moves selected teams from previous season to current
     * @apiGroup Master Team
     *
     */
    'post /api/club/teams/season-duplicate': 'Club/MasterTeamController.copyToCurrentSeason',

    /**
     *
     * @api {get} /api/club/teams/current/short Teams List (for filters)
     * @apiDescription Teams list used for filter at Club Athletes tab
     * @apiGroup Master Team
     *
     */
    'get /api/club/teams/current/short': 'Club/MasterTeamController.currentSeasonList',

    /**
     *
     * @api {get} /api/club/teams Teams List
     * @apiDescription Club teams list
     * @apiGroup Master Team
     *
     */
    'get /api/club/teams': 'Club/MasterTeamController.teamsList',

    /**
     *
     * @api {get} /api/club/team/:team/edit Teams Info (for update)
     * @apiDescription Gets team data for update form
     * @apiGroup Master Team
     *
     */
    'get /api/club/team/:team/edit': 'Club/MasterTeamController.teamToEdit',

    /**
     *
     * @api {post} /api/club/team/create Team Create
     * @apiDescription Creates new team
     * @apiGroup Master Team
     *
     */
    'post /api/club/team/create': 'Club/MasterTeamController.createTeam',

    //TODO: why POST?
    /**
     *
     * @api {post} /api/club/teams/entered-list Teams List entered on Event
     * @apiDescription Finds all teams assigned to event (it is for check before removing)
     * @apiGroup Master Team
     *
     */
    'post /api/club/teams/entered-list': 'Club/MasterTeamController.getEnteredTeamsEventList',

    //TODO: why POST?
    /**
     *
     * @api {post} /api/club/teams/events/assigned-to Team Events List
     * @apiDescription Finds all team's events (not used on UI)
     * @apiGroup Master Team
     *
     */
    'post /api/club/teams/events/assigned-to': 'Club/MasterTeamController.masterTeamEventsList',

    /**
     *
     * @api {get} /api/club/teams/:team/events/upcoming Team Upcoming Events List
     * @apiDescription Returns list of upcoming events the team is assigned to
     * @apiGroup Master Team
     *
     */
    'get /api/club/teams/:team/events/upcoming': 'Club/MasterTeamController.eventsAssignedTo',

    //TODO: why POST?
    /**
     *
     * @api {post} /api/club/teams/remove Team Remove
     * @apiDescription Removes team from club
     * @apiGroup Master Team
     *
     */
    'post /api/club/teams/remove': 'Club/MasterTeamController.remove',

    /**
     *
     * @api {post} /api/master_team/bulk Team Create Bulk (import)
     * @apiDescription Bulk teams creation from import file
     * @apiGroup Master Team
     *
     */
    'post /api/master_team/bulk': 'Club/MasterTeamController.bulkCreate',

    /**
     *
     * @api {get} /api/master_team/count Club Teams Count
     * @apiDescription Returns count of club teams
     * @apiGroup Master Team
     *
     */
    'get /api/master_team/count': 'Club/MasterTeamController.count',

    /**
     *
     * @api {put} /api/club/team/:team/update Team Update
     * @apiDescription Updates specific team
     * @apiGroup Master Team
     *
     */
    'put /api/club/team/:team/update': 'Club/MasterTeamController.updateTeam',
}

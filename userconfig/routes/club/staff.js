

module.exports = {
    /**
     *
     * @api {get} /api/master_staff Staffers List
     * @apiDescription Returns club master staff list
     * @apiGroup Master Staff
     *
     */
    'get /api/master_staff': 'MasterStaffController.index',

    /**
     *
     * @api {get} /api/master_staff/:master_staff Staffer Info
     * @apiDescription Returns club master staff
     * @apiGroup Master Staff
     *
     */
    'get /api/master_staff/:master_staff': 'MasterStaffController.find',

    /**
     *
     * @api {get} /api/master_staff/team/:team Staffers List in Team
     * @apiDescription Returns club master staff list assigned to the specific team
     * @apiGroup Master Staff
     *
     */
    'get /api/master_staff/team/:team': 'MasterStaffController.findInTeam',

    /**
     *
     * @api {post} /api/master_staff/move Move Staff to Team
     * @apiDescription Assigns staffer to team
     * @apiGroup Master Staff
     *
     */
    'post /api/master_staff/move': 'MasterStaffController.move_to_team',

    /**
     *
     * @api {post} /api/master_staff/remove Remove Staff to Team
     * @apiDescription Removes staffer from team
     * @apiGroup Master Staff
     *
     */
    'post /api/master_staff/remove': 'MasterStaffController.remove_from_team',
    //'post /api/master_staff': 'MasterStaffController.create',

    /**
     *
     * @api {put} /api/master_staff/:master_staff Update Staff
     * @apiDescription Updates staff data
     * @apiGroup Master Staff
     *
     */
    'put /api/master_staff/:master_staff': 'MasterStaffController.update',
    //'delete /api/master_staff/:master_staff': 'MasterStaffController.destroy',
    // isAuthenticated, isEventOwner, canUpdateTeam
    // 'post /api/master_staff_role' : 'MasterStaffRoleController.create',
    // 'delete /api/master_staff_role' : 'MasterStaffRoleController.remove',
    // 'put /api/master_staff/:staff/role/team/:team': 'MasterStaffRoleController.update',

    /**
     *
     * @api {post} /api/master_staff/:staff/role/team/:team/primary/remove Remove Primary Role
     * @apiDescription Marks staff as not primary in team
     * @apiGroup Master Staff
     *
     */
    'post /api/master_staff/:staff/role/team/:team/primary/remove': 'MasterStaffRoleController.removePrimary',

    /**
     *
     * @api {post} /api/master_staff/:staff/team/:team/role/:action Change Staff Role
     * @apiDescription Changes staff role in team
     * @apiGroup Master Staff
     *
     */
    'post /api/master_staff/:staff/team/:team/role/:action': 'MasterStaffRoleController.changeStaffRole',

    /**
     *
     * @api {delete} /api/master_staff Remove Staff from Club
     * @apiDescription Removes staff from club
     * @apiGroup Master Staff
     *
     */
    'delete /api/master_staff': 'MasterStaffController.remove',
}

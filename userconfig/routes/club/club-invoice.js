
module.exports = {
    /**
     *
     * @api {get} /api/club/club-invoice Get Club Invoice List by Club
     * @apiDescription Returns club invoices list for a specific club
     * @apiGroup Clubs Club Invoices
     *
     */
    'get /api/club/club-invoice': { action: 'v2/club/club-invoice/list'},

    /**
     *
     * @api {post} /api/club/club-invoice/invoice/:invoice/init Create Payment Intent
     * @apiDescription Creates a new Stripe Payment Intent
     * @apiGroup Clubs Club Invoices
     *
     */
    'post /api/club/club-invoice/invoice/:invoice/init': { action: 'v2/club/club-invoice/init-payment'},

    /**
     *
     * @api {put} /api/club/club-invoice/invoice/:invoice Update Payment
     * @apiDescription Updates a Stripe Payment Intent
     * @apiGroup Clubs Club Invoices
     *
     */
    'put /api/club/club-invoice/invoice/:invoice': { action: 'v2/club/club-invoice/update-payment'},

    /**
     *
     * @api {put} /api/club/club-invoice/invoice/:invoice/payment-intent Update Payment
     * @apiDescription Updates a Stripe Payment Intent
     * @apiGroup Clubs Club Invoices
     *
     */
    'put /api/club/club-invoice/invoice/:invoice/payment-intent': { action: 'v2/club/club-invoice/update-payment-intent'},
};

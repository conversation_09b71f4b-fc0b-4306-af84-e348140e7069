const 	CTRL_PATH	= 'Club/OnlineCheckinController.',
		URL_PREFIX 	= '/api/club/event/:event/online-checkin/';

let routes = {};

/**
 *
 * @api {get} /api/club/event/:event/online-checkin/teams Teams List
 * @apiDescription Gets teams list for online checkin
 * @apiGroup Club Online Checkin
 *
 */
routes[`GET ${URL_PREFIX}teams`] = `${CTRL_PATH}teamsList`;

/**
 *
 * @api {get} /api/club/event/:event/online-checkin/staffers Staffers List
 * @apiDescription Gets staffers list for online checkin
 * @apiGroup Club Online Checkin
 *
 */
routes[`GET ${URL_PREFIX}staffers`] = `${CTRL_PATH}staffers`;

/**
 *
 * @api {get} /api/club/event/:event/online-checkin/apply Apply Team Checkin
 * @apiDescription Submits team online checkin on event
 * @apiGroup Club Online Checkin
 *
 */
routes[`POST ${URL_PREFIX}apply`] = `${CTRL_PATH}applyCheckin`;

/**
 *
 * @api {get} /online-checkin/:hash Staffers Barcode (Primary Staff Checkin)
 * @apiDescription Gets checkin info for primary staff (Primary Staff Checkin mode)
 * @apiGroup Club Online Checkin
 *
 */
routes[`GET /online-checkin/:hash`] = `${CTRL_PATH}showPrimaryStaffBarcodeDescription`;

/**
 *
 * @api {get} /online-checkin/:barcode/event/:event Staffers Barcode (Wristbands Staff Checkin)
 * @apiDescription Gets checkin info for staff (Wristbands Staff Checkin mode)
 * @apiGroup Club Online Checkin
 *
 */
routes[`GET /online-checkin/:barcode/event/:event`] = `${CTRL_PATH}showBarcodeDescription`;

/**
 *
 * @api {get} /api/club/event/:event/online-checkin/validate/:team Validate Team
 * @apiDescription Validates teams' rosters
 * @apiGroup Club Online Checkin
 *
 */
routes[`GET ${URL_PREFIX}validate/:team`] = `${CTRL_PATH}validateTeam`;

/**
 *
 * @api {post} /online-checkin/:staffer/:team/resend Resend Email to Staff (Primary)
 * @apiDescription Resend email with barcode to Primary staffers
 * @apiGroup Club Online Checkin
 *
 */
routes[`POST /online-checkin/:staffer/:team/resend`] = `${CTRL_PATH}resendPrimaryStaffEmail`;

module.exports = routes;

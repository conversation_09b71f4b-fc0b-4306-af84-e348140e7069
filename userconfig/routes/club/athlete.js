

module.exports = {
    /**
     *
     * @api {get} /api/v2/club/master_athlete Athletes List
     * @apiDescription All club athletes list
     * @apiGroup Master Athlete
     *
     */
    'get /api/v2/club/master_athlete': 'Club/MasterAthleteController.all',

    /**
     *
     * @api {get} /api/v2/club/athletes/positions Athletes Sport Positions
     * @apiDescription Club athletes sport positions list
     * @apiGroup Master Athlete
     *
     */
    'get /api/v2/club/athletes/positions': 'Club/MasterAthleteController.sportPositions',

    /**
     *
     * @api {post} /api/v2/club/master_athlete/move_to_team Assign Athlete to Team
     * @apiDescription Moves athlete to selected team
     * @apiGroup Master Athlete
     *
     */
    'post /api/v2/club/master_athlete/move_to_team': 'Club/MasterAthleteController.moveToTeam',

    /**
     *
     * @api {put} /api/v2/club/master_athlete/:id/update Athlete Update
     * @apiDescription Updates athlete data
     * @apiGroup Master Athlete
     *
     */
    'put /api/v2/club/master_athlete/:id/update': 'Club/MasterAthleteController.updateAthlete',

    /**
     *
     * @api {get} /api/v2/club/master_athlete/:id Find Athlete
     * @apiDescription Gets specific athlete's data
     * @apiGroup Master Athlete
     *
     */
    'get /api/v2/club/master_athlete/:id': 'Club/MasterAthleteController.findAthlete',

    /**
     *
     * @api {get} /api/master_athlete/team/:team Team's Athletes List
     * @apiDescription Gets specific team athletes list
     * @apiGroup Master Athlete
     *
     */
    'get /api/master_athlete/team/:team': 'MasterAthleteController.athletesByTeam',

    /**
     *
     * @api {delete} /api/v2/club/master_athlete Athletes Remove
     * @apiDescription Removes athletes from club
     * @apiGroup Master Athlete
     *
     */
    'delete /api/v2/club/master_athlete': 'Club/MasterAthleteController.remove'
}

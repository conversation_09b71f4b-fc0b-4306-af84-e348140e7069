'use strict';

const CONTROLLER = `Event/CampsController.`;

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/camps Event Camps List
     * @apiDescription Returns event camps list
     * @apiGroup Event Camps
     *
     */
	'GET /api/event/:event/camps': `${CONTROLLER}all`,

    /**
     *
     * @api {put} /api/event/:event/camps Event Camp Upsert
     * @apiDescription Creates or update event camp
     * @apiGroup Event Camps
     *
     */
	'PUT /api/event/:event/camps': `${CONTROLLER}upsertCamp`,

    /**
     *
     * @api {put} /api/event/:event/camps/age-date Event Camp Age Date Update
     * @apiDescription Updates camp's age date
     * @apiGroup Event Camps
     *
     */
	'PUT /api/event/:event/camps/age-date': `${CONTROLLER}updateEventAgeDate`,

    /**
     *
     * @api {put} /api/event/:event/camps/swap-sort-order Event Camps Sort Order Swap
     * @apiDescription Changes camps sort order to the opposite
     * @apiGroup Event Camps
     *
     */
	'PUT /api/event/:event/camps/swap-sort-order': `${CONTROLLER}swapSortOrder`,

    /**
     *
     * @api {delete} /api/event/:event/:camp(\\d+) Event Camp Delete
     * @apiDescription Deletes event camp
     * @apiGroup Event Camps
     *
     */
    'DELETE /api/event/:event/:camp(\\d+)': `${CONTROLLER}delete`,

    /**
     *
     * @api {post} /api/event/:event/:camp/copy Event Camp Copy
     * @apiDescription Copies event camp
     * @apiGroup Event Camps
     *
     */
    'POST /api/event/:event/:camp/copy': `${CONTROLLER}copy`
};

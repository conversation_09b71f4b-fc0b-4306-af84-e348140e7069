'use strict';

const CTRL = 'Event/BoothController.';

module.exports = {
    /**
     *
     * @api {get} /api/event/:event/booth Event Booths List
     * @apiDescription Returns event booths list
     * @apiGroup Event Booth
     *
     */
    'GET /api/event/:event/booth': `${CTRL}index`,

    /**
     *
     * @api {post} /api/event/:event/booth Event Booth Creation
     * @apiDescription Creates event booth
     * @apiGroup Event Booth
     *
     */
    'POST /api/event/:event/booth': `${CTRL}create`,

    /**
     *
     * @api {put} /api/event/:event/booth/:id Event Booth Update
     * @apiDescription Updates event booth
     * @apiGroup Event Booth
     *
     */
    'PUT /api/event/:event/booth/:id': `${CTRL}update`,

    /**
     *
     * @api {get} /api/event/:event/booth/:id Event Booth Info
     * @apiDescription Returns specific event booth data
     * @apiGroup Event Booth
     *
     */
    'GET /api/event/:event/booth/:id': `${CTRL}find`,

    /**
     *
     * @api {delete} /api/event/:event/booth/:id Event Booth Delete
     * @apiDescription Removes specific event booth
     * @apiGroup Event Booth
     *
     */
    'DELETE /api/event/:event/booth/:id': `${CTRL}destroy`
};



module.exports = {
    /**
     *
     * @api {get} /api/event/:event/officials Officials List
     * @apiDescription Returns event officials list
     * @apiGroup Event Official
     *
     */
    'GET /api/event/:event/officials': 'Event/OfficialsController.index',

    /**
     *
     * @api {get} /api/event/:event/official/:official Official Info
     * @apiDescription Returns event official info
     * @apiGroup Event Official
     *
     */
    'GET /api/event/:event/official/:official': 'Event/OfficialsController.find',

    /**
     *
     * @api {post} /api/event/:event/official/:official/make_head Make Official Head
     * @apiDescription Gives official head official role
     * @apiGroup Event Official
     *
     */
    'POST /api/event/:event/official/:official/make_head': 'Event/OfficialsController.make_head',

    /**
     *
     * @api {post} /api/event/:event/official/:official/make_head Remove Official Head
     * @apiDescription Removes head official role
     * @apiGroup Event Official
     *
     */
    'POST /api/event/:event/official/:official/demote': 'Event/OfficialsController.remove_head',

    /**
     *
     * @api {put} /api/event/:event/official/:official/set_receiver Allow To Receive Notifications
     * @apiDescription Sets head official as officials notification receiver
     * @apiGroup Event Official
     *
     */
    'PUT /api/event/:event/official/:official/set_receiver': 'Event/OfficialsController.setEmailNotificationsReceiver',

    /**
     *
     * @api {put} /api/event/:event/official/:official/set_contact_provider Add Contact Data To Notifications
     * @apiDescription Puts official's name and contacts to emails sent to the officials
     * @apiGroup Event Official
     *
     */
    'PUT /api/event/:event/official/:official/set_contact_provider': 'Event/OfficialsController.setEmailContactProvider',

    /**
     *
     * @api {put} /api/event/:event/official/:official/clinic Set official is clinic
     * @apiDescription Updates official use_clinic field
     * @apiGroup Event Official
     *
     */
    'PUT /api/event/:event/official/:official/clinic': 'Event/OfficialsController.setOfficialIsClinic',

    /**
     *
     * @api {get} /api/event/:event/officials/export Export Officials To Excel
     * @apiDescription Exports event officials list
     * @apiGroup Event Official
     *
     */
    'GET /api/event/:event/officials/export': 'Event/OfficialsController.export_to_excel',

    /**
     *
     * @api {put} /api/event/:event/official/:official/update Update Official Registration
     * @apiDescription Updates official's registration data
     * @apiGroup Event Official
     *
     */
    'PUT /api/event/:event/official/:official/update': 'Event/OfficialsController.updateRegInfo',

    /**
     *
     * @api {put} /api/event/:event/official/:official/update Update Official Registration
     * @apiDescription Updates official's registration data
     * @apiGroup Event Official
     *
     */
    'PUT /api/event/:event/officials/update': 'Event/OfficialsController.updateRegInfoGroup',

    /**
     *
     * @api {get} /api/event/:event/official/:official/:field(safesport|background|aau_background|aau_safesport)/ok Manual Update for SS/BKG
     * @apiDescription Updates official safesport of background status
     * @apiGroup Event Official
     *
     */
    'GET /api/event/:event/official/:official/:field(safesport|background|aau_background|aau_safesport)/ok':
        'Official/EventController.setSanctioningCheckFieldOk',
}

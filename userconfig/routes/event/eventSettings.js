'use strict';

const CTRL = 'Event/SettingsController.';

module.exports = {
    /**
     *
     * @api {get} /api/sport/:sport/info Event Settings Sport Info
     * @apiDescription Returns sport variations and sanctionings list
     * @apiGroup Event Settings
     *
     */
    'get /api/sport/:sport/info': `${CTRL}getEventSportInfo`,

    /**
     *
     * @api {get} /api/sport/:sport/variations Event Settings Sport Variations
     * @apiDescription Returns sport variation for specific sport
     * @apiGroup Event Settings
     *
     */
    'get /api/sport/:sport/variations': `${CTRL}getEventSportVariations`,

    /**
     *
     * @api {get} /api/sport/:sport/sanctionings Event Settings Sport Sanctionings
     * @apiDescription Returns sport sanctionings for specific sport
     * @apiGroup Event Settings
     *
     */
    'get /api/sport/:sport/sanctionings': `${CTRL}getEventSportSanctionings`,

    /**
     *
     * @api {put} /api/event/:event/update Event Update
     * @apiDescription Updates event
     * @apiGroup Event Settings
     *
     */
    'put /api/event/:event/update': `${CTRL}update`,

    /**
     *
     * @api {post} /api/event/create Event Creation
     * @apiDescription Creates event
     * @apiGroup Event Settings
     *
     */
    'post /api/event/create': `${CTRL}create`,

    /**
     *
     * @api {get} /api/event/:event/update/info Event Settings Info
     * @apiDescription Creates event
     * @apiGroup Event Settings
     *
     */
    'get /api/event/:event/update/info': `${CTRL}info`,

    /**
     *
     * @api {get} /api/event/:event/clothes-types Event Settings Clothes Types
     * @apiDescription Returns clothes types
     * @apiGroup Event Settings
     *
     */
    'get /api/event/:event/clothes-types': `${CTRL}getClothesTypes`,

    /**
     *
     * @api {get} /api/event/:event/locations Event Locations List
     * @apiDescription Returns event locations list
     * @apiGroup Event Settings
     *
     */
    'get /api/event/:event/locations': `${CTRL}getLocationsList`,

    /**
     *
     * @api {get} /api/event/:event/locations/:location Event Location
     * @apiDescription Returns specific event location
     * @apiGroup Event Settings
     *
     */
    'get /api/event/:event/locations/:location': `${CTRL}getLocationInfo`,

    /**
     *
     * @api {put} /api/event/:event/locations/:location Event Location Update
     * @apiDescription Updates specific event location
     * @apiGroup Event Settings
     *
     */
    'put /api/event/:event/locations/:location': `${CTRL}updateLocation`,

    /**
     *
     * @api {post} /api/event/:event/locations Event Location Create
     * @apiDescription Creates specific event location
     * @apiGroup Event Settings
     *
     */
    'post /api/event/:event/locations': `${CTRL}createLocation`,

    /**
     *
     * @api {delete} /api/event/:event/locations/:location Event Location Remove
     * @apiDescription Removes specific event location
     * @apiGroup Event Settings
     *
     */
    'delete /api/event/:event/locations/:location': `${CTRL}removeLocation`,

    /**
     *
     * @api {post} /api/event/:event/copy Event Copy
     * @apiDescription Copy event
     * @apiGroup Event Settings
     *
     */
    'post /api/event/:event/copy': `${CTRL}copyEvent`,

    /**
     *
     * @api {get} /api/event/:event/template-types Event Trigger Templates
     * @apiDescription Returns email trigger templates and trigger list
     * @apiGroup Event Settings
     *
     */
    'get /api/event/:event/template-types': `${CTRL}getTemplateTypes`,

    /**
     *
     * @api {put} /api/event/:event/assign-templates Event Trigger Template Assign
     * @apiDescription Assign specific email template on the trigger action
     * @apiGroup Event Settings
     *
     */
    'put /api/event/:event/assign-templates': `${CTRL}assignTmplToTrigger`,
    
    /**
     *
     * @api {get} /api/official-additional-roles Official Additional Roles
     * @apiDescription Official Additional Roles
     * @apiGroup Event Settings
     *
     */
    'get /api/official-additional-roles': `${CTRL}getOfficialAdditionalRoles`
};

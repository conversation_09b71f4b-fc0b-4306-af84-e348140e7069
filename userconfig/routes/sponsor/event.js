const CONTROLLER = 'Sponsor/EventController';

module.exports = {
    /**
     *
     * @api {get} /api/sponsor/events Events List
     * @apiDescription Returns sponsor events list
     * @apiGroup Sponsor Event
     *
     */
    'GET /api/sponsor/events': `${CONTROLLER}.get`,

    /**
     *
     * @api {get} /api/sponsor/payments Payments List
     * @apiDescription Returns sponsor payments list
     * @apiGroup Sponsor Event
     *
     */
    'GET /api/sponsor/payments': `${CONTROLLER}.getPaymentsList`,

    /**
     *
     * @api {get} /api/sponsor/event/:event/info Event Info
     * @apiDescription Returns a specific event info
     * @apiGroup Sponsor Event
     *
     */
    'GET /api/sponsor/event/:event/info': `${CONTROLLER}.getEventInfo`,

    /**
     *
     * @api {post} /api/sponsor/event/:event Register on Event
     * @apiDescription Register sponsor on the event
     * @apiGroup Sponsor Event
     *
     */
    'POST /api/sponsor/event/:event': `${CONTROLLER}.create`,

    /**
     *
     * @api {put} /api/sponsor/event/:event Update Registration
     * @apiDescription Update sponsor's registration on the event
     * @apiGroup Sponsor Event
     *
     */
    'PUT /api/sponsor/event/:event': `${CONTROLLER}.update`,

    /**
     *
     * @api {get} /api/sponsor/event/:event Registration Info
     * @apiDescription Returns sponsor's registration on the event data
     * @apiGroup Sponsor Event
     *
     */
    'GET /api/sponsor/event/:event': `${CONTROLLER}.getApplyingInfo`,
};



module.exports = {
    // 'get /api/test-auth': 'SigninController.officialsAppSignIn',
    // 'get /test/test': 'Test/EventController.index',
    // 'get /test/event': 'Test/TestJSONController.event',
    // 'get /test/event/divisions': 'Test/TestJSONController.divisions',
    // 'get /test/event/teams': 'Test/TestJSONController.teams',
    // 'get /api/qr/test': 'Test/testQRController.generate',
    // 'get /api/cron/tickets': 'Test/TestCronController.runTicketsCron',
    // 'get /api/cron/tickets-notif': 'Test/TestCronController.runNotifications',
    // 'get /api/test/cron/roster_snapshot': 'Test/TestCronController.runSnapshotCron',
    // 'get /api/test/club_snapshot': 'Test/TestRosterSnapshotController.test',
    // 'get /api/test/event-notifications/club': 'Test/TestNotificationsController.cd',
    // 'get /api/test/ban': 'Test/EventController.banDisputes',
    // 'get /api/transfers/cron': 'Test/EventController.transfersCron',
    // 'get /api/s2/test': 'Test/EventController.s2',
    // 'get /api/test-render': 'Test/EventController.testRender',
    // 'get /api/test/stripe-account/save-transfers': 'Test/StripeController.processTransfersForAcc',
    // 'get /api/test/housing-upd': 'Test/EventController.housing',
    // 'get /test/barcode': 'Test/EventController.barcode',
    // 'get /test/events/esw-id-gen': 'Test/EventController.eswIdGen',
    // 'get /test/zip_loc': 'Test/TestGMapController.zip_loc',//,
}

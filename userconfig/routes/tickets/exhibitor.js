const CTRL = 'Event/Tickets/ExhibitorTicketsController';

module.exports = {
    /**
     *
     * @api {post} /api/event/:event/ticket/exhibitor Exhibitor Tickets Creation
     * @apiDescription Creates Exhibitor ticket
     * @apiGroup Exhibitor Tickets
     *
     */
    'POST /api/event/:event/ticket/exhibitor': `${CTRL}.create`,

    /**
     *
     * @api {post} /api/event/:event/ticket/exhibitor-import Exhibitor Tickets Import
     * @apiDescription Creates Exhibitor tickets using imported file
     * @apiGroup Exhibitor Tickets
     *
     */
    'POST /api/event/:event/ticket/exhibitor-import': `${CTRL}.import`,

    /**
     *
     * @api {get} /api/event/:event/ticket/exhibitor-import/:id Exhibitor Import Info
     * @apiDescription Get Exhibitor import progress
     * @apiGroup Exhibitor Tickets
     *
     */
    'GET /api/event/:event/ticket/exhibitor-import/:id': `${CTRL}.getImport`
}


const REDIS_CTRL = 'Admin/RedisController';

module.exports = {
    /**
     *
     * @api {get} /api/admin/redis/keys/all Redis Keys List (cache)
     * @apiDescription Returns redis keys list (currently cache keys)
     * @apiGroup Admin Redis
     *
     */
    'GET /api/admin/redis/keys/all': `${REDIS_CTRL}.all_keys`,

    /**
     *
     * @api {post} /api/admin/redis/key/retrieve Redis Key Value
     * @apiDescription Returns redis specific key value
     * @apiGroup Admin Redis
     *
     */
    'POST /api/admin/redis/key/retrieve': {
        controller: REDIS_CTRL,
        action: 'key_value',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/redis/key/remove Remove Keys By Mask
     * @apiDescription Removes redis keys by mask value
     * @apiGroup Admin Redis
     *
     */
    'POST /api/admin/redis/key/remove': {
        controller: REDIS_CTRL,
        action: 'remove_by_mask',
        csrf: false,
    },

    /**
     *
     * @api {post} /api/admin/redis/cache/expires Set Redis Key Expired
     * @apiDescription Updates redis key expiration time - set expired
     * @apiGroup Admin Redis
     *
     */
    'POST /api/admin/redis/cache/expires': {
        controller: REDIS_CTRL,
        action: 'setExpiresCacheTime',
        csrf: false,
    },
}

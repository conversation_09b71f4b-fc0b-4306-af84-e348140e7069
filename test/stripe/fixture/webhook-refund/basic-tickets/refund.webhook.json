{"id": "evt_1EwqZKJyPh92VEwcts3cnPOG", "object": "event", "api_version": "2017-08-15", "created": **********, "data": {"object": {"id": "ch_1EwqAI2Yt0RbUG0qeu6tAfOT", "object": "charge", "amount": 400, "amount_refunded": 400, "application": "ca_6BluQhjxGw39pnci70csHwmx7Jp52tf2", "application_fee": null, "application_fee_amount": 461, "balance_transaction": "txn_1EwqAJJyPh92VEwcDdXh8QL9", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": null, "name": null, "phone": null}, "captured": true, "created": 1563281635, "currency": "usd", "customer": null, "description": "Test basic tickets event Tickets: 869-237-895, <NAME_EMAIL> 11231231231  Total: $4", "destination": 1, "dispute": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"purchase_id": "115624", "barcode": "*********", "email": "<EMAIL>", "phone": "11231231231", "event_name": "Test basic tickets event", "tickets": "Weekend Passes * 2 ($4); ", "cardholder": "wdwd wdwd", "total": "$1.39", "stripe_fee": "$0.41", "additional_fee": "$2.00", "sw_fee": "$2.20", "receipt_url": "https://dev.sportwrench.com/tickets/receipt/af33af9b4488113774a3024b655b1af5-*********-ecb3dd585367be9b42e8269c2022fd54", "ticket_holders": [{"event_ticket_id": 207, "price": 2, "quantity": 2}]}, "on_behalf_of": null, "order": null, "outcome": null, "paid": true, "payment_intent": null, "payment_method": null, "payment_method_details": {"stripe_account": {}, "type": "stripe_account"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_1BTtqQJyPh92VEwc/py_1EwqAJJyPh92VEwcdzg3OsG8/rcpt_FRjd3YeBgqnZTKikXZuB5JD5Mh4F18n", "refunded": true, "refunds": {"object": "list", "data": [{"id": "pyr_1EwqZKJyPh92VEwcGJ4AG2vW", "object": "refund", "amount": 400, "balance_transaction": "txn_1EwqZKJyPh92VEwcsqMBJK6v", "charge": "py_1EwqAJJyPh92VEwcdzg3OsG8", "created": **********, "currency": "usd", "metadata": {}, "reason": "requested_by_customer", "receipt_number": null, "source_transfer_reversal": null, "status": "succeeded", "transfer_reversal": null}], "has_more": false, "total_count": 1, "url": "/v1/charges/py_1EwqAJJyPh92VEwcdzg3OsG8/refunds"}, "review": null, "shipping": null, "source": {"id": "acct_102orH2Yt0RbUG0q", "object": "account", "application_icon": "https://s3.amazonaws.com/stripe-uploads/acct_102orH2Yt0RbUG0qapplication-icon-sw-icon-notext.png", "application_logo": "https://s3.amazonaws.com/stripe-uploads/acct_102orH2Yt0RbUG0qapplication-logo-sw-icon-textright.png", "application_name": "SportWrench.com", "application_url": "https://sportwrench.com/"}, "source_transfer": null, "statement_descriptor": null, "status": "succeeded", "transfer_data": null, "transfer_group": null}, "previous_attributes": {"amount_refunded": 0, "refunded": false, "refunds": {"data": [], "total_count": 0}}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_WDSQRarKNGqye6", "idempotency_key": null}, "type": "charge.refunded"}
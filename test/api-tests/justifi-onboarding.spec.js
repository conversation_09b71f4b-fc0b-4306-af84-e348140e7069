'use strict';

const request = require('request-promise');
const { expect } = require('chai');
const sinon = require('sinon');
const UserSignin = require('../ticket-refunds/user-signin');
const users = require('../ticket-refunds/fixture/user');

describe('POST /api/justifi/onboarding/generate-web-token', function () {
    let user;
    let cookies;
    let JustifiService;
    let createBusinessStub;
    let createWebTokenStub;

    const mockBusinessId = 'bus_123456789';
    const mockToken = 'web_token_123456789';

    before(async function () {
        JustifiService = global.sails.services.justifiservice;

        createBusinessStub = sinon.stub(JustifiService, 'createBusiness')
            .resolves({ businessId: mockBusinessId });

        createWebTokenStub = sinon.stub(JustifiService, 'createWebTokenForBusiness')
            .resolves(mockToken);
    });

    after(async function () {
        createBusinessStub.restore();
        createWebTokenStub.restore();

        if (user) {
            await user.del();
            user = null;
        }
    });

    context('when user is an event owner', function () {
        before(async function () {
            user = await UserSignin.create(users[0]);
            cookies = await user.signIn();
        });

        it('should generate a web token successfully', async function () {
            const response = await request({
                method: 'POST',
                uri: `http://${global.HOST}/api/justifi/onboarding/generate-web-token`,
                json: true,
                resolveWithFullResponse: true,
                headers: {
                    'cookie': cookies,
                    'content-type': 'application/json'
                }
            });

            expect(response.statusCode).to.equal(200);
            expect(response.body).to.deep.equal({
                token: mockToken,
                businessId: mockBusinessId
            });

            expect(createBusinessStub.calledOnce).to.be.true;
            expect(createWebTokenStub.calledOnce).to.be.true;
            expect(createWebTokenStub.firstCall.args[0]).to.deep.equal({
                businessId: mockBusinessId
            });
        });
    });

    context('when user is not an event owner', function () {
        before(async function () {
            user = await UserSignin.create(users[2]);
            cookies = await user.signIn();
        });

        it('should return 403 Forbidden due to isEventOwner policy', async function () {
            try {
                await request({
                    method: 'POST',
                    uri: `http://${global.HOST}/api/justifi/onboarding/generate-web-token`,
                    json: true,
                    resolveWithFullResponse: true,
                    headers: {
                        'cookie': cookies,
                        'content-type': 'application/json'
                    }
                });

                expect.fail('Request should have failed with 403');
            } catch (error) {
                expect(error.statusCode).to.equal(403);
            }
        });
    });
});

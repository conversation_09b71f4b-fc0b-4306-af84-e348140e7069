{"event": {"event_id": 20017, "name": "Master CAMP EP-C2020", "long_name": "Master CAMP EvPo-C 2020 (b/b | w/o Check SWFee)", "date_start": "2019-09-01 00:00:00.000000", "date_end": "2020-12-31 23:00:00.000000", "event_owner_id": 42, "sport_id": 2, "has_status_housing": false, "has_status_roster": false, "country": "US", "city": "New York", "zip": "10024", "state": "NY", "address": "1000 5th Ave", "sport_variation_id": 1, "website": "http://website.com", "email": "<EMAIL>", "has_male_teams": false, "has_female_teams": false, "has_coed_teams": false, "reg_fee": 0, "payment_country": "US", "mincount_enter": 0, "mincount_accept": 0, "has_late_reg": false, "housing_company_id": 0, "hosting_org_name": "Evg <PERSON>", "hosting_org_address": "Address str.", "hosting_org_city": "City", "hosting_org_state": "AL", "hosting_org_zip": "12345", "hosting_org_phone": "0000000000", "published": false, "show_teams_entered": "hide", "notify_frequency": "never", "has_officials": false, "official_applied_email_template_id": 11, "official_accepted_email_template_id": 16, "official_waitlisted_email_template_id": 17, "official_declined_email_template_id": 18, "timezone": "America/New_York", "has_rosters": true, "remote_sync_allowed": false, "schedule_published": false, "registration_method": "club", "usav_required": false, "has_match_barcodes": false, "tickets_published": true, "tickets_sw_fee": 4, "teams_entry_sw_fee": 0, "tickets_purchase_date_start": "2019-08-01 00:00:00.000000", "tickets_purchase_date_end": "2020-12-31 00:00:00.000000", "tickets_visible": false, "event_tickets_code": "*********", "tickets_purchase_additional_fields": "[{\"type\": \"text\", \"field\": \"first_name\", \"label\": \"First Name\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"Player First\"}, {\"type\": \"text\", \"field\": \"last_name\", \"label\": \"Last Name\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"Player Last\"}, {\"type\": \"select\", \"field\": \"gender\", \"label\": \"Gender\", \"options\": {\"male\": \"Male\", \"female\": \"Female\"}, \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"date-sel\", \"field\": \"birthdate\", \"label\": \"Birthdate\", \"show_on\": {\"receipt\": true, \"purchase\": true, \"payment_list\": true}, \"required\": true, \"short_label\": \"\", \"outer_dependency\": {\"age\": true}}, {\"type\": \"text\", \"field\": \"email\", \"label\": \"Email\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"confirm_email\", \"label\": \"Confirm Email\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"dependency\": {\"equal\": {\"email\": true}}, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"day_phone____and_ext_\", \"label\": \"Day Phone #  and ext.\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"evening_phone___and_ext_\", \"label\": \"Evening Phone # and ext.\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"address_line_1\", \"label\": \"Address Line 1\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"address_line_2\", \"label\": \"Address Line 2\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"city\", \"label\": \"City\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"select\", \"field\": \"state\", \"label\": \"State\", \"options\": {\"AK\": \"Alaska\", \"AL\": \"Alabama\", \"AR\": \"Arkansas\", \"AZ\": \"Arizona\", \"CA\": \"California\", \"CO\": \"Colorado\", \"CT\": \"Connecticut\", \"DC\": \"District of Columbia\", \"DE\": \"Delaware\", \"FL\": \"Florida\", \"GA\": \"Georgia\", \"HI\": \"Hawaii\", \"IA\": \"Iowa\", \"ID\": \"Idaho\", \"IL\": \"Illinois\", \"IN\": \"Indiana\", \"KS\": \"Kansas\", \"KY\": \"Kentucky\", \"LA\": \"Louisiana\", \"MA\": \"Massachusetts\", \"MD\": \"Maryland\", \"ME\": \"Maine\", \"MI\": \"Michigan\", \"MN\": \"Minnesota\", \"MO\": \"Missouri\", \"MS\": \"Mississippi\", \"MT\": \"Montana\", \"NC\": \"North Carolina\", \"ND\": \"North Dakota\", \"NE\": \"Nebraska\", \"NH\": \"New Hampshire\", \"NJ\": \"New Jersey\", \"NM\": \"New Mexico\", \"NV\": \"Nevada\", \"NY\": \"New York\", \"OH\": \"Ohio\", \"OK\": \"Oklahoma\", \"OR\": \"Oregon\", \"PA\": \"Pennsylvania\", \"PR\": \"Puerto Rico\", \"RI\": \"Rhode Island\", \"SC\": \"South Carolina\", \"SD\": \"South Dakota\", \"TN\": \"Tennessee\", \"TX\": \"Texas\", \"UT\": \"Utah\", \"VA\": \"Virginia\", \"VT\": \"Vermont\", \"WA\": \"Washington\", \"WI\": \"Wisconsin\", \"WV\": \"West Virginia\", \"WY\": \"Wyoming\"}, \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"select\", \"field\": \"country\", \"label\": \"Country\", \"options\": {\"us\": \"US\", \"canada\": \"Canada\"}, \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"zip_postal_code\", \"label\": \"Zip/Postal Code\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"emergency_contact_name\", \"label\": \"Emergency Contact Name\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"emergency_contact_phone_number\", \"label\": \"Emergency Contact Phone Number\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"select\", \"field\": \"how_did_you_hear_about_american_volleyball_camps_\", \"label\": \"How did you hear about American Volleyball Camps?\", \"options\": {\"road\": \"Road sign\", \"email\": \"Email\", \"other\": \"Other (please explain below)\", \"rumor\": \"Word of mouth\", \"google\": \"Google search\", \"magazine_ad\": \"Magazine ad\"}, \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"\"}, {\"type\": \"text\", \"field\": \"explain_other\", \"label\": \"Explain Other\", \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"dependency\": {\"available\": {\"how_did_you_hear_about_american_volleyball_camps_\": \"other\"}}, \"short_label\": \"\"}, {\"type\": \"select\", \"field\": \"position\", \"label\": \"Position\", \"options\": {\"left\": \"Left\", \"right\": \"Right\", \"center\": \"Center\"}, \"show_on\": {\"receipt\": false, \"purchase\": false, \"payment_list\": false}, \"required\": false, \"short_label\": \"Position\"}]", "allow_ticket_sales": true, "live_to_public": true, "officials_required": 0, "allow_teams_registration": false, "allow_card_payments": false, "public_teams_visibility": "hidden", "clubs_teams_visibility": "hidden", "ticket_camps_registration": true, "tickets_use_connect": true, "stripe_tickets_percent": 2.7, "stripe_tickets_fixed": 0.3, "stripe_tickets_fee_payer": "buyer", "tickets_sw_fee_payer": "buyer", "tie_breaker_type": 0, "tickets_purchase_by_card": true, "tickets_purchase_by_check": true, "tickets_purchase_by_ach": false, "esw_id": "ff5c0b949", "email_on_ticket_purchase": false, "tickets_has_barcodes": true, "is_test": false, "online_team_checkin_available": false, "maxcount_staff_accept": 5, "tickets_stripe_statement": "Stat-Desc", "team_members_validation": "{\"maxcount_enter\": 16, \"mincount_enter\": 0, \"maxcount_accept\": 16, \"mincount_accept\": 0, \"maxcount_checkin\": 16, \"mincount_checkin\": 7, \"primary_maxcount_staff_accept\": 3}", "tickets_sw_balance": 41, "tickets_sw_extra_fee": 0, "tickets_sw_target_balance": 0, "allow_check_payments": true, "allow_ach_payments": false, "teams_use_connect": true, "ach_surcharge": 0, "stripe_teams_percent": 2.9, "stripe_teams_fixed": 0.3, "ach_teams_percent": 0.8, "ach_teams_max_fee": 5, "teams_sw_balance": 0, "require_match_end_time": true, "official_payment_method": "{}", "score_entry_live": false, "enable_officials_reg": false, "enable_hotel_for_officials": false, "season": 2020, "hide_seeds": false, "teams_escrow_collected": 0, "tickets_settings": "{\"age_date\": \"09/01/2019\", \"not_require_sw_fee_for_checks\": true}", "online_team_checkin_mode": "default", "teams_settings": "{\"sort_by\": \"seed_current\", \"hide_standings\": false, \"manual_teams_addition\": false}", "teams_use_clubs_module": false, "has_staff": false, "enable_staff_reg": false, "staff_payment_method": "{}", "enable_hotel_for_staff": false, "coordinates": "{}", "club_private_reg_code": "9b82cc9a", "stripe_sw_percent": 2.7, "tickets_sw_fee_internal": 1, "show_on_home_page": false, "has_exhibitors": false, "enable_exhibitors_reg": false, "stripe_exhibitors_percent": 2.9, "show_number_of_teams_for_public": true, "show_number_of_teams_for_cd": true, "online_team_checkin_start": null}, "data": {"purchase": [{"purchase_id": 2007071, "event_id": 20017, "amount": "300.00", "status": "paid", "check_num": "123123123123", "received_date": "2020-04-10 00:00:00.000000", "type": "check", "email": "<EMAIL>", "phone": "10000000000", "payment_for": "tickets", "user_id": 82394, "ticket_barcode": 706623511, "zip": "45454", "source": "site", "first": "Apr10MCEPCx2", "last": "web", "tickets_additional": "{\"birthdate\":\"01/01/2001\"}", "additional_fee_amount": 0, "net_profit": 300, "stripe_fee": 0, "collected_sw_fee": 0, "purchase_discount": 0, "hash": "f47e06a1bee26f4952dada5291382f0c207edc8535b9892e988694eda6b21201.*********", "is_payment": true, "is_ticket": true, "registration_status": "active"}], "purchase_ticket": [{"purchase_ticket_id": 50002656, "purchase_id": 2007071, "amount": 300, "ticket_price": 300, "quantity": 1, "available": 1, "event_ticket_id": 1415, "discount": 0, "discounted_quantity": 0, "ticket_fee": 0, "kiosk_surcharge": 0, "registration_status": "active"}], "event_ticket": [{"event_id": 20017, "label": "Camp2-1", "initial_price": 300, "prices": "{}", "event_ticket_id": 1415, "published": true, "current_price": 300, "short_label": "C2-1", "application_fee": 0, "sort_order": 2, "description": "Desc2-1", "event_camp_id": 144, "waitlisted": false, "waitlist_switching_count": 500, "has_barcode": true, "ticket_type": "other", "can_be_scanned": true, "visible_in_kiosk": true, "related_events": []}]}}
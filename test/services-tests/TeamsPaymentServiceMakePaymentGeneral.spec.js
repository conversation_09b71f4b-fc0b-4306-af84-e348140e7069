'use strict';

const eventRow = require('./fixture/TeamsPaymentServiceMakePayment/event.row');
const divisionRows = require('./fixture/TeamsPaymentServiceMakePayment/division.rows');
const rosterTeamRows = require('./fixture/TeamsPaymentServiceMakePayment/roster_team.rows');
const masterClubRow = require('./fixture/TeamsPaymentServiceMakePayment/master_club.row');
const masterTeamRows = require('./fixture/TeamsPaymentServiceMakePayment/master_team.rows');
const rosterClubRow = require('./fixture/TeamsPaymentServiceMakePayment/roster_club.row');
const paymentData = require('./fixture/TeamsPaymentServiceMakePayment/makePaymentMethodData');

function prepareDB() {
    return Promise.all([
        Db.query(squel.insert().into('event').setFields(eventRow).toString()),
        Db.query(squel.insert().into('division').setFieldsRows(divisionRows).toString()),
        Db.query(squel.insert().into('roster_team').setFieldsRows(rosterTeamRows).toString()),
        Db.query(squel.insert().into('master_club').setFields(masterClubRow).toString()),
        Db.query(squel.insert().into('master_team').setFieldsRows(masterTeamRows).toString()),
        Db.query(squel.insert().into('roster_club').setFields(rosterClubRow).toString())
    ]);
};

function clearDB() {
    return Promise.all([
        Db.query('DELETE FROM "event" WHERE "event_id" = $1', [eventRow.event_id]),
        Db.query('DELETE FROM "division" WHERE "event_id" = $1', [eventRow.event_id]),
        Db.query('DELETE FROM "roster_team" WHERE "event_id" = $1', [eventRow.event_id]),
        Db.query('DELETE FROM "master_club" WHERE "master_club_id" = $1', [masterClubRow.master_club_id]),
        Db.query('DELETE FROM "purchase" WHERE "event_id" = $1', [eventRow.event_id]),
        Db.query('DELETE FROM "purchase_team" WHERE "event_id" = $1', [eventRow.event_id]),
        Db.query('DELETE FROM "master_team" WHERE "master_club_id" = $1', [masterClubRow.master_club_id]),
        Db.query('DELETE FROM "roster_club" WHERE "master_club_id" = $1', [masterClubRow.master_club_id])
    ]);
};

describe('TeamsPaymentServiceMakePayment.makePayment General Checks', function () {
    let service;
    let mode;
    let createStripeChargeStub;

    before(() => {
        service = global.sails.services.teamspaymentservice;
        mode = service.CLUB_DIRECTOR_PAYMENT_MODE;
        return createStripeChargeStub = sinon
            .stub(service, 'createStripeCharge')
            .resolves({
                id: "chargeID",
                source: {
                    id: "sourceID",
                    fingeprint: "sourceFingeprint"
                }
            });
    });

    beforeEach(() => {
        return prepareDB()
    });

    afterEach(() => {
        return clearDB();
    });

    after(() => {
        return createStripeChargeStub.restore();
    });


    // we don't check "allow_teams_registration" flag in code
    context.skip(`"event"."allow_teams_registration" == FALSE`, function () {
        beforeEach(() => {
            return Db.query(`UPDATE "event" SET "allow_teams_registration" = FALSE 
                           WHERE "event_id" = $1`, [eventRow.event_id])
        });
        afterEach(() => {
            return Db.query(`UPDATE "event" SET "allow_teams_registration" = TRUE 
                           WHERE "event_id" = $1`, [eventRow.event_id])
        });

        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });

    context(`"event"."allow_check_payments" == FALSE 
              or "event"."allow_ach_payments" == FALSE 
              or "event"."allow_card_payments" == FALSE`, function () {

        beforeEach(() => {
            return Db.query(`UPDATE "event" SET 
                                       "allow_check_payments" = FALSE,
                                       "allow_ach_payments" = FALSE,
                                       "allow_card_payments" = FALSE 
                               WHERE "event_id" = $1`, [eventRow.event_id])
        });
        afterEach(() => {
            return Db.query(`UPDATE "event" SET 
                                   "allow_check_payments" = TRUE,
                                   "allow_ach_payments" = TRUE,
                                   "allow_card_payments" = TRUE 
                           WHERE "event_id" = $1`, [eventRow.event_id])
        });

        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });


    context(`Already paid teams`, function () {

        beforeEach(() => {
            let rosterTeamIDs = rosterTeamRows.map(r => r.roster_team_id);
            return Db.query(`UPDATE "roster_team" SET "status_paid" = 22
                               WHERE roster_team_id = ANY ($1)`, [rosterTeamIDs]);
        });
        afterEach(() => {
            let rosterTeamIDs = rosterTeamRows.map(r => r.roster_team_id);
            return Db.query(`UPDATE "roster_team" SET "status_paid" = 21
                              WHERE roster_team_id = ANY ($1)`, [rosterTeamIDs]);
        });

        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });

    context(`Teams from different event`, function () {
        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            data.event_id++;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            data.event_id++;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            data.event_id++;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });

    context(`Deleted teams`, function () {

        beforeEach(() => {
            let rosterTeamIDs = rosterTeamRows.map(r => r.roster_team_id);
            return Db.query(`UPDATE "roster_team" SET "deleted" = '2016-10-07 17:26:18.111723'
                                           WHERE roster_team_id = ANY ($1)`, [rosterTeamIDs]);
        });
        afterEach(() => {
            let rosterTeamIDs = rosterTeamRows.map(r => r.roster_team_id);
            return Db.query(`UPDATE "roster_team" SET "deleted" = NULL
                                          WHERE roster_team_id = ANY ($1)`, [rosterTeamIDs]);
        });

        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });

    context(`Event event.season != current season`, function () {

        beforeEach(() => {
            return Db.query(`UPDATE "event" SET "season" = $2 - 1
                           WHERE "event_id" = $1`, [eventRow.event_id, sails.config.sw_season.current]);
        });
        afterEach(() => {
            return Db.query(`UPDATE "event" SET "season" = $2
                           WHERE "event_id" = $1`, [eventRow.event_id, sails.config.sw_season.current]);
        });

        it(`should be rejected for payment_type = "check"`, () => {
            let data = paymentData.cd_check;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "ach"`, () => {
            let data = paymentData.cd_ach;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
        it(`should be rejected for payment_type = "card"`, () => {
            let data = paymentData.cd_card;
            return service.makePayment(data, mode).should.be.rejectedWith();
        });
    });


});

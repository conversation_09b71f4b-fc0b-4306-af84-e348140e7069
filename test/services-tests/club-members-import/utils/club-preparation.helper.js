const DB_FIXTURES_PATH = '../../fixture/club-members-import/db/';

const user = require(DB_FIXTURES_PATH + 'user.json');
const club_owner = require(DB_FIXTURES_PATH + 'club_owner.json');
const master_club = require(DB_FIXTURES_PATH + 'master_club.json');
const master_team = require(DB_FIXTURES_PATH + 'master_team.json');
const roster_club = require(DB_FIXTURES_PATH + 'roster_club.json');
const master_staff = require(DB_FIXTURES_PATH + 'master_staff.json');
const master_athlete = require(DB_FIXTURES_PATH + 'master_athlete.json');
const master_club_sanctioning = require(DB_FIXTURES_PATH + 'master_club_sanctioning.json');

const fixtures = {
    user,
    club_owner,
    master_club,
    master_club_sanctioning,
    master_team,
    roster_club,
    master_staff,
    master_athlete
};

class ClubPreparationHelper {
    constructor(fixtures) {
        this.userFixture = fixtures.user;
        this.clubOwnerFixture = fixtures.club_owner;
        this.masterClubFixture = fixtures.master_club;
        this.masterClubSanctioningFixture = fixtures.master_club_sanctioning;
        this.masterTeamFixture = fixtures.master_team;
        this.rosterTeamFixture = fixtures.roster_club;
        this.masterAthleteFixture = fixtures.master_athlete;
        this.masterStaffFixture = fixtures.master_staff;
    }

    async addClubData() {
        return this.#runInTransaction(
            (tr) =>
                Promise.all([
                    this.#processCreation(tr, 'user', this.userFixture),
                    this.#processCreation(tr, 'club_owner', this.clubOwnerFixture),
                    this.#processCreation(tr, 'master_club', this.masterClubFixture),
                    this.#processCreation(tr, 'master_club_sanctioning', this.masterClubSanctioningFixture),
                    this.#processCreation(tr, 'master_team', this.masterTeamFixture),
                    this.#processCreation(tr, 'roster_club', this.rosterTeamFixture),
                ])
        );
    }

    async clearClubData() {
        return this.#runInTransaction(
            (tr) =>
                Promise.all([
                    this.#processClear(tr, 'user'),
                    this.#processClear(tr, 'club_owner'),
                    this.#processClear(tr, 'master_club'),
                    this.#processClear(tr, 'master_club_sanctioning'),
                    this.#processClear(tr, 'master_team'),
                    this.#processClear(tr, 'roster_club'),
                ])
        );
    }

    async clearMembersData() {
        return this.#runInTransaction(
            (tr) =>
                Promise.all([
                    this.#processClear(tr, 'master_athlete'),
                    this.#processClear(tr, 'master_staff'),
                    this.#processClear(tr, 'master_staff_role')
                ])
        )
    }

    async addMembersData() {
        return this.#runInTransaction(
            (tr) =>
                Promise.all([
                    this.#processCreation(tr, 'master_staff', this.masterStaffFixture),
                    this.#processCreation(tr, 'master_athlete', this.masterAthleteFixture),
                ])
        )
    }

    async getClubAthletes() {
        return this.#getClubMembers('master_athlete');
    }

    async getClubStaffers() {
        return this.#getClubMembers('master_staff');
    }

    #getClubMembers(tableName) {
        return Db.query(knex(tableName).where({ master_club_id: this.masterClubFixture.master_club_id}))
            .then(({rows}) => rows);
    }

    #processCreation(tr, table, fixture) {
        return tr.query((knex(table).insert(fixture)));
    }

    #processClear(tr, table) {
        return tr.query(knex(table).truncate());
    }

    async #runInTransaction(fn) {
        let tr = null;

        try {
            tr = await Db.begin();

            await fn(tr);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }
}

module.exports = new ClubPreparationHelper(fixtures);

const AAUImportHelper = require('../utils/aau-import.helper');
const SEImportHelper = require('../utils/se-import.helper');
const ClubPreparationHelper = require('../utils/club-preparation.helper');
const AAUResultsHelper = require('../utils/aau-results-check.helper');
const SEResultsHelper = require('../utils/se-results-check.helper');

/*
* Testing flows
* 1. Import from USAV/SE when there are members imported from XLSX file.
* 2. Import from USAV/SE after import from AAU
* 3. Import from USAV/SE after import from USAV/SE
*/

describe('USAV/SE Club Members Import', () => {
    let AAUImportService;
    let SEImportService;
    let RosterSnapshotServiceClubSnapshotStub;
    let AAUServiceGetMembersStub;
    let SEServiceGetMembersStub;
    let AAUService;
    let SportsEngineService;
    let RosterSnapshotService;
    let AAUMemberService;
    let AAUUtils;
    let SEUtils;
    let SEMembersService;


    before(async () => {
        AAUMemberService = sails.services.aaumemberservice;
        AAUUtils = AAUMemberService.utils;
        AAUImportService = AAUMemberService.import;
        AAUService = sails.services.aauservice;
        SEMembersService = sails.services.sportenginememberservice;
        SEUtils = SEMembersService.utils;
        SEImportService = SEMembersService.import;
        SportsEngineService = sails.services.sportsengineservice;
        RosterSnapshotService = sails.services.rostersnapshotservice;

        RosterSnapshotServiceClubSnapshotStub = sinon.stub(RosterSnapshotService, 'clubSnapshot').resolves({});

        await ClubPreparationHelper.addClubData();
    });

    beforeEach(async () => {
        AAUServiceGetMembersStub = sinon.stub(
            AAUService,
            'getMembersByProxy'
        ).resolves(AAUImportHelper.importResponse);

        SEServiceGetMembersStub = sinon.stub(
            SEImportService.process,
            'getClubMembersFromSportEngineAPI'
        ).resolves(SEImportHelper.importResponse);
    });

    after(async () => {
        await RosterSnapshotServiceClubSnapshotStub.restore();
        await ClubPreparationHelper.clearClubData();
    })

    afterEach(async () => {
        AAUServiceGetMembersStub.restore();
        SEServiceGetMembersStub.restore();

        await SEImportHelper.clearQueue();
    });

    context('Insert Mode', () => {
        beforeEach(async () => {
            await SEImportHelper.prepareImportInsertMode();
        })

        afterEach(async () => {
            await SEImportHelper.clearQueue();
            await ClubPreparationHelper.clearMembersData();
        })

        it('should successfully import members into empty club when there are members imported from XLSX file', async () => {
            //Add members to DB like it could be done using XLSX import
            await ClubPreparationHelper.addMembersData();

            //Run SE Import
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from AAU', async () => {
            //Add AAU queue record to DB
            await AAUImportHelper.prepareImportInsertMode();

            //Run AAU Import
            await AAUImportService.process.run();
            //Run SE Import
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check athletes AAU data
            * - check staffers AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromAAUImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromAAUImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromAAUImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromAAUImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from USAV/SE', async () => {
            //Run SE Import
            await SEMembersService.import.process.run();

            //Prepare and run new SE import with insert mode
            await SEImportHelper.prepareImportInsertMode();
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })
    })

    context('Update Mode', () => {
        beforeEach(async () => {
            await SEImportHelper.prepareImportUpdateMode();
        })

        afterEach(async () => {
            await SEImportHelper.clearQueue();
            await ClubPreparationHelper.clearMembersData();
        })

        it('should successfully import members into empty club when there are members imported from XLSX file', async () => {
            //Add members to DB like it could be done using XLSX import
            await ClubPreparationHelper.addMembersData();

            //Run SE Import
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from AAU', async () => {
            //Add AAU queue record to DB
            await AAUImportHelper.prepareImportInsertMode();

            //Run AAU Import
            await AAUImportService.process.run();
            //Run SE Import
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check athletes AAU data
            * - check staffers AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromAAUImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromAAUImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromAAUImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromAAUImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from USAV/SE', async () => {
            //Run SE Import
            await SEMembersService.import.process.run();

            //Prepare and run new SE import with insert mode
            await SEImportHelper.prepareImportUpdateMode();
            await SEMembersService.import.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })
    })
})

const fs = require('fs');
const nock = require('nock');
const moment = require('moment');

const SEUtilsService = require('../../api/lib/SEUtilsService');

let sportEngineDirectorResponsePath = '/fixture/SportEngineMemberService/sportengine-api-director-response.json';
let sportEngineClubResponsePath = '/fixture/SportEngineMemberService/sportengine-api-club-response.json';
let sportEngineTokenResponsePath = '/fixture/SportEngineMemberService/sportengine-token-response.json';
let sportEngineEmptyResponsePath = '/fixture/SportEngineMemberService/sportengine-api-empty-response.json';

let sportEngineDirectorResponse = require('.' + sportEngineDirectorResponsePath);
let sportEngineClubResponse = require('.' + sportEngineClubResponsePath);

const sportEngineDirectorResponseFile
    = fs.readFileSync(__dirname + sportEngineDirectorResponsePath);
const sportEngineTokenResponseFile
    = fs.readFileSync(__dirname + sportEngineTokenResponsePath);
const sportEngineEmptyResponseFile
    = fs.readFileSync(__dirname + sportEngineEmptyResponsePath);
const sportEngineClubResponseFile
    = fs.readFileSync(__dirname + sportEngineClubResponsePath);

const sportEngineQueueInsertModeRow = require('./fixture/SportEngineMemberService/sportengine-queue.insert.row.json');
const sportEngineQueueDefaultModeRow = require('./fixture/SportEngineMemberService/sportengine-queue.default.row.json');

const masterAthletesData = require('./fixture/SportEngineMemberService/club-athletes.rows.json');
const masterStaffersData = require('./fixture/SportEngineMemberService/club-staffers.rows.json');

const masterClubRow = require('./fixture/SportEngineMemberService/master-club.row.json');

describe('SportEngineMemberService.import.create()', () => {
    let SportEngineMemberService, clubOwnerID, SportEngineMemberImportService;

    before(() => {
        clubOwnerID = masterClubRow[0].club_owner_id;

        SportEngineMemberService = sails.services.sportenginememberservice;

        SportEngineMemberImportService = SportEngineMemberService.import;
    });

    context('params validation', () => {

        it('should throw error if no master_club_id passed', () =>
            SportEngineMemberImportService.create(null, 1, { option: 'insert' })
                .should.be.rejectedWith({ validation: 'Master Club ID required' })
        )

        it('should throw error if no club_owner_id passed', () =>
            SportEngineMemberImportService.create(1, null, { option: 'insert' })
                .should.be.rejectedWith({ validation: 'Club Owner ID required' })
        )

        it('should throw error if option not passed', () =>
            SportEngineMemberImportService.create(1, 1)
                .should.be.rejectedWith({ validation: 'Invalid option value' })
        )

        it('should throw error if option contains invalid value', () =>
            SportEngineMemberImportService.create(1, 1, { option: 'test' })
                .should.be.rejectedWith({ validation: 'Invalid option value' })
        )
    })

    context('import creation', () => {
        let masterClubID;

        let nockHTTPInterceptorSEToken,
            nockHTTPInterceptorSEData;

        before(async () => {
            masterClubID = await createMasterClub();
        })

        after(async () => {
            await clearMasterClub();
        })

        beforeEach(async () => {
            nockHTTPInterceptorSEToken = getSERequestTokenNock();
            nockHTTPInterceptorSEData = getSERequestDirectorDataNock();

            await clearSEImportQueue();
        })

        afterEach(async () => {
            nock.cleanAll();
        });

        it('success import', async () => {
            await SportEngineMemberImportService.create(masterClubID, clubOwnerID, { option: 'insert' });

            let queueItems = await getSEClubImportQueueItem(masterClubID);

            expect(queueItems).to.be.a('array');
            expect(queueItems.length).to.be.equal(1);
            expect(queueItems[0].option).to.be.equal('insert');
            expect(queueItems[0].sportengine_club_id)
                .to.be.equal(sportEngineDirectorResponse.result[0].affiliation_org_id);
        })

        it('should throw error if import already created for club', async () => {
            await SportEngineMemberImportService.create(masterClubID, clubOwnerID, { option: 'insert' });

            return SportEngineMemberImportService.create(masterClubID, clubOwnerID, { option: 'insert' })
                .should.be.rejectedWith({ validation: 'Sport Engine Import is already running' });
        })

        it('should throw error if club not found', () =>
            SportEngineMemberImportService.create(10000, clubOwnerID, { option: 'insert' })
                .should.be.rejectedWith({ validation: 'Club not found' })
        )
    })

    context('import creation', () => {
        let masterClubID, nockHTTPInterceptorSEToken, nockHTTPInterceptorSEData;

        before(async () => {
            masterClubID = await createMasterClub();
            await clearSEImportQueue();

            nockHTTPInterceptorSEToken = getSERequestTokenNock();
            nockHTTPInterceptorSEData = getSERequestDirectorDataNock({withEmptyResponse: true});
        })

        after(() => {
            nock.cleanAll();
            return clearMasterClub();
        })

        it('should throw error if club not found', () => {
            return SportEngineMemberImportService.create(masterClubID, clubOwnerID, {option: 'insert'})
                .should.be.rejectedWith({ validation: `SportEngine member not found` })
        })
    })
})

describe('SportEngineMemberService.import.process.run()', () => {
    let SportEngineMemberService, clubOwnerID, SportEngineMemberImportService;

    before(() => {
        clubOwnerID = masterClubRow[0].club_owner_id;

        SportEngineMemberService = sails.services.sportenginememberservice;

        SportEngineMemberImportService = SportEngineMemberService.import;
    });

    context('_importDataFromAPI() params validation', () => {
        it('should throw error if no SE club ID passed', async () =>
            SportEngineMemberImportService.process.importDataFromAPI({
                import_option: 'default', master_club_id: 1
            }).should.be.rejectedWith(Error, { message: 'SportEngine Club ID required' })
        )

        it('should throw error if invalid option passed', async () =>
            SportEngineMemberImportService.process.importDataFromAPI({
                sportengine_club_id: 1, import_option: 'test', master_club_id: 1
            }).should.be.rejectedWith(Error, { message: 'Invalid import mode option value' })
        )

        it('should throw error if no option passed', () =>
            SportEngineMemberImportService.process.importDataFromAPI({
                sportengine_club_id: 1, master_club_id: 1
            }).should.be.rejectedWith(Error, { message: 'Invalid import mode option value' })
        )

        it('should throw error if no master club ID passed', () =>
            SportEngineMemberImportService.process.importDataFromAPI({
                sportengine_club_id: 1, import_option: 'default'
            }).should.be.rejectedWith(Error, { message: 'Master Club ID required' })
        )
    })

    context('"insert" mode', () => {
        let masterClubID, nockHTTPInterceptorSEToken, nockHTTPInterceptorSEData;

        before(async () => {
            masterClubID = await createMasterClub();
        })

        after(async () => {
            await clearMasterClub();
        })

        beforeEach(async () => {
            nockHTTPInterceptorSEToken = getSERequestTokenNock();
            nockHTTPInterceptorSEData = getSERequestClubDataNock();
        })

        afterEach(async () => {
            nock.cleanAll();

            await clearSEImportQueue();
            await clearMasterAthletes();
            await clearMasterStaff();
        })

        it('should do successful import', async () => {
            await createSportEngineQueueRow('insert');
            await SportEngineMemberImportService.process.run();

            let { athletes, staffers, queue } = await getImportData();

            checkAddedMembersLists(athletes, staffers, queue);

            checkInsertedAthletesData(athletes);
            checkInsertedStaffersData(staffers);
        })

        it('should do successful import without athletes inserting', async () => {
            await createMasterAthletes(masterAthletesData);
            await createMasterStaffers(masterStaffersData);

            await createSportEngineQueueRow('insert');
            await SportEngineMemberImportService.process.run();

            let { athletes, staffers, queue } = await getImportData();

            checkAddedMembersLists(athletes, staffers, queue);
        })
    })

    context('"default" mode', () => {
        let masterClubID, nockHTTPInterceptorSEToken, nockHTTPInterceptorSEData;

        before(async () => {
            masterClubID = await createMasterClub();
        })

        after(async () => {
            await clearMasterClub();

            nock.cleanAll();
        })

        beforeEach(() => {
            nockHTTPInterceptorSEToken = getSERequestTokenNock();
            nockHTTPInterceptorSEData = getSERequestClubDataNock();
        })

        afterEach(async () => {
            await clearSEImportQueue();
            await clearMasterAthletes();
            await clearMasterStaff();
        })

        it('should do successful import', async () => {
            await createSportEngineQueueRow('default');
            await SportEngineMemberImportService.process.run();

            let { athletes, staffers, queue } = await getImportData();

            checkAddedMembersLists(athletes, staffers, queue);

            checkInsertedAthletesData(athletes);
            checkInsertedStaffersData(staffers);
        })

        it('should do successful import with update of existed athlete', async () => {
            let athlete = Object.assign({}, masterAthletesData[0]);

            athlete.first = 'test';

            await createMasterAthletes(athlete);

            await createSportEngineQueueRow('default');
            await SportEngineMemberImportService.process.run();

            let { athletes, staffers, queue } = await getImportData();

            checkAddedMembersLists(athletes, staffers, queue);

            let [changedAthleteFromDB] = athletes.filter(
                a => Number(a.usav_number) === Number(athlete.usav_number)
            );

            expect(changedAthleteFromDB).to.be.an('object');
            expect(changedAthleteFromDB.first).not.to.be.equal(athlete.first_name);
        })
    })

    context('unsuccessful import', () => {
        it('should do nothing if no imports in queue', () => {})
        it('should throw error if Age Group tag not found in SE response', () => {})
        it('should throw error if Age Group tag value contains invalid value', () => {})
        it('should throw error if empty response from SE', () => {})
    })
})

function checkAddedMembersLists (athletes, staffers, queue) {
    let athletesInResponse = getSEClubResponseAthletes();
    let staffersInResponse = getSEClubResponseStaffers();

    expect(athletes).to.be.an('array');
    expect(athletes.length).to.be.equal(athletesInResponse.length);

    expect(staffers).to.be.an('array');
    expect(staffers.length).to.be.equal(staffersInResponse.length);

    expect(queue[0]).to.be.an('object');
    expect(queue[0].responded).not.to.be.equal(null);
}

function checkInsertedAthletesData (athletesFromDB) {
    let athletesInSEResponse = getSEClubResponseAthletes();

    athletesFromDB.forEach(DBMember => {
        let [memberFromAPI] = athletesInSEResponse.filter(
            a => Number(a.membership_number) === Number(DBMember.usav_number)
        );

        checkInsertedMemberData(memberFromAPI, DBMember);

        expect(DBMember.gradyear).to.be.equal(memberFromAPI.graduation_year);

        if(DBMember.birthdate) {
            expect(DBMember.age).not.to.be.equal(null);
        }
    })
}

function checkInsertedStaffersData (staffersFromDB) {
    let staffersInSEResponse = getSEClubResponseStaffers();

    staffersFromDB.forEach(DBMember => {
        let [memberFromAPI] = staffersInSEResponse.filter(
            s => Number(s.membership_number) === Number(DBMember.usav_number)
        );

        checkInsertedMemberData(memberFromAPI, DBMember);
    })
}

function checkInsertedMemberData (apiMember, DBMember) {
    expect(apiMember).to.be.an('object');
    expect(DBMember.season).to.be.equal(global.sails.config.sw_season.current);
    expect(DBMember.membership_status).to.be.equal(apiMember.eligibility_status);
    expect(DBMember.first).to.be.equal(apiMember.first_name.trim());
    expect(DBMember.last).to.be.equal(apiMember.last_name.trim());
    expect(DBMember.gender).to.be.equal(apiMember.gender);
    expect(Number(DBMember.organization_code)).to.be.equal(Number(apiMember.membership_number));
    expect(moment(DBMember.birthdate).format('YYYY-MM-DD'))
        .to.be.equal(moment(apiMember.date_of_birth).format('YYYY-MM-DD'));
}

function createMasterAthletes (athletes) {
    if(Array.isArray(athletes)) {
        athletes = athletes.map(a => {
            a.season = global.sails.config.sw_season.current;
            return a;
        });
    } else {
        athletes.season = global.sails.config.sw_season.current;
    }

    return Db.query(knex('master_athlete').insert(athletes));
}

function createMasterStaffers (staffers) {
    if(Array.isArray(staffers)) {
        staffers = staffers.map(s => {
            s.season = global.sails.config.sw_season.current;
            return s;
        });
    } else {
        staffers.season = global.sails.config.sw_season.current;
    }

    return Db.query(knex('master_staff').insert(staffers));
}

function createMasterClub () {
    let query = knex('master_club').insert(masterClubRow).returning(['master_club_id']);

    return Db.query(query).then(result => result.rows && result.rows[0] && result.rows[0].master_club_id);
}

async function createSportEngineQueueRow (mode) {
    let data;

    if(mode === 'insert') {
        data = sportEngineQueueInsertModeRow;
    } else {
        data = sportEngineQueueDefaultModeRow;
    }

    await Db.query(knex('sportengine_queue').insert(data));
    await Db.query(
        knex('master_club')
            .update({sportengine_club_id: sportEngineDirectorResponse.result[0].affiliation_org_id})
            .where('master_club_id', data[0].master_club_id)
    );
}

function getSEClubImportQueueItem (masterClubID) {
    let query = knex('sportengine_queue AS sq')
        .select('sq.option', 'mc.sportengine_club_id', 'sq.sportengine_queue_id')
        .join('master_club AS mc', 'mc.master_club_id', 'sq.master_club_id')
        .where('sq.master_club_id', masterClubID);

    return Db.query(query).then(result => result.rows);
}

function clearMasterClub () {
    return clearTable('master_club');
}

function clearSEImportQueue () {
    return clearTable('sportengine_queue');
}

function clearMasterAthletes () {
    return clearTable('master_athlete');
}

function clearMasterStaff () {
    return clearTable('master_staff');
}

function clearTable (tableName) {
    return Db.query(knex(tableName).truncate());
}

function getSERequestTokenNock () {
    let { oauth_url } = global.sails.config.sportsEngine;

    return nock(oauth_url)
        .post('', 'grant_type=client_credentials')
        .delay(100)
        .reply(200, sportEngineTokenResponseFile)
}

function nockSEDataRequest (params, file) {
    let {
        credential_definition_id, org_id, api_base_url
    } = global.sails.config.sportsEngine;

    const queryParams = {
        per_page: SportsEngineService.RESULTS_PER_PAGE,
        page: 1,
        ...params,
    };

    return nock(api_base_url)
        .get(`/eligibility/${credential_definition_id}/${org_id}`)
        .query(queryParams)
        .delay(100)
        .reply(200, file);
}

function getSERequestClubDataNock () {
    let {
        affiliation_org_id
    } = sportEngineDirectorResponse.result[0];

    return nockSEDataRequest({ affiliation_org_id }, sportEngineClubResponseFile);
}

function getSERequestDirectorDataNock ({ withEmptyResponse = false } = {}) {
    let {
        director_usav_code: membership_number,
        director_birthdate: date_of_birth
    } = masterClubRow[0];

    let file = sportEngineDirectorResponseFile;

    if(withEmptyResponse) {
        file = sportEngineEmptyResponseFile;
    }

    return nockSEDataRequest({ membership_number, date_of_birth }, file);
}

async function getImportData () {
    let [athletes, staffers, queue] = await Promise.all([
        getMasterAthletes(),
        getMasterStaffers(),
        getQueueAfterImportProcessed()
    ]);

    return { athletes, staffers, queue };
}

function getClubRows (table) {
    return Db.query(
        knex(table)
            .select()
            .where('master_club_id', masterClubRow[0].master_club_id)
            .whereNull('deleted')
    ).then(result => result && result.rows);
}

function getMasterAthletes () {
    return getClubRows('master_athlete');
}

function getMasterStaffers () {
    return getClubRows('master_staff');
}

function getQueueAfterImportProcessed () {
    let query = knex('sportengine_queue AS sq')
        .select()
        .where('sq.master_club_id', masterClubRow[0].master_club_id);

    return Db.query(query).then(result => result && result.rows);
}

function getSEClubResponseMember(ageGroup) {
    return sportEngineClubResponse.result.filter(member => {
        return member.tags.some(tag => tag.tag_option_type === 'Age Group' && tag.tag_value.value === ageGroup);
    })
}

function getSEClubResponseAthletes () {
    return getSEClubResponseMember('Junior');
}

function getSEClubResponseStaffers () {
    return getSEClubResponseMember('Adult');
}

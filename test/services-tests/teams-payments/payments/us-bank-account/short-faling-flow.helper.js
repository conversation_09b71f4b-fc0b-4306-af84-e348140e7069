
const fixturesPath = '../../../fixture/teams-payments/payments/USBankAccountPayments/';
const rosterTeamData = require(fixturesPath + 'db_data/roster_team.row.json');

const FIXTURES = {
    PROCESSING: {
        WEBHOOK: require(fixturesPath + 'stripe_data/short-failing-flow/processing.webhook.json'),
        EXPANDED_CHARGE: require(fixturesPath + 'stripe_data/short-failing-flow/processing.expanded.charge.json'),
    },
    FAILED: {
        WEBHOOK: require(fixturesPath + 'stripe_data/short-failing-flow/failed.webhook.json'),
        EXPANDED_CHARGE: require(fixturesPath + 'stripe_data/short-failing-flow/failed.expanded.charge.json'),
    },
    PAYMENT_METHOD: require(fixturesPath + 'stripe_data/short-failing-flow/payment_method.json'),
};

const EXPECTED_VALUES = {
    purchase: {
        amount: 10777,
        status: 'canceled',
        stripe_charge_id: FIXTURES.FAILED.EXPANDED_CHARGE.id,
        type: 'ach',
        email: FIXTURES.FAILED.EXPANDED_CHARGE.billing_details.email,
        phone: FIXTURES.FAILED.EXPANDED_CHARGE.metadata.phone,
        payment_for: 'teams',
        first: null,
        last: null,
        additional_fee_amount: 0,
        net_profit: 10668.51,
        stripe_fee: 103.49,
        collected_sw_fee: 5,
        stripe_percent: 0.8,
        stripe_account_id: FIXTURES.FAILED.EXPANDED_CHARGE.destination,
        payment_intent_id: FIXTURES.FAILED.WEBHOOK.data.object.id,
    },
    purchase_team: {
        amount: 10777,
        surcharge: 0,
        event_fee: 10777,
        sw_fee: 5,
    },
    roster_team: {
        status_paid: 21,
    },
    stripe_charge: {
        amount: 10777,
        fee: 5,
        collected_fee: 0,
        stripe_payment_id: 'py_1Kw0MkLDYnTA1fGW76wjaU3t',
        stripe_account_id: FIXTURES.FAILED.EXPANDED_CHARGE.destination,
    },
    stripe_payment_intent: {
        payment_intent_status: 'requires_payment_method',
        stripe_charge_id: FIXTURES.FAILED.EXPANDED_CHARGE.id,
        amount: 10777,
        stripe_fee: 108.49,
        stripe_percent: 0.8,
    }
};

const SEARCH_RESULTS_VALUES = {
    stripe_payment_intent: FIXTURES.FAILED.WEBHOOK.data.object.id,
        stripe_charge: FIXTURES.FAILED.EXPANDED_CHARGE.id,
        roster_team: rosterTeamData.roster_team_id,
};

const PAYMENT_SESSION = require(fixturesPath + 'session_data/short-success-flow.session.json');

module.exports = {
    FIXTURES,
    EXPECTED_VALUES,
    SEARCH_RESULTS_VALUES,
    PAYMENT_SESSION
}

const fixturePath =
    '../../../fixture/teams-payments/payments/change-teams-payment-type/';

const EXPECTED_VALUES = {
    roster_team: {
        status_paid: 21,
    },
    purchase: {
        amount: 1300,
        status: 'canceled',
        stripe_charge_id: 'py_3M6rBoBXhnF9ioPp10IFDL6l',
        type: 'ach',
        additional_fee_amount: 0,
        net_profit: 1280.5,
        stripe_fee: 19.5,
        collected_sw_fee: 0,
        stripe_percent: 1.5,
        payment_intent_id: 'pi_3M6rBoBXhnF9ioPp17m4yxbN',
    },
    purchase_team: {
        amount: 1100,
        surcharge: 200,
        event_fee: 1100,
        sw_fee: 700,
    },
};

const SESSION_DATA = require(fixturePath + 'session/ach-failed');

const SEARCH_RESULTS_VALUES = {
    roster_team: SESSION_DATA.data.payment.receipt[0],
    purchase: SESSION_DATA.data.payment.purchase_id,
};

const stripeWebhookData = require(fixturePath + 'stripe/ach-failed');

const STRIPE_DATA = {
    PAYMENT_INTENT_PROCESSING: stripeWebhookData['paymentIntent.processing'],
    PAYMENT_INTENT_FAILED: stripeWebhookData['paymentIntent.succeeded'],
};

module.exports = {
    SESSION_DATA,
    EXPECTED_VALUES,
    STRIPE_DATA,
    SEARCH_RESULTS_VALUES,
};

'use strict';

const { expect } = require('chai');
const sinon = require('sinon');

describe('JustifiService', function () {
    let service;
    const mockAccessToken = 'mock_access_token';
    const mockApiUrl = 'https://api.justifi.ai';
    const mockClientId = 'client_id';
    const mockClientSecret = 'client_secret';

    before(function () {
        service = global.sails.services.justifiservice;

        this.originalConfig = { ...global.sails.config.justifi };

        global.sails.config.justifi = {
            apiUrl: mockApiUrl,
            clientId: mockClientId,
            clientSecret: mockClientSecret
        };

        if (!global.fetch) {
            global.fetch = function() {};
        }

        this.fetchStub = sinon.stub(global, 'fetch');
    });

    after(function () {
        global.sails.config.justifi = this.originalConfig;

        if (this.fetchStub) {
            this.fetchStub.restore();
        }


    });

    afterEach(function () {
        service._accessToken = null;
        service._expiresAt = 0;

        if (this.fetchStub) {
            this.fetchStub.reset();
        }
    });

    context('getAccessToken()', function () {
        it('should fetch and return access token', async function () {
            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };
            this.fetchStub.resolves(mockResponse);

            const token = await service.getAccessToken();

            expect(token).to.equal(mockAccessToken);
            expect(service._accessToken).to.equal(mockAccessToken);
            expect(service._expiresAt).to.be.above(Date.now());
            expect(this.fetchStub.calledOnce).to.be.true;

            const [url, options] = this.fetchStub.firstCall.args;
            expect(url).to.include('/oauth/token');
            expect(options.method).to.equal('POST');
            expect(options.headers['Content-Type']).to.equal('application/json');

            const body = JSON.parse(options.body);
            expect(body.client_id).to.exist;
            expect(body.client_secret).to.exist;
        });

        it('should reuse cached token if not expired', async function () {
            service._accessToken = mockAccessToken;
            service._expiresAt = Date.now() + 1000000;

            const token = await service.getAccessToken();

            expect(token).to.equal(mockAccessToken);
            expect(this.fetchStub.called).to.be.false;
        });

        it('should refresh token if forceRefresh is true', async function () {
            service._accessToken = 'old_token';
            service._expiresAt = Date.now() + 1000000; // Far in the future

            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };
            this.fetchStub.resolves(mockResponse);

            const token = await service.getAccessToken(true);

            expect(token).to.equal(mockAccessToken);
            expect(service._accessToken).to.equal(mockAccessToken);
            expect(this.fetchStub.calledOnce).to.be.true;
        });

        it('should throw error if auth request fails', async function () {
            const mockResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };
            this.fetchStub.resolves(mockResponse);

            await expect(service.getAccessToken()).to.be.rejectedWith(/Justifi auth failed/);
        });

        it('should throw error if access_token is missing in response', async function () {
            const mockResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ something_else: 'value' })
            };
            this.fetchStub.resolves(mockResponse);

            await expect(service.getAccessToken()).to.be.rejectedWith(/empty access_token/);
        });
    });

    context('_makeRequest()', function () {
        it('should make successful request with token', async function () {
            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success' })
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            const result = await service._makeRequest('/some/path');

            expect(result).to.deep.equal({ data: 'success' });
            expect(this.fetchStub.calledTwice).to.be.true;

            const [authUrl, authOptions] = this.fetchStub.firstCall.args;
            expect(authUrl).to.equal(`${mockApiUrl}/oauth/token`);
            expect(authOptions.method).to.equal('POST');
            expect(authOptions.headers['Content-Type']).to.equal('application/json');

            const [apiUrl, apiOptions] = this.fetchStub.secondCall.args;
            expect(apiUrl).to.equal(`${mockApiUrl}/some/path`);
            expect(apiOptions.method).to.equal('GET');
            expect(apiOptions.headers.Authorization).to.equal(`Bearer ${mockAccessToken}`);
        });

        it('should refresh token and retry on 401 error', async function () {
            const firstAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'first_token' })
            };

            const firstApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };

            const secondAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'refreshed_token' })
            };

            const secondApiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success after refresh' })
            };

            this.fetchStub.onCall(0).resolves(firstAuthResponse);
            this.fetchStub.onCall(1).resolves(firstApiResponse);
            this.fetchStub.onCall(2).resolves(secondAuthResponse);
            this.fetchStub.onCall(3).resolves(secondApiResponse);

            const result = await service._makeRequest('/some/path');

            expect(result).to.deep.equal({ data: 'success after refresh' });
            expect(service._accessToken).to.equal('refreshed_token');
            expect(this.fetchStub.callCount).to.equal(4);
        });

        it('should throw error if request fails after token refresh', async function () {
            const firstAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'first_token' })
            };

            const firstApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                text: sinon.stub().resolves('Unauthorized')
            };

            const secondAuthResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: 'refreshed_token' })
            };

            const secondApiResponse = {
                ok: false,
                status: 401,
                statusText: 'Still unauthorized',
                text: sinon.stub().resolves('Still unauthorized')
            };

            this.fetchStub.onCall(0).resolves(firstAuthResponse);
            this.fetchStub.onCall(1).resolves(firstApiResponse);
            this.fetchStub.onCall(2).resolves(secondAuthResponse);
            this.fetchStub.onCall(3).resolves(secondApiResponse);

            await expect(service._makeRequest('/some/path')).to.be.rejectedWith(
                'Unauthorized after token refresh: Still unauthorized'
            );
        });

        it('should throw error for non-401 failures', async function () {
            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: false,
                status: 500,
                statusText: 'Server error',
                text: sinon.stub().resolves('Server error')
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            await expect(service._makeRequest('/some/path')).to.be.rejectedWith('Server error');
        });

        it('should send body for non-GET requests', async function () {
            const requestBody = { key: 'value' };

            const authResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ access_token: mockAccessToken })
            };

            const apiResponse = {
                ok: true,
                status: 200,
                json: sinon.stub().resolves({ data: 'success' })
            };

            this.fetchStub.onFirstCall().resolves(authResponse);
            this.fetchStub.onSecondCall().resolves(apiResponse);

            const result = await service._makeRequest('/some/path', 'POST', requestBody);

            expect(result).to.deep.equal({ data: 'success' });

            const [, apiOptions] = this.fetchStub.secondCall.args;
            expect(apiOptions.method).to.equal('POST');
            expect(JSON.parse(apiOptions.body)).to.deep.equal(requestBody);
        });
    });

    context('createBusiness()', function () {
        it('should create a business and return businessId', async function () {
            const mockResponse = {
                data: { id: 'bus_123456789' }
            };

            const makeRequestStub = sinon.stub(service, '_makeRequest').resolves(mockResponse);

            const result = await service.createBusiness({
                email: '<EMAIL>',
                legalName: 'Test Business'
            });

            expect(result).to.deep.equal({ businessId: 'bus_123456789' });
            expect(makeRequestStub.calledOnce).to.be.true;

            const [path, method, body] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/entities/business');
            expect(method).to.equal('POST');
            expect(body).to.deep.equal({
                email: '<EMAIL>',
                legal_name: 'Test Business'
            });

            makeRequestStub.restore();
        });
    });

    context('createWebTokenForBusiness()', function () {
        it('should create a web token for business', async function () {
            const mockResponse = {
                access_token: 'web_token_123456789'
            };

            const makeRequestStub = sinon.stub(service, '_makeRequest').resolves(mockResponse);

            const result = await service.createWebTokenForBusiness({
                businessId: 'bus_123456789'
            });

            expect(result).to.equal('web_token_123456789');
            expect(makeRequestStub.calledOnce).to.be.true;

            const [path, method, body] = makeRequestStub.firstCall.args;
            expect(path).to.equal('/v1/web_component_tokens');
            expect(method).to.equal('POST');
            expect(body).to.deep.equal({
                resources: ['write:business:bus_123456789']
            });

            makeRequestStub.restore();
        });

        it('should throw error if web token creation fails', async function () {
            const makeRequestStub = sinon.stub(service, '_makeRequest').rejects(new Error('Failed to create web token'));

            await expect(service.createWebTokenForBusiness({
                businessId: 'bus_123456789'
            })).to.be.rejectedWith('Failed to create web token');

            makeRequestStub.restore();
        });
    });
});

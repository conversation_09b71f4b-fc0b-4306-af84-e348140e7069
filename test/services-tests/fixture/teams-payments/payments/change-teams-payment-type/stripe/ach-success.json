{"paymentIntent.processing": {"id": "evt_3M6rGuBXhnF9ioPp0VY4zWoj", "object": "event", "api_version": "2019-09-09", "created": 1669103543, "data": {"object": {"id": "pi_3M6rGuBXhnF9ioPp0inRbXnt", "object": "payment_intent", "amount": 130000, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 0, "application": null, "application_fee_amount": 1950, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "py_3M6rGuBXhnF9ioPp08Az9IcC", "object": "charge", "amount": 130000, "amount_captured": 130000, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": 1950, "balance_transaction": null, "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON><PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": 1669103543, "currency": "usd", "customer": "cus_Mhxh3xgGannULm", "description": null, "destination": "acct_1BTtqQJyPh92VEwc", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"total": "$1280.50", "sw_fee": "$0.00", "phone": "**********", "stripe_fee": "$19.50", "event_name": "TH Coupon 2.0 function", "email": "<EMAIL>", "event_id": "22033", "additional_fee": "$0.00", "project": "sw"}, "on_behalf_of": "acct_1BTtqQJyPh92VEwc", "order": null, "outcome": null, "paid": false, "payment_intent": "pi_3M6rGuBXhnF9ioPp0inRbXnt", "payment_method": "pm_1M6rHGBXhnF9ioPpRJpMHDXJ", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "hJtXanQIKIwyiRum", "last4": "6789", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3M6rGuBXhnF9ioPp08Az9IcC/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "FarNorthQualVB", "statement_descriptor_suffix": null, "status": "pending", "transfer_data": {"amount": null, "destination": "acct_1BTtqQJyPh92VEwc"}, "transfer_group": "group_py_3M6rGuBXhnF9ioPp08Az9IcC"}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_3M6rGuBXhnF9ioPp0inRbXnt"}, "client_secret": "pi_3M6rGuBXhnF9ioPp0inRbXnt_secret_5tri84pFtKLOZjLFMmEbnVVAj", "confirmation_method": "automatic", "created": **********, "currency": "usd", "customer": "cus_Mhxh3xgGannULm", "description": null, "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {"total": "$1280.50", "sw_fee": "$0.00", "phone": "**********", "stripe_fee": "$19.50", "event_name": "TH Coupon 2.0 function", "email": "<EMAIL>", "event_id": "22033", "additional_fee": "$0.00", "project": "sw"}, "next_action": null, "on_behalf_of": "acct_1BTtqQJyPh92VEwc", "payment_method": "pm_1M6rHGBXhnF9ioPpRJpMHDXJ", "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}, "us_bank_account": {"verification_method": "instant"}}, "payment_method_types": ["card", "us_bank_account"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": "off_session", "shipping": null, "source": null, "statement_descriptor": "FarNorthQualVB", "statement_descriptor_suffix": null, "status": "processing", "transfer_data": {"destination": "acct_1BTtqQJyPh92VEwc"}, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_VJfeuZbwlrtQQf", "idempotency_key": "5bdcfcf8-311e-4dac-9965-418dab46c72c"}, "type": "payment_intent.processing"}, "paymentIntent.succeeded": {"id": "evt_3M6rGuBXhnF9ioPp0O9UT1Jg", "object": "event", "api_version": "2019-09-09", "created": **********, "data": {"object": {"id": "pi_3M6rGuBXhnF9ioPp0inRbXnt", "object": "payment_intent", "amount": 130000, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 130000, "application": null, "application_fee_amount": 1950, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "py_3M6rGuBXhnF9ioPp08Az9IcC", "object": "charge", "amount": 130000, "amount_captured": 130000, "amount_refunded": 0, "application": null, "application_fee": "fee_1M6rHKJyPh92VEwcgF3vH1ZP", "application_fee_amount": 1950, "balance_transaction": "txn_3M6rGuBXhnF9ioPp0f8gD6fE", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON><PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": 1669103543, "currency": "usd", "customer": "cus_Mhxh3xgGannULm", "description": null, "destination": "acct_1BTtqQJyPh92VEwc", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"total": "$1280.50", "sw_fee": "$0.00", "phone": "**********", "stripe_fee": "$19.50", "event_name": "TH Coupon 2.0 function", "email": "<EMAIL>", "event_id": "22033", "additional_fee": "$0.00", "project": "sw"}, "on_behalf_of": "acct_1BTtqQJyPh92VEwc", "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "not_assessed", "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_3M6rGuBXhnF9ioPp0inRbXnt", "payment_method": "pm_1M6rHGBXhnF9ioPpRJpMHDXJ", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "hJtXanQIKIwyiRum", "last4": "6789", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xRk1aMDdCWGhuRjlpb1BwKPz_8ZsGMgaRsVvI-7k6LBadnKqvr02N6QQAMfQ3_85ohxPUZazO1_1C6K_5npLJeCxxa-xSv6AT_UVO", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3M6rGuBXhnF9ioPp08Az9IcC/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "FarNorthQualVB", "statement_descriptor_suffix": null, "status": "succeeded", "transfer": "tr_3M6rGuBXhnF9ioPp0ikF2PK7", "transfer_data": {"amount": null, "destination": "acct_1BTtqQJyPh92VEwc"}, "transfer_group": null}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_3M6rGuBXhnF9ioPp0inRbXnt"}, "client_secret": "pi_3M6rGuBXhnF9ioPp0inRbXnt_secret_5tri84pFtKLOZjLFMmEbnVVAj", "confirmation_method": "automatic", "created": **********, "currency": "usd", "customer": "cus_Mhxh3xgGannULm", "description": null, "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {"total": "$1280.50", "sw_fee": "$0.00", "phone": "**********", "stripe_fee": "$19.50", "event_name": "TH Coupon 2.0 function", "email": "<EMAIL>", "event_id": "22033", "additional_fee": "$0.00", "project": "sw", "purchase_id": "********", "link_to_purchase": "https://my.dev.sportwrench.com/#/event/22033/payments?purchase_id=********"}, "next_action": null, "on_behalf_of": "acct_1BTtqQJyPh92VEwc", "payment_method": "pm_1M6rHGBXhnF9ioPpRJpMHDXJ", "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}, "us_bank_account": {"verification_method": "instant"}}, "payment_method_types": ["card", "us_bank_account"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": "off_session", "shipping": null, "source": null, "statement_descriptor": "FarNorthQualVB", "statement_descriptor_suffix": null, "status": "succeeded", "transfer_data": {"destination": "acct_1BTtqQJyPh92VEwc"}, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": null, "idempotency_key": null}, "type": "payment_intent.succeeded"}}
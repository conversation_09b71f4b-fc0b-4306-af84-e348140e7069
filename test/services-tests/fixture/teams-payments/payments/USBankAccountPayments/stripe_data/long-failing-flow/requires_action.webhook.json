{"id": "evt_3KyJDgBXhnF9ioPp0mLWpRys", "object": "event", "api_version": "2019-09-09", "created": 1652289867, "data": {"object": {"id": "pi_3KyJDgBXhnF9ioPp0Wgw4SyD", "object": "payment_intent", "amount": 1077700, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 0, "application": null, "application_fee_amount": 10849, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges?payment_intent=pi_3KyJDgBXhnF9ioPp0Wgw4SyD"}, "client_secret": "pi_3KyJDgBXhnF9ioPp0Wgw4SyD_secret_nkmuHZlAUgDkKdqCHW0sUsVu5", "confirmation_method": "automatic", "created": 1652289664, "currency": "usd", "customer": null, "description": null, "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {"total": "$10668.51", "stripe_fee": "$103.49", "phone": "0000000000", "sw_fee": "$5.00", "event_name": "ACH testing", "event_id": "22012", "email": "<EMAIL>", "additional_fee": "$0.00", "project": "sw"}, "next_action": {"type": "verify_with_microdeposits", "verify_with_microdeposits": {"arrival_date": **********, "hosted_verification_url": "https://payments.stripe.com/microdeposit/pacs_test_YWNjdF8xRk1aMDdCWGhuRjlpb1BwLHBhX25vbmNlX0xmZWFGY085ZlA2UW9PanRtM2dwcWExOGw2VmJuaGg00009Y8tmCu7", "microdeposit_type": "descriptor_code"}}, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "payment_method": "pm_1KyJGxBXhnF9ioPpvLLvrxT5", "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}, "us_bank_account": {"verification_method": "automatic"}}, "payment_method_types": ["card", "us_bank_account"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": "off_session", "shipping": null, "source": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "requires_action", "transfer_data": {"destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_20t2Mn37znZ1QF", "idempotency_key": "2a1e636f-0a11-43da-81e7-052a085db106"}, "type": "payment_intent.requires_action"}
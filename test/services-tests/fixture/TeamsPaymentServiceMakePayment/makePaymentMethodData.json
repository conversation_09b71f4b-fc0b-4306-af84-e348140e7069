{"cd_check": {"event_id": 170070, "receipt": [116240, 116190], "type": "check", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "club_owner_id": 401}, "cd_ach": {"event_id": 170070, "receipt": [116240, 116190], "type": "ach", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "club_owner_id": 401}, "cd_card": {"event_id": 170070, "receipt": [116240, 116190], "type": "card", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "club_owner_id": 401}, "eo_check": {"event_id": 170070, "receipt": [116240, 116190], "type": "check", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "roster_club_id": 2351, "event_owner_id": 3, "discount": {}}, "eo_check_discount": {"event_id": 170070, "receipt": [116240, 116190], "type": "check", "total": 350, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "roster_club_id": 2351, "event_owner_id": 3, "discount": {"116240": 20, "116190": 30}}, "eo_ach": {"event_id": 170070, "receipt": [116240, 116190], "type": "ach", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "roster_club_id": 2351, "event_owner_id": 3, "discount": {}}, "eo_card": {"event_id": 170070, "receipt": [116240, 116190], "type": "card", "total": 400, "user": {"user_id": 3564, "email": "<EMAIL>", "phone": "", "first": "<PERSON><PERSON><PERSON>", "last": "<PERSON>"}, "ip": "::1", "token": "test_token", "season": 2018, "master_club_id": 3000, "roster_club_id": 2351, "event_owner_id": 3, "discount": {}}}
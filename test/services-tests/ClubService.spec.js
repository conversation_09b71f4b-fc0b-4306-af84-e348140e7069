'use strict';

describe('ClubService', function () {

	let ClubService;

	before(() => {
		ClubService = sails.services.clubservice;
	})

	context('_formatSports()', function () {

		it('should return empty empty array for empty list passed', () => {
			let res = ClubService._formatSports(1, [], 'some_field');

			expect(res).to.be.a('array');
			expect(res).to.be.empty;
		})

		it('should format input array', () => {
			let masterClubID 	= 1;
			let inputList 		= ['first_item', 'second_item'];

			let res = ClubService._formatSports(masterClubID, inputList, 'some_field');

			expect(res).to.be.a('array');
			expect(res.length).to.equal(2);
			expect(res).to.eql([
				{ master_club_id: 1, some_field: 'first_item' },
				{ master_club_id: 1, some_field: 'second_item' }
			])
		})

	})

})
'use strict';

let squel = require('squel').useFlavour('postgres');

let _select = squel.select,
    _insert = squel.insert,
    _update = squel.update,
    _delete = squel.delete,
    _remove = squel.remove

const defaults = {
    tableAliasQuoteCharacter    : '"',
    autoQuoteTableNames         : true,
    nameQuote<PERSON><PERSON>cter          : '"',
    replaceSingleQuotes         : true, 
    singleQuoteReplacement      : "''",

    // https://github.com/hiddentao/squel/issues/373
    numberedParameters          : false,
};

// https://github.com/hiddentao/squel/issues/373
const _oldToParamString = squel.cls.QueryBuilder.prototype._toParamString;
squel.cls.QueryBuilder.prototype._toParamString = _toParamString;


squel.select = function(opts) {
    return _select(_.assign(_.clone(defaults), opts));
};
squel.insert = function(opts) {
    return _insert(_.assign(_.clone(defaults), opts));
};
squel.update = function(opts) {
    return _update(_.assign(_.clone(defaults), opts));
};
squel.delete = function(opts) {
    return _delete(_.assign(_.clone(defaults), opts));
};
squel.remove = function(opts) {
    return _remove(_.assign(_.clone(defaults), opts));
};

module.exports = squel;

function _toParamString (options = {}) {
    const _options = { ...this.options, ...options };

    let result = _oldToParamString.bind(this)(options);

    if (!_options.nested) {
        // If default behaviour is disabled, run updated code
        if(!_options.numberedParameters) {
            let i = (undefined !== options.numberedParametersStartAt)
                ? options.numberedParametersStartAt
                : 1;

            result.text = result.text
                .split(_options.parameterCharacter)
                .reduce(
                    (result, part, index) => {
                        if (index > 0) {
                            // all quotes are closed
                            if (result.quotes % 2 === 0) {
                                result.parts.push(`${_options.numberedParametersPrefix}${i++}`);
                            }
                            else {
                                result.parts.push(_options.parameterCharacter);
                            }
                        }

                        result.parts.push(part);
                        result.quotes += part.split(`'`).length - 1;

                        return result;
                    },
                    {parts: [], quotes: 0}
                )
                .parts
                .join('');
        }
    }

    return result;
}

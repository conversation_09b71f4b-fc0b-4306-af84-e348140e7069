'use strict';

const request = require('request-promise');
const cookie = require('cookie');

class UserSignin {
    constructor(userId, eventOwnerId, email) {
        this._userId = userId;
        this._eventOwnerId = eventOwnerId;
        this._email = email;
    }

    get userId() {
        return this._userId;
    }

    /**
     * @param {Object} user
     * @returns {Promise<UserSignin>}
     */
    static async create(user) {
        const {email} = user;
        const userId = await this.__addUser(user);
        const eventOwnerId = await this.__addEventOwner(userId);

        return new UserSignin(userId, eventOwnerId, email);
    }

    async assignToEvent(eventID) {
        const query = knex('event_user')
            .insert({
                user_id: this._userId,
                event_owner_id: this._eventOwnerId,
                event_id: eventID,
                role_co_owner: true,
            });

        await Db.query(query);
    }

    static __addEventOwner(userID) {
        const query = knex('event_owner')
            .insert({
                user_id: userID,
                approved: true,
            })
            .returning('event_owner_id');

        return Db.query(query).then(result => result.rows[0] && result.rows[0].event_owner_id)
    }

    static __addUser(user) {
        const query = knex('user')
            .insert(user)
            .returning('user_id');

        return Db.query(query)
            .then(
                result => result.rows[0] && result.rows[0].user_id
            )
    }

    async signIn() {
        const resp = await request({
            method: 'POST',
            uri: `http://${HOST}/api/signin`,
            body: {
                email: this._email,
                password: 'QxXniI*GQ'
            },
            json: true,
            resolveWithFullResponse: true,
            headers: {
                'content-type': 'application/json',
                'User-Agent': 'testing'
            }
        }).promise();
        let parsedCookie = cookie.parse(resp.headers['set-cookie'][0]);

        return request.cookie(cookie.serialize('sw.sid', parsedCookie['sw.sid']));
    }

    async del() {
        await this._deleteEventUsers();
        await this._deleteEventOwner();
        await this._deleteUser();
        this._eventOwnerId = null;
        this._userId = null;
        this._email = null;
    }

    async _deleteEventUsers() {
        const query = knex('event_user').truncate();
        await Db.query(query);
    }

    async _deleteEventOwner() {
        const query = knex('event_owner').truncate();
        await Db.query(query);
    }

    async _deleteUser() {
        const query = knex('user').truncate();
        await Db.query(query);
    }
}

module.exports = UserSignin;

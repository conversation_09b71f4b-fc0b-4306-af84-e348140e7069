/**
 * [
 *   [ [primaryTable, primaryKey], [foreignTable, foreignKey] ],
 *   ...
 * ]
 * @type {string[][][]}
 */
const foreignConstraints = [
    [['purchase', 'purchase_id'], ['purchase', 'linked_purchase_id']],
    [['purchase', 'purchase_id'], ['purchase_ticket', 'purchase_id']],
    [['ticket_discount', 'ticket_discount_id'], ['purchase_ticket', 'ticket_discount_id']],
    [['event_ticket', 'event_ticket_id'], ['purchase_ticket', 'event_ticket_id']],
];

const byPrimary = _.groupBy(foreignConstraints, fc => fc[0][0]);
const byForeign = _.groupBy(foreignConstraints, fc => fc[1][0]);

module.exports = {
    list: foreignConstraints,
    byPrimary(table) {
        return byPrimary[table]||[];
    },
    byForeign(table) {
        return byForeign[table]||[];
    },
};

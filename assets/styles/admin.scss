.progress-bar-empty {
    background: #aaa;
}

.test-item {
    min-height: 150px;
}

.navbar .navbar-nav>li>span.admin-dropdown {
    padding-top: 22px;
    display: block;
    line-height: 20px;    
    padding-left: 15px;
    padding-right: 15px;
}

span.admin-dropdown>a {
    color: #777 !important;
    text-decoration: none;
}

span.admin-dropdown>a:hover {
    color: #333 !important;
    background-color: transparent;
    text-decoration: none;
}

.collapse-sm {
    margin-bottom: 5px !important;
}

.collapse-sm .panel {
    margin-bottom: 5px !important;
}

.collapse-sm .panel-default {
    border-bottom: 0px;
}

.collapse-sm .panel-heading {
    padding: 3px 3px !important;
    font-size: 12px !important;
}

.collapse-sm .panel-heading a {
    color: #5e5e5e;
}

.collapse-sm .panel-heading a:hover {
    cursor: pointer;
}

.collapse-sm .panel-heading .panel-title {
    line-height: 1 !important;
}

pre.payments-json {
    font-size: 10px;
    line-height: 1.2 !important;
    padding: 4px !important;
    border: 0 !important;

}

.white-ro[readonly] {
    background-color: #fff
}

.pointer {
  cursor: pointer !important;
}


ul.dropdown-menu[datepicker-popup-wrap] .btn {
    border: 0 !important;
    padding: 3px 8px !important;
}

ul.dropdown-menu[datepicker-popup-wrap] .btn:focus {
    outline: none;
}

.sw-statistics tbody td, .sw-statistics thead tr:nth-child(2) th {
    text-align: right;
}

.sw-statistics .not-right {
    text-align: left;
}

.sw-statistics .col-event-name {
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.users-list-search-box {
    margin-bottom: 15px;
}

.bold-text {
    font-weight: bold;
}

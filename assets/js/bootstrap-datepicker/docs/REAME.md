Documentation
=============

Project documentation is built using [Sphinx docs](http://sphinx-doc.org/), which uses [ReST](http://docutils.sf.net/rst.html) for markup.  This allows the docs to cover a vast amount of topics without using a thousand-line README file.

Sphinx docs is pip-installable via `pip install sphinx`.  Once installed, open a command line in the docs folder and run `make html`; the output files will be placed in the `_build/html/` directory, and can be browsed (locally) with any browser.

The docs can also be found online at http://bootstrap-datepicker.readthedocs.org/.

/*
 * angular-ui-bootstrap
 * http://angular-ui.github.io/bootstrap/

 * Version: 1.3.3 - 2016-05-22
 * License: MIT
 */angular.module("ui.bootstrap",["ui.bootstrap.tpls","ui.bootstrap.collapse","ui.bootstrap.accordion","ui.bootstrap.alert","ui.bootstrap.modal","ui.bootstrap.stackedMap","ui.bootstrap.position","ui.bootstrap.tabs","ui.bootstrap.popover","ui.bootstrap.tooltip","ui.bootstrap.timepicker","ui.bootstrap.typeahead","ui.bootstrap.debounce","ui.bootstrap.progressbar","ui.bootstrap.dropdown","ui.bootstrap.datepickerPopup","ui.bootstrap.datepicker","ui.bootstrap.dateparser","ui.bootstrap.isClass"]),angular.module("ui.bootstrap.tpls",["uib/template/accordion/accordion-group.html","uib/template/accordion/accordion.html","uib/template/alert/alert.html","uib/template/modal/backdrop.html","uib/template/modal/window.html","uib/template/tabs/tab.html","uib/template/tabs/tabset.html","uib/template/popover/popover-html.html","uib/template/popover/popover-template.html","uib/template/popover/popover.html","uib/template/tooltip/tooltip-html-popup.html","uib/template/tooltip/tooltip-popup.html","uib/template/tooltip/tooltip-template-popup.html","uib/template/timepicker/timepicker.html","uib/template/typeahead/typeahead-match.html","uib/template/typeahead/typeahead-popup.html","uib/template/progressbar/bar.html","uib/template/progressbar/progress.html","uib/template/progressbar/progressbar.html","uib/template/datepickerPopup/popup.html","uib/template/datepicker/datepicker.html","uib/template/datepicker/day.html","uib/template/datepicker/month.html","uib/template/datepicker/year.html"]),angular.module("ui.bootstrap.collapse",[]).directive("uibCollapse",["$animate","$q","$parse","$injector",function(e,t,n,o){var a=o.has("$animateCss")?o.get("$animateCss"):null;return{link:function(o,i,r){function l(){i.hasClass("collapse")&&i.hasClass("in")||t.resolve(c(o)).then(function(){i.removeClass("collapse").addClass("collapsing").attr("aria-expanded",!0).attr("aria-hidden",!1),a?a(i,{addClass:"in",easing:"ease",to:{height:i[0].scrollHeight+"px"}}).start()["finally"](s):e.addClass(i,"in",{to:{height:i[0].scrollHeight+"px"}}).then(s)})}function s(){i.removeClass("collapsing").addClass("collapse").css({height:"auto"}),d(o)}function p(){return i.hasClass("collapse")||i.hasClass("in")?void t.resolve(m(o)).then(function(){i.css({height:i[0].scrollHeight+"px"}).removeClass("collapse").addClass("collapsing").attr("aria-expanded",!1).attr("aria-hidden",!0),a?a(i,{removeClass:"in",to:{height:"0"}}).start()["finally"](u):e.removeClass(i,"in",{to:{height:"0"}}).then(u)}):u()}function u(){i.css({height:"0"}),i.removeClass("collapsing").addClass("collapse"),f(o)}var c=n(r.expanding),d=n(r.expanded),m=n(r.collapsing),f=n(r.collapsed);o.$eval(r.uibCollapse)||i.addClass("in").addClass("collapse").attr("aria-expanded",!0).attr("aria-hidden",!1).css({height:"auto"}),o.$watch(r.uibCollapse,function(e){e?p():l()})}}}]),angular.module("ui.bootstrap.accordion",["ui.bootstrap.collapse"]).constant("uibAccordionConfig",{closeOthers:!0}).controller("UibAccordionController",["$scope","$attrs","uibAccordionConfig",function(e,t,n){this.groups=[],this.closeOthers=function(o){var a=angular.isDefined(t.closeOthers)?e.$eval(t.closeOthers):n.closeOthers;a&&angular.forEach(this.groups,function(e){e!==o&&(e.isOpen=!1)})},this.addGroup=function(e){var t=this;this.groups.push(e),e.$on("$destroy",function(){t.removeGroup(e)})},this.removeGroup=function(e){var t=this.groups.indexOf(e);-1!==t&&this.groups.splice(t,1)}}]).directive("uibAccordion",function(){return{controller:"UibAccordionController",controllerAs:"accordion",transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/accordion/accordion.html"}}}).directive("uibAccordionGroup",function(){return{require:"^uibAccordion",transclude:!0,replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/accordion/accordion-group.html"},scope:{heading:"@",panelClass:"@?",isOpen:"=?",isDisabled:"=?"},controller:function(){this.setHeading=function(e){this.heading=e}},link:function(e,t,n,o){o.addGroup(e),e.openClass=n.openClass||"panel-open",e.panelClass=n.panelClass||"panel-default",e.$watch("isOpen",function(n){t.toggleClass(e.openClass,!!n),n&&o.closeOthers(e)}),e.toggleOpen=function(t){e.isDisabled||t&&32!==t.which||(e.isOpen=!e.isOpen)};var a="accordiongroup-"+e.$id+"-"+Math.floor(1e4*Math.random());e.headingId=a+"-tab",e.panelId=a+"-panel"}}}).directive("uibAccordionHeading",function(){return{transclude:!0,template:"",replace:!0,require:"^uibAccordionGroup",link:function(e,t,n,o,a){o.setHeading(a(e,angular.noop))}}}).directive("uibAccordionTransclude",function(){function e(){return"uib-accordion-header,data-uib-accordion-header,x-uib-accordion-header,uib\\:accordion-header,[uib-accordion-header],[data-uib-accordion-header],[x-uib-accordion-header]"}return{require:"^uibAccordionGroup",link:function(t,n,o,a){t.$watch(function(){return a[o.uibAccordionTransclude]},function(t){if(t){var o=angular.element(n[0].querySelector(e()));o.html(""),o.append(t)}})}}}),angular.module("ui.bootstrap.alert",[]).controller("UibAlertController",["$scope","$attrs","$interpolate","$timeout",function(e,t,n,o){e.closeable=!!t.close;var a=angular.isDefined(t.dismissOnTimeout)?n(t.dismissOnTimeout)(e.$parent):null;a&&o(function(){e.close()},parseInt(a,10))}]).directive("uibAlert",function(){return{controller:"UibAlertController",controllerAs:"alert",templateUrl:function(e,t){return t.templateUrl||"uib/template/alert/alert.html"},transclude:!0,replace:!0,scope:{type:"@",close:"&"}}}),angular.module("ui.bootstrap.modal",["ui.bootstrap.stackedMap","ui.bootstrap.position"]).factory("$$multiMap",function(){return{createNew:function(){var e={};return{entries:function(){return Object.keys(e).map(function(t){return{key:t,value:e[t]}})},get:function(t){return e[t]},hasKey:function(t){return!!e[t]},keys:function(){return Object.keys(e)},put:function(t,n){e[t]||(e[t]=[]),e[t].push(n)},remove:function(t,n){var o=e[t];if(o){var a=o.indexOf(n);-1!==a&&o.splice(a,1),o.length||delete e[t]}}}}}}).provider("$uibResolve",function(){var e=this;this.resolver=null,this.setResolver=function(e){this.resolver=e},this.$get=["$injector","$q",function(t,n){var o=e.resolver?t.get(e.resolver):null;return{resolve:function(e,a,i,r){if(o)return o.resolve(e,a,i,r);var l=[];return angular.forEach(e,function(e){l.push(angular.isFunction(e)||angular.isArray(e)?n.resolve(t.invoke(e)):angular.isString(e)?n.resolve(t.get(e)):n.resolve(e))}),n.all(l).then(function(t){var n={},o=0;return angular.forEach(e,function(e,a){n[a]=t[o++]}),n})}}}]}).directive("uibModalBackdrop",["$animate","$injector","$uibModalStack",function(e,t,n){function o(t,o,a){a.modalInClass&&(e.addClass(o,a.modalInClass),t.$on(n.NOW_CLOSING_EVENT,function(n,i){var r=i();t.modalOptions.animation?e.removeClass(o,a.modalInClass).then(r):r()}))}return{replace:!0,templateUrl:"uib/template/modal/backdrop.html",compile:function(e,t){return e.addClass(t.backdropClass),o}}}]).directive("uibModalWindow",["$uibModalStack","$q","$animateCss","$document",function(e,t,n,o){return{scope:{index:"@"},replace:!0,transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/modal/window.html"},link:function(a,i,r){i.addClass(r.windowClass||""),i.addClass(r.windowTopClass||""),a.size=r.size,a.close=function(t){var n=e.getTop();n&&n.value.backdrop&&"static"!==n.value.backdrop&&t.target===t.currentTarget&&(t.preventDefault(),t.stopPropagation(),e.dismiss(n.key,"backdrop click"))},i.on("click",a.close),a.$isRendered=!0;var l=t.defer();r.$observe("modalRender",function(e){"true"===e&&l.resolve()}),l.promise.then(function(){var l=null;r.modalInClass&&(l=n(i,{addClass:r.modalInClass}).start(),a.$on(e.NOW_CLOSING_EVENT,function(e,t){var o=t();n(i,{removeClass:r.modalInClass}).start().then(o)})),t.when(l).then(function(){var t=e.getTop();if(t&&e.modalRendered(t.key),!o[0].activeElement||!i[0].contains(o[0].activeElement)){var n=i[0].querySelector("[autofocus]");n?n.focus():i[0].focus()}})})}}}]).directive("uibModalAnimationClass",function(){return{compile:function(e,t){t.modalAnimation&&e.addClass(t.uibModalAnimationClass)}}}).directive("uibModalTransclude",function(){return{link:function(e,t,n,o,a){a(e.$parent,function(e){t.empty(),t.append(e)})}}}).factory("$uibModalStack",["$animate","$animateCss","$document","$compile","$rootScope","$q","$$multiMap","$$stackedMap","$uibPosition",function(e,t,n,o,a,i,r,l,s){function p(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}function u(){for(var e=-1,t=$.keys(),n=0;n<t.length;n++)$.get(t[n]).value.backdrop&&(e=n);return e>-1&&M>e&&(e=M),e}function c(e,t){var n=$.get(e).value,o=n.appendTo;$.remove(e),x=$.top(),x&&(M=parseInt(x.value.modalDomEl.attr("index"),10)),f(n.modalDomEl,n.modalScope,function(){var t=n.openedClass||y;k.remove(t,e);var a=k.hasKey(t);o.toggleClass(t,a),!a&&w&&w.heightOverflow&&w.scrollbarWidth&&(o.css(w.originalRight?{paddingRight:w.originalRight+"px"}:{paddingRight:""}),w=null),d(!0)},n.closedDeferred),m(),t&&t.focus?t.focus():o.focus&&o.focus()}function d(e){var t;$.length()>0&&(t=$.top().value,t.modalDomEl.toggleClass(t.windowTopClass||"",e))}function m(){if(b&&-1===u()){var e=v;f(b,v,function(){e=null}),b=void 0,v=void 0}}function f(t,n,o,a){function r(){r.done||(r.done=!0,e.leave(t).then(function(){t.remove(),a&&a.resolve()}),n.$destroy(),o&&o())}var l,s=null,p=function(){return l||(l=i.defer(),s=l.promise),function(){l.resolve()}};return n.$broadcast(D.NOW_CLOSING_EVENT,p),i.when(s).then(r)}function h(e){if(e.isDefaultPrevented())return e;var t=$.top();if(t)switch(e.which){case 27:t.value.keyboard&&(e.preventDefault(),a.$apply(function(){D.dismiss(t.key,"escape key press")}));break;case 9:var n=D.loadFocusElementList(t),o=!1;e.shiftKey?(D.isFocusInFirstItem(e,n)||D.isModalFocused(e,t))&&(o=D.focusLastFocusableElement(n)):D.isFocusInLastItem(e,n)&&(o=D.focusFirstFocusableElement(n)),o&&(e.preventDefault(),e.stopPropagation())}}function g(e,t,n){return!e.value.modalScope.$broadcast("modal.closing",t,n).defaultPrevented}var b,v,w,y="modal-open",$=l.createNew(),k=r.createNew(),D={NOW_CLOSING_EVENT:"modal.stack.now-closing"},M=0,x=null,T="a[href], area[href], input:not([disabled]), button:not([disabled]),select:not([disabled]), textarea:not([disabled]), iframe, object, embed, *[tabindex], *[contenteditable=true]";return a.$watch(u,function(e){v&&(v.index=e)}),n.on("keydown",h),a.$on("$destroy",function(){n.off("keydown",h)}),D.open=function(t,i){var r=n[0].activeElement,l=i.openedClass||y;d(!1),x=$.top(),$.add(t,{deferred:i.deferred,renderDeferred:i.renderDeferred,closedDeferred:i.closedDeferred,modalScope:i.scope,backdrop:i.backdrop,keyboard:i.keyboard,openedClass:i.openedClass,windowTopClass:i.windowTopClass,animation:i.animation,appendTo:i.appendTo}),k.put(l,t);var p=i.appendTo,c=u();if(!p.length)throw new Error("appendTo element not found. Make sure that the element passed is in DOM.");c>=0&&!b&&(v=a.$new(!0),v.modalOptions=i,v.index=c,b=angular.element('<div uib-modal-backdrop="modal-backdrop"></div>'),b.attr("backdrop-class",i.backdropClass),i.animation&&b.attr("modal-animation","true"),o(b)(v),e.enter(b,p),w=s.scrollbarPadding(p),w.heightOverflow&&w.scrollbarWidth&&p.css({paddingRight:w.right+"px"})),M=x?parseInt(x.value.modalDomEl.attr("index"),10)+1:0;var m=angular.element('<div uib-modal-window="modal-window"></div>');m.attr({"template-url":i.windowTemplateUrl,"window-class":i.windowClass,"window-top-class":i.windowTopClass,size:i.size,index:M,animate:"animate"}).html(i.content),i.animation&&m.attr("modal-animation","true"),p.addClass(l),e.enter(o(m)(i.scope),p),$.top().value.modalDomEl=m,$.top().value.modalOpener=r},D.close=function(e,t){var n=$.get(e);return n&&g(n,t,!0)?(n.value.modalScope.$$uibDestructionScheduled=!0,n.value.deferred.resolve(t),c(e,n.value.modalOpener),!0):!n},D.dismiss=function(e,t){var n=$.get(e);return n&&g(n,t,!1)?(n.value.modalScope.$$uibDestructionScheduled=!0,n.value.deferred.reject(t),c(e,n.value.modalOpener),!0):!n},D.dismissAll=function(e){for(var t=this.getTop();t&&this.dismiss(t.key,e);)t=this.getTop()},D.getTop=function(){return $.top()},D.modalRendered=function(e){var t=$.get(e);t&&t.value.renderDeferred.resolve()},D.focusFirstFocusableElement=function(e){return e.length>0?(e[0].focus(),!0):!1},D.focusLastFocusableElement=function(e){return e.length>0?(e[e.length-1].focus(),!0):!1},D.isModalFocused=function(e,t){if(e&&t){var n=t.value.modalDomEl;if(n&&n.length)return(e.target||e.srcElement)===n[0]}return!1},D.isFocusInFirstItem=function(e,t){return t.length>0?(e.target||e.srcElement)===t[0]:!1},D.isFocusInLastItem=function(e,t){return t.length>0?(e.target||e.srcElement)===t[t.length-1]:!1},D.loadFocusElementList=function(e){if(e){var t=e.value.modalDomEl;if(t&&t.length){var n=t[0].querySelectorAll(T);return n?Array.prototype.filter.call(n,function(e){return p(e)}):n}}},D}]).provider("$uibModal",function(){var e={options:{animation:!0,backdrop:!0,keyboard:!0},$get:["$rootScope","$q","$document","$templateRequest","$controller","$uibResolve","$uibModalStack",function(t,n,o,a,i,r,l){function s(e){return e.template?n.when(e.template):a(angular.isFunction(e.templateUrl)?e.templateUrl():e.templateUrl)}var p={},u=null;return p.getPromiseChain=function(){return u},p.open=function(a){function p(){return b}var c=n.defer(),d=n.defer(),m=n.defer(),f=n.defer(),h={result:c.promise,opened:d.promise,closed:m.promise,rendered:f.promise,close:function(e){return l.close(h,e)},dismiss:function(e){return l.dismiss(h,e)}};if(a=angular.extend({},e.options,a),a.resolve=a.resolve||{},a.appendTo=a.appendTo||o.find("body").eq(0),!a.template&&!a.templateUrl)throw new Error("One of template or templateUrl options is required.");var g,b=n.all([s(a),r.resolve(a.resolve,{},null,null)]);return g=u=n.all([u]).then(p,p).then(function(e){var n=a.scope||t,o=n.$new();o.$close=h.close,o.$dismiss=h.dismiss,o.$on("$destroy",function(){o.$$uibDestructionScheduled||o.$dismiss("$uibUnscheduledDestruction")});var r,s,p={};a.controller&&(p.$scope=o,p.$scope.$resolve={},p.$uibModalInstance=h,angular.forEach(e[1],function(e,t){p[t]=e,p.$scope.$resolve[t]=e}),s=i(a.controller,p,!0,a.controllerAs),a.controllerAs&&a.bindToController&&(r=s.instance,r.$close=o.$close,r.$dismiss=o.$dismiss,angular.extend(r,{$resolve:p.$scope.$resolve},n)),r=s(),angular.isFunction(r.$onInit)&&r.$onInit()),l.open(h,{scope:o,deferred:c,renderDeferred:f,closedDeferred:m,content:e[0],animation:a.animation,backdrop:a.backdrop,keyboard:a.keyboard,backdropClass:a.backdropClass,windowTopClass:a.windowTopClass,windowClass:a.windowClass,windowTemplateUrl:a.windowTemplateUrl,size:a.size,openedClass:a.openedClass,appendTo:a.appendTo}),d.resolve(!0)},function(e){d.reject(e),c.reject(e)})["finally"](function(){u===g&&(u=null)}),h},p}]};return e}),angular.module("ui.bootstrap.stackedMap",[]).factory("$$stackedMap",function(){return{createNew:function(){var e=[];return{add:function(t,n){e.push({key:t,value:n})},get:function(t){for(var n=0;n<e.length;n++)if(t===e[n].key)return e[n]},keys:function(){for(var t=[],n=0;n<e.length;n++)t.push(e[n].key);return t},top:function(){return e[e.length-1]},remove:function(t){for(var n=-1,o=0;o<e.length;o++)if(t===e[o].key){n=o;break}return e.splice(n,1)[0]},removeTop:function(){return e.splice(e.length-1,1)[0]},length:function(){return e.length}}}}}),angular.module("ui.bootstrap.position",[]).factory("$uibPosition",["$document","$window",function(e,t){var n,o,a={normal:/(auto|scroll)/,hidden:/(auto|scroll|hidden)/},i={auto:/\s?auto?\s?/i,primary:/^(top|bottom|left|right)$/,secondary:/^(top|bottom|left|right|center)$/,vertical:/^(top|bottom)$/},r=/(HTML|BODY)/;return{getRawNode:function(e){return e.nodeName?e:e[0]||e},parseStyle:function(e){return e=parseFloat(e),isFinite(e)?e:0},offsetParent:function(n){function o(e){return"static"===(t.getComputedStyle(e).position||"static")}n=this.getRawNode(n);for(var a=n.offsetParent||e[0].documentElement;a&&a!==e[0].documentElement&&o(a);)a=a.offsetParent;return a||e[0].documentElement},scrollbarWidth:function(a){if(a){if(angular.isUndefined(o)){var i=e.find("body");i.addClass("uib-position-body-scrollbar-measure"),o=t.innerWidth-i[0].clientWidth,o=isFinite(o)?o:0,i.removeClass("uib-position-body-scrollbar-measure")}return o}if(angular.isUndefined(n)){var r=angular.element('<div class="uib-position-scrollbar-measure"></div>');e.find("body").append(r),n=r[0].offsetWidth-r[0].clientWidth,n=isFinite(n)?n:0,r.remove()}return n},scrollbarPadding:function(e){e=this.getRawNode(e);var n=t.getComputedStyle(e),o=this.parseStyle(n.paddingRight),a=this.parseStyle(n.paddingBottom),i=this.scrollParent(e,!1,!0),l=this.scrollbarWidth(i,r.test(i.tagName));return{scrollbarWidth:l,widthOverflow:i.scrollWidth>i.clientWidth,right:o+l,originalRight:o,heightOverflow:i.scrollHeight>i.clientHeight,bottom:a+l,originalBottom:a}},isScrollable:function(e,n){e=this.getRawNode(e);var o=n?a.hidden:a.normal,i=t.getComputedStyle(e);return o.test(i.overflow+i.overflowY+i.overflowX)},scrollParent:function(n,o,i){n=this.getRawNode(n);var r=o?a.hidden:a.normal,l=e[0].documentElement,s=t.getComputedStyle(n);if(i&&r.test(s.overflow+s.overflowY+s.overflowX))return n;var p="absolute"===s.position,u=n.parentElement||l;if(u===l||"fixed"===s.position)return l;for(;u.parentElement&&u!==l;){var c=t.getComputedStyle(u);if(p&&"static"!==c.position&&(p=!1),!p&&r.test(c.overflow+c.overflowY+c.overflowX))break;u=u.parentElement}return u},position:function(n,o){n=this.getRawNode(n);var a=this.offset(n);if(o){var i=t.getComputedStyle(n);a.top-=this.parseStyle(i.marginTop),a.left-=this.parseStyle(i.marginLeft)}var r=this.offsetParent(n),l={top:0,left:0};return r!==e[0].documentElement&&(l=this.offset(r),l.top+=r.clientTop-r.scrollTop,l.left+=r.clientLeft-r.scrollLeft),{width:Math.round(angular.isNumber(a.width)?a.width:n.offsetWidth),height:Math.round(angular.isNumber(a.height)?a.height:n.offsetHeight),top:Math.round(a.top-l.top),left:Math.round(a.left-l.left)}},offset:function(n){n=this.getRawNode(n);var o=n.getBoundingClientRect();return{width:Math.round(angular.isNumber(o.width)?o.width:n.offsetWidth),height:Math.round(angular.isNumber(o.height)?o.height:n.offsetHeight),top:Math.round(o.top+(t.pageYOffset||e[0].documentElement.scrollTop)),left:Math.round(o.left+(t.pageXOffset||e[0].documentElement.scrollLeft))}},viewportOffset:function(n,o,a){n=this.getRawNode(n),a=a!==!1?!0:!1;var i=n.getBoundingClientRect(),r={top:0,left:0,bottom:0,right:0},l=o?e[0].documentElement:this.scrollParent(n),s=l.getBoundingClientRect();if(r.top=s.top+l.clientTop,r.left=s.left+l.clientLeft,l===e[0].documentElement&&(r.top+=t.pageYOffset,r.left+=t.pageXOffset),r.bottom=r.top+l.clientHeight,r.right=r.left+l.clientWidth,a){var p=t.getComputedStyle(l);r.top+=this.parseStyle(p.paddingTop),r.bottom-=this.parseStyle(p.paddingBottom),r.left+=this.parseStyle(p.paddingLeft),r.right-=this.parseStyle(p.paddingRight)}return{top:Math.round(i.top-r.top),bottom:Math.round(r.bottom-i.bottom),left:Math.round(i.left-r.left),right:Math.round(r.right-i.right)}},parsePlacement:function(e){var t=i.auto.test(e);return t&&(e=e.replace(i.auto,"")),e=e.split("-"),e[0]=e[0]||"top",i.primary.test(e[0])||(e[0]="top"),e[1]=e[1]||"center",i.secondary.test(e[1])||(e[1]="center"),e[2]=t?!0:!1,e},positionElements:function(e,n,o,a){e=this.getRawNode(e),n=this.getRawNode(n);var r=angular.isDefined(n.offsetWidth)?n.offsetWidth:n.prop("offsetWidth"),l=angular.isDefined(n.offsetHeight)?n.offsetHeight:n.prop("offsetHeight");o=this.parsePlacement(o);var s=a?this.offset(e):this.position(e),p={top:0,left:0,placement:""};if(o[2]){var u=this.viewportOffset(e,a),c=t.getComputedStyle(n),d={width:r+Math.round(Math.abs(this.parseStyle(c.marginLeft)+this.parseStyle(c.marginRight))),height:l+Math.round(Math.abs(this.parseStyle(c.marginTop)+this.parseStyle(c.marginBottom)))};if(o[0]="top"===o[0]&&d.height>u.top&&d.height<=u.bottom?"bottom":"bottom"===o[0]&&d.height>u.bottom&&d.height<=u.top?"top":"left"===o[0]&&d.width>u.left&&d.width<=u.right?"right":"right"===o[0]&&d.width>u.right&&d.width<=u.left?"left":o[0],o[1]="top"===o[1]&&d.height-s.height>u.bottom&&d.height-s.height<=u.top?"bottom":"bottom"===o[1]&&d.height-s.height>u.top&&d.height-s.height<=u.bottom?"top":"left"===o[1]&&d.width-s.width>u.right&&d.width-s.width<=u.left?"right":"right"===o[1]&&d.width-s.width>u.left&&d.width-s.width<=u.right?"left":o[1],"center"===o[1])if(i.vertical.test(o[0])){var m=s.width/2-r/2;u.left+m<0&&d.width-s.width<=u.right?o[1]="left":u.right+m<0&&d.width-s.width<=u.left&&(o[1]="right")}else{var f=s.height/2-d.height/2;u.top+f<0&&d.height-s.height<=u.bottom?o[1]="top":u.bottom+f<0&&d.height-s.height<=u.top&&(o[1]="bottom")}}switch(o[0]){case"top":p.top=s.top-l;break;case"bottom":p.top=s.top+s.height;break;case"left":p.left=s.left-r;break;case"right":p.left=s.left+s.width}switch(o[1]){case"top":p.top=s.top;break;case"bottom":p.top=s.top+s.height-l;break;case"left":p.left=s.left;break;case"right":p.left=s.left+s.width-r;break;case"center":i.vertical.test(o[0])?p.left=s.left+s.width/2-r/2:p.top=s.top+s.height/2-l/2}return p.top=Math.round(p.top),p.left=Math.round(p.left),p.placement="center"===o[1]?o[0]:o[0]+"-"+o[1],p},positionArrow:function(e,n){e=this.getRawNode(e);var o=e.querySelector(".tooltip-inner, .popover-inner");if(o){var a=angular.element(o).hasClass("tooltip-inner"),r=e.querySelector(a?".tooltip-arrow":".arrow");if(r){var l={top:"",bottom:"",left:"",right:""};if(n=this.parsePlacement(n),"center"===n[1])return void angular.element(r).css(l);var s="border-"+n[0]+"-width",p=t.getComputedStyle(r)[s],u="border-";u+=i.vertical.test(n[0])?n[0]+"-"+n[1]:n[1]+"-"+n[0],u+="-radius";var c=t.getComputedStyle(a?o:e)[u];switch(n[0]){case"top":l.bottom=a?"0":"-"+p;break;case"bottom":l.top=a?"0":"-"+p;break;case"left":l.right=a?"0":"-"+p;break;case"right":l.left=a?"0":"-"+p}l[n[1]]=c,angular.element(r).css(l)}}}}}]),angular.module("ui.bootstrap.tabs",[]).controller("UibTabsetController",["$scope",function(e){function t(e){for(var t=0;t<o.tabs.length;t++)if(o.tabs[t].index===e)return t}var n,o=this;o.tabs=[],o.select=function(e,i){if(!a){var r=t(n),l=o.tabs[r];if(l){if(l.tab.onDeselect({$event:i,$selectedIndex:e}),i&&i.isDefaultPrevented())return;l.tab.active=!1}var s=o.tabs[e];s?(s.tab.onSelect({$event:i}),s.tab.active=!0,o.active=s.index,n=s.index):!s&&angular.isDefined(n)&&(o.active=null,n=null)}},o.addTab=function(e){if(o.tabs.push({tab:e,index:e.index}),o.tabs.sort(function(e,t){return e.index>t.index?1:e.index<t.index?-1:0}),e.index===o.active||!angular.isDefined(o.active)&&1===o.tabs.length){var n=t(e.index);o.select(n)}},o.removeTab=function(e){for(var t,n=0;n<o.tabs.length;n++)if(o.tabs[n].tab===e){t=n;break}if(o.tabs[t].index===o.active){var a=t===o.tabs.length-1?t-1:t+1%o.tabs.length;o.select(a)}o.tabs.splice(t,1)},e.$watch("tabset.active",function(e){angular.isDefined(e)&&e!==n&&o.select(t(e))});var a;e.$on("$destroy",function(){a=!0})}]).directive("uibTabset",function(){return{transclude:!0,replace:!0,scope:{},bindToController:{active:"=?",type:"@"},controller:"UibTabsetController",controllerAs:"tabset",templateUrl:function(e,t){return t.templateUrl||"uib/template/tabs/tabset.html"},link:function(e,t,n){e.vertical=angular.isDefined(n.vertical)?e.$parent.$eval(n.vertical):!1,e.justified=angular.isDefined(n.justified)?e.$parent.$eval(n.justified):!1}}}).directive("uibTab",["$parse",function(e){return{require:"^uibTabset",replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/tabs/tab.html"},transclude:!0,scope:{heading:"@",index:"=?",classes:"@?",onSelect:"&select",onDeselect:"&deselect"},controller:function(){},controllerAs:"tab",link:function(t,n,o,a,i){t.disabled=!1,o.disable&&t.$parent.$watch(e(o.disable),function(e){t.disabled=!!e}),angular.isUndefined(o.index)&&(t.index=a.tabs&&a.tabs.length?Math.max.apply(null,a.tabs.map(function(e){return e.index}))+1:0),angular.isUndefined(o.classes)&&(t.classes=""),t.select=function(e){if(!t.disabled){for(var n,o=0;o<a.tabs.length;o++)if(a.tabs[o].tab===t){n=o;break}a.select(n,e)}},a.addTab(t),t.$on("$destroy",function(){a.removeTab(t)}),t.$transcludeFn=i}}}]).directive("uibTabHeadingTransclude",function(){return{restrict:"A",require:"^uibTab",link:function(e,t){e.$watch("headingElement",function(e){e&&(t.html(""),t.append(e))})}}}).directive("uibTabContentTransclude",function(){function e(e){return e.tagName&&(e.hasAttribute("uib-tab-heading")||e.hasAttribute("data-uib-tab-heading")||e.hasAttribute("x-uib-tab-heading")||"uib-tab-heading"===e.tagName.toLowerCase()||"data-uib-tab-heading"===e.tagName.toLowerCase()||"x-uib-tab-heading"===e.tagName.toLowerCase()||"uib:tab-heading"===e.tagName.toLowerCase())}return{restrict:"A",require:"^uibTabset",link:function(t,n,o){var a=t.$eval(o.uibTabContentTransclude).tab;a.$transcludeFn(a.$parent,function(t){angular.forEach(t,function(t){e(t)?a.headingElement=t:n.append(t)})})}}}),angular.module("ui.bootstrap.popover",["ui.bootstrap.tooltip"]).directive("uibPopoverTemplatePopup",function(){return{replace:!0,scope:{uibTitle:"@",contentExp:"&",placement:"@",popupClass:"@",animation:"&",isOpen:"&",originScope:"&"},templateUrl:"uib/template/popover/popover-template.html"}}).directive("uibPopoverTemplate",["$uibTooltip",function(e){return e("uibPopoverTemplate","popover","click",{useContentExp:!0})}]).directive("uibPopoverHtmlPopup",function(){return{replace:!0,scope:{contentExp:"&",uibTitle:"@",placement:"@",popupClass:"@",animation:"&",isOpen:"&"},templateUrl:"uib/template/popover/popover-html.html"}}).directive("uibPopoverHtml",["$uibTooltip",function(e){return e("uibPopoverHtml","popover","click",{useContentExp:!0})}]).directive("uibPopoverPopup",function(){return{replace:!0,scope:{uibTitle:"@",content:"@",placement:"@",popupClass:"@",animation:"&",isOpen:"&"},templateUrl:"uib/template/popover/popover.html"}}).directive("uibPopover",["$uibTooltip",function(e){return e("uibPopover","popover","click")}]),angular.module("ui.bootstrap.tooltip",["ui.bootstrap.position","ui.bootstrap.stackedMap"]).provider("$uibTooltip",function(){function e(e){var t=/[A-Z]/g,n="-";return e.replace(t,function(e,t){return(t?n:"")+e.toLowerCase()})}var t={placement:"top",placementClassPrefix:"",animation:!0,popupDelay:0,popupCloseDelay:0,useContentExp:!1},n={mouseenter:"mouseleave",click:"click",outsideClick:"outsideClick",focus:"blur",none:""},o={};this.options=function(e){angular.extend(o,e)},this.setTriggers=function(e){angular.extend(n,e)},this.$get=["$window","$compile","$timeout","$document","$uibPosition","$interpolate","$rootScope","$parse","$$stackedMap",function(a,i,r,l,s,p,u,c,d){function m(e){if(27===e.which){var t=f.top();t&&(t.value.close(),f.removeTop(),t=null)}}var f=d.createNew();return l.on("keypress",m),u.$on("$destroy",function(){l.off("keypress",m)}),function(a,u,d,m){function h(e){var t=(e||m.trigger||d).split(" "),o=t.map(function(e){return n[e]||e});return{show:t,hide:o}}m=angular.extend({},t,o,m);var g=e(a),b=p.startSymbol(),v=p.endSymbol(),w="<div "+g+'-popup uib-title="'+b+"title"+v+'" '+(m.useContentExp?'content-exp="contentExp()" ':'content="'+b+"content"+v+'" ')+'placement="'+b+"placement"+v+'" popup-class="'+b+"popupClass"+v+'" animation="animation" is-open="isOpen" origin-scope="origScope" class="uib-position-measure"></div>';return{compile:function(){var e=i(w);return function(t,n,o){function i(){R.isOpen?d():p()}function p(){(!H||t.$eval(o[u+"Enable"]))&&(w(),k(),R.popupDelay?P||(P=r(g,R.popupDelay,!1)):g())}function d(){b(),R.popupCloseDelay?I||(I=r(v,R.popupCloseDelay,!1)):v()}function g(){return b(),w(),R.content?(y(),void R.$evalAsync(function(){R.isOpen=!0,D(!0),W()})):angular.noop}function b(){P&&(r.cancel(P),P=null),U&&(r.cancel(U),U=null)}function v(){R&&R.$evalAsync(function(){R&&(R.isOpen=!1,D(!1),R.animation?E||(E=r($,150,!1)):$())})}function w(){I&&(r.cancel(I),I=null),E&&(r.cancel(E),E=null)}function y(){O||(S=R.$new(),O=e(S,function(e){F?l.find("body").append(e):n.after(e)}),M())}function $(){b(),w(),x(),O&&(O.remove(),O=null),S&&(S.$destroy(),S=null)}function k(){R.title=o[u+"Title"],R.content=q?q(t):o[a],R.popupClass=o[u+"Class"],R.placement=angular.isDefined(o[u+"Placement"])?o[u+"Placement"]:m.placement;var e=s.parsePlacement(R.placement);A=e[1]?e[0]+"-"+e[1]:e[0];var n=parseInt(o[u+"PopupDelay"],10),i=parseInt(o[u+"PopupCloseDelay"],10);R.popupDelay=isNaN(n)?m.popupDelay:n,R.popupCloseDelay=isNaN(i)?m.popupCloseDelay:i}function D(e){Y&&angular.isFunction(Y.assign)&&Y.assign(t,e)}function M(){z.length=0,q?(z.push(t.$watch(q,function(e){R.content=e,!e&&R.isOpen&&v()})),z.push(S.$watch(function(){V||(V=!0,S.$$postDigest(function(){V=!1,R&&R.isOpen&&W()}))}))):z.push(o.$observe(a,function(e){R.content=e,!e&&R.isOpen?v():W()})),z.push(o.$observe(u+"Title",function(e){R.title=e,R.isOpen&&W()})),z.push(o.$observe(u+"Placement",function(e){R.placement=e?e:m.placement,R.isOpen&&W()}))}function x(){z.length&&(angular.forEach(z,function(e){e()}),z.length=0)}function T(e){R&&R.isOpen&&O&&(n[0].contains(e.target)||O[0].contains(e.target)||d())}function C(){var e=o[u+"Trigger"];j(),N=h(e),"none"!==N.show&&N.show.forEach(function(e,t){"outsideClick"===e?(n.on("click",i),l.on("click",T)):e===N.hide[t]?n.on(e,i):e&&(n.on(e,p),n.on(N.hide[t],d)),n.on("keypress",function(e){27===e.which&&d()})})}var O,S,E,P,I,U,A,F=angular.isDefined(m.appendToBody)?m.appendToBody:!1,N=h(void 0),H=angular.isDefined(o[u+"Enable"]),R=t.$new(!0),V=!1,Y=angular.isDefined(o[u+"IsOpen"])?c(o[u+"IsOpen"]):!1,q=m.useContentExp?c(o[a]):!1,z=[],W=function(){O&&O.html()&&(U||(U=r(function(){var e=s.positionElements(n,O,R.placement,F);O.css({top:e.top+"px",left:e.left+"px"}),O.hasClass(e.placement.split("-")[0])||(O.removeClass(A.split("-")[0]),O.addClass(e.placement.split("-")[0])),O.hasClass(m.placementClassPrefix+e.placement)||(O.removeClass(m.placementClassPrefix+A),O.addClass(m.placementClassPrefix+e.placement)),O.hasClass("uib-position-measure")?(s.positionArrow(O,e.placement),O.removeClass("uib-position-measure")):A!==e.placement&&s.positionArrow(O,e.placement),A=e.placement,U=null},0,!1)))};R.origScope=t,R.isOpen=!1,f.add(R,{close:v}),R.contentExp=function(){return R.content},o.$observe("disabled",function(e){e&&b(),e&&R.isOpen&&v()}),Y&&t.$watch(Y,function(e){R&&!e===R.isOpen&&i()});var j=function(){N.show.forEach(function(e){"outsideClick"===e?n.off("click",i):(n.off(e,p),n.off(e,i))}),N.hide.forEach(function(e){"outsideClick"===e?l.off("click",T):n.off(e,d)})};C();var B=t.$eval(o[u+"Animation"]);R.animation=angular.isDefined(B)?!!B:m.animation;var L,_=u+"AppendToBody";L=_ in o&&void 0===o[_]?!0:t.$eval(o[_]),F=angular.isDefined(L)?L:F,t.$on("$destroy",function(){j(),$(),f.remove(R),R=null})}}}}}]}).directive("uibTooltipTemplateTransclude",["$animate","$sce","$compile","$templateRequest",function(e,t,n,o){return{link:function(a,i,r){var l,s,p,u=a.$eval(r.tooltipTemplateTranscludeScope),c=0,d=function(){s&&(s.remove(),s=null),l&&(l.$destroy(),l=null),p&&(e.leave(p).then(function(){s=null}),s=p,p=null)};a.$watch(t.parseAsResourceUrl(r.uibTooltipTemplateTransclude),function(t){var r=++c;t?(o(t,!0).then(function(o){if(r===c){var a=u.$new(),s=o,m=n(s)(a,function(t){d(),e.enter(t,i)});l=a,p=m,l.$emit("$includeContentLoaded",t)}},function(){r===c&&(d(),a.$emit("$includeContentError",t))}),a.$emit("$includeContentRequested",t)):d()}),a.$on("$destroy",d)}}}]).directive("uibTooltipClasses",["$uibPosition",function(e){return{restrict:"A",link:function(t,n,o){if(t.placement){var a=e.parsePlacement(t.placement);n.addClass(a[0])}t.popupClass&&n.addClass(t.popupClass),t.animation()&&n.addClass(o.tooltipAnimationClass)}}}]).directive("uibTooltipPopup",function(){return{replace:!0,scope:{content:"@",placement:"@",popupClass:"@",animation:"&",isOpen:"&"},templateUrl:"uib/template/tooltip/tooltip-popup.html"}}).directive("uibTooltip",["$uibTooltip",function(e){return e("uibTooltip","tooltip","mouseenter")}]).directive("uibTooltipTemplatePopup",function(){return{replace:!0,scope:{contentExp:"&",placement:"@",popupClass:"@",animation:"&",isOpen:"&",originScope:"&"},templateUrl:"uib/template/tooltip/tooltip-template-popup.html"}
}).directive("uibTooltipTemplate",["$uibTooltip",function(e){return e("uibTooltipTemplate","tooltip","mouseenter",{useContentExp:!0})}]).directive("uibTooltipHtmlPopup",function(){return{replace:!0,scope:{contentExp:"&",placement:"@",popupClass:"@",animation:"&",isOpen:"&"},templateUrl:"uib/template/tooltip/tooltip-html-popup.html"}}).directive("uibTooltipHtml",["$uibTooltip",function(e){return e("uibTooltipHtml","tooltip","mouseenter",{useContentExp:!0})}]),angular.module("ui.bootstrap.timepicker",[]).constant("uibTimepickerConfig",{hourStep:1,minuteStep:1,secondStep:1,showMeridian:!0,showSeconds:!1,meridians:null,readonlyInput:!1,mousewheel:!0,arrowkeys:!0,showSpinners:!0,templateUrl:"uib/template/timepicker/timepicker.html"}).controller("UibTimepickerController",["$scope","$element","$attrs","$parse","$log","$locale","uibTimepickerConfig",function(e,t,n,o,a,i,r){function l(){var t=+e.hours,n=e.showMeridian?t>0&&13>t:t>=0&&24>t;return n&&""!==e.hours?(e.showMeridian&&(12===t&&(t=0),e.meridian===$[1]&&(t+=12)),t):void 0}function s(){var t=+e.minutes,n=t>=0&&60>t;return n&&""!==e.minutes?t:void 0}function p(){var t=+e.seconds;return t>=0&&60>t?t:void 0}function u(e,t){return null===e?"":angular.isDefined(e)&&e.toString().length<2&&!t?"0"+e:e.toString()}function c(e){d(),y.$setViewValue(new Date(v)),m(e)}function d(){y.$setValidity("time",!0),e.invalidHours=!1,e.invalidMinutes=!1,e.invalidSeconds=!1}function m(t){if(y.$modelValue){var n=v.getHours(),o=v.getMinutes(),a=v.getSeconds();e.showMeridian&&(n=0===n||12===n?12:n%12),e.hours="h"===t?n:u(n,!k),"m"!==t&&(e.minutes=u(o)),e.meridian=v.getHours()<12?$[0]:$[1],"s"!==t&&(e.seconds=u(a)),e.meridian=v.getHours()<12?$[0]:$[1]}else e.hours=null,e.minutes=null,e.seconds=null,e.meridian=$[0]}function f(e){v=g(v,e),c()}function h(e,t){return g(e,60*t)}function g(e,t){var n=new Date(e.getTime()+1e3*t),o=new Date(e);return o.setHours(n.getHours(),n.getMinutes(),n.getSeconds()),o}function b(){return!(null!==e.hours&&""!==e.hours||null!==e.minutes&&""!==e.minutes||e.showSeconds&&(!e.showSeconds||null!==e.seconds&&""!==e.seconds))}var v=new Date,w=[],y={$setViewValue:angular.noop},$=angular.isDefined(n.meridians)?e.$parent.$eval(n.meridians):r.meridians||i.DATETIME_FORMATS.AMPMS,k=angular.isDefined(n.padHours)?e.$parent.$eval(n.padHours):!0;e.tabindex=angular.isDefined(n.tabindex)?n.tabindex:0,t.removeAttr("tabindex"),this.init=function(t,o){y=t,y.$render=this.render,y.$formatters.unshift(function(e){return e?new Date(e):null});var a=o.eq(0),i=o.eq(1),l=o.eq(2),s=angular.isDefined(n.mousewheel)?e.$parent.$eval(n.mousewheel):r.mousewheel;s&&this.setupMousewheelEvents(a,i,l);var p=angular.isDefined(n.arrowkeys)?e.$parent.$eval(n.arrowkeys):r.arrowkeys;p&&this.setupArrowkeyEvents(a,i,l),e.readonlyInput=angular.isDefined(n.readonlyInput)?e.$parent.$eval(n.readonlyInput):r.readonlyInput,this.setupInputEvents(a,i,l)};var D=r.hourStep;n.hourStep&&w.push(e.$parent.$watch(o(n.hourStep),function(e){D=+e}));var M=r.minuteStep;n.minuteStep&&w.push(e.$parent.$watch(o(n.minuteStep),function(e){M=+e}));var x;w.push(e.$parent.$watch(o(n.min),function(e){var t=new Date(e);x=isNaN(t)?void 0:t}));var T;w.push(e.$parent.$watch(o(n.max),function(e){var t=new Date(e);T=isNaN(t)?void 0:t}));var C=!1;n.ngDisabled&&w.push(e.$parent.$watch(o(n.ngDisabled),function(e){C=e})),e.noIncrementHours=function(){var e=h(v,60*D);return C||e>T||v>e&&x>e},e.noDecrementHours=function(){var e=h(v,60*-D);return C||x>e||e>v&&e>T},e.noIncrementMinutes=function(){var e=h(v,M);return C||e>T||v>e&&x>e},e.noDecrementMinutes=function(){var e=h(v,-M);return C||x>e||e>v&&e>T},e.noIncrementSeconds=function(){var e=g(v,O);return C||e>T||v>e&&x>e},e.noDecrementSeconds=function(){var e=g(v,-O);return C||x>e||e>v&&e>T},e.noToggleMeridian=function(){return v.getHours()<12?C||h(v,720)>T:C||h(v,-720)<x};var O=r.secondStep;n.secondStep&&w.push(e.$parent.$watch(o(n.secondStep),function(e){O=+e})),e.showSeconds=r.showSeconds,n.showSeconds&&w.push(e.$parent.$watch(o(n.showSeconds),function(t){e.showSeconds=!!t})),e.showMeridian=r.showMeridian,n.showMeridian&&w.push(e.$parent.$watch(o(n.showMeridian),function(t){if(e.showMeridian=!!t,y.$error.time){var n=l(),o=s();angular.isDefined(n)&&angular.isDefined(o)&&(v.setHours(n),c())}else m()})),this.setupMousewheelEvents=function(t,n,o){var a=function(e){e.originalEvent&&(e=e.originalEvent);var t=e.wheelDelta?e.wheelDelta:-e.deltaY;return e.detail||t>0};t.bind("mousewheel wheel",function(t){C||e.$apply(a(t)?e.incrementHours():e.decrementHours()),t.preventDefault()}),n.bind("mousewheel wheel",function(t){C||e.$apply(a(t)?e.incrementMinutes():e.decrementMinutes()),t.preventDefault()}),o.bind("mousewheel wheel",function(t){C||e.$apply(a(t)?e.incrementSeconds():e.decrementSeconds()),t.preventDefault()})},this.setupArrowkeyEvents=function(t,n,o){t.bind("keydown",function(t){C||(38===t.which?(t.preventDefault(),e.incrementHours(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementHours(),e.$apply()))}),n.bind("keydown",function(t){C||(38===t.which?(t.preventDefault(),e.incrementMinutes(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementMinutes(),e.$apply()))}),o.bind("keydown",function(t){C||(38===t.which?(t.preventDefault(),e.incrementSeconds(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementSeconds(),e.$apply()))})},this.setupInputEvents=function(t,n,o){if(e.readonlyInput)return e.updateHours=angular.noop,e.updateMinutes=angular.noop,void(e.updateSeconds=angular.noop);var a=function(t,n,o){y.$setViewValue(null),y.$setValidity("time",!1),angular.isDefined(t)&&(e.invalidHours=t),angular.isDefined(n)&&(e.invalidMinutes=n),angular.isDefined(o)&&(e.invalidSeconds=o)};e.updateHours=function(){var e=l(),t=s();y.$setDirty(),angular.isDefined(e)&&angular.isDefined(t)?(v.setHours(e),v.setMinutes(t),x>v||v>T?a(!0):c("h")):a(!0)},t.bind("blur",function(){y.$setTouched(),b()?d():null===e.hours||""===e.hours?a(!0):!e.invalidHours&&e.hours<10&&e.$apply(function(){e.hours=u(e.hours,!k)})}),e.updateMinutes=function(){var e=s(),t=l();y.$setDirty(),angular.isDefined(e)&&angular.isDefined(t)?(v.setHours(t),v.setMinutes(e),x>v||v>T?a(void 0,!0):c("m")):a(void 0,!0)},n.bind("blur",function(){y.$setTouched(),b()?d():null===e.minutes?a(void 0,!0):!e.invalidMinutes&&e.minutes<10&&e.$apply(function(){e.minutes=u(e.minutes)})}),e.updateSeconds=function(){var e=p();y.$setDirty(),angular.isDefined(e)?(v.setSeconds(e),c("s")):a(void 0,void 0,!0)},o.bind("blur",function(){b()?d():!e.invalidSeconds&&e.seconds<10&&e.$apply(function(){e.seconds=u(e.seconds)})})},this.render=function(){var t=y.$viewValue;isNaN(t)?(y.$setValidity("time",!1),a.error('Timepicker directive: "ng-model" value must be a Date object, a number of milliseconds since 01.01.1970 or a string representing an RFC2822 or ISO 8601 date.')):(t&&(v=t),x>v||v>T?(y.$setValidity("time",!1),e.invalidHours=!0,e.invalidMinutes=!0):d(),m())},e.showSpinners=angular.isDefined(n.showSpinners)?e.$parent.$eval(n.showSpinners):r.showSpinners,e.incrementHours=function(){e.noIncrementHours()||f(60*D*60)},e.decrementHours=function(){e.noDecrementHours()||f(60*-D*60)},e.incrementMinutes=function(){e.noIncrementMinutes()||f(60*M)},e.decrementMinutes=function(){e.noDecrementMinutes()||f(60*-M)},e.incrementSeconds=function(){e.noIncrementSeconds()||f(O)},e.decrementSeconds=function(){e.noDecrementSeconds()||f(-O)},e.toggleMeridian=function(){var t=s(),n=l();e.noToggleMeridian()||(angular.isDefined(t)&&angular.isDefined(n)?f(720*(v.getHours()<12?60:-60)):e.meridian=e.meridian===$[0]?$[1]:$[0])},e.blur=function(){y.$setTouched()},e.$on("$destroy",function(){for(;w.length;)w.shift()()})}]).directive("uibTimepicker",["uibTimepickerConfig",function(e){return{require:["uibTimepicker","?^ngModel"],controller:"UibTimepickerController",controllerAs:"timepicker",replace:!0,scope:{},templateUrl:function(t,n){return n.templateUrl||e.templateUrl},link:function(e,t,n,o){var a=o[0],i=o[1];i&&a.init(i,t.find("input"))}}}]),angular.module("ui.bootstrap.typeahead",["ui.bootstrap.debounce","ui.bootstrap.position"]).factory("uibTypeaheadParser",["$parse",function(e){var t=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?\s+for\s+(?:([\$\w][\$\w\d]*))\s+in\s+([\s\S]+?)$/;return{parse:function(n){var o=n.match(t);if(!o)throw new Error('Expected typeahead specification in form of "_modelValue_ (as _label_)? for _item_ in _collection_" but got "'+n+'".');return{itemName:o[3],source:e(o[4]),viewMapper:e(o[2]||o[1]),modelMapper:e(o[1])}}}}]).controller("UibTypeaheadController",["$scope","$element","$attrs","$compile","$parse","$q","$timeout","$document","$window","$rootScope","$$debounce","$uibPosition","uibTypeaheadParser",function(e,t,n,o,a,i,r,l,s,p,u,c,d){function m(){Y.moveInProgress||(Y.moveInProgress=!0,Y.$digest()),Z()}function f(){Y.position=E?c.offset(t):c.position(t),Y.position.top+=t.prop("offsetHeight")}var h,g,b=[9,13,27,38,40],v=200,w=e.$eval(n.typeaheadMinLength);w||0===w||(w=1),e.$watch(n.typeaheadMinLength,function(e){w=e||0===e?e:1});var y=e.$eval(n.typeaheadWaitMs)||0,$=e.$eval(n.typeaheadEditable)!==!1;e.$watch(n.typeaheadEditable,function(e){$=e!==!1});var k,D,M=a(n.typeaheadLoading).assign||angular.noop,x=n.typeaheadShouldSelect?a(n.typeaheadShouldSelect):function(e,t){var n=t.$event;return 13===n.which||9===n.which},T=a(n.typeaheadOnSelect),C=angular.isDefined(n.typeaheadSelectOnBlur)?e.$eval(n.typeaheadSelectOnBlur):!1,O=a(n.typeaheadNoResults).assign||angular.noop,S=n.typeaheadInputFormatter?a(n.typeaheadInputFormatter):void 0,E=n.typeaheadAppendToBody?e.$eval(n.typeaheadAppendToBody):!1,P=n.typeaheadAppendTo?e.$eval(n.typeaheadAppendTo):null,I=e.$eval(n.typeaheadFocusFirst)!==!1,U=n.typeaheadSelectOnExact?e.$eval(n.typeaheadSelectOnExact):!1,A=a(n.typeaheadIsOpen).assign||angular.noop,F=e.$eval(n.typeaheadShowHint)||!1,N=a(n.ngModel),H=a(n.ngModel+"($$$p)"),R=function(t,n){return angular.isFunction(N(e))&&g&&g.$options&&g.$options.getterSetter?H(t,{$$$p:n}):N.assign(t,n)},V=d.parse(n.uibTypeahead),Y=e.$new(),q=e.$on("$destroy",function(){Y.$destroy()});Y.$on("$destroy",q);var z="typeahead-"+Y.$id+"-"+Math.floor(1e4*Math.random());t.attr({"aria-autocomplete":"list","aria-expanded":!1,"aria-owns":z});var W,j;F&&(W=angular.element("<div></div>"),W.css("position","relative"),t.after(W),j=t.clone(),j.attr("placeholder",""),j.attr("tabindex","-1"),j.val(""),j.css({position:"absolute",top:"0px",left:"0px","border-color":"transparent","box-shadow":"none",opacity:1,background:"none 0% 0% / auto repeat scroll padding-box border-box rgb(255, 255, 255)",color:"#999"}),t.css({position:"relative","vertical-align":"top","background-color":"transparent"}),W.append(j),j.after(t));var B=angular.element("<div uib-typeahead-popup></div>");B.attr({id:z,matches:"matches",active:"activeIdx",select:"select(activeIdx, evt)","move-in-progress":"moveInProgress",query:"query",position:"position","assign-is-open":"assignIsOpen(isOpen)",debounce:"debounceUpdate"}),angular.isDefined(n.typeaheadTemplateUrl)&&B.attr("template-url",n.typeaheadTemplateUrl),angular.isDefined(n.typeaheadPopupTemplateUrl)&&B.attr("popup-template-url",n.typeaheadPopupTemplateUrl);var L=function(){F&&j.val("")},_=function(){Y.matches=[],Y.activeIdx=-1,t.attr("aria-expanded",!1),L()},G=function(e){return z+"-option-"+e};Y.$watch("activeIdx",function(e){0>e?t.removeAttr("aria-activedescendant"):t.attr("aria-activedescendant",G(e))});var K=function(e,t){return Y.matches.length>t&&e?e.toUpperCase()===Y.matches[t].label.toUpperCase():!1},X=function(n,o){var a={$viewValue:n};M(e,!0),O(e,!1),i.when(V.source(e,a)).then(function(i){var r=n===h.$viewValue;if(r&&k)if(i&&i.length>0){Y.activeIdx=I?0:-1,O(e,!1),Y.matches.length=0;for(var l=0;l<i.length;l++)a[V.itemName]=i[l],Y.matches.push({id:G(l),label:V.viewMapper(Y,a),model:i[l]});if(Y.query=n,f(),t.attr("aria-expanded",!0),U&&1===Y.matches.length&&K(n,0)&&(angular.isNumber(Y.debounceUpdate)||angular.isObject(Y.debounceUpdate)?u(function(){Y.select(0,o)},angular.isNumber(Y.debounceUpdate)?Y.debounceUpdate:Y.debounceUpdate["default"]):Y.select(0,o)),F){var s=Y.matches[0].label;j.val(angular.isString(n)&&n.length>0&&s.slice(0,n.length).toUpperCase()===n.toUpperCase()?n+s.slice(n.length):"")}}else _(),O(e,!0);r&&M(e,!1)},function(){_(),M(e,!1),O(e,!0)})};E&&(angular.element(s).on("resize",m),l.find("body").on("scroll",m));var Z=u(function(){Y.matches.length&&f(),Y.moveInProgress=!1},v);Y.moveInProgress=!1,Y.query=void 0;var J,Q=function(e){J=r(function(){X(e)},y)},et=function(){J&&r.cancel(J)};_(),Y.assignIsOpen=function(t){A(e,t)},Y.select=function(o,a){var i,l,s={};D=!0,s[V.itemName]=l=Y.matches[o].model,i=V.modelMapper(e,s),R(e,i),h.$setValidity("editable",!0),h.$setValidity("parse",!0),T(e,{$item:l,$model:i,$label:V.viewMapper(e,s),$event:a}),_(),Y.$eval(n.typeaheadFocusOnSelect)!==!1&&r(function(){t[0].focus()},0,!1)},t.on("keydown",function(t){if(0!==Y.matches.length&&-1!==b.indexOf(t.which)){var n=x(e,{$event:t});if(-1===Y.activeIdx&&n||9===t.which&&t.shiftKey)return _(),void Y.$digest();t.preventDefault();var o;switch(t.which){case 27:t.stopPropagation(),_(),e.$digest();break;case 38:Y.activeIdx=(Y.activeIdx>0?Y.activeIdx:Y.matches.length)-1,Y.$digest(),o=B.find("li")[Y.activeIdx],o.parentNode.scrollTop=o.offsetTop;break;case 40:Y.activeIdx=(Y.activeIdx+1)%Y.matches.length,Y.$digest(),o=B.find("li")[Y.activeIdx],o.parentNode.scrollTop=o.offsetTop;break;default:n&&Y.$apply(function(){angular.isNumber(Y.debounceUpdate)||angular.isObject(Y.debounceUpdate)?u(function(){Y.select(Y.activeIdx,t)},angular.isNumber(Y.debounceUpdate)?Y.debounceUpdate:Y.debounceUpdate["default"]):Y.select(Y.activeIdx,t)})}}}),t.bind("focus",function(e){k=!0,0!==w||h.$viewValue||r(function(){X(h.$viewValue,e)},0)}),t.bind("blur",function(e){C&&Y.matches.length&&-1!==Y.activeIdx&&!D&&(D=!0,Y.$apply(function(){angular.isObject(Y.debounceUpdate)&&angular.isNumber(Y.debounceUpdate.blur)?u(function(){Y.select(Y.activeIdx,e)},Y.debounceUpdate.blur):Y.select(Y.activeIdx,e)})),!$&&h.$error.editable&&(h.$setViewValue(),h.$setValidity("editable",!0),h.$setValidity("parse",!0),t.val("")),k=!1,D=!1});var tt=function(n){t[0]!==n.target&&3!==n.which&&0!==Y.matches.length&&(_(),p.$$phase||e.$digest())};l.on("click",tt),e.$on("$destroy",function(){l.off("click",tt),(E||P)&&nt.remove(),E&&(angular.element(s).off("resize",m),l.find("body").off("scroll",m)),B.remove(),F&&W.remove()});var nt=o(B)(Y);E?l.find("body").append(nt):P?angular.element(P).eq(0).append(nt):t.after(nt),this.init=function(t,n){h=t,g=n,Y.debounceUpdate=h.$options&&a(h.$options.debounce)(e),h.$parsers.unshift(function(t){return k=!0,0===w||t&&t.length>=w?y>0?(et(),Q(t)):X(t):(M(e,!1),et(),_()),$?t:t?void h.$setValidity("editable",!1):(h.$setValidity("editable",!0),null)}),h.$formatters.push(function(t){var n,o,a={};return $||h.$setValidity("editable",!0),S?(a.$model=t,S(e,a)):(a[V.itemName]=t,n=V.viewMapper(e,a),a[V.itemName]=void 0,o=V.viewMapper(e,a),n!==o?n:t)})}}]).directive("uibTypeahead",function(){return{controller:"UibTypeaheadController",require:["ngModel","^?ngModelOptions","uibTypeahead"],link:function(e,t,n,o){o[2].init(o[0],o[1])}}}).directive("uibTypeaheadPopup",["$$debounce",function(e){return{scope:{matches:"=",query:"=",active:"=",position:"&",moveInProgress:"=",select:"&",assignIsOpen:"&",debounce:"&"},replace:!0,templateUrl:function(e,t){return t.popupTemplateUrl||"uib/template/typeahead/typeahead-popup.html"},link:function(t,n,o){t.templateUrl=o.templateUrl,t.isOpen=function(){var e=t.matches.length>0;return t.assignIsOpen({isOpen:e}),e},t.isActive=function(e){return t.active===e},t.selectActive=function(e){t.active=e},t.selectMatch=function(n,o){var a=t.debounce();angular.isNumber(a)||angular.isObject(a)?e(function(){t.select({activeIdx:n,evt:o})},angular.isNumber(a)?a:a["default"]):t.select({activeIdx:n,evt:o})}}}}]).directive("uibTypeaheadMatch",["$templateRequest","$compile","$parse",function(e,t,n){return{scope:{index:"=",match:"=",query:"="},link:function(o,a,i){var r=n(i.templateUrl)(o.$parent)||"uib/template/typeahead/typeahead-match.html";e(r).then(function(e){var n=angular.element(e.trim());a.replaceWith(n),t(n)(o)})}}}]).filter("uibTypeaheadHighlight",["$sce","$injector","$log",function(e,t,n){function o(e){return e.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")}function a(e){return/<.*>/g.test(e)}var i;return i=t.has("$sanitize"),function(t,r){return!i&&a(t)&&n.warn("Unsafe use of typeahead please use ngSanitize"),t=r?(""+t).replace(new RegExp(o(r),"gi"),"<strong>$&</strong>"):t,i||(t=e.trustAsHtml(t)),t}}]),angular.module("ui.bootstrap.debounce",[]).factory("$$debounce",["$timeout",function(e){return function(t,n){var o;return function(){var a=this,i=Array.prototype.slice.call(arguments);o&&e.cancel(o),o=e(function(){t.apply(a,i)},n)}}}]),angular.module("ui.bootstrap.progressbar",[]).constant("uibProgressConfig",{animate:!0,max:100}).controller("UibProgressController",["$scope","$attrs","uibProgressConfig",function(e,t,n){function o(){return angular.isDefined(e.maxParam)?e.maxParam:n.max}var a=this,i=angular.isDefined(t.animate)?e.$parent.$eval(t.animate):n.animate;this.bars=[],e.max=o(),this.addBar=function(e,t,n){i||t.css({transition:"none"}),this.bars.push(e),e.max=o(),e.title=n&&angular.isDefined(n.title)?n.title:"progressbar",e.$watch("value",function(){e.recalculatePercentage()}),e.recalculatePercentage=function(){var t=a.bars.reduce(function(e,t){return t.percent=+(100*t.value/t.max).toFixed(2),e+t.percent},0);t>100&&(e.percent-=t-100)},e.$on("$destroy",function(){t=null,a.removeBar(e)})},this.removeBar=function(e){this.bars.splice(this.bars.indexOf(e),1),this.bars.forEach(function(e){e.recalculatePercentage()})},e.$watch("maxParam",function(){a.bars.forEach(function(e){e.max=o(),e.recalculatePercentage()})})}]).directive("uibProgress",function(){return{replace:!0,transclude:!0,controller:"UibProgressController",require:"uibProgress",scope:{maxParam:"=?max"},templateUrl:"uib/template/progressbar/progress.html"}}).directive("uibBar",function(){return{replace:!0,transclude:!0,require:"^uibProgress",scope:{value:"=",type:"@"},templateUrl:"uib/template/progressbar/bar.html",link:function(e,t,n,o){o.addBar(e,t,n)}}}).directive("uibProgressbar",function(){return{replace:!0,transclude:!0,controller:"UibProgressController",scope:{value:"=",maxParam:"=?max",type:"@"},templateUrl:"uib/template/progressbar/progressbar.html",link:function(e,t,n,o){o.addBar(e,angular.element(t.children()[0]),{title:n.title})}}}),angular.module("ui.bootstrap.dropdown",["ui.bootstrap.position"]).constant("uibDropdownConfig",{appendToOpenClass:"uib-dropdown-open",openClass:"open"}).service("uibDropdownService",["$document","$rootScope",function(e,t){var n=null;this.open=function(t,i){n||(e.on("click",o),i.on("keydown",a)),n&&n!==t&&(n.isOpen=!1),n=t},this.close=function(t,i){n===t&&(n=null,e.off("click",o),i.off("keydown",a))};var o=function(e){if(n&&!(e&&"disabled"===n.getAutoClose()||e&&3===e.which)){var o=n.getToggleElement();if(!(e&&o&&o[0].contains(e.target))){var a=n.getDropdownElement();e&&"outsideClick"===n.getAutoClose()&&a&&a[0].contains(e.target)||(n.isOpen=!1,t.$$phase||n.$apply())}}},a=function(e){27===e.which?(e.stopPropagation(),n.focusToggleElement(),o()):n.isKeynavEnabled()&&-1!==[38,40].indexOf(e.which)&&n.isOpen&&(e.preventDefault(),e.stopPropagation(),n.focusDropdownEntry(e.which))}}]).controller("UibDropdownController",["$scope","$element","$attrs","$parse","uibDropdownConfig","uibDropdownService","$animate","$uibPosition","$document","$compile","$templateRequest",function(e,t,n,o,a,i,r,l,s,p,u){var c,d,m=this,f=e.$new(),h=a.appendToOpenClass,g=a.openClass,b=angular.noop,v=n.onToggle?o(n.onToggle):angular.noop,w=!1,y=null,$=!1,k=s.find("body");t.addClass("dropdown"),this.init=function(){if(n.isOpen&&(d=o(n.isOpen),b=d.assign,e.$watch(d,function(e){f.isOpen=!!e})),angular.isDefined(n.dropdownAppendTo)){var a=o(n.dropdownAppendTo)(f);a&&(y=angular.element(a))}w=angular.isDefined(n.dropdownAppendToBody),$=angular.isDefined(n.keyboardNav),w&&!y&&(y=k),y&&m.dropdownMenu&&(y.append(m.dropdownMenu),t.on("$destroy",function(){m.dropdownMenu.remove()}))},this.toggle=function(e){return f.isOpen=arguments.length?!!e:!f.isOpen,angular.isFunction(b)&&b(f,f.isOpen),f.isOpen},this.isOpen=function(){return f.isOpen},f.getToggleElement=function(){return m.toggleElement},f.getAutoClose=function(){return n.autoClose||"always"},f.getElement=function(){return t},f.isKeynavEnabled=function(){return $},f.focusDropdownEntry=function(e){var n=m.dropdownMenu?angular.element(m.dropdownMenu).find("a"):t.find("ul").eq(0).find("a");switch(e){case 40:m.selectedOption=angular.isNumber(m.selectedOption)?m.selectedOption===n.length-1?m.selectedOption:m.selectedOption+1:0;break;case 38:m.selectedOption=angular.isNumber(m.selectedOption)?0===m.selectedOption?0:m.selectedOption-1:n.length-1}n[m.selectedOption].focus()},f.getDropdownElement=function(){return m.dropdownMenu},f.focusToggleElement=function(){m.toggleElement&&m.toggleElement[0].focus()},f.$watch("isOpen",function(n,o){if(y&&m.dropdownMenu){var a,s,d,$=l.positionElements(t,m.dropdownMenu,"bottom-left",!0);if(a={top:$.top+"px",display:n?"block":"none"},s=m.dropdownMenu.hasClass("dropdown-menu-right"),s?(a.left="auto",d=l.scrollbarWidth(!0),a.right=window.innerWidth-d-($.left+t.prop("offsetWidth"))+"px"):(a.left=$.left+"px",a.right="auto"),!w){var k=l.offset(y);a.top=$.top-k.top+"px",s?a.right=window.innerWidth-($.left-k.left+t.prop("offsetWidth"))+"px":a.left=$.left-k.left+"px"}m.dropdownMenu.css(a)}var D=y?y:t,M=D.hasClass(y?h:g);if(M===!n&&r[n?"addClass":"removeClass"](D,y?h:g).then(function(){angular.isDefined(n)&&n!==o&&v(e,{open:!!n})}),n)m.dropdownMenuTemplateUrl&&u(m.dropdownMenuTemplateUrl).then(function(e){c=f.$new(),p(e.trim())(c,function(e){var t=e;m.dropdownMenu.replaceWith(t),m.dropdownMenu=t})}),f.focusToggleElement(),i.open(f,t);else{if(m.dropdownMenuTemplateUrl){c&&c.$destroy();var x=angular.element('<ul class="dropdown-menu"></ul>');m.dropdownMenu.replaceWith(x),m.dropdownMenu=x}i.close(f,t),m.selectedOption=null}angular.isFunction(b)&&b(e,n)})}]).directive("uibDropdown",function(){return{controller:"UibDropdownController",link:function(e,t,n,o){o.init()}}}).directive("uibDropdownMenu",function(){return{restrict:"A",require:"?^uibDropdown",link:function(e,t,n,o){if(o&&!angular.isDefined(n.dropdownNested)){t.addClass("dropdown-menu");var a=n.templateUrl;a&&(o.dropdownMenuTemplateUrl=a),o.dropdownMenu||(o.dropdownMenu=t)}}}}).directive("uibDropdownToggle",function(){return{require:"?^uibDropdown",link:function(e,t,n,o){if(o){t.addClass("dropdown-toggle"),o.toggleElement=t;var a=function(a){a.preventDefault(),t.hasClass("disabled")||n.disabled||e.$apply(function(){o.toggle()})};t.bind("click",a),t.attr({"aria-haspopup":!0,"aria-expanded":!1}),e.$watch(o.isOpen,function(e){t.attr("aria-expanded",!!e)}),e.$on("$destroy",function(){t.unbind("click",a)})}}}}),angular.module("ui.bootstrap.datepickerPopup",["ui.bootstrap.datepicker","ui.bootstrap.position"]).value("$datepickerPopupLiteralWarning",!0).constant("uibDatepickerPopupConfig",{altInputFormats:[],appendToBody:!1,clearText:"Clear",closeOnDateSelection:!0,closeText:"Done",currentText:"Today",datepickerPopup:"yyyy-MM-dd",datepickerPopupTemplateUrl:"uib/template/datepickerPopup/popup.html",datepickerTemplateUrl:"uib/template/datepicker/datepicker.html",html5Types:{date:"yyyy-MM-dd","datetime-local":"yyyy-MM-ddTHH:mm:ss.sss",month:"yyyy-MM"},onOpenFocus:!0,showButtonBar:!0,placement:"auto bottom-left"}).controller("UibDatepickerPopupController",["$scope","$element","$attrs","$compile","$log","$parse","$window","$document","$rootScope","$uibPosition","dateFilter","uibDateParser","uibDatepickerPopupConfig","$timeout","uibDatepickerConfig","$datepickerPopupLiteralWarning",function(e,t,n,o,a,i,r,l,s,p,u,c,d,m,f,h){function g(t){var n=c.parse(t,k,e.date);if(isNaN(n))for(var o=0;o<A.length;o++)if(n=c.parse(t,A[o],e.date),!isNaN(n))return n;return n}function b(e){if(angular.isNumber(e)&&(e=new Date(e)),!e)return null;if(angular.isDate(e)&&!isNaN(e))return e;if(angular.isString(e)){var t=g(e);if(!isNaN(t))return c.toTimezone(t,F)}return P.$options&&P.$options.allowInvalid?e:void 0}function v(e,t){var o=e||t;return n.ngRequired||o?(angular.isNumber(o)&&(o=new Date(o)),o?angular.isDate(o)&&!isNaN(o)?!0:angular.isString(o)?!isNaN(g(t)):!1:!0):!0}function w(n){if(e.isOpen||!e.disabled){var o=U[0],a=t[0].contains(n.target),i=void 0!==o.contains&&o.contains(n.target);!e.isOpen||a||i||e.$apply(function(){e.isOpen=!1})}}function y(n){27===n.which&&e.isOpen?(n.preventDefault(),n.stopPropagation(),e.$apply(function(){e.isOpen=!1}),t[0].focus()):40!==n.which||e.isOpen||(n.preventDefault(),n.stopPropagation(),e.$apply(function(){e.isOpen=!0}))}function $(){if(e.isOpen){var o=angular.element(U[0].querySelector(".uib-datepicker-popup")),a=n.popupPlacement?n.popupPlacement:d.placement,i=p.positionElements(t,o,a,M);o.css({top:i.top+"px",left:i.left+"px"}),o.hasClass("uib-position-measure")&&o.removeClass("uib-position-measure")}}var k,D,M,x,T,C,O,S,E,P,I,U,A,F,N=!1,H=[];this.init=function(a){if(P=a,I=a.$options,D=angular.isDefined(n.closeOnDateSelection)?e.$parent.$eval(n.closeOnDateSelection):d.closeOnDateSelection,M=angular.isDefined(n.datepickerAppendToBody)?e.$parent.$eval(n.datepickerAppendToBody):d.appendToBody,x=angular.isDefined(n.onOpenFocus)?e.$parent.$eval(n.onOpenFocus):d.onOpenFocus,T=angular.isDefined(n.datepickerPopupTemplateUrl)?n.datepickerPopupTemplateUrl:d.datepickerPopupTemplateUrl,C=angular.isDefined(n.datepickerTemplateUrl)?n.datepickerTemplateUrl:d.datepickerTemplateUrl,A=angular.isDefined(n.altInputFormats)?e.$parent.$eval(n.altInputFormats):d.altInputFormats,e.showButtonBar=angular.isDefined(n.showButtonBar)?e.$parent.$eval(n.showButtonBar):d.showButtonBar,d.html5Types[n.type]?(k=d.html5Types[n.type],N=!0):(k=n.uibDatepickerPopup||d.datepickerPopup,n.$observe("uibDatepickerPopup",function(e){var t=e||d.datepickerPopup;if(t!==k&&(k=t,P.$modelValue=null,!k))throw new Error("uibDatepickerPopup must have a date format specified.")})),!k)throw new Error("uibDatepickerPopup must have a date format specified.");if(N&&n.uibDatepickerPopup)throw new Error("HTML5 date input types do not support custom formats.");O=angular.element("<div uib-datepicker-popup-wrap><div uib-datepicker></div></div>"),I?(F=I.timezone,e.ngModelOptions=angular.copy(I),e.ngModelOptions.timezone=null,e.ngModelOptions.updateOnDefault===!0&&(e.ngModelOptions.updateOn=e.ngModelOptions.updateOn?e.ngModelOptions.updateOn+" default":"default"),O.attr("ng-model-options","ngModelOptions")):F=null,O.attr({"ng-model":"date","ng-change":"dateSelection(date)","template-url":T}),S=angular.element(O.children()[0]),S.attr("template-url",C),e.datepickerOptions||(e.datepickerOptions={}),N&&"month"===n.type&&(e.datepickerOptions.datepickerMode="month",e.datepickerOptions.minMode="month"),S.attr("datepicker-options","datepickerOptions"),N?P.$formatters.push(function(t){return e.date=c.fromTimezone(t,F),t}):(P.$$parserName="date",P.$validators.date=v,P.$parsers.unshift(b),P.$formatters.push(function(t){return P.$isEmpty(t)?(e.date=t,t):(angular.isNumber(t)&&(t=new Date(t)),e.date=c.fromTimezone(t,F),c.filter(e.date,k))})),P.$viewChangeListeners.push(function(){e.date=g(P.$viewValue)}),t.on("keydown",y),U=o(O)(e),O.remove(),M?l.find("body").append(U):t.after(U),e.$on("$destroy",function(){for(e.isOpen===!0&&(s.$$phase||e.$apply(function(){e.isOpen=!1})),U.remove(),t.off("keydown",y),l.off("click",w),E&&E.off("scroll",$),angular.element(r).off("resize",$);H.length;)H.shift()()})},e.getText=function(t){return e[t+"Text"]||d[t+"Text"]},e.isDisabled=function(t){"today"===t&&(t=c.fromTimezone(new Date,F));var n={};return angular.forEach(["minDate","maxDate"],function(t){e.datepickerOptions[t]?angular.isDate(e.datepickerOptions[t])?n[t]=c.fromTimezone(new Date(e.datepickerOptions[t]),F):(h&&a.warn("Literal date support has been deprecated, please switch to date object usage"),n[t]=new Date(u(e.datepickerOptions[t],"medium"))):n[t]=null}),e.datepickerOptions&&n.minDate&&e.compare(t,n.minDate)<0||n.maxDate&&e.compare(t,n.maxDate)>0},e.compare=function(e,t){return new Date(e.getFullYear(),e.getMonth(),e.getDate())-new Date(t.getFullYear(),t.getMonth(),t.getDate())},e.dateSelection=function(n){angular.isDefined(n)&&(e.date=n);var o=e.date?c.filter(e.date,k):null;t.val(o),P.$setViewValue(o),D&&(e.isOpen=!1,t[0].focus())},e.keydown=function(n){27===n.which&&(n.stopPropagation(),e.isOpen=!1,t[0].focus())},e.select=function(t,n){if(n.stopPropagation(),"today"===t){var o=new Date;angular.isDate(e.date)?(t=new Date(e.date),t.setFullYear(o.getFullYear(),o.getMonth(),o.getDate())):t=new Date(o.setHours(0,0,0,0))}e.dateSelection(t)},e.close=function(n){n.stopPropagation(),e.isOpen=!1,t[0].focus()},e.disabled=angular.isDefined(n.disabled)||!1,n.ngDisabled&&H.push(e.$parent.$watch(i(n.ngDisabled),function(t){e.disabled=t})),e.$watch("isOpen",function(o){o?e.disabled?e.isOpen=!1:m(function(){$(),x&&e.$broadcast("uib:datepicker.focus"),l.on("click",w);var o=n.popupPlacement?n.popupPlacement:d.placement;M||p.parsePlacement(o)[2]?(E=E||angular.element(p.scrollParent(t)),E&&E.on("scroll",$)):E=null,angular.element(r).on("resize",$)},0,!1):(l.off("click",w),E&&E.off("scroll",$),angular.element(r).off("resize",$))}),e.$on("uib:datepicker.mode",function(){m($,0,!1)})}]).directive("uibDatepickerPopup",function(){return{require:["ngModel","uibDatepickerPopup"],controller:"UibDatepickerPopupController",scope:{datepickerOptions:"=?",isOpen:"=?",currentText:"@",clearText:"@",closeText:"@"},link:function(e,t,n,o){var a=o[0],i=o[1];i.init(a)}}}).directive("uibDatepickerPopupWrap",function(){return{replace:!0,transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepickerPopup/popup.html"}}}),angular.module("ui.bootstrap.datepicker",["ui.bootstrap.dateparser","ui.bootstrap.isClass"]).value("$datepickerSuppressError",!1).value("$datepickerLiteralWarning",!0).constant("uibDatepickerConfig",{datepickerMode:"day",formatDay:"dd",formatMonth:"MMMM",formatYear:"yyyy",formatDayHeader:"EEE",formatDayTitle:"MMMM yyyy",formatMonthTitle:"yyyy",maxDate:null,maxMode:"year",minDate:null,minMode:"day",ngModelOptions:{},shortcutPropagation:!1,showWeeks:!0,yearColumns:5,yearRows:4}).controller("UibDatepickerController",["$scope","$attrs","$parse","$interpolate","$locale","$log","dateFilter","uibDatepickerConfig","$datepickerLiteralWarning","$datepickerSuppressError","uibDateParser",function(e,t,n,o,a,i,r,l,s,p,u){function c(t){e.datepickerMode=t,e.datepickerOptions.datepickerMode=t}{var d=this,m={$setViewValue:angular.noop},f={},h=[];!!t.datepickerOptions}e.datepickerOptions||(e.datepickerOptions={}),this.modes=["day","month","year"],["customClass","dateDisabled","datepickerMode","formatDay","formatDayHeader","formatDayTitle","formatMonth","formatMonthTitle","formatYear","maxDate","maxMode","minDate","minMode","showWeeks","shortcutPropagation","startingDay","yearColumns","yearRows"].forEach(function(t){switch(t){case"customClass":case"dateDisabled":e[t]=e.datepickerOptions[t]||angular.noop;break;case"datepickerMode":e.datepickerMode=angular.isDefined(e.datepickerOptions.datepickerMode)?e.datepickerOptions.datepickerMode:l.datepickerMode;break;case"formatDay":case"formatDayHeader":case"formatDayTitle":case"formatMonth":case"formatMonthTitle":case"formatYear":d[t]=angular.isDefined(e.datepickerOptions[t])?o(e.datepickerOptions[t])(e.$parent):l[t];break;case"showWeeks":case"shortcutPropagation":case"yearColumns":case"yearRows":d[t]=angular.isDefined(e.datepickerOptions[t])?e.datepickerOptions[t]:l[t];break;case"startingDay":d.startingDay=angular.isDefined(e.datepickerOptions.startingDay)?e.datepickerOptions.startingDay:angular.isNumber(l.startingDay)?l.startingDay:(a.DATETIME_FORMATS.FIRSTDAYOFWEEK+8)%7;break;case"maxDate":case"minDate":e.$watch("datepickerOptions."+t,function(e){e?angular.isDate(e)?d[t]=u.fromTimezone(new Date(e),f.timezone):(s&&i.warn("Literal date support has been deprecated, please switch to date object usage"),d[t]=new Date(r(e,"medium"))):d[t]=l[t]?u.fromTimezone(new Date(l[t]),f.timezone):null,d.refreshView()
});break;case"maxMode":case"minMode":e.datepickerOptions[t]?e.$watch(function(){return e.datepickerOptions[t]},function(n){d[t]=e[t]=angular.isDefined(n)?n:datepickerOptions[t],("minMode"===t&&d.modes.indexOf(e.datepickerOptions.datepickerMode)<d.modes.indexOf(d[t])||"maxMode"===t&&d.modes.indexOf(e.datepickerOptions.datepickerMode)>d.modes.indexOf(d[t]))&&(e.datepickerMode=d[t],e.datepickerOptions.datepickerMode=d[t])}):d[t]=e[t]=l[t]||null}}),e.uniqueId="datepicker-"+e.$id+"-"+Math.floor(1e4*Math.random()),e.disabled=angular.isDefined(t.disabled)||!1,angular.isDefined(t.ngDisabled)&&h.push(e.$parent.$watch(t.ngDisabled,function(t){e.disabled=t,d.refreshView()})),e.isActive=function(t){return 0===d.compare(t.date,d.activeDate)?(e.activeDateId=t.uid,!0):!1},this.init=function(t){m=t,f=t.$options||l.ngModelOptions,e.datepickerOptions.initDate?(d.activeDate=u.fromTimezone(e.datepickerOptions.initDate,f.timezone)||new Date,e.$watch("datepickerOptions.initDate",function(e){e&&(m.$isEmpty(m.$modelValue)||m.$invalid)&&(d.activeDate=u.fromTimezone(e,f.timezone),d.refreshView())})):d.activeDate=new Date;var n=m.$modelValue?new Date(m.$modelValue):new Date;this.activeDate=isNaN(n)?u.fromTimezone(new Date,f.timezone):u.fromTimezone(n,f.timezone),m.$render=function(){d.render()}},this.render=function(){if(m.$viewValue){var e=new Date(m.$viewValue),t=!isNaN(e);t?this.activeDate=u.fromTimezone(e,f.timezone):p||i.error('Datepicker directive: "ng-model" value must be a Date object')}this.refreshView()},this.refreshView=function(){if(this.element){e.selectedDt=null,this._refreshView(),e.activeDt&&(e.activeDateId=e.activeDt.uid);var t=m.$viewValue?new Date(m.$viewValue):null;t=u.fromTimezone(t,f.timezone),m.$setValidity("dateDisabled",!t||this.element&&!this.isDisabled(t))}},this.createDateObject=function(t,n){var o=m.$viewValue?new Date(m.$viewValue):null;o=u.fromTimezone(o,f.timezone);var a=new Date;a=u.fromTimezone(a,f.timezone);var i=this.compare(t,a),r={date:t,label:u.filter(t,n),selected:o&&0===this.compare(t,o),disabled:this.isDisabled(t),past:0>i,current:0===i,future:i>0,customClass:this.customClass(t)||null};return o&&0===this.compare(t,o)&&(e.selectedDt=r),d.activeDate&&0===this.compare(r.date,d.activeDate)&&(e.activeDt=r),r},this.isDisabled=function(t){return e.disabled||this.minDate&&this.compare(t,this.minDate)<0||this.maxDate&&this.compare(t,this.maxDate)>0||e.dateDisabled&&e.dateDisabled({date:t,mode:e.datepickerMode})},this.customClass=function(t){return e.customClass({date:t,mode:e.datepickerMode})},this.split=function(e,t){for(var n=[];e.length>0;)n.push(e.splice(0,t));return n},e.select=function(t){if(e.datepickerMode===d.minMode){var n=m.$viewValue?u.fromTimezone(new Date(m.$viewValue),f.timezone):new Date(0,0,0,0,0,0,0);n.setFullYear(t.getFullYear(),t.getMonth(),t.getDate()),n=u.toTimezone(n,f.timezone),m.$setViewValue(n),m.$render()}else d.activeDate=t,c(d.modes[d.modes.indexOf(e.datepickerMode)-1]),e.$emit("uib:datepicker.mode");e.$broadcast("uib:datepicker.focus")},e.move=function(e){var t=d.activeDate.getFullYear()+e*(d.step.years||0),n=d.activeDate.getMonth()+e*(d.step.months||0);d.activeDate.setFullYear(t,n,1),d.refreshView()},e.toggleMode=function(t){t=t||1,e.datepickerMode===d.maxMode&&1===t||e.datepickerMode===d.minMode&&-1===t||(c(d.modes[d.modes.indexOf(e.datepickerMode)+t]),e.$emit("uib:datepicker.mode"))},e.keys={13:"enter",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down"};var g=function(){d.element[0].focus()};e.$on("uib:datepicker.focus",g),e.keydown=function(t){var n=e.keys[t.which];if(n&&!t.shiftKey&&!t.altKey&&!e.disabled)if(t.preventDefault(),d.shortcutPropagation||t.stopPropagation(),"enter"===n||"space"===n){if(d.isDisabled(d.activeDate))return;e.select(d.activeDate)}else!t.ctrlKey||"up"!==n&&"down"!==n?(d.handleKeyDown(n,t),d.refreshView()):e.toggleMode("up"===n?1:-1)},e.$on("$destroy",function(){for(;h.length;)h.shift()()})}]).controller("UibDaypickerController",["$scope","$element","dateFilter",function(e,t,n){function o(e,t){return 1!==t||e%4!==0||e%100===0&&e%400!==0?i[t]:29}function a(e){var t=new Date(e);t.setDate(t.getDate()+4-(t.getDay()||7));var n=t.getTime();return t.setMonth(0),t.setDate(1),Math.floor(Math.round((n-t)/864e5)/7)+1}var i=[31,28,31,30,31,30,31,31,30,31,30,31];this.step={months:1},this.element=t,this.init=function(t){angular.extend(t,this),e.showWeeks=t.showWeeks,t.refreshView()},this.getDates=function(e,t){for(var n,o=new Array(t),a=new Date(e),i=0;t>i;)n=new Date(a),o[i++]=n,a.setDate(a.getDate()+1);return o},this._refreshView=function(){var t=this.activeDate.getFullYear(),o=this.activeDate.getMonth(),i=new Date(this.activeDate);i.setFullYear(t,o,1);var r=this.startingDay-i.getDay(),l=r>0?7-r:-r,s=new Date(i);l>0&&s.setDate(-l+1);for(var p=this.getDates(s,42),u=0;42>u;u++)p[u]=angular.extend(this.createDateObject(p[u],this.formatDay),{secondary:p[u].getMonth()!==o,uid:e.uniqueId+"-"+u});e.labels=new Array(7);for(var c=0;7>c;c++)e.labels[c]={abbr:n(p[c].date,this.formatDayHeader),full:n(p[c].date,"EEEE")};if(e.title=n(this.activeDate,this.formatDayTitle),e.rows=this.split(p,7),e.showWeeks){e.weekNumbers=[];for(var d=(11-this.startingDay)%7,m=e.rows.length,f=0;m>f;f++)e.weekNumbers.push(a(e.rows[f][d].date))}},this.compare=function(e,t){var n=new Date(e.getFullYear(),e.getMonth(),e.getDate()),o=new Date(t.getFullYear(),t.getMonth(),t.getDate());return n.setFullYear(e.getFullYear()),o.setFullYear(t.getFullYear()),n-o},this.handleKeyDown=function(e){var t=this.activeDate.getDate();if("left"===e)t-=1;else if("up"===e)t-=7;else if("right"===e)t+=1;else if("down"===e)t+=7;else if("pageup"===e||"pagedown"===e){var n=this.activeDate.getMonth()+("pageup"===e?-1:1);this.activeDate.setMonth(n,1),t=Math.min(o(this.activeDate.getFullYear(),this.activeDate.getMonth()),t)}else"home"===e?t=1:"end"===e&&(t=o(this.activeDate.getFullYear(),this.activeDate.getMonth()));this.activeDate.setDate(t)}}]).controller("UibMonthpickerController",["$scope","$element","dateFilter",function(e,t,n){this.step={years:1},this.element=t,this.init=function(e){angular.extend(e,this),e.refreshView()},this._refreshView=function(){for(var t,o=new Array(12),a=this.activeDate.getFullYear(),i=0;12>i;i++)t=new Date(this.activeDate),t.setFullYear(a,i,1),o[i]=angular.extend(this.createDateObject(t,this.formatMonth),{uid:e.uniqueId+"-"+i});e.title=n(this.activeDate,this.formatMonthTitle),e.rows=this.split(o,3)},this.compare=function(e,t){var n=new Date(e.getFullYear(),e.getMonth()),o=new Date(t.getFullYear(),t.getMonth());return n.setFullYear(e.getFullYear()),o.setFullYear(t.getFullYear()),n-o},this.handleKeyDown=function(e){var t=this.activeDate.getMonth();if("left"===e)t-=1;else if("up"===e)t-=3;else if("right"===e)t+=1;else if("down"===e)t+=3;else if("pageup"===e||"pagedown"===e){var n=this.activeDate.getFullYear()+("pageup"===e?-1:1);this.activeDate.setFullYear(n)}else"home"===e?t=0:"end"===e&&(t=11);this.activeDate.setMonth(t)}}]).controller("UibYearpickerController",["$scope","$element","dateFilter",function(e,t){function n(e){return parseInt((e-1)/a,10)*a+1}var o,a;this.element=t,this.yearpickerInit=function(){o=this.yearColumns,a=this.yearRows*o,this.step={years:a}},this._refreshView=function(){for(var t,i=new Array(a),r=0,l=n(this.activeDate.getFullYear());a>r;r++)t=new Date(this.activeDate),t.setFullYear(l+r,0,1),i[r]=angular.extend(this.createDateObject(t,this.formatYear),{uid:e.uniqueId+"-"+r});e.title=[i[0].label,i[a-1].label].join(" - "),e.rows=this.split(i,o),e.columns=o},this.compare=function(e,t){return e.getFullYear()-t.getFullYear()},this.handleKeyDown=function(e){var t=this.activeDate.getFullYear();"left"===e?t-=1:"up"===e?t-=o:"right"===e?t+=1:"down"===e?t+=o:"pageup"===e||"pagedown"===e?t+=("pageup"===e?-1:1)*a:"home"===e?t=n(this.activeDate.getFullYear()):"end"===e&&(t=n(this.activeDate.getFullYear())+a-1),this.activeDate.setFullYear(t)}}]).directive("uibDatepicker",function(){return{replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/datepicker.html"},scope:{datepickerOptions:"=?"},require:["uibDatepicker","^ngModel"],controller:"UibDatepickerController",controllerAs:"datepicker",link:function(e,t,n,o){var a=o[0],i=o[1];a.init(i)}}}).directive("uibDaypicker",function(){return{replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/day.html"},require:["^uibDatepicker","uibDaypicker"],controller:"UibDaypickerController",link:function(e,t,n,o){var a=o[0],i=o[1];i.init(a)}}}).directive("uibMonthpicker",function(){return{replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/month.html"},require:["^uibDatepicker","uibMonthpicker"],controller:"UibMonthpickerController",link:function(e,t,n,o){var a=o[0],i=o[1];i.init(a)}}}).directive("uibYearpicker",function(){return{replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/year.html"},require:["^uibDatepicker","uibYearpicker"],controller:"UibYearpickerController",link:function(e,t,n,o){var a=o[0];angular.extend(a,o[1]),a.yearpickerInit(),a.refreshView()}}}),angular.module("ui.bootstrap.dateparser",[]).service("uibDateParser",["$log","$locale","dateFilter","orderByFilter",function(e,t,n,o){function a(e,t){var n=[],a=e.split(""),i=e.indexOf("'");if(i>-1){var r=!1;e=e.split("");for(var l=i;l<e.length;l++)r?("'"===e[l]&&(l+1<e.length&&"'"===e[l+1]?(e[l+1]="$",a[l+1]=""):(a[l]="",r=!1)),e[l]="$"):"'"===e[l]&&(e[l]="$",a[l]="",r=!0);e=e.join("")}return angular.forEach(m,function(o){var i=e.indexOf(o.key);if(i>-1){e=e.split(""),a[i]="("+o.regex+")",e[i]="$";for(var r=i+1,l=i+o.key.length;l>r;r++)a[r]="",e[r]="$";e=e.join(""),n.push({index:i,key:o.key,apply:o[t],matcher:o.regex})}}),{regex:new RegExp("^"+a.join("")+"$"),map:o(n,"index")}}function i(e,t,n){return 1>n?!1:1===t&&n>28?29===n&&(e%4===0&&e%100!==0||e%400===0):3===t||5===t||8===t||10===t?31>n:!0}function r(e){return parseInt(e,10)}function l(e,t){return e&&t?c(e,t):e}function s(e,t){return e&&t?c(e,t,!0):e}function p(e,t){e=e.replace(/:/g,"");var n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function u(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function c(e,t,n){n=n?-1:1;var o=e.getTimezoneOffset(),a=p(t,o);return u(e,n*(a-o))}var d,m,f=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g;this.init=function(){d=t.id,this.parsers={},this.formatters={},m=[{key:"yyyy",regex:"\\d{4}",apply:function(e){this.year=+e},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"yyyy")}},{key:"yy",regex:"\\d{2}",apply:function(e){e=+e,this.year=69>e?e+2e3:e+1900},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"yy")}},{key:"y",regex:"\\d{1,4}",apply:function(e){this.year=+e},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"y")}},{key:"M!",regex:"0?[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){var t=e.getMonth();return/^[0-9]$/.test(t)?n(e,"MM"):n(e,"M")}},{key:"MMMM",regex:t.DATETIME_FORMATS.MONTH.join("|"),apply:function(e){this.month=t.DATETIME_FORMATS.MONTH.indexOf(e)},formatter:function(e){return n(e,"MMMM")}},{key:"MMM",regex:t.DATETIME_FORMATS.SHORTMONTH.join("|"),apply:function(e){this.month=t.DATETIME_FORMATS.SHORTMONTH.indexOf(e)},formatter:function(e){return n(e,"MMM")}},{key:"MM",regex:"0[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){return n(e,"MM")}},{key:"M",regex:"[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){return n(e,"M")}},{key:"d!",regex:"[0-2]?[0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){var t=e.getDate();return/^[1-9]$/.test(t)?n(e,"dd"):n(e,"d")}},{key:"dd",regex:"[0-2][0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){return n(e,"dd")}},{key:"d",regex:"[1-2]?[0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){return n(e,"d")}},{key:"EEEE",regex:t.DATETIME_FORMATS.DAY.join("|"),formatter:function(e){return n(e,"EEEE")}},{key:"EEE",regex:t.DATETIME_FORMATS.SHORTDAY.join("|"),formatter:function(e){return n(e,"EEE")}},{key:"HH",regex:"(?:0|1)[0-9]|2[0-3]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"HH")}},{key:"hh",regex:"0[0-9]|1[0-2]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"hh")}},{key:"H",regex:"1?[0-9]|2[0-3]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"H")}},{key:"h",regex:"[0-9]|1[0-2]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"h")}},{key:"mm",regex:"[0-5][0-9]",apply:function(e){this.minutes=+e},formatter:function(e){return n(e,"mm")}},{key:"m",regex:"[0-9]|[1-5][0-9]",apply:function(e){this.minutes=+e},formatter:function(e){return n(e,"m")}},{key:"sss",regex:"[0-9][0-9][0-9]",apply:function(e){this.milliseconds=+e},formatter:function(e){return n(e,"sss")}},{key:"ss",regex:"[0-5][0-9]",apply:function(e){this.seconds=+e},formatter:function(e){return n(e,"ss")}},{key:"s",regex:"[0-9]|[1-5][0-9]",apply:function(e){this.seconds=+e},formatter:function(e){return n(e,"s")}},{key:"a",regex:t.DATETIME_FORMATS.AMPMS.join("|"),apply:function(e){12===this.hours&&(this.hours=0),"PM"===e&&(this.hours+=12)},formatter:function(e){return n(e,"a")}},{key:"Z",regex:"[+-]\\d{4}",apply:function(e){var t=e.match(/([+-])(\d{2})(\d{2})/),n=t[1],o=t[2],a=t[3];this.hours+=r(n+o),this.minutes+=r(n+a)},formatter:function(e){return n(e,"Z")}},{key:"ww",regex:"[0-4][0-9]|5[0-3]",formatter:function(e){return n(e,"ww")}},{key:"w",regex:"[0-9]|[1-4][0-9]|5[0-3]",formatter:function(e){return n(e,"w")}},{key:"GGGG",regex:t.DATETIME_FORMATS.ERANAMES.join("|").replace(/\s/g,"\\s"),formatter:function(e){return n(e,"GGGG")}},{key:"GGG",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"GGG")}},{key:"GG",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"GG")}},{key:"G",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"G")}}]},this.init(),this.filter=function(e,n){if(!angular.isDate(e)||isNaN(e)||!n)return"";n=t.DATETIME_FORMATS[n]||n,t.id!==d&&this.init(),this.formatters[n]||(this.formatters[n]=a(n,"formatter"));var o=this.formatters[n],i=o.map,r=n;return i.reduce(function(t,n,o){var a=r.match(new RegExp("(.*)"+n.key));a&&angular.isString(a[1])&&(t+=a[1],r=r.replace(a[1]+n.key,""));var l=o===i.length-1?r:"";return n.apply?t+n.apply.call(null,e)+l:t+l},"")},this.parse=function(n,o,r){if(!angular.isString(n)||!o)return n;o=t.DATETIME_FORMATS[o]||o,o=o.replace(f,"\\$&"),t.id!==d&&this.init(),this.parsers[o]||(this.parsers[o]=a(o,"apply"));var l=this.parsers[o],s=l.regex,p=l.map,u=n.match(s),c=!1;if(u&&u.length){var m,h;angular.isDate(r)&&!isNaN(r.getTime())?m={year:r.getFullYear(),month:r.getMonth(),date:r.getDate(),hours:r.getHours(),minutes:r.getMinutes(),seconds:r.getSeconds(),milliseconds:r.getMilliseconds()}:(r&&e.warn("dateparser:","baseDate is not a valid date"),m={year:1900,month:0,date:1,hours:0,minutes:0,seconds:0,milliseconds:0});for(var g=1,b=u.length;b>g;g++){var v=p[g-1];"Z"===v.matcher&&(c=!0),v.apply&&v.apply.call(m,u[g])}var w=c?Date.prototype.setUTCFullYear:Date.prototype.setFullYear,y=c?Date.prototype.setUTCHours:Date.prototype.setHours;return i(m.year,m.month,m.date)&&(!angular.isDate(r)||isNaN(r.getTime())||c?(h=new Date(0),w.call(h,m.year,m.month,m.date),y.call(h,m.hours||0,m.minutes||0,m.seconds||0,m.milliseconds||0)):(h=new Date(r),w.call(h,m.year,m.month,m.date),y.call(h,m.hours,m.minutes,m.seconds,m.milliseconds))),h}},this.toTimezone=l,this.fromTimezone=s,this.timezoneToOffset=p,this.addDateMinutes=u,this.convertTimezoneToLocal=c}]),angular.module("ui.bootstrap.isClass",[]).directive("uibIsClass",["$animate",function(e){var t=/^\s*([\s\S]+?)\s+on\s+([\s\S]+?)\s*$/,n=/^\s*([\s\S]+?)\s+for\s+([\s\S]+?)\s*$/;return{restrict:"A",compile:function(o,a){function i(e,t){s.push(e),p.push({scope:e,element:t}),f.forEach(function(t){r(t,e)}),e.$on("$destroy",l)}function r(t,o){var a=t.match(n),i=o.$eval(a[1]),r=a[2],l=u[t];if(!l){var s=function(t){var n=null;p.some(function(e){var o=e.scope.$eval(d);return o===t?(n=e,!0):void 0}),l.lastActivated!==n&&(l.lastActivated&&e.removeClass(l.lastActivated.element,i),n&&e.addClass(n.element,i),l.lastActivated=n)};u[t]=l={lastActivated:null,scope:o,watchFn:s,compareWithExp:r,watcher:o.$watch(r,s)}}l.watchFn(o.$eval(r))}function l(e){var t=e.targetScope,n=s.indexOf(t);if(s.splice(n,1),p.splice(n,1),s.length){var o=s[0];angular.forEach(u,function(e){e.scope===t&&(e.watcher=o.$watch(e.compareWithExp,e.watchFn),e.scope=o)})}else u={}}var s=[],p=[],u={},c=a.uibIsClass.match(t),d=c[2],m=c[1],f=m.split(",");return i}}}]),angular.module("uib/template/accordion/accordion-group.html",[]).run(["$templateCache",function(e){e.put("uib/template/accordion/accordion-group.html",'<div class="panel" ng-class="panelClass || \'panel-default\'">\n  <div role="tab" id="{{::headingId}}" aria-selected="{{isOpen}}" class="panel-heading" ng-keypress="toggleOpen($event)">\n    <h4 class="panel-title">\n      <a role="button" data-toggle="collapse" href aria-expanded="{{isOpen}}" aria-controls="{{::panelId}}" tabindex="0" class="accordion-toggle" ng-click="toggleOpen()" uib-accordion-transclude="heading"><span uib-accordion-header ng-class="{\'text-muted\': isDisabled}">{{heading}}</span></a>\n    </h4>\n  </div>\n  <div id="{{::panelId}}" aria-labelledby="{{::headingId}}" aria-hidden="{{!isOpen}}" role="tabpanel" class="panel-collapse collapse" uib-collapse="!isOpen">\n    <div class="panel-body" ng-transclude></div>\n  </div>\n</div>\n')}]),angular.module("uib/template/accordion/accordion.html",[]).run(["$templateCache",function(e){e.put("uib/template/accordion/accordion.html",'<div role="tablist" class="panel-group" ng-transclude></div>')}]),angular.module("uib/template/alert/alert.html",[]).run(["$templateCache",function(e){e.put("uib/template/alert/alert.html",'<div class="alert" ng-class="[\'alert-\' + (type || \'warning\'), closeable ? \'alert-dismissible\' : null]" role="alert">\n    <button ng-show="closeable" type="button" class="close" ng-click="close({$event: $event})">\n        <span aria-hidden="true">&times;</span>\n        <span class="sr-only">Close</span>\n    </button>\n    <div ng-transclude></div>\n</div>\n')}]),angular.module("uib/template/modal/backdrop.html",[]).run(["$templateCache",function(e){e.put("uib/template/modal/backdrop.html",'<div class="modal-backdrop"\n     uib-modal-animation-class="fade"\n     modal-in-class="in"\n     ng-style="{\'z-index\': 1040 + (index && 1 || 0) + index*10}"\n></div>\n')}]),angular.module("uib/template/modal/window.html",[]).run(["$templateCache",function(e){e.put("uib/template/modal/window.html",'<div modal-render="{{$isRendered}}" tabindex="-1" role="dialog" class="modal"\n    uib-modal-animation-class="fade"\n    modal-in-class="in"\n    ng-style="{\'z-index\': 1050 + index*10, display: \'block\'}">\n    <div class="modal-dialog {{size ? \'modal-\' + size : \'\'}}"><div class="modal-content" uib-modal-transclude></div></div>\n</div>\n')}]),angular.module("uib/template/tabs/tab.html",[]).run(["$templateCache",function(e){e.put("uib/template/tabs/tab.html",'<li ng-class="[{active: active, disabled: disabled}, classes]" class="uib-tab nav-item">\n  <a href ng-click="select($event)" class="nav-link" uib-tab-heading-transclude>{{heading}}</a>\n</li>\n')}]),angular.module("uib/template/tabs/tabset.html",[]).run(["$templateCache",function(e){e.put("uib/template/tabs/tabset.html",'<div>\n  <ul class="nav nav-{{tabset.type || \'tabs\'}}" ng-class="{\'nav-stacked\': vertical, \'nav-justified\': justified}" ng-transclude></ul>\n  <div class="tab-content">\n    <div class="tab-pane"\n         ng-repeat="tab in tabset.tabs"\n         ng-class="{active: tabset.active === tab.index}"\n         uib-tab-content-transclude="tab">\n    </div>\n  </div>\n</div>\n')}]),angular.module("uib/template/popover/popover-html.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover-html.html",'<div class="popover"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="arrow"></div>\n\n  <div class="popover-inner">\n      <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n      <div class="popover-content" ng-bind-html="contentExp()"></div>\n  </div>\n</div>\n')}]),angular.module("uib/template/popover/popover-template.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover-template.html",'<div class="popover"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="arrow"></div>\n\n  <div class="popover-inner">\n      <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n      <div class="popover-content"\n        uib-tooltip-template-transclude="contentExp()"\n        tooltip-template-transclude-scope="originScope()"></div>\n  </div>\n</div>\n')}]),angular.module("uib/template/popover/popover.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover.html",'<div class="popover"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="arrow"></div>\n\n  <div class="popover-inner">\n      <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n      <div class="popover-content" ng-bind="content"></div>\n  </div>\n</div>\n')}]),angular.module("uib/template/tooltip/tooltip-html-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-html-popup.html",'<div class="tooltip"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="tooltip-arrow"></div>\n  <div class="tooltip-inner" ng-bind-html="contentExp()"></div>\n</div>\n')}]),angular.module("uib/template/tooltip/tooltip-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-popup.html",'<div class="tooltip"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="tooltip-arrow"></div>\n  <div class="tooltip-inner" ng-bind="content"></div>\n</div>\n')}]),angular.module("uib/template/tooltip/tooltip-template-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-template-popup.html",'<div class="tooltip"\n  tooltip-animation-class="fade"\n  uib-tooltip-classes\n  ng-class="{ in: isOpen() }">\n  <div class="tooltip-arrow"></div>\n  <div class="tooltip-inner"\n    uib-tooltip-template-transclude="contentExp()"\n    tooltip-template-transclude-scope="originScope()"></div>\n</div>\n')}]),angular.module("uib/template/timepicker/timepicker.html",[]).run(["$templateCache",function(e){e.put("uib/template/timepicker/timepicker.html",'<table class="uib-timepicker">\n  <tbody>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-increment hours"><a ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" class="btn btn-link" ng-disabled="noIncrementHours()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td>&nbsp;</td>\n      <td class="uib-increment minutes"><a ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" class="btn btn-link" ng-disabled="noIncrementMinutes()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-increment seconds"><a ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" class="btn btn-link" ng-disabled="noIncrementSeconds()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n    <tr>\n      <td class="form-group uib-time hours" ng-class="{\'has-error\': invalidHours}">\n        <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementHours()" ng-blur="blur()">\n      </td>\n      <td class="uib-separator">:</td>\n      <td class="form-group uib-time minutes" ng-class="{\'has-error\': invalidMinutes}">\n        <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementMinutes()" ng-blur="blur()">\n      </td>\n      <td ng-show="showSeconds" class="uib-separator">:</td>\n      <td class="form-group uib-time seconds" ng-class="{\'has-error\': invalidSeconds}" ng-show="showSeconds">\n        <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center" ng-readonly="readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementSeconds()" ng-blur="blur()">\n      </td>\n      <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-default text-center" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="{{::tabindex}}">{{meridian}}</button></td>\n    </tr>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-decrement hours"><a ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" class="btn btn-link" ng-disabled="noDecrementHours()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td>&nbsp;</td>\n      <td class="uib-decrement minutes"><a ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" class="btn btn-link" ng-disabled="noDecrementMinutes()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-decrement seconds"><a ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" class="btn btn-link" ng-disabled="noDecrementSeconds()" tabindex="{{::tabindex}}"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/typeahead/typeahead-match.html",[]).run(["$templateCache",function(e){e.put("uib/template/typeahead/typeahead-match.html",'<a href\n   tabindex="-1"\n   ng-bind-html="match.label | uibTypeaheadHighlight:query"\n   ng-attr-title="{{match.label}}"></a>\n')}]),angular.module("uib/template/typeahead/typeahead-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/typeahead/typeahead-popup.html",'<ul class="dropdown-menu" ng-show="isOpen() && !moveInProgress" ng-style="{top: position().top+\'px\', left: position().left+\'px\'}" role="listbox" aria-hidden="{{!isOpen()}}">\n    <li ng-repeat="match in matches track by $index" ng-class="{active: isActive($index) }" ng-mouseenter="selectActive($index)" ng-click="selectMatch($index, $event)" role="option" id="{{::match.id}}">\n        <div uib-typeahead-match index="$index" match="match" query="query" template-url="templateUrl"></div>\n    </li>\n</ul>\n')}]),angular.module("uib/template/progressbar/bar.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/bar.html",'<div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n')}]),angular.module("uib/template/progressbar/progress.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/progress.html",'<div class="progress" ng-transclude aria-labelledby="{{::title}}"></div>')}]),angular.module("uib/template/progressbar/progressbar.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/progressbar.html",'<div class="progress">\n  <div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n</div>\n')}]),angular.module("uib/template/datepickerPopup/popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepickerPopup/popup.html",'<div>\n  <ul class="uib-datepicker-popup dropdown-menu uib-position-measure" dropdown-nested ng-if="isOpen" ng-keydown="keydown($event)" ng-click="$event.stopPropagation()">\n    <li ng-transclude></li>\n    <li ng-if="showButtonBar" class="uib-button-bar">\n      <span class="btn-group pull-left">\n        <button type="button" class="btn btn-sm btn-info uib-datepicker-current" ng-click="select(\'today\', $event)" ng-disabled="isDisabled(\'today\')">{{ getText(\'current\') }}</button>\n        <button type="button" class="btn btn-sm btn-danger uib-clear" ng-click="select(null, $event)">{{ getText(\'clear\') }}</button>\n      </span>\n      <button type="button" class="btn btn-sm btn-success pull-right uib-close" ng-click="close($event)">{{ getText(\'close\') }}</button>\n    </li>\n  </ul>\n</div>\n')}]),angular.module("uib/template/datepicker/datepicker.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/datepicker.html",'<div class="uib-datepicker" ng-switch="datepickerMode" role="application" ng-keydown="keydown($event)">\n  <uib-daypicker ng-switch-when="day" tabindex="0"></uib-daypicker>\n  <uib-monthpicker ng-switch-when="month" tabindex="0"></uib-monthpicker>\n  <uib-yearpicker ng-switch-when="year" tabindex="0"></uib-yearpicker>\n</div>\n')}]),angular.module("uib/template/datepicker/day.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/day.html",'<table class="uib-daypicker" role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-left"></i></button></th>\n      <th colspan="{{::5 + showWeeks}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-right"></i></button></th>\n    </tr>\n    <tr>\n      <th ng-if="showWeeks" class="text-center"></th>\n      <th ng-repeat="label in ::labels track by $index" class="text-center"><small aria-label="{{::label.full}}">{{::label.abbr}}</small></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-weeks" ng-repeat="row in rows track by $index">\n      <td ng-if="showWeeks" class="text-center h6"><em>{{ weekNumbers[$index] }}</em></td>\n      <td ng-repeat="dt in row" class="uib-day text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default btn-sm"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-muted\': dt.secondary, \'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/datepicker/month.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/month.html",'<table class="uib-monthpicker" role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-left"></i></button></th>\n      <th><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-right"></i></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-months" ng-repeat="row in rows track by $index">\n      <td ng-repeat="dt in row" class="uib-month text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')
}]),angular.module("uib/template/datepicker/year.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/year.html",'<table class="uib-yearpicker" role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-left"></i></button></th>\n      <th colspan="{{::columns - 2}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i class="glyphicon glyphicon-chevron-right"></i></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-years" ng-repeat="row in rows track by $index">\n      <td ng-repeat="dt in row" class="uib-year text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("ui.bootstrap.position").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibPositionCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-position-measure{display:block !important;visibility:hidden !important;position:absolute !important;top:-9999px !important;left:-9999px !important;}.uib-position-scrollbar-measure{position:absolute !important;top:-9999px !important;width:50px !important;height:50px !important;overflow:scroll !important;}.uib-position-body-scrollbar-measure{overflow:scroll !important;}</style>'),angular.$$uibPositionCss=!0}),angular.module("ui.bootstrap.tooltip").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTooltipCss&&angular.element(document).find("head").prepend('<style type="text/css">[uib-tooltip-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-bottom > .tooltip-arrow,[uib-popover-popup].popover.top-left > .arrow,[uib-popover-popup].popover.top-right > .arrow,[uib-popover-popup].popover.bottom-left > .arrow,[uib-popover-popup].popover.bottom-right > .arrow,[uib-popover-popup].popover.left-top > .arrow,[uib-popover-popup].popover.left-bottom > .arrow,[uib-popover-popup].popover.right-top > .arrow,[uib-popover-popup].popover.right-bottom > .arrow,[uib-popover-html-popup].popover.top-left > .arrow,[uib-popover-html-popup].popover.top-right > .arrow,[uib-popover-html-popup].popover.bottom-left > .arrow,[uib-popover-html-popup].popover.bottom-right > .arrow,[uib-popover-html-popup].popover.left-top > .arrow,[uib-popover-html-popup].popover.left-bottom > .arrow,[uib-popover-html-popup].popover.right-top > .arrow,[uib-popover-html-popup].popover.right-bottom > .arrow,[uib-popover-template-popup].popover.top-left > .arrow,[uib-popover-template-popup].popover.top-right > .arrow,[uib-popover-template-popup].popover.bottom-left > .arrow,[uib-popover-template-popup].popover.bottom-right > .arrow,[uib-popover-template-popup].popover.left-top > .arrow,[uib-popover-template-popup].popover.left-bottom > .arrow,[uib-popover-template-popup].popover.right-top > .arrow,[uib-popover-template-popup].popover.right-bottom > .arrow{top:auto;bottom:auto;left:auto;right:auto;margin:0;}[uib-popover-popup].popover,[uib-popover-html-popup].popover,[uib-popover-template-popup].popover{display:block !important;}</style>'),angular.$$uibTooltipCss=!0}),angular.module("ui.bootstrap.timepicker").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTimepickerCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-time input{width:50px;}</style>'),angular.$$uibTimepickerCss=!0}),angular.module("ui.bootstrap.typeahead").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTypeaheadCss&&angular.element(document).find("head").prepend('<style type="text/css">[uib-typeahead-popup].dropdown-menu{display:block;}</style>'),angular.$$uibTypeaheadCss=!0}),angular.module("ui.bootstrap.datepickerPopup").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibDatepickerpopupCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker-popup.dropdown-menu{display:block;float:none;margin:0;}.uib-button-bar{padding:10px 9px 2px;}</style>'),angular.$$uibDatepickerpopupCss=!0}),angular.module("ui.bootstrap.datepicker").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibDatepickerCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker .uib-title{width:100%;}.uib-day button,.uib-month button,.uib-year button{min-width:100%;}.uib-left,.uib-right{width:100%}</style>'),angular.$$uibDatepickerCss=!0});
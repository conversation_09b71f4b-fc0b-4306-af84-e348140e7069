const express = require('express');
const createProxyMiddleware = require('http-proxy-middleware');
const path = require('node:path');

const app = express();
const PORT = 8079;

app.use('/api', createProxyMiddleware({ target: 'http://localhost:3000', changeOrigin: true }));

app.use(express.static(path.join(__dirname, '.tmp/public')));

// For SPA: serve index.html for all other routes
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '.tmp/public', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`SPA served at http://localhost:${PORT}`);
});

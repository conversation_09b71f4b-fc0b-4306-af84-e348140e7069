const argv = require('optimist').argv;
const path  = require('path');

const isApp = path.basename(require.main.filename) === 'app.js';

let log = {
    level: 'verbose',
    filePath: '../logs/application1.log',
};

if (argv['enable-logging'] && parseInt(argv['enable-logging']) === 1) {
    if (argv['verbose-log-file-path']) {
        log.filePath =  argv['verbose-log-file-path'];
    }
} else if (process.env.ENABLE_LOGGING && parseInt(process.env.ENABLE_LOGGING) === 1) {
    if (process.env.VERBOSE_LOG_FILE_PATH) {
        log.filePath = process.env.VERBOSE_LOG_FILE_PATH;
    }
} else {
    //log.filePath = argv['verbose-log-file-path'] || process.env.VERBOSE_LOG_FILE_PATH;
}

const {
    pm_id = 0,
    name = isApp? 'App' : 'Scheduler',
} = process.env;

// https://github.com/winstonjs/winston-syslog/tree/1.2.6#usage
// log.syslogOptions = {
//     app_name: `${name} #${pm_id}`,
//     host: 'localhost',
//     port: 514,
//     path: '/dev/log',
//     // path: '/var/run/syslog',// OS X
//     protocol: 'unix',
//     // level: 'error',
//     // protocol: 'tcp4',
//     formatter: msg => msg.replace(/\n/g, '##'),
// };

// log.graylogOptions = {
//     name: 'Graylog',
//     level: 'error',
//     silent: true,
//     handleExceptions: true,
//     graylog: {
//         servers: [
//             { host: '127.0.0.1', port: 12201 },
//         ],
//         handleExceptions: true,
//         hostname: `${name} #${pm_id}`,
//         // facility: 'Node.js',
//         bufferSize: 1400,
//     },
//     staticMeta: {
//         env: argv.prod ? 'production' : 'development',
//     },
// };

module.exports = {
    log,
};

const utils = require('../api/lib/swUtils');
const EMAIL_PRIORITY = require('../api/constants/emails').PRIORITY;

const redisUrl = utils.getConnection('REDIS_URL');

const redis = utils.parseBullRedisCs(redisUrl);
const emailRedis = utils.parseBullRedisCs(utils.getConnection('EMAIL_REDIS_URL', redisUrl));
const { WORKERS_QUEUE_PREFIX } = require('../api/constants/workers-queue');
const BACKOFF_TIMES = [
    { amount: 10, unit: 'seconds' },
    { amount: 20, unit: 'seconds' },
    { amount: 30, unit: 'seconds' },
    { amount: 2, unit: 'minutes' },
    { amount: 30, unit: 'minutes' },
    { amount: 2, unit: 'hours' },
    { amount: 24, unit: 'hours' },
];

module.exports.redis_queue = {
    send_email: {
        redis: emailRedis,
        timeout: 5 * 1000,
        prefix: 'email-sender',
        defaultJobOptions: {
            backoff: {
                type: 'exponential',
                delay: 60000,
            }, //interval between retries
            attempts: 3,
            removeOnComplete: 100,
            removeOnFail: 1000,
            priority: EMAIL_PRIORITY.TRANSACTIONAL,
        },
    },
    auto_payout_process: {
        redis,
        prefix: 'auto-payouts-process-job',
        ttl: 10 * 1000,
        attempts: 3,
        limiter: {
            max: 5,
            duration: 5000,
            bounceBack: true // important
        },
        defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 1000
        }
    },
    workers_queue: {
        redis,
        prefix: WORKERS_QUEUE_PREFIX,
        defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 1000,
            backoff: {
                type: "exponential",
                delay: 30000
            },
            attempts: 5
        },
        concurrency: 50,
        settings: {
            backoffStrategies: {
                webhookBackoff: (attemptsMade) => {
                    const maxAttempts = BACKOFF_TIMES.length;
                    const backoffIndex = Math.min(attemptsMade, maxAttempts) - 1;
                    const backoffTime = BACKOFF_TIMES[backoffIndex];

                    let delay = 0;
                    switch (backoffTime.unit) {
                        case 'seconds':
                            delay = backoffTime.amount * 1000;
                            break;
                        case 'minutes':
                            delay = backoffTime.amount * 60 * 1000;
                            break;
                        case 'hours':
                            delay = backoffTime.amount * 60 * 60 * 1000;
                            break;
                        default:
                            delay = backoffTime.amount * 1000;
                    }

                    return delay;
                }
            }
        }
    }
};

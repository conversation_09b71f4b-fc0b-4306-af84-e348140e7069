module.exports = {
    
    friendlyName: 'UTC to local',
    description: 'Replace UTC to local',
    
    sync: true,
    
    inputs: {
        timestamp: {
            type: 'string',
            example: '2022-08-30T13:10:35.116Z',
            description: 'Current UTC timestamp',
            required: true
        }
    },
    
    exits: {},
    
    fn: function(inputs) {
        
        try {
            
            let ts = new Date(inputs.timestamp);
            return ts.toISOString().slice(0, 19).replace('T', ' ');
            
        } catch(err) {
            return null;
        }
        
    }
    
};


module.exports = {
    ENTRY_STATUSES: {
        DECLINED: 11,
        ACCEPTED: 12,
        PENDING: 13,
        WAITLIST: 14,
    },
    isValidEntryStatus(status) {
        return Object.values(this.ENTRY_STATUSES).includes(status);
    },
    PAYMENT_STATUSES: {
        NOT_PAID: 21,
        PAID: 22,
        PARTIAL: 23,
        PENDING: 24,
        DISPUTED: 26,
        /*
            NOTE: we set this status for those teams which were canceled from a received check
         */
        REFUNDED: 25,
    },
    USAV_SANC_BODY: {
        AAU: 1,
        JVA: 2,
        USAV: 3,
        OTHER: 7,
        NINE_MAN: 9,
        NONE: 100,
    },
    USAV_SEASONALITY: {
        LOCAL: 'local',
        FULL: 'full'
    },
    ROSTER_TEAM_GENDER: {
        FEMALE: 'female',
        MALE: 'male',
        COED: 'coed',
    }
};

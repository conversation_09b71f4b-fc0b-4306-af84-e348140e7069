module.exports = function(data, viewFilePath) {
  var statusCode = 201,
    res = this.res,
    req = res.req;

  var result = {
    status: statusCode
  };

  if (data) {
    result.data = data;
  }

  if (req.wantsJSON) {
    return res.status(result.status).json(result);
  }

  if (viewFilePath) {
    res.status(result.status);

    for (var key in result) {
      res.locals[key] = result[key];
    }

    if (viewFilePath) {
      res.render(viewFilePath, result, function(err) {
        if (err) {
          return res.status(result.status).json(result);
        }

        res.render('500', result);
      });
    }
  }

  return res.status(result.status).send(result);
}

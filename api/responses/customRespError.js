// covered 😄👍
module.exports = function customRespError (err, { status } = {}) {
    const req = LoggingService.collectInfo(this.req);

    if(_.isObject(err) && (err.validation || err.validationErrors || err.access_errors)) {
        loggers.debug_log.debug(err, req);
    }
    else {
        loggers.errors_log.error(err, req);
    }
    if(!err) return this.res.serverError();
    if(err instanceof Error) {
        if(!status || status === 500) {
            return this.res.serverError(err.message, {log: false});
        }
        else {
            return this.res.validation(err.message, status);
        }
    }
    if(err.validation) return this.res.validation(err.validation, status);
    if(err.validationErrors) return this.res.validationErrors(err.validationErrors, status);
    if(err.access_errors) return this.res.status(400).json({ access_errors: err.access_errors });
    if(err.message) return this.res.status(status || 400).json({ message: err.message });
    return this.res.serverError();
}

const nodeutil = require('util');

module.exports = function (errors) {
  var statusCode = 500,
    viewFilePath = '500',
    i, errorToLog, errorToJSON, res = this.res,
    req = res.req;

  var result = {
    status: statusCode
  };

  var errorsToDisplay = SailsUtilService.normalizeErrors(errors);
  for (i in errorsToDisplay) {

    if (errorsToDisplay[i].original) {
      errorToLog = nodeutil.inspect(errorsToDisplay[i].original);
    } else {
      errorToLog = errorsToDisplay[i].stack;
    }

    errorToJSON = errorsToDisplay[i].original || errorsToDisplay[i].message;
    errorsToDisplay[i] = errorToJSON;
  }

  result.errors = errorsToDisplay;

  if (sails.config.environment === 'development') {
    result.errorsRaw = errors;
  }

  if (req.wantsJSON) {
    return res.status(result.status).json(result);
  }

  res.status(result.status);

  for (var key in result) {
    res.locals[key] = result[key];
  }

  res.render(viewFilePath, result, function(err) {
    if (err) {
      return res.status(result.status).json(result);
    }

    res.render('500', result);
  });
};

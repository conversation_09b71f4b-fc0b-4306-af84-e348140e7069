module.exports = async function(items, executor, limit) {
    const threadCount = Math.min(items.length, limit);
    const itemIterator = _getItemIterator(items);
    let executing = true;
    const threadFunction = async () => {
        while(executing && itemIterator.isItemAvailable()) {
            const currentItem = itemIterator.getNextItem();
            try {
                await executor(currentItem);
            }
            catch(err) {
                executing = false;
                throw err;
            }
        }
    };
    await Promise.all(
        Array.from(
            {length: threadCount},
            () => threadFunction()
        )
    );
}

function _getItemIterator(items) {
    let itemPointer = 0;
    const isItemAvailable = () => {
        return itemPointer < items.length;
    };
    const getNextItem = () => {
        if(isItemAvailable()) {
            return items[itemPointer++];
        }
    };
    return {
        isItemAvailable,
        getNextItem,
    };
}

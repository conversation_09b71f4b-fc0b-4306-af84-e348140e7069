var LINE_BREAK = require('os').EOL,
    crypto = require('crypto'),
    MESSAGE_FORMAT_LONG = 
        '{eol}URL: {location}{eol}Message: {msg}{eol}Cause: {cause}{eol}User Agent: {agent}{eol}' + 
        'IP: {ip}{eol}Hash: {hash}{eol}User: {user}{eol}Stack: {stack}{eol}',
    LAST_MESSAGE_HASH = '';
module.exports = {
    logError: function (data) {    
        var msg = MESSAGE_FORMAT_LONG.format({
            eol: LINE_BREAK,
            location: data.location,
            msg: data.error,
            cause: data.cause,
            agent: data.userAgent,
            ip: data.ip,
            hash: (_.isEmpty(data.frontEndHash))?null:JSON.stringify(data.frontEndHash),
            stack: data.stacktrace,
            user: (_.isEmpty(data.user))?null:JSON.stringify(data.user)
        }),         
            currentHash = crypto.createHash('md5').update(msg).digest('hex');
  
        if(LAST_MESSAGE_HASH === currentHash) return;        
        LAST_MESSAGE_HASH = currentHash;
        switch(data.source) {
            case 'esw':
                return _logESWError(msg);
            default: 
                return _logSWError(msg);
        }
    }
}

function _logESWError (msg) {
    loggers.esw_client.error(msg);
}

function _logSWError (msg) {
    loggers.sw_client.error(msg);
}

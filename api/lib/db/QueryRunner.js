'use strict';
const EventEmitter = require('events');
const Cursor = require('pg-cursor');

// Will fail if table name contains "
const TABLE_NAME_REGEX = /^\s*(?:INSERT\s+INTO|UPDATE|DELETE\s+FROM)\s+(?:(?:"[^"]+"|[\w_]+)\.)?(?:"([^"]+)"|([\w_]+))/i;
const CTE_QUERY_REGEX = /^\s*(?:WITH)\s+/i;

class QueryRunner extends EventEmitter {
    get EVENT_AFTER_RUN_SQL() { return 'EVENT_AFTER_RUN_SQL'; }

    constructor (client, logger) {
        super();
        this.__client = client;
        this.__logger = logger;
    }

    query (a, b) {
        if (!this.__client) {
            return Promise.reject(new Error('The Client was released'));
        }

        let sql = this.__normalizeQuery(a, b);

        return this.__runSQL(sql);
    }

    async queryCursor (query, batchSize = 100, { client = this.__client } = {}) {
        // if([totalResultSize] % batchSize === 0) then for last batch empty array will be returned
        const sql = this.__normalizeQuery(query);
        const resultCursor = client.query(new Cursor(sql.text, sql.params));
        let hasMore = true;
        const nextResult = () => (
            new Promise(async (resolve, reject) => {
                resultCursor.read(batchSize, (err, rows) => {
                    if (err) {
                        hasMore = false;
                        return reject(err);
                    }
                    hasMore = rows.length === batchSize;
                    return resolve(rows);
                });
            })
        );
        const firstBatch = await nextResult();
        return (
            function*() {
                if(firstBatch.length === 0) {
                    return;
                }
                yield Promise.resolve(firstBatch);
                while (hasMore) {
                    yield nextResult();
                }
            }
        )();
    }

    release () {
        return this.__client.release();
    }


    __normalizeQuery (a, b) {
        let q = {};
        if(a.toSQL) {
            const sql = a.toSQL().toNative();
            a = sql.sql;
            b = sql.bindings;
        }

        a  		 = a.toParam ? a.toParam() : a;
        q.text   = a.text || a;
        q.params = a.values || ((Array.isArray(b)) ? b : null);

        return q;
    }

    __runSQL (sql) {
        let startTime = Date.now();

        return this.__client.query(sql.text, sql.params)
            .then((result) => {
                const duration = (Date.now() - startTime);
                const { command, rowCount } = result;
                let tableName;
                if(['INSERT', 'UPDATE', 'DELETE'].includes(command) && !CTE_QUERY_REGEX.test(sql.text)) {
                    const result = TABLE_NAME_REGEX.exec(sql.text);
                    tableName = result && (result[1] || result[2]);
                    if(!tableName) {
                        loggers.errors_log.error(`failed to parse table name in ${command} query:\n${sql.text}`);
                    }
                }
                this.emit(this.EVENT_AFTER_RUN_SQL, {
                    sql,
                    duration,
                    command,
                    rowCount,
                    tableName,
                });

                return result;
            })
            .catch(err => {
                this.__logger.error(sql.text, sql.params, err, {startTime});
                throw err;
            })
    }
}


module.exports = QueryRunner;

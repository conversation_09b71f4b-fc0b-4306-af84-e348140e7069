const moment = require('moment');

class SportEngineUtilsService {
    get MIN_USAV_AGE () {
        return 8;
    }

    get INVALID_NUMERIC_STATUS () {
        return '1';
    }

    get VALID_NUMERIC_STATUS () {
        return '2';
    }

    get SE_FIELDS () {
        return {
            FIRST: 'first_name',
            LAST: 'last_name',
            ORGANIZATION_CODE: 'membership_number',
            USAV_CODE: 'membership_number',
            MEMBERSHIP_NAME: 'membership_name',
            MEMBERSHIP_DEFINITION_ID: 'membership_definition_id',
            GENDER: 'gender',
            BIRTHDATE: 'date_of_birth',
            MEMBERSHIP_STATUS: 'eligibility_status',
            GRAD_YEAR: 'graduation_year',
            CLUB_CODE: 'affiliation_code',
            USAV_REGION: 'parent_affiliation_code',
            CLUB_ID: 'affiliation_org_id',
            MEMBERSHIP_START_DATE: 'start_date',
            MEMBERSHIP_END_DATE: 'end_date',
        }
    }

    get UM_FIELDS() {
        return {
            MEMBER_TYPE: 'member_type',
            MESSAGE: 'message',
        }
    }

    get MEMBER_TYPE () {
        return {
            ATHLETE: 'athlete',
            STAFF: 'staff',
        }
    }

    get TAG_FIELD () {
        return {
            TYPE: 'tag_option_type',
            VALUE: 'tag_value'
        }
    }

    get SEASONALITY_TAG () {
        return {
            OPTION: 'Seasonality',
            VALUE: 'Season',
            POSSIBLE_VALUES: {
                SEASON: 'season',
                FULL: 'full',
                ONE_EVENT: 'one event',
                OUTDOOR: 'outdoor',
                SUMMER: 'summer',
                REGIONAL: 'regional',
            },
        }
    }

    get SEASONALITY_FULL_TAGS() {
        return [
            this.SEASONALITY_TAG.POSSIBLE_VALUES.SEASON,
            this.SEASONALITY_TAG.POSSIBLE_VALUES.ONE_EVENT,
            this.SEASONALITY_TAG.POSSIBLE_VALUES.SUMMER,
        ]
    }

    get SEASONALITY_FULL_MEMBERSHIP_DEFINITION_IDS () {
        return [
            '11ef29cd-ab41-43c4-bfa7-72c4bd0db680'
        ]
    }

    get IMPORT_MODE () {
        return {
            DEFAULT: 'default',
            INSERT: 'insert'
        }
    }

    get TAG_NAME () {
        return {
            AGE_GROUP: 'Age Group',
            AGE: 'Age',
            SEASONALITY: 'Seasonality',
        }
    }

    get ELIGIBLE_MEMBER_STATUS () {
        return 'eligible';
    }

    get CANCELED_MEMBER_STATUS () {
        return 'canceled';
    }

    get PROFILE_COMPLETED_FIELD () {
        return 'profile_completed_at';
    }

    get FORBIDDEN_MEMBERSHIPS () {
        return [
            'USAV Administrator',
        ]
    }

    get SEASONALITY_ALLOWED_TO_BE_IMPORTED_AS_INCOMPLETE () {
        return [
            this.SEASONALITY_TAG.POSSIBLE_VALUES.ONE_EVENT
        ]
    }

    getMinAge (birthday) {

        if(!birthday) {
           return null;
        }

        const currentSeason = sails.config.sw_season.current;
        const bday = moment(birthday, 'YYYY-MM-DD');
        let USAVAge = currentSeason - bday.year();
        let birthdateInCurrentSeason = bday.clone().set('year', currentSeason);

        let seasonStart = moment().year(currentSeason).month(7 - 1).date(0);

        if (birthdateInCurrentSeason.isAfter(seasonStart)) {
            USAVAge--;
        }

        if (USAVAge < this.MIN_USAV_AGE) {
            USAVAge = this.MIN_USAV_AGE;
        }

        return USAVAge;

    }

    isSeasonalityFull(seasonality) {
        return this.SEASONALITY_FULL_TAGS.includes(seasonality);
    }

    isMembershipDefinitionHasFullSeasonality(membershipDefinitionId) {
        return this.SEASONALITY_FULL_MEMBERSHIP_DEFINITION_IDS.includes(membershipDefinitionId);
    }
}

module.exports = new SportEngineUtilsService();

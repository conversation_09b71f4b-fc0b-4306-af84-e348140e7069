module.exports = function (req, res, next) {
    
    const authorization = req.get('Authorization');
    if(!authorization) {
        res.status(401).json({ error: {
                messages: ['Not authorized'],
                properties: [
                    {
                        "property": "token",
                        "messages": [
                            "Authorization token not exists"
                        ]
                    }
                ]
            } });
        return;
    }
    
    if(authorization === sails.config.acs.apiKey) {
        next();
    } else {
        
        res.status(401).json({ error: {
                messages: ['Not authorized'],
                properties: [
                    {
                        "property": "token invalid",
                        "messages": [
                            "Authorization token invalid"
                        ]
                    }
                ]
            } });
        
    }
    
}

'use strict';

module.exports = function findEventOwnerID (req, res, next) {
    let eventID         = Number(req.param('event'));
    let officialID      = Number(req.session.passport.user.official_id);

    if(!eventID) {
        return res.status(400).json({error: 'No event selected'});
    }

    if(!officialID) {
        return next();
    }

    Db.query(
        `WITH ho_access AS (
            SELECT eo.event_official_id "id"
            FROM "event_official" eo
            WHERE eo.official_id = $2
                  AND eo.work_status = 'approved'
                  AND eo.head_official IS TRUE
                  AND eo.event_id = $1
        )
        SELECT eo.event_owner_id "id"
        FROM "event" e
          INNER JOIN "event_owner" eo
            ON eo.event_owner_id = e.event_owner_id
        WHERE e.event_id = $1 AND EXISTS(SELECT * FROM ho_access)`,
        [eventID, officialID]
    ).then(result => result.rows[0] || null).then(eventOwnerData => {
        if(eventOwnerData && eventOwnerData.id) {
            req.session.passport.user.head_official_events = {
                [eventID]: {
                    event_owner_id: eventOwnerData.id
                }
            };
            return next();
        } else {
            res.forbidden('Access denied');
        }
    }, function (err) {
        res.customRespError(err)
    })
};

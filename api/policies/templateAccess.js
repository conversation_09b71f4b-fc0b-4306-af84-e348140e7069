module.exports = function (req, res, next) {
    var $events = req.user.events,
        $sharedEvents = req.user.shared_events,
        keys;

    if($events && $events.length)
        return next();

    if($sharedEvents) {
        keys = Object.keys($sharedEvents)
        for(var i = 0, l = keys.length; i < l; ++i) {
            if($sharedEvents[keys[i]].role_co_owner)
                return next();
        }
    } 

    return res.forbidden('Access denied');
}  

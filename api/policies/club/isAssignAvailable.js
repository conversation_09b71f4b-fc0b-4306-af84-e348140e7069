'use strict';

module.exports = function isAssignAvailable (req, res, next) {
    var $event_id           = parseInt(req.options.event || req.params.event, 10),
        $division_id        = parseInt(req.body.division, 10),
        $roster_team_id     = parseInt(req.body.team, 10),
        params              = [$event_id],
        clubUsesPrivateLink = !!req.options.event,
        sql;
    if(!$event_id)          return res.validation('Invalid event identifier passed');
    if(!$roster_team_id)    return res.validation('No team identifier passed');
    if(!$division_id)       return res.validation('No division identifier passed');

    if($division_id >= 0) {
        sql = 
            `SELECT e.event_id
            FROM "event" e
            INNER JOIN "division" d
                ON d.event_id = e.event_id
                AND d.division_id = $2
            WHERE e.event_id = $1
                ${clubUsesPrivateLink ? '' : 'and e.date_reg_open < (now() AT TIME ZONE e.timezone)'} 
                and coalesce(d.date_reg_close, e.date_reg_close) > (now() AT TIME ZONE e.timezone)
                and e.teams_use_clubs_module IS TRUE
                and e.published is TRUE
                and e.live_to_public is TRUE`;
        params.push($division_id);
    } else {
        sql = 
            `SELECT e.event_id
            FROM "event" e
            INNER JOIN "roster_team" rt 
                ON rt.roster_team_id = $2
                AND rt.event_id = e.event_id
            INNER JOIN "division" d 
                ON d.event_id = e.event_id
                AND d.division_id = rt.division_id
            WHERE e.event_id = $1
                ${clubUsesPrivateLink ? '' : 'and e.date_reg_open < (now() AT TIME ZONE e.timezone)'} 
                and coalesce(d.date_reg_close, e.date_reg_close) > (now() AT TIME ZONE e.timezone)
                and e.teams_use_clubs_module IS TRUE
                and e.published is TRUE
                and e.live_to_public is TRUE`;
        params.push($roster_team_id)
    }
    
    Db.query(sql, params).then(function (result) {
        if(result.rowCount === 0) return res.validation('Teams registration is closed');
        return next();
    }).catch(() => {
        res.serverError();
    })
}

'use strict';

module.exports = function (req, res, next) {
    var event_id = req.options.event || req.params.event;

    var cd_id = req.session.passport.user.club_owner_id;
    if(!event_id) return res.status(400).json({error: 'No event selected'});
    if(!cd_id) return res.status(403).json({error: 'Access denied.'});

    Db.query(
        'SELECT rc.roster_club_id id ' + 
        ' FROM roster_club rc ' + 
        ' INNER JOIN master_club mc ' + 
        '   ON mc.master_club_id = rc.master_club_id ' + 
        ' WHERE rc.event_id = $1 ' + 
        '   AND mc.club_owner_id = $2', 
        [event_id, cd_id]
    ).then(result => {
        let row = result.rows[0];
        if(row && row.id) {
            next()
        } else {
            res.render('500', { error: 'Club is not Assigned to the selected Event' })
        }
    }).catch(() => {
        res.serverError();
    });
}

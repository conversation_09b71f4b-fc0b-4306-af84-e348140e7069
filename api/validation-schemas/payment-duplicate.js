const Joi = require('joi');

const { TICKET_TYPES } = require('../lib/joi-constants');

const paymentDuplicateBodySchema = Joi.object().keys({
    email       : Joi.string().optional().allow(null, '').label('Purchaser Email'),
    phone       : Joi.string().optional().allow(null, '').label('Purchaser Phone'),
    event_id    : Joi.number().required().label('Event Identifier'),
    fingerprint : Joi.string().required().label('Card Fingerpring'),
    tickets     : Joi.array().items(
        Joi.object().keys({
            type    : Joi.string().allow(TICKET_TYPES.WEEKEND, TICKET_TYPES.DAILY).required().label('Ticket Type'),
            first   : Joi.string().required().label('First Name'),
            last    : Joi.string().required().label('Last Name'),
            quantity: Joi.number().min(0).required().label('Tickets Quantity')
        })
    ).label('Tickets List')
}).label('Request body');

module.exports = {
    paymentDuplicateBodySchema,
};

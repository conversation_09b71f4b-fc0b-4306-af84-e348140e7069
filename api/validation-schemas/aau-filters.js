const Joi = require('joi');
const {
    US_ZIP_REG_EXP,
    NAME_REGEX,
    AAU_CA_ZIP_REG_EXP,
    BM_ZIP_REG_EXP,
    CH_ZIP_REG_EXP,
} = require("../lib/joi-constants");

module.exports.filterSchema = Joi.object().keys({
    club_code: Joi.string().alphanum().optional().label("Club Code"),
    membership_identifier: Joi.string().alphanum().optional().label("Membership Identifier"),
    zip_code: Joi.alternatives('any')
        .try(
            Joi.string().pattern(US_ZIP_REG_EXP),
            Joi.string().pattern(AAU_CA_ZIP_REG_EXP),
            Joi.string().pattern(BM_ZIP_REG_EXP),
            Joi.string().pattern(CH_ZIP_REG_EXP)
        ).optional().label("Zip Code"),
    last_name: Joi.string().trim().optional().regex(NAME_REGEX).label("Last Name"),
    birth_date: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/).label("Birth Date"),
})

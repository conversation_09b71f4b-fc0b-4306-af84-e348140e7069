
const Joi = require('joi');
const { SALES_HUB: { CUSTOM_FIELD_TYPE} } = require('../../lib/joi-constants');

const customFieldSchema = Joi.object().keys({
    type: Joi.string().valid(CUSTOM_FIELD_TYPE.SELECT, CUSTOM_FIELD_TYPE.TEXT).required().label('Custom Field Type'),
    label: Joi.string().required().label('Label'),
    shortLabel: Joi.string().optional().allow(null).label('Short Label'),
    options: Joi.array()
        .items(
            Joi.object().keys({
                key: Joi.string().required().label('Option Key'),
                value: Joi.string().required().label('Value'),
            })
        )
        .when(Joi.ref('type'), {
            is: CUSTOM_FIELD_TYPE.SELECT,
            then: Joi.required(),
            otherwise: Joi.optional().allow(null)
        })
        .label('Custom Field Options'),
    required: Joi.boolean().required().label('Does Custom Field Required Flag'),
    order: Joi.number().min(0).required().label('Order Value'),
    isHidden: Joi.boolean().required().label('Does Custom Field Is Hidden'),
});

const createSchema = customFieldSchema;
const updateSchema = customFieldSchema;

module.exports = {
    createSchema,
    updateSchema,
}


const Joi = require('joi');
const { FEE_PAYER } = require('../../constants/payments');

const baseSchema = Joi.object().keys({
    statementDescriptor: Joi.string().optional().allow(null).label('Statement Descriptor'),
    paymentProviderFeeFixed: Joi.number().required().label('Payment Provider Fee Fixed'),
    paymentProviderFeePercentage: Joi.number().required().label('Payment Provider Fee Percent'),
    paymentProviderFeePayer: Joi.string()
        .required()
        .valid(FEE_PAYER.BUYER, FEE_PAYER.SELLER)
        .label('Payment Provider Fee Payer')
});

const creationSchema = baseSchema.keys({
    paymentProviderAccountId: Joi.string().required().label('Payment Provider Account ID'),
});

const updateSchema = baseSchema;

module.exports = {
    creationSchema,
    updateSchema,
}

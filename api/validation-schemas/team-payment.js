const Joi = require('joi');

const basePaymentData = Joi.object().keys({
    event_id: Joi.number().min(1).required().label('Event ID'),
    amount: Joi.number().min(1).required().label('Total Amount'),
    master_club_id: Joi.number().required().label('Master Club ID'),
    club_owner_id: Joi.number().required().label('Club Owner ID'),
    season: Joi.number().required().label('Season'),
    type: Joi.string().required().label('Payment Type'),
    user: Joi.object().keys({
        first: Joi.string().required().label('User First Name'),
        last: Joi.string().required().label('User Last Name'),
        user_id: Joi.number().required().label('User ID'),
        phone: Joi.number().required().label('User Phone'),
        email: Joi.string().required().label('User Email'),
    }),
});

const paymentIntentData = basePaymentData.keys({
    payment_intent_id: Joi.string().required().label('Payment Intent ID'),
})

const paymentHubData = basePaymentData.keys({
    payment_hub_payment_intent_id: Joi.string().required().label('Payment Hub Payment Intent ID'),
})

const justifiData = basePaymentData.keys({
    justifi_checkout_id: Joi.string().required().label('Justifi Checkout ID'),
});

module.exports = {
    paymentHubData: paymentHubData.keys({
        receipt: Joi.array().items(Joi.number().min(1)).min(1).required().label('Teams List'),
    }),
    justifiData: justifiData.keys({
        receipt: Joi.array().items(Joi.number().min(1)).min(1).required().label('Teams List'),
    }),
    changePaymentHubPaymentTypeData: paymentHubData.keys({
        purchase_id: Joi.number().required().label('Payment ID'),
    }),
    changeJustifiPaymentTypeData: justifiData.keys({
        purchase_id: Joi.number().required().label('Payment ID'),
    }),
    paymentData: paymentIntentData.keys({
        receipt: Joi.array().items(Joi.number().min(1)).min(1).required().label('Teams List'),
        verticalInsuranceQuote: Joi.object().keys({
            quote_id: Joi.string().required().label('Quote ID'),
            total_amount: Joi.number().min(1).required().label('Total Amount'),
        }).optional().label('Vertical Insurance Quote Data'),
    }),
    changePaymentTypeData: paymentIntentData.keys({
        purchase_id: Joi.number().required().label('Payment ID'),
    })
};

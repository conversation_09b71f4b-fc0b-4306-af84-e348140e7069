'use strict';

const Joi = require('joi');
const { EMAIL_REGEX } = require("../lib/joi-constants");

module.exports = {
	staffer: Joi.object().keys({
		email 		        : Joi.string().pattern(EMAIL_REGEX).required().label('Email'),
        name                : Joi.string().allow(null).label('Staff full name'),
		phonem 		        : Joi.string().allow(null).pattern(/^([0-9]{10,15})$/).label('Cell Phone'),
		phonew 		        : Joi.string().allow(null).pattern(/^([0-9]{10,15})$/).label('Work Phone'),
		phoneh 		        : Joi.string().allow(null).pattern(/^([0-9]{10,15})$/).label('Home Phone'),
		phoneo 		        : Joi.string().allow(null).pattern(/^([0-9]{10,15})$/).label('Other Phone'),
		role_id 	        : Joi.number().required().label('Role'),
        safesport_statusid  : Joi.number().allow(null),
        is_impact           : Joi.boolean().allow(null),
        cert                : Joi.string().allow(null),
		primary 	        : Joi.boolean()
	}),
	create_transfer: Joi.object().keys({
		event_id 			: Joi.number().required().min(1).label('Event ID'),
		stripe_account_id 	: Joi.string().required().label('Stripe Account ID'),
		currency 			: Joi.string().required().label('Currency'),
		amount 				: Joi.number().min(1).label('Transfer Amount'),
		description 		: Joi.string().optional().allow(null, '').label('Notes'),
		apiVer 				: Joi.string().optional().allow(null).label('Stripe API Version'),
		payment_for  		: Joi.string().allow('teams', 'tickets').required().label('Purchase type')
	}).rename('notes', 'description').rename('account_id', 'stripe_account_id'),

    //TODO: use global constants for role and gender ids
    clothes_requirements: Joi.object().pattern(/^(staff|official)$/,
        Joi.object().pattern(/^(male|female)$/,
            Joi.array().items(
                Joi.object().keys({
                    common_item_id: Joi.string().required(),
                    required: Joi.boolean().required(),
                })
            )
        )
    ),
}

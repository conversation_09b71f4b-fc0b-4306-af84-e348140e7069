const Joi = require('joi');

const sponsorRegistrationInfoSchema = Joi.object().keys({
    is_sponsor: Joi.boolean().label('Is Sponsor'),
    is_exhibitor: Joi.boolean().label('Is Exhibitor'),
    is_non_profit: Joi.boolean().label('Is Non Profit'),
    is_other: Joi.boolean().label('Is Non Profit'),
});

const sponsorInvoiceInfoSchema = Joi.object().keys({
    event_dates         : Joi.object().pattern(/^/, Joi.boolean()).min(1)
        .custom((value) => {
            if (Object.values(value).every(val => val === false)) {
                throw new Error('At least one event date must be selected');
            }
            return value;
        }).required().label('Dates'),
    booths              : Joi.array().items(Joi.number().min(0)).min(1).required().label('Booths'),
    comment             : Joi.string().allow(null, '').label('Comment'),
    otherBooths         : Joi.array().items(Joi.object().keys({
        amount          : Joi.number().required().positive().label('Other Booth Amount'),
        title           : Joi.string().required().label('Other Booth Title'),
        description     : Joi.string().required().allow(null).label('Other Booth Description'),
        quantity        : Joi.number().min(1).label('Other Booth Quantity'),
        fee             : Joi.number().positive().label('Other Booth Fee'),
        event_booth_id  : Joi.number().allow(null).label('Other Booth Identifier'),
        booth_label     : Joi.string().max(10).required().allow('').label('Other Booth Label'),
        notes           : Joi.string().allow('').label('Other Booth Notes'),
    })).label('Other Booths'),
    amount              : Joi.number().required().positive().label('Payment Amount'),
});


module.exports = {
    sponsorRegistrationInfoSchema,
    sponsorInvoiceInfoSchema,

}

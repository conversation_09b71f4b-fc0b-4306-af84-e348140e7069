const Joi = require("joi");

const fileLinkSchema = Joi.object().keys({
    id: Joi.string().required().label("File Link ID"),
    object: Joi.string().required().label("Object type"),
    created: Joi.number().required().label("Creation date UNIX"),
    expired: Joi.boolean().required().label("Has been expired"),
    expires_at: Joi.number().allow(null).label("Expiration date UNIX"),
    file: Joi.string().required().label("File ID"),
    livemode: Joi.boolean().required().label("Is in livemode"),
    metadata: Joi.object().label("Metadata"),
    url: Joi.string().required().label("Url"),
});

const fileSchema = Joi.object().keys({
    id: Joi.string().required().label("File ID"),
    object: Joi.string().required().label("Object type"),
    created: Joi.number().required().label("Creation date UNIX"),
    expires_at: Joi.number().label("Expiration date UNIX"),
    url: Joi.string().required().label("Filename"),
    purpose: Joi.string().required().label("Purpose"),
    size: Joi.number().required().label("File size"),
    title: Joi.string().allow(null).label("Title"),
    type: Joi.string().required().label("Type"),
    url: Joi.string().required().label("Type"),
    links: Joi.object().keys({
        object: Joi.string().required("Link object"),
        data: Joi.array().items(fileLinkSchema),
    }),
});

const disputeEvidenceSchema = Joi.object({
    receipt: fileSchema.required().label("Receipt"),
    service_date: Joi.string().required().label("Service Date"),
    customer_name: Joi.string().required().label("Customer Name"),
    refund_policy: fileSchema.required().label("Refund Policy"),
    shipping_date: Joi.string().label("Shipping Date"),
    billing_address: Joi.string().label("Billing Address"),
    shipping_address: Joi.string().label("Shipping Address"),
    shipping_carrier: Joi.string().label("Shipping Carrier"),
    customer_signature: fileSchema.label("Customer Signature"),
    uncategorized_file: fileSchema.label("Uncategorized File"),
    uncategorized_text: Joi.string().label("Uncategorized Text"),
    access_activity_log: Joi.string().label("Access Activity Log"),
    cancellation_policy: fileSchema.required().label("Cancellation Policy"),
    duplicate_charge_id: Joi.string().label("Duplicate Charge Id"),
    product_description: Joi.string().required().label("Product Description"),
    customer_purchase_ip: Joi.string().label("Customer Purchase Ip"),
    cancellation_rebuttal: Joi.string().label("Cancellation Rebuttal"),
    customer_communication: fileSchema
        .label("Customer Communication"),
    customer_email_address: Joi.string()
        .required()
        .label("Customer Email Address"),
    shipping_documentation: fileSchema.label("Shipping Documentation"),
    refund_policy_disclosure: Joi.string()
        .required()
        .label("Refund Policy Disclosure"),
    shipping_tracking_number: Joi.string().label("Shipping Tracking Number"),
    refund_refusal_explanation: Joi.string()
        .optional()
        .label("Refund Refusal Explanation"),
    duplicate_charge_explanation: Joi.string().label(
        "Duplicate Charge Explanation"
    ),
    cancellation_policy_disclosure: Joi.string()
        .required()
        .label("Cancellation Policy Disclosure"),
    duplicate_charge_documentation: fileSchema.label(
        "Duplicate Charge Documentation"
    ),
});

module.exports = {
    disputeEvidenceSchema,
};

const Joi = require('joi');
const { OFFICIALS_PAYMENT_OPTION } = require('../lib/joi-constants');

const markPayoutSchema = Joi.object().keys({
    date_paid: Joi.string().required().label('Date paid'),
    amount: Joi.number().min(0).precision(2).required().label('Amount'),
    notes: Joi.string().allow(null, '').optional().label('Notes'),
    payment_method: Joi.string().valid(
        OFFICIALS_PAYMENT_OPTION.DIRECT_DEPOSIT,
        OFFICIALS_PAYMENT_OPTION.ON_SITE,
        OFFICIALS_PAYMENT_OPTION.MAILED,
        OFFICIALS_PAYMENT_OPTION.ARBITER_PAY,
        OFFICIALS_PAYMENT_OPTION.NO_PAYMENT_REQUIRED
    ).required().label('Payment method'),
    check_number: Joi.when('payment_method', {
        is: Joi.valid('on_site', 'mailed'),
        then: Joi.string().optional().allow(null, '').label('Check number'),
        otherwise: Joi.forbidden().label('Check number'),
    })
})

const exportExcel = Joi.object().keys({
    member_type: Joi.string()
        .valid(OfficialsService.payout.MEMBER_TYPE.OFFICIAL, OfficialsService.payout.MEMBER_TYPE.STAFF)
        .required()
        .label('Label Type')
})

module.exports = {
    markPayoutSchema,
    exportExcel,
}

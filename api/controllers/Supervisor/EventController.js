'use strict';

/*
* Controller for /#/event-supervisor Angular app page
*/

module.exports = {
	// GET /api/event-supervisor/events
	eventsList: function (req, res) {
		SupervisorService.event.getEventsList()
		.then(events => {
			res.status(200).json({ events });
		})
		.catch(res.customRespError.bind(res));
	},

	// GET /api/event-supervisor/events/:event/stripe-account
	stripeAccountInfo: function (req, res) {
		let eventID = parseInt(req.params.event, 10);
		let apiVer 	= sails.config.stripe_api.version;

		SupervisorService.event.getEventStripeAccount(eventID, apiVer)
		.then(account => {
			res.status(200).json({ account });
		})
		.catch(res.customRespError.bind(res));
	},

	// GET /api/event-supervisor/events/:event/monetary
	eventMonetaryInfo: function (req, res) {
		let eventID = parseInt(req.params.event, 10);

		SupervisorService.event.getEventMonetaryData(eventID)
		.then(event => {
			res.status(200).json({ event });
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/event-supervisor/events/:event/monetary
	updateMonetary: function (req, res) {
		let eventID = parseInt(req.params.event, 10);

		SupervisorService.event.updateMonetary(eventID, req.body)
		.then(() => {
			res.status(200).send('OK');
		})
		.catch(res.customRespError.bind(res));
	}
}
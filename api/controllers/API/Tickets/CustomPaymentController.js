'use strict';

module.exports = {

    //GET /api/custom-payment/items
    getItemsList: function(req, res) {
        CustomPaymentService.getTicketsList()
            .then(list => res.status(200).json(list))
            .catch(res.customRespError.bind(res));
    },

    //POST /api/custom-payment/buy
    buy: function(req, res) {
        CustomPaymentService.buy(req.body)
            .then(() => res.status(200).json({}))
            .catch(res.customRespError.bind(res));
    }
};

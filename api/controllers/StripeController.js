'use strict';

let moment          = require('moment'),
    co              = require('co'),
    Joi             = require('joi');

const CONNECTION_TOKEN_SCHEMA = Joi.object().keys({
    event_id                : Joi.number().integer().required(),
    connected_to_account_id : Joi.string().required(),
    platform_client_id      : Joi.string().required(),
    user_id                 : Joi.number().integer().required(),
    account_email           : Joi.string().required()
});

const CLIENT_SQL_SERVER_ERROR_MSG = {
    'unique_stripe_account_id'      : 'Account with livemode must be unique.',
    'user_connected_account_unique' : 'You already have connection with this account.',
    'unique_row_id'                 : 'Same id and livemode.'
};

const STRIPE_CONNECT_TMPL   = 'stripe/connect';
const CONNECT_REDIRECT_URL  = '/#/events/stripe-acc'; /* Modal window with accounts list */

const StripeConnect = require('../lib/StripeConnect');

module.exports = {
    /**
    * Returns TRUE if Stripe Event belongs to SW Acc.
    * Returns FALSE if Stripe Event does NOT belong to SW Acc.
    *
    * NOTE: all the events from connected accounts, 
    * but related with the SW Platform (e.g. payment reached the desctination account, etc.) will be skipped this way
    */
    checkEventBelongsToSWAcc: function (stripeEventID) {
        /*
        * Consider test event belongin to the Platfrom's acc
        */
        let accPromise = (stripeEventID === 'evt_00000000000000')
                            ? Promise.resolve()
                            : StripeConnect.getStripeEvent(stripeEventID);

        return accPromise
        .then(() => true)
        .catch(err => {
            const types = ['StripeInvalidRequest', 'StripeInvalidRequestError'];    

            if (types.includes(err.type) &&
                err.message.indexOf('No such event') >= 0) {
                return false;
            } else {
                return Promise.reject(err);
            }
        })
    },
    // post /api/stripe/webhook-upd
    webhookUpd: async function (req, res) {
        const rawBodyBuffer = req.rawBody;
        const body = JSON.parse(req.rawBody);
        const signature = req.header('stripe-signature');

        if(_.isEmpty(rawBodyBuffer)) {
            ErrorSender.webhookError({
                stripeEventObj  : rawBodyBuffer,
                error           : 'Empty webhook data'
            });

            return res.status(200).send('OK');
        }

        let eventType = body.type;

        try {
            const webhookData = await StripeService.webhook.__signatureCheck(rawBodyBuffer, signature);
            await StripeService.webhook.processWebhook(webhookData, signature);

            res.status(200).send('OK');
        } catch (errors) {
            if (errors) {
                if(!_.isArray(errors)) {
                    errors = [errors];
                }
                errors.forEach((err) => {
                    if (!_.isBoolean(err.not_belongs) && !err.duplicate) {
                        loggers.errors_log.error(`Event Type: ${eventType}. Webhook error:`, err);
                        if(err.sendEmail !== false) {
                            ErrorSender.webhookError({
                                error: err,
                                stripeEventObj: body,
                            })
                        }
                    }
                    else {
                        loggers.debug_log.verbose(err);
                    }
                });
            }

            // force Stripe webhook for retry
            res.status(500).send('Internal Server Error');
        }
    },
    // get /api/platform/client-id
    getPlatformClientID: function (req, res) {
        StripeService.getSWClientID().then(data => {
            res.status(200).json(data);
        }).catch(res.customRespError.bind(res));
    },
    // get /stripe/redirect_url
    connectHandler: function (req, res) {
        LogRequestService.logStripeConnectHook(req);

        if (req.query.error) return onStripeError(res, req.query.error, req.query.error_description);

        let authCode = req.query.code;
        let isLiveMode = (req.query.state == 'live');

        StripeService.account.connectAccount(authCode, isLiveMode)
        .then(() => {
            /* Note: ejs does not allow "undefined" variables, so we need to pass "url" and "error" always */
            res.render(STRIPE_CONNECT_TMPL, {
                url     : CONNECT_REDIRECT_URL,
                error   : null
            });
        })
        .catch(err => {
            loggers.errors_log.error(err);

            let errorMsg = (err && err.validation)
                                ?err.validation
                                :'Internal Error. Please, try again later';

            res.render(STRIPE_CONNECT_TMPL, {
                error   : errorMsg,
                url     : null
            });
        });
    },

    /**
     * This method is used for development purposes for now
     */

    // covered 😄👍
    // get /stripe/express_acc_connect
    handleExpressAccConnection: function (req, res) {
        let isStripeAcc = true;

        if (req.query.error) return onStripeError(res, req.query.error, req.query.error_description, isStripeAcc);
        
        let platformSK = null;

        let authCode = req.query.code;
        
        let token = null;
        if(req.query.state) {
            try {
                let state = JSON.parse(req.query.state);
                token = CipherService.decryptStripeToken(state.token);
            }
            catch (err) {
                token = null;
            }
        }

        if (token && !CONNECTION_TOKEN_SCHEMA.validate(token).error) {
            StripeService.account.findAccCredentials(token.connected_to_account_id)
                .then(data => {
                    /* jshint eqnull:true */
                    if (data == null) {
                        return Promise.reject({ error: 'integrity_error', validation: 'Invalid Account ID' });
                    }

                    platformSK = data.sk;
                    return StripeService.account.getStripeConnectAccessToken(authCode, platformSK, token.platform_client_id);
                })
                .then(data => {
                    if (data.error) return Promise.reject({
                        error: data.error,
                        error_description: data.error_description
                    });

                    // get dashboard url
                    return StripeService.payouts.createLoginLink(platformSK, data.stripe_user_id)
                        .then(login_link => {

                            if (login_link.error) return Promise.reject({
                                error: login_link.error,
                                error_description: login_link.error_description
                            });

                            data.dashboard_url = login_link.url;
                            return data;
                        });
                })
                .then(data => {
                    // ready to insert into stripe_account
                    return StripeService.account.createExpressAccount(data, token);
                })
                .then(() => {
                    res.status(200).send('OK');
                })
                .catch(err => {
                    if (err.error && err.validation) return onStripeError(res, err.error, err.validation, isStripeAcc);

                    if (err.error && err.error_description) return onStripeError(res, err.error, err.error_description, isStripeAcc);

                    if (err.constraint && CLIENT_SQL_SERVER_ERROR_MSG[err.constraint]) {
                        return onStripeError(res, 'integrity_error', CLIENT_SQL_SERVER_ERROR_MSG[err.constraint], isStripeAcc);
                    }

                    return onStripeError(res, 'internal_server_error', 'Internal Server Error', isStripeAcc);
                });
        } else {
            return onStripeError(res, 'invalid_token', 'Invalid Token', isStripeAcc);
        }
    },

    // GET /api/stripe/upd-availability
    updateAvailability: function (req, res) {
        let lte = req.query.lte ? +moment(req.query.lte, 'YYYY-MM-DD').format('X') : (void 0)

        StripeConnect.updateChargeAvailability(lte)
        .then(data => {
            res.status(200).json({ data });
        })
        .catch(res.customRespError.bind(res));
    }
};

function onStripeError(res, error, errorMsg, isExpress) {
    loggers.errors_log.error('Stripe Connect Error', error, errorMsg);
    ErrorSender.stripeConnectError(errorMsg);

    res.render(STRIPE_CONNECT_TMPL, {
        url: isExpress 
                ? StripeService.account.STRIPE_CONNECT_EXPRESS_ACCOUNT.redirect_uri
                : '',
        error: errorMsg
    });
}

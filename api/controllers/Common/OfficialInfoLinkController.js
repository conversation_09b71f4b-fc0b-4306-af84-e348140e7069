'use strict';

module.exports = {
	// GET /api/common/event/:event/official/:official
	linkRedirect: function (req, res) {
		let $eventID 	= req.params.event,
			$officialID = req.params.official;

		let sessOfficialID 	= req.session.passport.user.official_id,
			eventOwnerID 	= eventOwnerService.findId($eventID, req.user)


		Promise.all([
			hasEOAccess(eventOwnerID, $eventID),
			hasHOAccess($eventID, sessOfficialID),
			($officialID == sessOfficialID)?hasOfficialAccess($eventID, $officialID):false
		]).then(results => {

			if (results[0]) {
				res.setHeader('Location', `/#/event/${$eventID}/officials/${$officialID}/info`)
				res.status(302)
			} else if (results[1]) {
				res.setHeader('Location', `/#/official/event/${$eventID}/manage/${$officialID}/info`)
				res.status(302)
			} else if (results[2]) {
				res.setHeader('Location', `/#/official/events/${$eventID}/check`)
				res.status(302)
			} else {
				res.status(403)
				res.write('Forbidden');
			}
			
			res.end();
		});
	}
}

function hasEOAccess (eventOwnerID, eventID) {
	if (!eventOwnerID) {
		return false;
	}

	return Db.query(
		`SELECT e.event_id
		 FROM "event" e 
		 WHERE e.event_owner_id = $1
		 	AND e.event_id = $2`,
		[eventOwnerID, eventID]
	).then(result => (result.rows.length > 0));
}

function hasHOAccess (eventID, officialID) {
	if (!officialID) {
		return false;
	}

	return OfficialsService.isHeadOfficial(eventID, officialID)
}

function hasOfficialAccess (eventID, officialID) {
	return Db.query(
		`SELECT eof.event_official_id
		 FROM "event_official" eof 
		 WHERE eof.event_id = $1 
		 	AND eof.official_id = $2`,
		[eventID, officialID]
	).then(result => (result.rows.length > 0))
}
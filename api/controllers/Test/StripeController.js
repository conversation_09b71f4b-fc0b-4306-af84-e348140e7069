'use strict'


// GET /api/test/stripe-account/save-transfers?acc&ver
module.exports.processTransfersForAcc = function (req, res) {
	let stripeAccountID = req.query.acc;
	let stripeApiVer 	= req.query.ver;

	if (!stripeAccountID) {
		return res.validation('Invalid Stripe Account ID');
	}

	StripeService.findTransfers(stripeAccountID)
	.then(transfers => {
		if (transfers.length === 0) {
			return Promise.reject(new Error('No transfers found'));
		}

		loggers.debug_log.verbose('Got', transfers.length, 'paid transfers')

		return transfers.reduce((prev, tr) => {
			return prev.then(() => 
							StripeService.payouts.processStripePayout(stripeAccountID, tr, stripeApiVer))
		}, Promise.resolve())
	})
	.then(() => {
		res.status(200).send('OK')
	})
	.catch(res.customRespError.bind(res));
}

module.exports = {
    // PUT /api/housing/events/:event/teams/:team/status
    updateTeamStatus: async function (req, res) {
        const event = Number(req.params.event);
        const team = Number(req.params.team);
        const housingStatus = Number(req.body.status_housing);
        const notes = req.body.notes || null;
        const { user_id } = req.user;

        try {
            if(notes !== null && !_.isString(notes)) {
                throw { validation: 'notes field should be a string' };
            }
            const {
                oldRosterTeam,
                newRosterTeam,
            } = await HousingService.changeTeamStatus(event, team, housingStatus);
            await eventNotifications.add_notification(event, {
                action: 'team.housing.status-change',
                user_id,
                roster_team_id: team,
                old_housing_status: HousingService.getStatusName(oldRosterTeam.status_housing),
                new_housing_status: HousingService.getStatusName(newRosterTeam.status_housing),
                team_name: newRosterTeam.team_name,
                comments: notes,
            });
            res.status(204).send();
        }
        catch(err) {
            res.customRespError(err);
        }
    }
};

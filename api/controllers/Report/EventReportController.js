'use strict';
const { promisify } = require('util');

module.exports = {
    // GET /api/report/event-seasonal
    seasonal: async function (req, res) {
        try {
            const events = (() => {
                if(!req.query.events) {
                    return undefined;
                }
                return req.query.events.split(',').map(n => parseInt(n, 10));
            })();
            const skipCamps = parseInt(req.query.skipCamps) || 0;
            const season = req.query.season
                ? Number(req.query.season)
                : sails.config.sw_season.current;
            const report = ReportService.event.getSeasonalReport({
                season,
                events,
                skipCamps,
            });
            const requestEndHandler = () => {
                report.cancel();
            };
            res.on('close', requestEndHandler);
            res.on('finish', requestEndHandler);
            await promisify(res.download).call(res, await report.filePath);
        }
        catch(err) {
            if(_.isObject(err) && err.canceled) {
                return res.end();
            }
            err = _.isString(err) ? {message: err} : err;
            res.customRespError(err);
        }
    },
};

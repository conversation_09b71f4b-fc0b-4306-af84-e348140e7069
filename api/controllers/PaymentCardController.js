
const argv              = require('optimist').argv;
const stripeSettingsRow = argv.prod?'stripe_connect':'stripe_connect_dev';

module.exports = {
    //GET /api/eo/payment-card
    list: async function (req, res) {
        let isAdmin = req.user.is_admin;
        let eventID = !!req.query.event && Number(req.query.event) || null;

        let eventOwnerID;
        let userID;

        if(eventID) {
            eventOwnerID = eventOwnerService.findId(eventID, req.user);
        } else if (req.user.event_owner_id) {
            eventOwnerID = req.user.event_owner_id;
        }

        if(!eventOwnerID) {
            return res.forbidden('You have no access to this event');
        }

        if(eventOwnerID !== req.user.event_owner_id) {
            userID = req.user.user_id;
        }

        try {
            let paymentCards = await StripeService.paymentCard.getPaymentCards(userID, eventOwnerID, eventID, isAdmin);

            res.status(200).json({ paymentCards });
        } catch (err) {
            res.customRespError(err);
        }
    },

    // POST /api/eo/payment-bank-account/session
    createFinancialConnectionSession: async function (req, res) {
        const userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        try {
            const financialConnectionSessionSK = await StripeService.paymentCard.getUserCustomerStripeFinancialConnectionSession(userID);

            res.json({
                client_secret: financialConnectionSessionSK
            });
        } catch (err) {
            res.customRespError(err);
        }
    },

    //GET /api/eo/payment-card/settings
    settings: async function (req, res) {
        let userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        try {
            let setupIntentSK = await StripeService.paymentCard.getUserCustomerStripeSetupIntent(userID);

            let stripeClientKey = await Db.query(
                squel.select()
                    .field(`"value"->>'public_key'`, 'public_key')
                    .from('settings')
                    .where('"key" = ?', stripeSettingsRow)
            ).then(result => result && result.rows[0] && result.rows[0].public_key);

            res.status(200).json({
                settings: {
                    intent_client: setupIntentSK,
                    stripe_client: stripeClientKey,
                }
            })
        } catch (err) {
            res.customRespError(err);
        }
    },

    //POST /api/eo/payment-card
    add: async function (req, res) {
        let paymentMethodID = req.body.payment_method_id;
        let userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        if(!paymentMethodID) {
            return res.validation('Payment Method ID required');
        }

        try {
            await StripeService.paymentCard.saveUserPaymentMethod(userID, paymentMethodID);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    //POST /api/eo/payment-bank-account
    addBankAccount: async function (req, res) {
        const financialAccountID = req.body.financial_account_id;
        const userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        if(!financialAccountID) {
            return res.validation('Financial Account ID required')
        }

        try {
            await StripeService.paymentCard.saveBankAccount(
                userID,
                financialAccountID,
                { ipAddress: req.getIP(), userAgent: req.getUserAgent() }
            );

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    //DELETE /api/eo/payment-card
    remove: async function (req, res) {
        let paymentMethodID = req.body.payment_method_id;
        let userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        if(!paymentMethodID) {
            return res.validation('Payment Method ID required');
        }

        try {
            let paymentMethodEvents = await EventPaymentMethodService.settings.getPaymentMethodEvents(paymentMethodID);

            if(paymentMethodEvents.length) {
                let eventNames = paymentMethodEvents.map(event => event.name);

                throw { validation: `Payment method used in ${eventNames.join(', ')}` };
            }

            await StripeService.paymentCard.removeUserPaymentMethod(userID, paymentMethodID);

            await UserService.history.savePaymentCartDeletingAction({
                userID: req.user.user_id,
                sessionID: req.sessionID,
                ip: req.getIP(),
                userAgent: req.getUserAgent(),
                paymentMethodID,
                method: UserService.history.PAYMENT_METHOD_REMOVE_ROLE.EO,
            }).catch(err => loggers.errors_log.error(err));

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    //PUT /api/eo/payment-card/default
    setDefault: async function (req, res) {
        let paymentMethodID = req.body.payment_method_id;
        let userID = Number(req.user.user_id);

        if(!userID) {
            return res.validation('User not found');
        }

        if(!paymentMethodID) {
            return res.validation('Payment Method ID required');
        }

        try {
            await StripeService.paymentCard.setDefault(userID, paymentMethodID);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    }
}

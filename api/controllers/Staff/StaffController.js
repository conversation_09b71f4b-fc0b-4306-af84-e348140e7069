module.exports = {
    // get /api/staff/event/:event/staff/:staff
    getInfo: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const staffID = Number(req.params.staff);

            const staff     = await OfficialsService.getStaffInfo(eventID, staffID);
            const event     = await OfficialsService.findEvent(eventID);
            const clothes   = await OfficialsService.clothing.getMemberClothes(staffID, eventID, 'staff');

            res.status(200).json({ staff, event, clothes });
        } catch (e) {
            res.customRespError(e);
        }
    },
    // put /api/staff/event/:event/staff/:staff/update
    updateInfo: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const staffID = Number(req.params.staff);

            const data = req.body;

            const {
                work_status,
                old_work_status,
                event_official_id,
            } = await OfficialsService.updateStaffInfo(eventID, staffID, data);

            if (work_status !== old_work_status) {
                await OfficialsService.sendRoleNotification(
                    work_status,
                    AEMService.STAFF_GROUP,
                    event_official_id,
                    eventID
                )
            }

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },
};

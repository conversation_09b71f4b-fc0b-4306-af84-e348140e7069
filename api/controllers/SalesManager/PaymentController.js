'use strict';

const CARD_PAYMENT_TYPE     = 'card';

module.exports = {
    //GET /api/sales/payments
    get_payments: function (req, res) {
        let salesManagerID  = Number(req.session.passport.user.sales_manager_id);
        let eventOwnerID    = Number(req.session.passport.user.event_owner_id);
        let sponsorIDs      = req.session.passport.user.sponsor_ids;

        let userWithoutSponsorRole = !Array.isArray(sponsorIDs) || _.isEmpty(sponsorIDs);

        if(!salesManagerID && !eventOwnerID && userWithoutSponsorRole) {
            return res.status(403).json({error: 'Access denied.'});
        }

        return BoothsService.payment.getInvoicesList({ salesManagerID, eventOwnerID, sponsorIDs })
            .then(payments => res.status(200).json({ payments }) )
            .catch(res.customRespError);
    },
    // GET /api/sales/payments/:payment
    get_booths_payments: async function (req, res) {
        const purchaseId = req.param('payment');

        if(!purchaseId) {
            return res.validation('Invalid Payment Identifier');
        }

        try {
            const boothPayments = await BoothsService.payment.getBoothsPayments(purchaseId);

            res.json({booth_payments: boothPayments})
        } catch (err) {
            res.customRespError(err)
        }
    },
    // POST /api/event/:event/sales/payment/:payment/receive
    receive: function (req, res) {
        var purchase_id = req.param('payment');
        if(!req.body.date_paid || !req.body.check_num) {
            return res.badRequest('No receive date or check number');
        }
        // TODO refactor the query and the logic
        Db.query(
            `UPDATE "purchase" 
             SET "date_paid"     = $1, 
                 "canceled_date" = NULL,
                 "check_num"     = $2,
                 "status"        = 'paid'
             WHERE "purchase_id" = $3`,[
                new Date(req.body.date_paid).toFormat('YYYY-MM-DD'),
                req.body.check_num,
                purchase_id
            ]
        ).then(() => {
            res.ok()
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // POST /api/event/:event/sales/payment/:payment/void
    void: function (req, res) {
        var purchase_id = req.param('payment');
        if(!req.body.date_canceled) {
            return res.badRequest('No date canceled');
        }

        Db.query(
            `UPDATE purchase
             SET canceled_date = $1,
                 status          = 'canceled'
             WHERE purchase_id = $2`,
            [new Date(req.body.date_canceled).toFormat('YYYY-MM-DD'), purchase_id]
        ).then(() => {
            res.ok()
        }).catch(err => {
            res.customRespError(err);
        });
    },
    //POST /api/event/:event/sales/booths/pay/:type/change
    changePaymentType: function (req, res) {
        let paymentType        = req.params.type,
            eventID            = Number(req.params.event),
            paymentObject      = req.body.payment,
            salesManagerID     = parseInt(req.session.passport.user.sales_manager_id, 10),
            eventOwnerID       = parseInt(req.session.passport.user.event_owner_id, 10),
            userID             = parseInt(req.session.passport.user.user_id, 10),
            userEvent          = req.user.shared_events && req.user.shared_events[eventID],
            isCoOwner          = userEvent && userEvent.role_co_owner,
            iDsObject          = {};

        if(paymentType !== CARD_PAYMENT_TYPE)
            return res.validation('Invalid Payment Type');
        if(_.isEmpty(paymentObject))
            return res.validation('Empty Payment Passed');
        if(!_.isObject(paymentObject))
            return res.validation('Expecting Payments to be an object');
        if(!(salesManagerID || eventOwnerID || isCoOwner))
            return res.forbidden();

        iDsObject.sales_manager_id      = salesManagerID;
        iDsObject.event_owner_id        = eventOwnerID;
        iDsObject.user_id               = userID;

        return BoothsService.paymentTypeChange.change(paymentObject, paymentType, iDsObject)
            .then(() => res.status(200).json({}))
            .catch(err => res.customRespError(err));
    },
    // POST /api/event/:event/sales/payment/:payment/refund
    refund: async (req, res) => {
        let purchaseID  = parseInt(req.params.payment, 10);
        let eventID     = parseInt(req.params.event, 10);

        try {
            let date_refunded = await BoothsService.refund.full.process(purchaseID);

            await BoothsService.notifications.sendFullRefundNotification(eventID, purchaseID);

            res.status(200).json({ date_refunded })
        } catch (err) {
            res.customRespError(err)
        }
    },
}

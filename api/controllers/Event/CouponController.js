const {
    validateCouponTeamGenerationTask,
    validateCustomCouponGenerationTask
} = require('../../validation-schemas/coupons');

module.exports = {
    // GET /api/event/:event/coupon/ticket-types
    async getTicketTypes(req, res) {
        try {
            const eventID = Number(req.params.event);

            if(!eventID) {
                return res.validation('Event ID required');
            }

            const ticketTypes = await CouponService.getTicketTypes(eventID);
            res.json(ticketTypes);
        }
        catch(err) {
            res.customRespError(err);
        }
    },

    // POST /api/event/:event/coupon/generation-task
    async createGenerationTask(req, res) {
        try {
            const eventID = Number(req.params.event);

            if(!eventID) {
                return res.validation('Event ID required');
            }

            const eventTicketIds = await CouponService.getTicketTypes(eventID).then(r => r.map(
                row => row.event_ticket_id
            ));
            const {error, value} = validateCouponTeamGenerationTask(req.body, eventTicketIds);
            if (error) {
                throw { validation: error.details[0].message };
            }
            const counters = await CouponService.generation.processCouponGenerationTask(eventID, value);
            res.json(counters);
        }
        catch(err) {
            res.customRespError(err);
        }
    },

    // GET /api/event/:event/coupon/list
    async getCouponsList(req, res) {
        try {
            const eventID = Number(req.params.event);
            const filters = req.query;

            if(!eventID) {
                return res.validation('Event ID required');
            }

            let coupons = await CouponService.list.getList(eventID, filters)

            res.json({coupons});

        } catch(err) {
            res.customRespError(err);
        }
    },

    //PUT /api/event/:event/coupons
    async updateCoupons(req, res) {
        const eventID = Number(req.params.event);
        const filters = req.body.filters;
        const allowedTicketsCount = Number(req.body.allowed_tickets_count);

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(_.isNaN(allowedTicketsCount) || allowedTicketsCount < 0) {
            return res.validation('Allowed Tickets Count invalid');
        }

        try {
            let updatedCount = await CouponService.couponUpdate.updateTicketsCount(
                eventID, filters, allowedTicketsCount
            );

            res.status(200).json({ updatedCount });

        } catch (err) {
            res.customRespError(err);
        }
    },

    // PUT /api/event/:event/coupon/:coupon/active
    async updateCouponActiveStatus(req, res) {
        const eventID = Number(req.params.event);
        const couponID = Number(req.params.coupon);
        const { active, reason } = req.body;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!couponID) {
            return res.validation('Coupon ID required');
        }

        if(typeof active !== 'boolean') {
            return res.validation('New status required');
        }

        try {
            await CouponService.couponUpdate.updateActiveStatus(
                eventID, couponID, active, reason
            );

            res.status(200).send();

        } catch (err) {
            res.customRespError(err);
        }
    },

    //PUT /api/event/:event/coupons/sending
    async sendCoupons(req, res) {
        const eventID = Number(req.params.event);
        const filters = req.body.filters;
        const isResend = req.body.resend || false;
        const teamsRecipientTypes = req.body.email_recipient_types;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        try {
            let sentEmails = await CouponService.sending.sendCouponsMessages({
                eventID, filters, isResend, teamsRecipientTypes
            });

            res.status(200).json({sentEmails});
        } catch (err) {
            res.customRespError(err);
        }
    },

    //POST /api/event/:event/coupon
    async createCoupon(req, res) {
        const eventID = Number(req.params.event);
        const couponData = req.body;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(_.isEmpty(couponData)) {
            return res.validation('Coupon Data is Empty');
        }

        try {
            const eventTicketIds = await CouponService.getTicketTypes(eventID).then(r => r.map(
                row => row.event_ticket_id
            ));

            couponData.for = CouponService.COUPON_RECEIVER_TYPES.CUSTOM;

            const {error, value} = validateCustomCouponGenerationTask(couponData, eventTicketIds);

            if (error) {
                throw { validation: error.details[0].message };
            }
            const counters = await CouponService.generation.processCouponGenerationTask(eventID, value);

            res.status(200).json(counters);
        }
        catch(err) {
            res.customRespError(err);
        }
    },

    //GET /api/event/:event/coupon/:code/tickets
    boughtTicketsList (req, res) {
        const eventID = Number(req.params.event);
        const code = req.params.code;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!code) {
            return res.validation('Coupon code');
        }

        return CouponService.purchase.getBoughtTicketsList(eventID, code)
            .then(tickets => res.status(200).json({tickets}))
            .catch(res.customRespError.bind(res));
    }
};

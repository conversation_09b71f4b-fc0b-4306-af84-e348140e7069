'use strict';

module.exports = {
    //GET /api/event/:event/accounting/:item(teams|tickets|booths)/payouts
    getPayouts: function (req, res) {
        let eventID = Number(req.params.event);
        let item    = req.params.item;

        AccountingService.PayoutsService.getPayouts(item, eventID)
            .then(payouts => {
                res.status(200).json({ payouts });
            }).catch(err => {
            res.customRespError(err);
        })
    }
};

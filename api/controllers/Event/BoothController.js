'use strict';
const Joi = require('joi');
const { createSchema, updateSchema } = require('../../validation-schemas/event-booth.js');

module.exports = {
    // GET /api/event/:event/booth
    index: function(req, res) {
        Db.query(
            squel.select()
            .field('event_booth_id')
            .field('event_id')
            .field('title')
            .field(`COALESCE(description, 'N/A')`, 'description')
            .field('fee::float')
            .field('is_enabled')
            .from('event_booth')
            .where('event_id = ?', req.params.event)
            .order('created')
        ).then(result => {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // GET /api/event/:event/booth/:id
    find: function(req, res) {
        let $boothId = Number(req.params.id);

        if(!$boothId)
            return res.validation('Invalid Booth Identifier');

        Db.query(
            squel.select()
            .from('event_booth')
                .field('event_booth_id')
                .field('event_id')
                .field('title')
                .field('description')
                .field('fee::float')
                .field('is_enabled')
            .where('event_booth_id = ?', $boothId)
        ).then(result => {
            res.status(200).json({ booth: _.first(result.rows) || {} });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // POST /api/event/:event/booth
    create: async function(req, res) {
        try {
            const eventID = Number(req.params.event);

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }
    
            const { error } = createSchema.validate(req.body);
    
            if (error) {
                throw { validation: error.details[0].message };
            }
    
            await Db.query(squel.insert()
                .into('event_booth')
                .setFields(_.extend({ event_id: eventID }, req.body))
            );
    
            res.ok();
        } catch(err) {
            res.customRespError(err);
        }
    },
    // PUT /api/event/:event/booth/:id
    update: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const boothID = Number(req.params.id);
    
            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }
    
            if (!boothID) {
                throw { validation: 'Invalid Booth Identifier' };
            }
    
            const { error } = updateSchema.validate(req.body);
        
            if (error) {
                throw { validation: error.details[0].message };
            }
    
            await Db.query(
                squel.update()
                .table('event_booth')
                .where('event_booth_id = ?', boothID)
                .where('event_id = ?', eventID)
                .setFields(req.body)
            );
    
            res.ok();
        } catch(err) {
            res.customRespError(err);
        }
    },
    // DELETE /api/event/:event/booth/:id
    destroy: function(req, res) {
        let $eventId = req.params.event,
            $boothId = Number(req.params.id);

        if(!$boothId)
            return res.validation('Invalid Booth Identifier');

        Db.query(
            squel.delete()
            .from('event_booth')
            .where('event_booth_id = ?', $boothId)
            .where('event_id = ?', $eventId)
        ).then(() => {
            res.ok();
        }).catch(err => {
            res.customRespError(err);
        });
    },
};

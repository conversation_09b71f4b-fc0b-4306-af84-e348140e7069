'use strict';

module.exports = {
    // GET /api/event/:event/camps
    all: function (req, res) {
        let $eventId = parseInt(req.params.event, 10);
        if (!$eventId) return res.validation('Invalid event id specified');


        Promise.all([
                CampsService.getAllCamps($eventId),
                CampsService.getEventSeason($eventId),
                CampsService.getEventAgeDate($eventId),
                CampsService.getEventTicketsInfo($eventId)
            ])
            .then(result => {
                res.status(200).json({
                    camps: result[0].rows,
                    season: result[1].rows[0].season,
                    event_age_date: result[2].rows[0].event_age_date,
                    event_date_start: result[2].rows[0].date_start,
                    tickets_info: result[3].rows
                })
            }).catch(err => {
                res.customRespError(err);
            });
    },

    //PUT /api/event/:event/camps
    upsertCamp: function (req, res) {
        let $eventId = parseInt(req.params.event, 10);
        if (!$eventId) return res.validation('Invalid event id specified');

        let $camp = req.body.camp;
        if (!$camp) return res.validation('Invalid Camp specified');

        CampsService.upsertCamp($camp)
            .then((result) => {
                res.status(200).json({
                    camp: result.rows[0]
                });
            }).catch(err => {
                res.customRespError(err);
            });
    },

    // PUT /api/event/:event/camps/age-date 
    updateEventAgeDate: function (req, res) {
        let $eventId = parseInt(req.params.event, 10);
        if (!$eventId) return res.validation('Invalid Event ID Specified');

        let $event_age_date = req.body.event_age_date;

        CampsService.updateEventAgeDate($eventId, $event_age_date)
            .then((result) => {
                if (result.rowCount > 0) {
                    res.status(200).json({
                        updated: $event_age_date
                    });
                } else {
                    throw new Error('Error updating event age date');
                }
            })
            .catch(err => {
                res.customRespError(err);
            });
    },
    // PUT /api/event/:event/camps/swap-sort-order'
    swapSortOrder: function (req, res) {
        let $eventId = parseInt(req.params.event, 10);
        if (!$eventId) return res.validation('Invalid Event ID Specified');

        let $swapObj = req.body.swapObj;
        if (!$swapObj) return res.validation('Invalid Swap Objects Specified');

        let $eventOwnerId = eventOwnerService.findId(req.params.event, req.user);

        CampsService.swapSortOrder($eventId , $eventOwnerId, $swapObj)
            .then((result) => {
                if (result.rowCount == 2) {
                    res.status(200).json({
                        updated : result.rows
                    })
                } else {
                    throw new Error('Error updating sort order');
                }
            })
            .catch(err => {
                res.customRespError(err);
            });
    },
    // DELETE /api/event/:event/:camp
    delete: function (req, res) {
        let event   = Number(req.params.event);
        let camp    = Number(req.params.camp);

        CampsService.deleteCamp(event, camp).then(deleted => {
            if(!deleted) {
                res.validation('Camp purchased or not found');
            } else {
                res.status(200).json({});
            }
        }).catch(err => {
            res.customRespError(err);
        })

    },

    // POST /api/event/:event/:camp/copy
    copy: function (req, res) {
        let event   = Number(req.params.event);
        let camp    = Number(req.params.camp);

        CampsService.copy(event, camp).then(createdCamp => {
            if(!createdCamp) {
                res.validation('Camp is not copied');
            } else {
                res.status(200).json({camp: createdCamp});
            }
        }).catch(err => {
            res.customRespError(err);
        })

    }
}

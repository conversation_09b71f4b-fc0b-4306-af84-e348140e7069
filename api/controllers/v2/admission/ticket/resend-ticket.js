const { PHONE_REGEX } = require('../../../../lib/joi-constants');

module.exports = {
    friendlyName: 'Resends ticket to user',
    description:
        'Resend user tickets for provided email and phone, or resend to saved info',

    inputs: {
        barcode: {
            type: 'number',
            example: 22,
            description: 'Ticket Barcode',
            required: true,
        },
        type: {
            type: 'string',
            example: 'email',
            description: 'Type of info to resend ticket',
            required: false,
            isIn: ['email', 'phone'],
        },
        email: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email',
            isEmail: true,
            required: true,
        },
        phone: {
            type: 'string',
            example: '10000000000',
            description: 'Phone number',
            regex: PHONE_REGEX,
            required: true,
        }
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const { barcode, type, email, phone } = inputs;

        const isAppleDevice = /iPad|iPhone|iPod|Macintosh/gm.test(
            this.req.get('User-Agent')
        );

        try {
            await SWTReceiptService.resendTickets({
                code: barcode,
                type,
                isAppleDevice,
                email,
                phone,
            });

            exits.success({});
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

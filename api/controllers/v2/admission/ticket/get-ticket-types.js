module.exports = {
    friendlyName: 'Get available ticket types to change',
    description:
        'Returns ticket types that could be changed from current type for given ticket',

    inputs: {
        barcode: {
            type: 'number',
            example: 111111111,
            description: 'Ticket Barcode',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const { barcode } = inputs;

        try {
            const eventTicketTypes =
                await TicketsService.eventTicket.getEventTicketTypes({ ticketBarcode: barcode });

            exits.success({ types: eventTicketTypes });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

module.exports = {
    friendlyName: 'User Admission Events',
    description: "Returns events for user's purchased tickets",
    
    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);

        try {
            const events = await getPurchaseEvents(userId);

            exits.success({events});
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

async function getPurchaseEvents(user_id) {
    const query = `WITH user_purchases AS (
                SELECT p.event_id, p.purchase_id FROM purchase AS p
                    INNER JOIN purchase AS pr 
                        ON pr.is_payment IS TRUE
                        AND (pr.purchase_id = p.linked_purchase_id OR pr.purchase_id = p.purchase_id)
                        AND (pr.kiosk IS NULL OR pr.status <> 'pending')
                    WHERE pr.user_id = $1 
                        AND p.is_ticket IS TRUE
                        AND p.payment_for='tickets'
            ),
            ticket_events AS (
                SELECT  
                    e.event_id,
                    e.long_name,  
                    e.name AS "short_name",
                    e.city,
                    e.state,
                    e.timezone,
                    e.date_start,
                    e.date_end,
                    (SELECT COALESCE(jsonb_object_agg(file_type, event_media), '{}'::jsonb) as images
                        FROM (
                            SELECT file_type, json_build_object('id', event_media_id, 'path', concat(file_path, '.', file_ext)) as event_media
                                FROM event_media
                                WHERE event_id = e.event_id AND file_type <> 'small-logo'
                        ) AS event_images) as "images",
                    SUM(pt.quantity)::INT tickets_count
                FROM user_purchases up  
                INNER JOIN "event" e 
                    ON e.event_id = up.event_id
                INNER JOIN purchase_ticket AS pt 
                    ON pt.purchase_id = up.purchase_id
                WHERE e.deleted IS NULL
                GROUP BY e.event_id
                ORDER BY e.date_start
            )
            select te.*,
                (
                    CASE 
                        WHEN NOW() AT TIME ZONE te.timezone < te.date_start
                            THEN 'upcoming'
                        WHEN NOW() AT TIME ZONE te.timezone > te.date_end
                            THEN 'past'
                        ELSE 'current'
                    END
                ) AS status
            FROM ticket_events AS te`;

    return Db.query(query, [user_id])
        .then(({ rows }) => rows)
}

const aauProxyKey = sails.config.aau.proxyKey;

module.exports = {
    friendlyName: 'Get Members',
    description: 'Get Members list',
    inputs: {
        aau_proxy_key: {
            type: 'string',
            description: 'Aau proxy key',
        },
        club_code: {
            type: 'string',
            description: 'Club Code',
        },
        membership_identifier: {
            type: 'string',
            description: 'Membership Identifier',
        },
        zip_code: {
            type: 'string',
            description: 'Zip Code',
        },
        birth_date: {
            type: 'string',
            description: 'Birth Date',
        },
        last_name: {
            type: 'string',
            description: 'Birth Date',
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },
    fn: async function (inputs, exits) {
        try {
            const { aau_proxy_key, ...filters} = inputs;

            if(aauProxyKey !== aau_proxy_key) {
                return this.res.forbidden('Key validation is not passed');
            }

            const members = await AAUService.getMembers(filters);

            exits.success(members);
        }
        catch (err) {
            this.res.customRespError(err);
        }
    },
};

const path = require('path');
const { Worker } = require('worker_threads');
const fs = require('fs');

module.exports = {
    friendlyName: 'Get SW disputes list',
    description: 'Returns all disputes',

    inputs: {
        limit: {
            type: 'number',
            description: 'Limit returned disputes'
        },
        offset: {
            type: 'number',
            description: 'Page number'
        },
        status: {
            type: 'string',
            isIn: ['needs_response', 'under_review', 'won', 'lost'],
            description: 'Status of dispute',
        }
    },

    exits: {
        success: {
            statusCode: 200
        },
        unauthorized: {
            statusCode: 401
        }
    },
    fn: async function (inputs, exits) {
        try {
            const filePath = await __exportDisputeList(inputs);

            this.res.download(filePath, (err) => {
                if (err) {
                    loggers.errors_log.error(err);
                    if (err.code === 'ENOENT') {
                        return this.res.render('500', { error: 'File not found' });
                    } 
                    else {
                        return this.res.serverError();
                    }
                }
                fs.promises.unlink(filePath).catch((err) => loggers.errors_log.error(err));
            });
        } catch (err) {
            loggers.errors_log.error(err);
            this.res.customRespError(err);
        }
    }
};

async function __exportDisputeList(filters) {
    const scriptPath = path.resolve(__dirname, '../../../../../sw-utils/admin-disputes-export.js');

    return new Promise((resolve, reject) => {
        const worker = new Worker(scriptPath, {
            workerData: Buffer.from(JSON.stringify({
                filters,
                connection: sails.config.connections[sails.config.db.connection]
            })).toString('base64')
        });
        worker.on('message', resolve);
        worker.on('error', reject);
        worker.on('exit', (code) => {
            if (code !== 0)
                reject(new Error(`Worker stopped with exit code ${code}`));
        });
    });
}

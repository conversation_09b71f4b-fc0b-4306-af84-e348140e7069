const AdminDisputeService = require("../../../../services/AdminDisputeService");

module.exports = {
    friendlyName: 'Get SW disputes list',
    description: 'Returns all disputes',

    inputs: {
        limit: {
            type: 'number',
            description: 'Limit returned disputes'
        },
        offset: {
            type: 'number',
            description: 'Page number'
        },
        status: {
            type: 'string',
            isIn: ['needs_response', 'under_review', 'won', 'lost'],
            description: 'Status of dispute',
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            const disputes = await AdminDisputeService.getDisputeList(inputs);

            exits.success({ disputes });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
};

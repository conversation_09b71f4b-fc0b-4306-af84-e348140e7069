
const crypto = require('crypto');

const {
    UNIQUE_EVENT_PER_POLICY_DB_CONSTRAINT,
    VERTICAL_INSURANCE_WEBHOOK_SIGNATURE_HEADER
} = require('../../../constants/vertical-insurance');

// post api/vertical-insurance/webhook
module.exports = {
    friendlyName: 'Vertical Insurance Webhook Handler',
    description: 'Handles Vertical Insurance Webhooks',

    exits: {
        success: {
            statusCode: 200,
        },
        unauthorized: {
            statusCode: 401,
        }
    },

    fn: async function (inputs, exits) {
        const webhookSignatureHeaderValue = this.req.headers[VERTICAL_INSURANCE_WEBHOOK_SIGNATURE_HEADER];

        const signatureIsValid = verifySignature(webhookSignatureHeaderValue, this.req.rawBody);

        if(!signatureIsValid) {
            return exits.unauthorized();
        }

        const eventData = JSON.parse(this.req.rawBody);

        try {
            await createEventRow(eventData);

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

function verifySignature(headerHash, rawBody) {
    const signatureKey = sails.config.verticalInsurance.webhook_signature;
    const eventJson = rawBody;

    const hmac = crypto.createHmac('sha256', signatureKey);
    hmac.update(JSON.stringify(eventJson), 'utf-8');

    const signature = hmac.digest('hex');

    return headerHash !== signature;
}

async function createEventRow(eventData) {
    const insertData = {
        event_type: eventData.event,
        policy_id: eventData?.data?.id,
        data: JSON.stringify(eventData)
    }

    const query = knex('vertical_insurance_event').insert(insertData);

    try {
        const { rowCount } = await Db.query(query);

        if (!rowCount) {
            throw new Error('Not Saved');
        }

    } catch (err) {
        if(!Db.utils.isUniqueConstraintViolation(err, UNIQUE_EVENT_PER_POLICY_DB_CONSTRAINT)) {
            throw err;
        }
    }
}

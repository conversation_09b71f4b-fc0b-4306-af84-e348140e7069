
//POST /api/custom-form/event/:event/form/:form_id
module.exports = {
    friendlyName: 'Custom Event Form Submission',
    description: 'Submits custom event form fields',

    inputs: {
        event: {
            type: 'ref',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Event Form ID'
        }
    },

    exits: {
        success: {
            statusCode: 201
        }
    },

    fn: async function (inputs, exits) {
        const {
            event,
            form_id: eventFormID
        } = inputs;

        const {
            submitter,
            values: submittedData
        } = this.req.body;

        // For clubs with private registration
        const eventID = Number(this.req.options.event || event);

        try {
            await EventService.eventCustomForm.submitForm(
                eventID,
                eventFormID,
                submitter,
                submittedData
            );

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}

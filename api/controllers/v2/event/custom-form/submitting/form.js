
//GET /api/custom-form/event/:event/form/:form_id
module.exports = {
    friendlyName: 'Custom Event Form',
    description: 'Returns custom event form fields',

    inputs: {
        event: {
            type: 'ref',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Event Form ID'
        }
    },

    exits: {
        success: {
            statusCode: 200
        },
        validation: {

        }
    },

    fn: async function (inputs, exits) {
        let { event: eventID, form_id: eventFormID } = inputs;
        const submitterID = this.req.query.submitter;

        if(!eventID) {
            return this.res.validation('Event ID invalid');
        }

        if(!eventFormID) {
            return this.res.validation('Form ID invalid');
        }

        try {
            const form = await EventService.eventCustomForm.getForm(eventID, eventFormID, submitterID);

            exits.success(form);
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}

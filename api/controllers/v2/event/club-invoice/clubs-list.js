
//GET /api/event/:event/club-invoice/clubs
module.exports = {
    friendlyName: 'Active clubs list',
    description: 'Returns active clubs list for a specific event',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID } = inputs;

        try {
            const clubs = await ClubInvoiceService.clubsList(eventID);

            exits.success({ clubs });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}

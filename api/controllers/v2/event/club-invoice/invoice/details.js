
//GET /api/event/:event/club-invoice/:invoice
module.exports = {
    friendlyName: 'Event Club Invoices Details',
    description: 'Returns details for a specific club invoice',

    inputs: {
        event: {
            type: 'number',
            description: 'Event Id'
        },
        invoice: {
            type: 'number',
            description: 'Club Invoice Id'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventId, invoice: invoiceId } = inputs;

        try {
            const invoice = await ClubInvoiceService.details.event.get(eventId, invoiceId);

            exits.success({ invoice });
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}

const { PAYMENT_PROVIDER } = require('../../../../constants/payments');
const Permissions = require('../../../../services/event/operations');

const SUB_ACCOUNT_TICKETS_TYPE = 'tickets';
const SUB_ACCOUNT_EVENT_TYPE = 'event';

// GET /api/eo/justifi-sub-accounts/:type
module.exports = {
    friendlyName: 'Justifi Sub Accounts',
    description: 'Justifi Sub Accounts',

    inputs: {
        type: {
            type: 'string',
            example: SUB_ACCOUNT_TICKETS_TYPE,
            description: 'Source type',
            required: true,
            isIn: [SUB_ACCOUNT_TICKETS_TYPE, SUB_ACCOUNT_EVENT_TYPE],
        },
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: false,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
        unauthorized: {
            statusCode: 401,
        },
        forbidden: {
            statusCode: 403,
        },
    },

    fn: async function (inputs, exits) {
        const { type, event } = inputs;

        let userId;
        const isAdmin = this.req.user.is_admin;

        let eventOwnerId;

        if (event) {
            eventOwnerId = eventOwnerService.findId(event, this.req.user);
        } else if (this.req.user.event_owner_id) {
            eventOwnerId = this.req.user.event_owner_id;
        }
        
        if (!eventOwnerId) {
            return exits.forbidden('You have no access to this event');
        }

        if(eventOwnerId !== this.req.user.event_owner_id) {
            userId = this.req.user.user_id;
        }

        try {
            const accounts = await getJustifiSubAccounts({
                type,
                eventOwnerId,
                eventId: event,
                userId,
                isAdmin,
            });

            exits.success({
                accounts,
            });
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

function getJustifiSubAccounts({type, eventOwnerId, eventId, userId, isAdmin}) {
    switch (type) {
        case SUB_ACCOUNT_TICKETS_TYPE:
            return getTicketSubAccounts(eventOwnerId);
        case SUB_ACCOUNT_EVENT_TYPE:
            return getEventSubAccounts(eventOwnerId, eventId, userId, isAdmin);
        default:
            throw { validation: 'Invalid type passed' };
    }
}

function getTicketSubAccounts(eventOwnerId) {
    const query = knex('justifi_sub_account')
        .select({
            account_id: 'justifi_sub_account_id',
            id_at_justifi: 'id_at_justifi',
            title: 'name',
            email: 'email',
            account_statement: knex.raw('null'),
            account_type: knex.raw(`'${SUB_ACCOUNT_TICKETS_TYPE}'`),
            created: 'created',
            modified: 'modified',
            is_test: 'is_test',
            is_platform: knex.raw('false'),
            hidden: 'hidden',
            payment_provider: knex.raw(`'${PAYMENT_PROVIDER.JUSTIFI}'`),
        })
        .where('event_owner_id', eventOwnerId)
        .orderBy('justifi_sub_account_id');

    return Db.query(query).then(({ rows }) => rows);
}

function getEventSubAccounts(eventOwnerId, eventId, userId, isAdmin) {
    if (!eventId) {
        throw { validation: 'Event ID required for "event" type' };
    }

    const coOwnersCte = knex('event_user_permission as eup')
        .select('eo.event_owner_id')
        .leftJoin('event_owner as eo', 'eo.user_id', 'eup.user_id')
        .leftJoin('event as e', 'e.event_id', 'eup.event_id')
        .where('e.event_owner_id', eventOwnerId)
        .whereIn('eup.event_operation_id', [
            Permissions.EDIT_EVENT,
            Permissions.TICKETS_TAB,
        ])
        .whereNull('eup.deleted')
        .where('eup.event_id', eventId)
        .groupBy('eo.event_owner_id');

    const query = knex
        .with('co_owners', coOwnersCte)
        .select({
            account_id: 'justifi_sub_account_id',
            id_at_justifi: 'id_at_justifi',
            title: 'name',
            email: 'email',
            account_statement: knex.raw('null'),
            account_type: knex.raw(`'${SUB_ACCOUNT_EVENT_TYPE}'`),
            created: 'created',
            modified: 'modified',
            is_test: 'is_test',
            is_platform: knex.raw('false'),
            hidden: 'hidden',
            payment_provider: knex.raw(`'${PAYMENT_PROVIDER.JUSTIFI}'`),
        })
        .from('justifi_sub_account as sa');

    if ((!userId || isAdmin) && eventId) {
        query.where(function () {
            this.where('sa.event_owner_id', eventOwnerId).orWhereIn(
                'sa.event_owner_id',
                knex.select('*').from('co_owners')
            );
        });
    } else {
        query
            .leftJoin('event_owner as eo', function () {
                this.on('eo.user_id', '=', knex.raw('?', [userId]));
            })
            .where(function () {
                this.where('sa.event_owner_id', eventOwnerId).orWhere(
                    'sa.event_owner_id',
                    knex.ref('eo.event_owner_id')
                );
            });
    }

    query.orderBy('sa.justifi_sub_account_id');

    return Db.query(query).then(({ rows }) => rows);
}

const moment = require('moment');

module.exports = {
    friendlyName: 'Team Schedule',
    description: 'Returns team schedule',

    inputs: {
        event_id: {
            type: 'ref',
            description: 'Event ID'
        }
    },

    exits: {
        success: {
            statusCode: 200
        },
        resourceNotFound: {
            statusCode: 404
        },
        validationError: {
            statusCode: 400
        },
        serverError: {
            statusCode: 500
        }
    },

    fn: async function(inputs, exits) {
        const eventId = Number(inputs.event_id);
        const {
            startFrom,
            startTill
        } = this.req.query || {};

        const paramsValidationError = await sails.helpers.acs.validateProperties.with({
            properties: {
                event_id: eventId,
                start_from: startFrom,
                start_till: startTill
            }
        });

        if(!_.isEmpty(paramsValidationError)) {
            throw {
                validationError: {
                    error: paramsValidationError
                }
            };
        }

        const dataExistenceError = await sails.helpers.acs.checkEventTeamByCode.with({ eventId });

        if(dataExistenceError) {
            throw {
                resourceNotFound: {
                    error: {
                        messages: [dataExistenceError],
                        properties: []
                    }
                }
            };
        }

        try {
            const formattedStartFrom = _formatDate(startFrom);
            const formattedStartTill = _formatDate(startTill);

            const scheduleData = await sails.helpers.acs.schedule.getTeamsSchedule.with({
                eventId: eventId,
                startFrom: formattedStartFrom,
                startTill: formattedStartTill
            });

            exits.success(scheduleData);
        } catch (err) {
            loggers.errors_log.error(err);

            throw {
                serverError: {
                    error: {
                        messages: ['Server internal error'],
                        properties: []
                    }
                }
            };
        }
    }
}

function _formatDate (date) {
    return date
        ? moment(date, moment.ISO_8601).format('YYYY-MM-DD h:mm:ss a')
        : undefined;
}

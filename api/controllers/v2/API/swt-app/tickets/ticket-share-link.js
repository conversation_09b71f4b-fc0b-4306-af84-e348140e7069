const { PAYMENT_FOR } = require('../../../../../constants/payments');
const QRTicketsGenerator = require('../../../../../lib/QRTicketsGenerator');
const { getReceiptUrl } = require('../../../../../services/SWTReceiptService');

module.exports = {
    friendlyName: "Open shared user's ticket for SWT App",
    description: "Open shared user's ticket for SWT App",

    inputs: {
        shareCode: {
            type: 'string',
            example: '99999999',
            description: 'Code of the ticket',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ shareCode }) {
        try {
            const template = 'tickets/ticket-share-link.ejs';

            const data = await getTicketShareUrls(shareCode);

            this.res.renderMin(template, data);
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

async function getTicketShareUrls(shareCode) {
    const decodedLink = Buffer.from(shareCode, 'base64').toString();

    const [version, linkId, ticketBarcode, timestamp] = decodedLink.split('_');
    const purchase = await getPurchaseByBarcode(ticketBarcode);

    const receiptHash = QRTicketsGenerator.generateHash(
        {
            ticket_barcode: purchase.ticket_barcode,
            purchase_id: purchase.purchase_id,
            user_id: purchase.user_id,
            event_id: purchase.event_id,
        },
        true
    );

    return {
        appUri: `${sails.config.swtApp.appBaseUrl}ticket/${shareCode}`,
        ticketPageUrl: getReceiptUrl(receiptHash),
    };
}

async function getPurchaseByBarcode(barcode) {
    const query = knex('purchase')
        .select('purchase_id', 'event_id', 'user_id', 'ticket_barcode')
        .where('ticket_barcode', barcode)
        .andWhere('payment_for', PAYMENT_FOR.TICKETS);

    const purchase = await Db.query(query).then(({ rows }) => rows[0] || null);

    if (!purchase) {
        throw { validation: 'Invalid code' };
    }

    return purchase;
}

const RecognitionVerificationService = require('../../../../../services/recognition/RecognitionVerificationService');

module.exports = {
    friendlyName: 'Init recognition verification',
    description:
        'Init recognition verification. Creates session for rekognition',

    exits: {
        created: {
            statusCode: 201,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);

        try {
            const recognitionVerification =
                await RecognitionVerificationService.init({
                    userId,
                });

            exits.created({ sessionId: recognitionVerification.session_id });
        } catch (err) {
            if (_.isArray(err) && err[0]?.message) {
                return this.res.status(400).json({ validationErrors: err });
            }

            this.res.customRespError(err);
        }
    },
};

const RecognitionVerificationService = require('../../../../../services/recognition/RecognitionVerificationService');

module.exports = {
    friendlyName: 'Rekogniation action validation',
    description: 'Validate action for rekognition',

    inputs: {
        sessionId: {
            type: 'string',
            description: 'Session ID of recognition verification',
            required: true,
        },
        action: {
            type: 'string',
            description: 'Recognition verification action',
            required: true,
        },
        image: {
            type: 'string',
            description: 'Base64 image for action data',
            required: true,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function ({ sessionId, action, image }, exits) {
        try {
            await RecognitionVerificationService.handleVerificationAction({
                sessionId,
                action,
                image,
            });

            exits.success({ ok: true });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

const HOLDER_OWNER_SHOULD_DIFFER_CONSTRAINT = 'ticket_wallet_holder_user_id_ticket_barcode_unique_index';

module.exports = {
    friendlyName: 'Add ticket to wallet',
    description: 'Adds ticket to user\'s wallet',

    inputs: {
        link_id: {
            type: 'ref',
            example: 12345,
            description: 'Shared ticket link ID',
        },
        ticket_barcode: {
            type: 'ref',
            example: *********,
            description: 'Ticket barcode number',
        }
    },

    exits: {
        success: {
            statusCode: 200
        },
        validationError: {
            statusCode: 400
        },
        serverError: {
            statusCode: 500
        }
    },

    fn: async function (inputs, exits) {
        const user = this.req.session.passport.user;
        const linkID = inputs.link_id;
        const barcode = inputs.ticket_barcode;

        const validationError = __validateParams(inputs);

        if(validationError) {
            return exits.validationError({
                validation: validationError
            });
        }

        try {
            const ticket = await __addTicketToWallet(
                user,
                linkID,
                barcode
            );

            exits.success(ticket);
        } catch (err) {
            if(err.validation) {
                return exits.validationError({
                    validation: err.validation
                });
            }

            exits.serverError({
                message: err.message
            });
        }
    }
};

function __validateParams (inputs) {
    const {
        ticket_barcode: barcode,
        link_id: linkID
    } = inputs || {};

    if(_.isUndefined(barcode) || _.isNull(barcode)) {
        return 'Ticket barcode required';
    }

    if(!_.isNumber(Number(barcode)) || _.isNaN(Number(barcode))) {
        return 'Ticket barcode should be a number';
    }

    if(_.isUndefined(linkID) || _.isNull(linkID)) {
        return 'Link ID required';
    }

    if(!_.isNumber(Number(linkID)) || _.isNaN(Number(linkID))) {
        return 'Link ID should be a number';
    }
}

async function __addTicketToWallet (user, linkID, barcode) {
    const userID = Number(user.user_id);

    const linkData = await __getLinkData(userID, linkID, barcode);

    const ticketData = await sails.helpers.swtApp.getTicketDataForValidation.with({ userID, barcode });

    await __validateTicket(ticketData);

    const userNameIsEqualHolderName = __isUserNameIsEqualToHolderName(user, ticketData);
    const ticketSharedByBuyer = __isTicketSharedByBuyer(linkData, ticketData);

    const ticketIsAllowedForFastLine = userNameIsEqualHolderName && ticketSharedByBuyer;

    await __addTicketWalletRow({
        purchaser_user_id: ticketData.purchaser_user_id,
        ticket_barcode: barcode,
        holder_user_id: userID,
        shared_by_purchaser: ticketSharedByBuyer
    });

    return __getTicket(barcode, ticketIsAllowedForFastLine);
}

async function __getLinkData (userID, linkID, barcode) {
    const query = knex('ticket_wallet_link')
        .select({
            is_expired: knex.raw(`expires_at < NOW()`),
            ticket_barcode: 'ticket_barcode',
            shared_by_user_id: 'shared_by_user_id'
        })
        .where({
            ticket_wallet_link_id: linkID
        });

    const {rows: [data] = {}} = await Db.query(query);

    if (_.isEmpty(data)) {
        throw {validation: 'Link not found'};
    }

    const {
        is_expired,
        ticket_barcode,
        shared_by_user_id
    } = data;

    if (is_expired) {
        throw {validation: 'Link expired'};
    }

    if (Number(ticket_barcode) !== Number(barcode)) {
        throw {validation: 'Invalid ticket in link'};
    }

    if (Number(shared_by_user_id) === Number(userID)) {
        throw {validation: 'Own ticket can\'t be added to the wallet'};
    }

    return data;
}

function __isUserNameIsEqualToHolderName (user, ticket) {
    const userFirst = user.first.trim().toLowerCase();
    const userLast = user.last.trim().toLowerCase();
    const ticketHolderFirst = ticket.first.trim().toLowerCase();
    const ticketHolderLast = ticket.last.trim().toLowerCase();

    return userFirst === ticketHolderFirst && userLast === ticketHolderLast;
}

function __isTicketSharedByBuyer (link, ticket) {
    const sharedByUserID = Number(link.shared_by_user_id);
    const ticketBuyerUserID = Number(ticket.purchaser_user_id);

    return sharedByUserID === ticketBuyerUserID;
}

async function __validateTicket (ticketData) {
    if(_.isEmpty(ticketData)) {
        throw { validation: 'Ticket not found' };
    }

    const {
        is_cancelled,
        user_is_owning_ticket
    } = ticketData;

    if(is_cancelled) {
        throw { validation: 'Ticket not allowed to be added' };
    }

    if(user_is_owning_ticket) {
        throw { validation: `Own ticket can't be added to the wallet` };
    }
}

async function __addTicketWalletRow (data) {
    const query = knex('ticket_wallet').insert(data);

    try {
        const { rowCount } = await Db.query(query);

        if(rowCount === 0) {
            throw new Error('Ticket not added');
        }
    } catch (err) {
        if(Db.utils.isUniqueConstraintViolation(err, HOLDER_OWNER_SHOULD_DIFFER_CONSTRAINT)) {
            throw { validation: 'Own ticket can\'t be added to the wallet' };
        }

        throw err;
    }
}

async function __getTicket (barcode, ticketIsAllowedForFastLine = false) {
    const query = `
        SELECT e.event_id,
            e.long_name,  
            e.name AS "short_name",
            e.city,
            e.state,
            e.date_start::DATE,
            e.date_end::DATE,
            COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
            e.tickets_settings->>'qrcode_version' "qrcode_version",

            (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                FROM ( 
                    SELECT
                        et.event_ticket_id,
                        et.sort_order
                    FROM event_ticket et 
                    WHERE et.event_id = e.event_id 
                    ORDER BY et.event_ticket_id ASC 
                ) "t" 
            ) AS event_tickets,

            JSON_BUILD_OBJECT(
                'quantity', pt.quantity,
                'order', et.sort_order,
                'label', et.label,
                'ticket_type', et.ticket_type,
                'valid_dates_formatted', (
                    SELECT (
                        ARRAY_AGG(
                            TO_TIMESTAMP(
                                vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS'
                            )::TIMESTAMP AT TIME ZONE e.timezone
                            ORDER BY (TO_TIMESTAMP(vd::TEXT || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')) ASC
                        )
                    )
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) AS vd
                ),
                'ticket_barcode', ticket.ticket_barcode,
                'created', FLOOR(EXTRACT(EPOCH FROM ticket.created)),
                'is_own', ${ticketIsAllowedForFastLine},
                'first', ticket.first,
                'last', ticket.last,
                'short_label', et.short_label,
                'ticket_type', et.ticket_type,
                'scannable', et.can_be_scanned,
                'is_refunded', pt."canceled" IS NOT NULL OR ticket."status" = 'canceled' OR ticket."dispute_status" = 'lost',
                'is_deactivated', ticket.deactivated_at NOTNULL,
                'is_scanned', (pt.available = 0 AND ticket.scanned_at IS NOT NULL),
                'valid_dates', (
                    SELECT
                        COALESCE(
                            JSONB_OBJECT_AGG(
                                TO_CHAR(TO_TIMESTAMP(vd::TEXT, 'YYYY-MM-DD'), 'Dy, Mon DD'),
                                TRUE
                            ),
                            '{}'::JSONB
                        )
                    FROM JSONB_OBJECT_KEYS(et.valid_dates) vd
                ),
                'scanned_at', (ticket.scanned_at::TIMESTAMPTZ AT TIME ZONE e.timezone)::DATE,
                'is_purchased_ticket', false
            ) AS ticket
        FROM purchase ticket
                 JOIN purchase payment ON payment.purchase_id = ticket.linked_purchase_id
            AND payment.is_payment IS TRUE
                 JOIN event e ON ticket.event_id = e.event_id
                 JOIN purchase_ticket pt ON pt.purchase_id = ticket.purchase_id
                 JOIN event_ticket et ON et.event_id = e.event_id AND et.event_ticket_id = pt.event_ticket_id
        WHERE ticket.is_ticket IS TRUE
          AND ticket.ticket_barcode = $1
          AND payment.payment_for = 'tickets'
          AND COALESCE((e.tickets_settings ->> 'require_recipient_name_for_each_ticket')::BOOLEAN, FALSE) IS TRUE
    `;

    const { rows: [data] } = await Db.query(query, [barcode]);

    if(_.isEmpty(data)) {
        throw { validation: 'Ticket not found' };
    }

    const tickets = sails.helpers.swtApp.ticketFormatter.with({ ticket: data.ticket, event: data });

    return {
        event_id: data.event_id,
        long_name: data.long_name,
        short_name: data.short_name,
        city: data.city,
        state: data.state,
        date_start: data.date_start,
        date_end: data.date_end,
        tickets: tickets
    };
}

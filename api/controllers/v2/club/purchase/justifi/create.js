//POST /api/club/v2/purchase/justifi

module.exports = {
    friendlyName: 'Teams payment',
    description: 'Payment for teams entry.',

    inputs: {
        event: {
            type: 'number',
            required: true
        },
        amount: {
            type: 'number',
            required: true
        },
        type: {
            type: 'string',
            required: false
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const user = this.req.session.passport.user;

        try{
            const payment = await PaymentService.teams.createJustifiPayment(inputs.event, inputs.amount, user);

            exits.success(payment)
        }catch(err) {
            this.res.customRespError(err);
        }
    }
};

// PUT /api/club/v2/purchase/justifi/change-type

module.exports = {
    friendlyName: 'Teams payment change type for justifi',
    description: 'Payment Type change for justifi',

    inputs: {
        event: {
            type: 'number',
            required: true
        },
        amount: {
            type: 'number',
            required: true
        },
        justifi_checkout_id: {
            type: 'string',
            required: true
        },
        type: {
            type: 'string',
            required: false
        },
        purchase_id: {
            type: 'number',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const master_club_id = Number(this.req.user.master_club_id);
        const club_owner_id = Number(this.req.user.club_owner_id);
        const season = sails.config.sw_season.current;
        const user = {
            ..._.pick(this.req.user, 'user_id', 'email', 'first', 'last'),
            phone: this.req.user.phone_mob
        };

        const data = {
            purchase_id: inputs.purchase_id,
            event_id: inputs.event,
            amount: inputs.amount,
            justifi_checkout_id: inputs.justifi_checkout_id,
            type: inputs.type,
            club_owner_id, 
            master_club_id, 
            season, 
            user,
        }

        try {
            await PaymentService.teams.changeJustifiPaymentType(data);

            exits.success()
        } catch (err) {
            this.res.customRespError(err);
        }
    }
};

//DELETE /api/club/v2/payment-card
module.exports = {
    friendlyName: 'Payments methods Delete',
    description: 'Delete selected payment methods for club director',

    inputs: {
        payment_method_id: {
            type: 'ref',
            description: 'Payment method ID',
        },
    },
    exits: {
        success: {
            statusCode: 204
        }
    },

    fn: async function (inputs, exits) {
        const { payment_method_id: paymentMethodID } = inputs;

        try {
            const userID = this.req.session.passport.user.user_id;

            await checkIfPaymentMethodIsInUse(paymentMethodID);

            await StripeService.paymentCard.removeUserPaymentMethod(userID, paymentMethodID);

            await UserService.history.savePaymentCartDeletingAction(
                getPaymentHistoryParams(this.req, paymentMethodID)
            ).catch(err => loggers.errors_log.error(err));

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}

function getPaymentHistoryParams(req, paymentMethodID) {
    return {
        userID: req.user.user_id,
        sessionID: req.sessionID,
        ip: req.getIP(),
        userAgent: req.getUserAgent(),
        paymentMethodID,
        method: UserService.history.PAYMENT_METHOD_REMOVE_ROLE.CD
    };
}

async function checkIfPaymentMethodIsInUse(paymentMethodID) {
    let paymentMethodEvents = await EventPaymentMethodService.settings.getPaymentMethodEvents(paymentMethodID);

    if(paymentMethodEvents.length) {
        let eventNames = paymentMethodEvents.map(event => event.name);
        throw { validation: `Payment method used in ${eventNames.join(', ')}` };
    }
}

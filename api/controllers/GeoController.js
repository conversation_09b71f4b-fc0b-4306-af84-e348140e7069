'use strict';

module.exports = {
    // GET /api/countries
    get_countries: function (req, res) {
        Db.query(`SELECT c.code, c.name, c.calling_code
                  FROM country c
                  ORDER BY CASE name 
                        WHEN 'United States' THEN 1
                        WHEN 'Canada' THEN 2
                        ELSE 3
                    END, name`)
        .then(result => {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // GET /api/states
    get_states: function (req, res) {
        var country_code = req.param('code');

        var q = squel.select()
            .from('state', 's')
            .field('s.state')
            .field('s.name');

        if (country_code) {
            q.where('s.country = ?', country_code);
        }

        Db.query(q)
        .then(result => {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // GET /api/regions
    get_regions: function (req, res) {
        let country = req.query.country || 'us';

        Db.query(
            `SELECT r.region, r.name 
             FROM region r 
             WHERE lower(country) = lower(trim($1)) 
             ORDER BY (
                CASE 
                    WHEN country = 'US' THEN 1 
                    ELSE 2 
                END
             ), r.name`,
            [country]
        ).then(result => {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.customRespError(err);
        });
    }
};

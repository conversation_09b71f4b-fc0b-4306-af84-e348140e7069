'use strict';

module.exports = {
    //get /api/club/event/:event/divisions
    index: function (req, res) {
        var event_id = parseInt(req.options.event || req.params.event, 10);
        if(!event_id) return res.status(400).json({validation: 'Invalid event identifier passed'});

        Db.query(
            `SELECT 
                d.division_id "id", 
                d.name, 
                d.gender, 
                d.max_age, 
                d.closed  
             FROM division d 
             WHERE d.event_id = $1 
               AND d.published = TRUE  
               AND d.closed IS NULL`,
            [event_id]
        ).then(result => {
            res.status(200).json({ divisions: result.rows });
        }).catch(err => {
            res.customRespError(err);
        });
    }
}

'use strict';

module.exports = {
    index: function (req, res) {
        var jsonData = '[{"field":"Participant-First","label":"Participant First","type":"text","required":true},{"field":"Participant-Last","label":"Participant Last","type":"text","required":true},{"field":"College-name","label":"College name","type":"text","required":true},{"field":"Position","label":"Position","type":"select","options":{"OH/RS":"OH/RS","MB":"MB","S":"S","DS/L":"DS/L"},"required":true},{"field":"Height","label":"Height ","type":"select","options":{"4\'06\\"":"4\'06\\"","4\'07\\"":"4\'07\\"","4\'08\\"":"4\'08\\"","4\'09\\"":"4\'09\\"","4\'10\\"":"4\'10\\"","4\'11\\"":"4\'11\\"","5\'00\\"":"5\'00\\"","5\'01\\"":"5\'01\\"","5\'02\\"":"5\'02\\"","5\'03\\"":"5\'03\\"","5\'04\\"":"5\'04\\"","5\'05\\"":"5\'05\\"","5\'06\\"":"5\'06\\"","5\'07\\"":"5\'07\\"","5\'08\\"":"5\'08\\"","5\'09\\"":"5\'09\\"","5\'10\\"":"5\'10\\"","5\'11\\"":"5\'11\\"","6\'00\\"":"6\'00\\"","6\'01\\"":"6\'01\\"","6\'02\\"":"6\'02\\"","6\'03\\"":"6\'03\\"","6\'04\\"":"6\'04\\"","6\'05\\"":"6\'05\\"","6\'06\\"":"6\'06\\"","6\'07\\"":"6\'07\\"","6\'08\\"":"6\'08\\"","6\'09\\"":"6\'09\\"","6\'10\\"":"6\'10\\"","6\'11\\"":"6\'11\\"","7\'00\\"":"7\'00\\"","7\'01\\"":"7\'01\\"","7\'02\\"":"7\'02\\"","7\'03\\"":"7\'03\\"","7\'04\\"":"7\'04\\"","7\'05\\"":"7\'05\\"","7\'06\\"":"7\'06\\""},"required":true},{"field":"Weight","label":"Weight","type":"text","required":true},' + 
        '{"field":"Standing-reach","label":"Standing reach","type":"select","options":{"7\'00\\"":"7\'00\\"","7\'01\\"":"7\'01\\"","7\'02\\"":"7\'02\\"","7\'03\\"":"7\'03\\"","7\'04\\"":"7\'04\\"","7\'05\\"":"7\'05\\"","7\'06\\"":"7\'06\\"","7\'07\\"":"7\'07\\"","7\'08\\"":"7\'08\\"","7\'09\\"":"7\'09\\"","7\'10\\"":"7\'10\\"","7\'11\\"":"7\'11\\"","8\'00\\"":"8\'00\\"","8\'01\\"":"8\'01\\"","8\'02\\"":"8\'02\\"","8\'03\\"":"8\'03\\"","8\'04\\"":"8\'04\\"","8\'05\\"":"8\'05\\"","8\'06\\"":"8\'06\\"","8\'07\\"":"8\'07\\"","8\'08\\"":"8\'08\\"","8\'09\\"":"8\'09\\"","8\'10\\"":"8\'10\\"","8\'11\\"":"8\'11\\"","9\'00\\"":"9\'00\\"","9\'01\\"":"9\'01\\"","9\'02\\"":"9\'02\\"","9\'03\\"":"9\'03\\"","9\'04\\"":"9\'04\\"","9\'05\\"":"9\'05\\"","9\'06\\"":"9\'06\\"","9\'07\\"":"9\'07\\"","9\'08\\"":"9\'08\\"","9\'09\\"":"9\'09\\"","9\'10\\"":"9\'10\\"","9\'11\\"":"9\'11\\"","10\'00\\"":"10\'00\\"","10\'01\\"":"10\'01\\"","10\'02\\"":"10\'02\\"","10\'03\\"":"10\'03\\"","10\'04\\"":"10\'04\\"","10\'05\\"":"10\'05\\"","10\'06\\"":"10\'06\\"","10\'07\\"":"10\'07\\"","10\'08\\"":"10\'08\\"","10\'09\\"":"10\'09\\"","10\'10\\"":"10\'10\\"","10\'11\\"":"10\'11\\"","11\'00\\"":"11\'00\\"","11\'01\\"":"11\'01\\"","11\'02\\"":"11\'02\\"","11\'03\\"":"11\'03\\"","11\'04\\"":"11\'04\\"","11\'05\\"":"11\'05\\"","11\'06\\"":"11\'06\\"","11\'07\\"":"11\'07\\"","11\'08\\"":"11\'08\\"","11\'09\\"":"11\'09\\"","11\'10\\"":"11\'10\\"","11\'11\\"":"11\'11\\"","12\'00\\"":"12\'00\\""},"required":false},' + 
        '{"field":"Approach-Reach","label":"Approach Reach","type":"select","options":{"7\'00\\"":"7\'00\\"","7\'01\\"":"7\'01\\"","7\'02\\"":"7\'02\\"","7\'03\\"":"7\'03\\"","7\'04\\"":"7\'04\\"","7\'05\\"":"7\'05\\"","7\'06\\"":"7\'06\\"","7\'07\\"":"7\'07\\"","7\'08\\"":"7\'08\\"","7\'09\\"":"7\'09\\"","7\'10\\"":"7\'10\\"","7\'11\\"":"7\'11\\"","8\'00\\"":"8\'00\\"","8\'01\\"":"8\'01\\"","8\'02\\"":"8\'02\\"","8\'03\\"":"8\'03\\"","8\'04\\"":"8\'04\\"","8\'05\\"":"8\'05\\"","8\'06\\"":"8\'06\\"","8\'07\\"":"8\'07\\"","8\'08\\"":"8\'08\\"","8\'09\\"":"8\'09\\"","8\'10\\"":"8\'10\\"","8\'11\\"":"8\'11\\"","9\'00\\"":"9\'00\\"","9\'01\\"":"9\'01\\"","9\'02\\"":"9\'02\\"","9\'03\\"":"9\'03\\"","9\'04\\"":"9\'04\\"","9\'05\\"":"9\'05\\"","9\'06\\"":"9\'06\\"","9\'07\\"":"9\'07\\"","9\'08\\"":"9\'08\\"","9\'09\\"":"9\'09\\"","9\'10\\"":"9\'10\\"","9\'11\\"":"9\'11\\"","10\'00\\"":"10\'00\\"","10\'01\\"":"10\'01\\"","10\'02\\"":"10\'02\\"","10\'03\\"":"10\'03\\"","10\'04\\"":"10\'04\\"","10\'05\\"":"10\'05\\"","10\'06\\"":"10\'06\\"","10\'07\\"":"10\'07\\"","10\'08\\"":"10\'08\\"","10\'09\\"":"10\'09\\"","10\'10\\"":"10\'10\\"","10\'11\\"":"10\'11\\"","11\'00\\"":"11\'00\\"","11\'01\\"":"11\'01\\"","11\'02\\"":"11\'02\\"","11\'03\\"":"11\'03\\"","11\'04\\"":"11\'04\\"","11\'05\\"":"11\'05\\"","11\'06\\"":"11\'06\\"","11\'07\\"":"11\'07\\"","11\'08\\"":"11\'08\\"","11\'09\\"":"11\'09\\"","11\'10\\"":"11\'10\\"","11\'11\\"":"11\'11\\"","12\'00\\"":"12\'00\\""},"required":false},' + 
        '{"field":"Player-phone-number","label":"Player phone number","type":"text","required":true},{"field":"Player-address","label":"Player address","type":"text","required":true},{"field":"City","label":"City","type":"text","required":true},{"field":"State","label":"State","type":"select","options":{"AL":"Alabama","AK":"Alaska","AZ":"Arizona","AR":"Arkansas","CA":"California","CO":"Colorado","CT":"Connecticut","DC":"District of Columbia","DE":"Delaware","FL":"Florida","GA":"Georgia","HI":"Hawaii","ID":"Idaho","IL":"Illinois","IN":"Indiana","IA":"Iowa","KS":"Kansas","KY":"Kentucky","LA":"Louisiana","ME":"Maine","MD":"Maryland","MA":"Massachusetts","MI":"Michigan","MN":"Minnesota","MS":"Mississippi","MO":"Missouri","MT":"Montana","NE":"Nebraska","NV":"Nevada","NH":"New Hampshire","NJ":"New Jersey","NM":"New Mexico","NY":"New York","NC":"North Carolina","ND":"North Dakota","OH":"Ohio","OK":"Oklahoma","OR":"Oregon","PA":"Pennsylvania","PR":"Puerto Rico","RI":"Rhode Island","SC":"South Carolina","SD":"South Dakota","TN":"Tennessee","TX":"Texas","UT":"Utah","VT":"Vermont","VA":"Virginia","WA":"Washington","WV":"West Virginia","WI":"Wisconsin","WY":"Wyoming"},"required":true},{"field":"participant_zip","label":"Zip","type":"text","required":true},{"field":"Player-email","label":"Player email","type":"text","required":true},{"field":"Coaches-name","label":"Coaches name","type":"text","required":false},{"field":"Coaches-phone-number","label":"Coaches phone number","type":"text","required":false},{"field":"Coaches-email","label":"Coaches email","type":"text","required":false},{"field":"NCAA-Qualifier","label":"NCAA Qualifier","type":"select","options":{"Y":"Yes","N":"No"},"required":true},{"field":"Clearinghouse-number","label":"Clearinghouse number","type":"text","required":false},{"field":"High-school-GPA","label":"High school GPA","type":"select","options": {"2.0":"2.0 or less","2.1":"2.1","2.2":"2.2","2.3":"2.3","2.4":"2.4","2.5":"2.5","2.6":"2.6","2.7":"2.7","2.8":"2.8","2.9":"2.9","3.0":"3.0","3.1":"3.1","3.2":"3.2","3.3":"3.3","3.4":"3.4","3.5":"3.5","3.6":"3.6","3.7":"3.7","3.8":"3.8","3.9":"3.9","4.0":"4.0","4.1":"4.1","4.2":"4.2","4.3":"4.3 or higher"},"required":false},{"field":"Two-year-college-GPA","label":"Two year college GPA","type":"select","options":{"2.0":"2.0 or less","2.1":"2.1","2.2":"2.2","2.3":"2.3","2.4":"2.4","2.5":"2.5","2.6":"2.6","2.7":"2.7","2.8":"2.8","2.9":"2.9","3.0":"3.0","3.1":"3.1","3.2":"3.2","3.3":"3.3","3.4":"3.4","3.5":"3.5","3.6":"3.6","3.7":"3.7","3.8":"3.8","3.9":"3.9","4.0":"4.0","4.1":"4.1","4.2":"4.2","4.3":"4.3 or higher"},"required":false},{"field":"Semester/quarter-units-completed","label":"Semester/quarter units completed","type":"text","required":false},{"field":"Major","label":"Major","type":"text","required":false},{"field":"AA-completion-date","label":"AA completion date","type":"text","required":false},{"field":"Athletic-awards","label":"Athletic awards","type":"text","required":false},{"field":"Link-to-CCCAA-stats","label":"Link to CCCAA stats","type":"text","required":false},{"field":"Link-to-highlight-video","label":"Link to highlight video","type":"text","required":false},{"field":"T-Shirt-Size","label":"T-Shirt Size","type":"select", "options": {"XS":"XS","S":"S","M":"M","L":"L","XL":"XL"},"required":false},' + 

        '{"field": "current-year-in-school", "label": "Current Year in School", "type":"select", "options": {"freshman": "Freshman", "sophomore": "Sophomore"}, "required": true}' 
        +']',
            sql = 'update "event" set tickets_purchase_additional_fields = $1 where event_id = 60 returning tickets_purchase_additional_fields;'
       Db.query(sql, jsonData)
       .then(result => {
            res.ok();
       }).catch(err => {
            res.customRespError(err);
       });
    }
}

const { POINT_OF_SALES_TYPE } = require("../constants/sales-hub");
const TicketBuyEntryCodeService = require("../services/TicketBuyEntryCodeService");

module.exports = {

    //GET /api/ticket-entry-code/event/:event/code/:code/validation
    validateCode: async (req, res) => {
        const eventID = Number(req.params.event);
        const code = req.params.code;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!code) {
            return res.validation('Code required');
        }

        try {
            let { success } = await TicketBuyEntryCodeService.validateCode(eventID, code);

            if(success) {
                res.status(200).json({});
            } else {
                res.validation('Code is not valid');
            }
        } catch (err) {
            res.customRespError(err);
        }
    },

    //POST /api/ticket-entry-code/event/:event/settings
    upsertSettings: async (req, res) => {
        const eventID = Number(req.params.event);
        const settings = req.body.settings;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(_.isEmpty(settings)) {
            return res.validation('Settings can\'t be empty');
        }

        try {
            await TicketBuyEntryCodeService.settings.upsert(eventID, settings);

            await SalesHubService.sync.syncPointOfSales(eventID, POINT_OF_SALES_TYPE.TICKETS);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    //GET /api/ticket-entry-code/event/:event/list
    couponsList: async (req, res) => {
        const eventID = Number(req.params.event);
        const filters = req.query;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        try {
            let { codes, settings } = await TicketBuyEntryCodeService.getCodesList(eventID, filters);

            res.status(200).json({ codes, settings });
        } catch (err) {
            res.customRespError(err);
        }
    },

    //GET /api/ticket-entry-code/event/:event/code/:code/tickets
    couponTicketsList: async (req, res) => {
        const eventID = Number(req.params.event);
        const ticketBuyEntryCode = req.params.code;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!ticketBuyEntryCode) {
            return res.validation('Coupon code required');
        }

        try {
            let tickets = await TicketBuyEntryCodeService.getCodeTicketsList(eventID, ticketBuyEntryCode);

            res.status(200).json({tickets});
        } catch (err) {
            res.customRespError(err);
        }
    },

    //POST /api/ticket-entry-code/event/:event/code/custom
    createCustomCode: async (req, res) => {
        const eventID = Number(req.params.event);
        const code = req.body?.code;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!code) {
            return res.validation('Code required');
        }

        try {
            await TicketBuyEntryCodeService.codeCreation.createCode(eventID, code);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    }
}

'use strict';

module.exports = {
	// GET /api/admin/user/activate_eo/:code
	activateEORole  : activateEORole,

    // GET /api/admin/user/all
    getUsersList    : getUsersList
};

function activateEORole (req, res) {
	let $code = req.params.code;

	UserService.reg.activateEORole($code, req.user.user_id, req.protocol, req.headers.host)
	.then(() => {
		res.send('OK');
	})
	.catch(res.customRespError.bind(res));
}

function getUsersList (req, res) {
    let params = req.query;

    UserService.admin.find(params)
        .then(users => res.status(200).json({users}))
        .catch(res.customRespError.bind(res));
}

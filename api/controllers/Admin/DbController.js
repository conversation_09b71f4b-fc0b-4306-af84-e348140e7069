'use strict';

const spawn = require('child_process').spawn;
const path  = require('path');
const argv 	= require('optimist').argv;
const Utils = require('../../lib/swUtils');

const REQUEST_TIMEOUT = 10;

const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

const TABLES_TO_COPY = [
    'event', 'event_location', 'event_camp', 'event_ticket', 'division', 'roster_team',
    'roster_club', 'courts', 'division_standing', 'event_journal', 'matches', 'poolbrackets',
    'results', 'rounds', 'purchase', 'event_official_schedule'
];

module.exports = {
    replaceLineBreaks: function (req, res) {
        var query = 
            "DO \
            $do$ \
            BEGIN     \
                EXECUTE ( \
                  SELECT     \
                    string_agg( \
                        format('UPDATE %s SET %s RETURNING *', attrelid, columns), \
                        E';\n' \
                    ) \
                    FROM ( \
                        SELECT  \
                            a.attrelid::regclass,  \
                            string_agg(format('\"%1$s\" =  regexp_replace(\"%1$s\", E''[\\n\\r]+'', '' '', ''g'')', attname),', ') AS columns        \
                        FROM pg_attribute a \
                        JOIN pg_class c  \
                            ON c.oid = a.attrelid \
                        JOIN pg_namespace n  \
                            ON n.oid = c.relnamespace \
                        WHERE a.atttypid = 'varchar'::regtype \
                           AND NOT a.attisdropped                       \
                           AND a.attnum > 0                             \
                           AND format_type(a.atttypid, a.atttypmod) LIKE 'character varying%' \
                           AND n.nspname NOT LIKE ALL ('{pg_%, information_schema}'::TEXT[]) \
                           AND c.relkind = 'r' \
                           AND a.attrelid::regclass NOT IN ('courts', 'matches', 'poolbrackets', 'rounds', 'results') \
                        GROUP BY a.attrelid \
                    ) relation \
                ); \
            END \
            $do$;";

        Db.query(query)
        .then(() => {
            res.ok();
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/admin/db/age/set
    set_age: function (req, res) {
        Db.query(
            `SELECT ma.master_athlete_id, ma.birthdate 
             FROM master_athlete ma 
             WHERE ma.birthdate IS NOT NULL 
                AND ma.age IS NULL
                AND ma.season = $1`,
            [sails.config.sw_season.current]
        ).then(result => {
            let athletes = result.rows;

            if(!athletes.length) {
                return 0;
            }

            return Db.client()
            .then(client => {
                let count = 0;

                return Promise.all(
                    athletes.map(a => {
                        let age = WebpointUtility.getMinAge(a.birthdate);
                        return client.query(
                            squel.update().table('master_athlete')
                                .set('age', age)
                                .where('master_athlete_id = ?', a.master_athlete_id)
                                .returning('master_athlete_id')
                        ).then(result => {
                            count += result.rowCount;
                        })
                    })
                ).then(() => {
                    return client.release();
                }).then(() => {
                    return count;
                });
            });
        }).then(count => {
            res.status(200).json({count});
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // GET /api/admin/db-connections
    connectionsQty: function (req, res) {
        res.status(200).json(Db.poolStats());
    },

    // POST /api/admin/db/copy-prod/
    copyProdDB: function (req, res) {
        if(!argv.prod) {
            //set request timeout
            req = Utils.changeRequestTimeout(req, REQUEST_TIMEOUT);
        }

        return proceedDbCopy(req.body).then(() => {
            res.status(200).send('OK')
        })
        .catch(res.customRespError.bind(res));
    },

    // POST /api/admin/db/copy-prod/:table
    copyProdDBTable: function (req, res) {

        if(!argv.prod) {
            //set request timeout
            req = Utils.changeRequestTimeout(req, REQUEST_TIMEOUT);
        }

        let tableName = req.params.table;

        if(!TABLES_TO_COPY.includes(tableName)) {
            return res.status(400).json({ validation: `"${tableName}" not exist`});
        }

        return proceedDbCopy(req.body, tableName)
            .then(() => {
                res.status(200).json({ table_name: tableName })
            })
            .catch(res.customRespError.bind(res));
    }
}

function convertConnectionObjToStr (obj) {
    if (_.isString(obj)) {
        return obj;
    } else {
        return `postgres://${obj.user}:${obj.password}@${obj.host}:${obj.port}/${obj.database}`;
    }
}

function getErrorMSG (msg) {
    try {
        let jsonErr = JSON.parse(msg);
        return jsonErr.error;
    } catch (err) {
        return msg;
    }
}

function proceedDbCopy(params, tableName) {
    return new Promise((resolve, reject) => {
        let errorMsg = [];

        let env             = Object.create(process.env);
        env.__IS_CHILD__    = true;

        let proc = spawn('node', [
            'copy-tables-contents.js',
            convertConnectionObjToStr(SAILS_CONNECTION),
            params.pswd,
            params.event_id,
            tableName
        ], {
            cwd: path.resolve(__dirname, '..', '..', '..', 'sw-utils'),
            env
        });

        proc.on('error', err => {
            reject({
                validation: (err && err.toString())
                    ? getErrorMSG(err)
                    : getErrorMSG(errorMsg.join(''))
            });
        });

        proc.on('close', exitCode => {
            if (exitCode !== 0) {
                reject({ validation: errorMsg.length ? getErrorMSG(errorMsg.join('')) : 'Internal Error' });
            } else {
                const updatedTables = [
                    'event',
                    'event_ticket'
                ].filter(n => tableName ? n === tableName : true);
                if(updatedTables.length > 0) {
                    Cache.invalidateTags(updatedTables.map(n => Cache.tag.dbTable(n)))
                        .catch(err => Cache.tagInvalidator.errorHandler(updatedTables.join(', '), err));
                }
                resolve();
            }
        });

        proc.stdout.on('data', data => {
            loggers.debug_log.verbose(data.toString());
        });

        proc.stderr.on('data', function (error) {
            errorMsg.push(error);
        });
    })
}

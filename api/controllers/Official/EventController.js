'use strict';

const co = require('co');
const {SANCTIONING_CHECK_FIELDS} = require('../../constants/event-official');

module.exports = {
    // GET /api/official/events
    index: async function (req, res) {
        let official_id = Number(req.session.passport.user.official_id);
        const filters = req.query || {};

        try {
            const events = await OfficialsService.officialEvents.list(official_id, filters);

            res.status(200).json({ events });
        } catch (err) {
            res.customRespError(err);
        }
    },

    // POST /api/official/event/:event/check_in
    check_in: function (req, res) {
        let eventID = parseInt(req.params.event, 10);
        let data    = _.extend({
            official_id     : req.session.passport.user.official_id, 
            first           : req.user.first, 
            last            : req.user.last 
        }, req.body);

        OfficialsService.checkin.applyToEvent(eventID, data)
        .then(result => {
            let { eventOfficialID, is_staff, is_official } = result;

            let actions = [];

            if(is_staff) {
                actions.push(
                    OfficialsService.sendRoleNotification('enter', AEMService.STAFF_GROUP, eventOfficialID, eventID)
                );
            }

            if(is_official) {
                actions.push(
                    OfficialsService.sendRoleNotification('enter', AEMService.OFFICIAL_GROUP, eventOfficialID, eventID)
                );
            }

            return actions.length
                ? Promise.all(actions)
                : Promise.resolve();
        })
        .then(() => {
            res.ok();
        })
        .catch(res.customRespError.bind(res));   
    },
    // PUT /api/official/event/:event/update
    update: function (req, res) {
        let eventID     = parseInt(req.params.event, 10);
        let officialID  = req.session.passport.user.official_id;

        let data        = _.extend({
            official_id     : officialID, 
            first           : req.user.first, 
            last            : req.user.last 
        }, req.body);


        OfficialsService.checkin.updateApplicationData(eventID, data)
        .then(result => {
            let notifications = [];

            if (result.general.work_status !== 'declined') {
                result.general.name     = `${req.user.first} ${req.user.last}`;   
                result.general.first    = req.user.first;
                result.general.last     = req.user.last;
                result.general.email    = req.user.email;

                let url = `${req.protocol}://${req.hostname}`

                let regInfoChangeNotif = OfficialsService.regInfoChangeNotify(
                    eventID, officialID, result, url
                );
                notifications.push(regInfoChangeNotif);
            }

            let {initial, current} = result;

            let receiverGroup = null;

            if (!initial.is_staff && current.is_staff) { 
                receiverGroup = AEMService.STAFF_GROUP
            } else if (!initial.is_official && current.is_official) { 
                receiverGroup = AEMService.OFFICIAL_GROUP
            }

            if (receiverGroup) {
                let enterEventNotif = OfficialsService.sendRoleNotification(
                    'enter', receiverGroup, initial.event_official_id, eventID
                );
                notifications.push(enterEventNotif);
            }

            let logError = loggers.errors_log.error.bind(loggers.errors_log);

            return Promise.all(notifications).catch(logError);
        })
        .then(() => {
            res.status(200).send('OK');
        })
        .catch(res.customRespError.bind(res));
    },
    // DELETE /api/official/event/:event/check_out
    check_out: async function (req, res) {
        try {
            let $eventID    = Number(req.params.event),
                $officialID = Number(req.session.passport.user.official_id);
    
            const { reason, role } = req.body;
    
            if (!reason) {
                return res.validation('Reason Required!');
            }
    
            if(!role || !OfficialsService.checkin.PROFILE_ROLES.includes(role)) {
                return res.validation('Role Required!');
            }
    
            let withdrewOfficial = await OfficialsService.checkin.checkOutFromEvent(
                $eventID, $officialID, role, reason
            );
            
            await sendWithdrawEmailToEO($eventID, $officialID, withdrewOfficial, reason, role);
            
            let notifications = [];
    
            if(role === OfficialsService.checkin.ROLE.OFFICIAL) {
                notifications.push(
                    sendWithdrawNotificationToOfficial($eventID, withdrewOfficial)
                );
            } else if(role === OfficialsService.checkin.ROLE.STAFF) {
                notifications.push(
                    sendWithdrawNotificationToStaff($eventID, withdrewOfficial)
                );
            }
    
            await Promise.all(notifications);
    
            res.status(200).end();
        } catch (error) {
            res.customRespError(error)
        }
    },
    // GET /api/official/event/:event/checked/data
    show_checked_data: function (req, res) {
        // for handling unspecified gender
        const allowedGenders = ['male', 'female'];
        const defaultGender = 'male';
        const userClothingGender = allowedGenders.includes(req.user.gender) ? req.user.gender : defaultGender;

        let official_id = req.session.passport.user.official_id,
            event_id    = Number(req.params.event);

        Db.query(
            `SELECT (
                SELECT (CASE WHEN ("eof"."event_official_id" IS NOT NULL) THEN ROW_TO_JSON("d") ELSE '{}'::JSON END)
                FROM (
                    SELECT 
                      eof.additional_restrictions, eof.departure_datetime, eof.is_official, 
                      eof.is_staff, eof.need_hotel_room, eof.roommate_preference, eof.staff_payment_option,
                      eof.schedule_availability, eof.travel_method, eof.payment_option, 
                      TO_CHAR(eof.staff_arrival_datetime, 'YYYY-MM-DD HH24:MI:SS') "staff_arrival_datetime",
                      eof.bank_account_number, eof.bank_account_routing, eof.hotel_nights_required, 
                      (
                        CASE
                            WHEN (eof.deleted IS NOT NULL)
                                THEN TO_CHAR(eof.deleted AT TIME ZONE e.timezone, 'Mon DD, YYYY')                                                          
                            ELSE NULL
                        END 
                      ) "deleted_official_date",
                      (
                        CASE
                            WHEN (eof.staff_deleted IS NOT NULL)
                                THEN TO_CHAR(eof.staff_deleted AT TIME ZONE e.timezone, 'Mon DD, YYYY')                                   
                            ELSE NULL
                        END 
                      ) "deleted_staff_date",
                      eof.arbiterpay_username, eof.arbiterpay_account_number, eoar.official_additional_role_id,
                      eof.rq_pay_username
                ) "d"
             ) "role_data",(
                SELECT (CASE WHEN ("eof"."event_official_id" IS NOT NULL) THEN ROW_TO_JSON("d") ELSE '{}'::JSON END)
                FROM (
                    SELECT 
                      eof.head_official, eof.work_status
                ) "d"
             ) "official_data", (
                SELECT COALESCE(ROW_TO_JSON("event_data"), '{}'::JSON)
                FROM (
                    SELECT
                        e.name, e.date_start, e.long_name, e.date_end, e.date_reg_open,
                        (DATE_PART('day',e.date_end - e.date_start) + 1) "days_count",  
                        e.date_reg_close, e.reg_fee, e.roster_deadline, e.state, e.city,
                        e.address, e.website, e.email, e.has_male_teams, e.has_female_teams, 
                        e.has_coed_teams, e.social_links, e.official_payment_method, 
                        e.officials_hotel_comp, e.enable_hotel_for_officials, e.sport_sanctioning_id,
                        e.available_officials_sanctionings,
                        (
                            e.enable_officials_reg IS TRUE
                            AND e.has_officials IS TRUE
                            AND e.date_official_reg_close > (NOW() AT TIME ZONE e.timezone)
                            AND e.date_official_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        ) "allow_official_registration",
                        (
                            e.enable_staff_reg IS TRUE
                            AND e.has_staff IS TRUE
                            AND e.date_staff_reg_close > (NOW() AT TIME ZONE e.timezone)
                            AND e.date_staff_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        ) "allow_staff_registration",                        
                        e.staff_payment_method, e.enable_hotel_for_staff, e.staff_hotel_comp,                     
                        ARRAY( 
                            SELECT TO_CHAR(dd, 'Dy, Mon DD') 
                            FROM GENERATE_SERIES ( 
                                e.date_start, 
                                e.date_end, 
                                '1 day'::INTERVAL 
                            ) "dd"
                        ) "days_dates", (
                            CASE 
                                WHEN  (e.date_end::DATE < (NOW() AT TIME ZONE e.timezone)::DATE)
                                    THEN TO_CHAR(e.date_end, 'Mon DD, YYYY')
                                ELSE NULL
                            END
                        ) "finished_at",
                        e.official_additional_role_enable
                ) "event_data"
              ) "event", (
                SELECT COALESCE(ROW_TO_JSON("profile_data"), '{}'::JSON)
                    FROM (
                        SELECT o.arbiter_pay_username, 
                        o.arbiter_pay_account_number,
                        o.usav_num, o.aau_number,
                        COALESCE(o.is_staff, FALSE) :: BOOLEAN "user_has_staff_role", 
                        COALESCE(o.is_official, FALSE) :: BOOLEAN "user_has_official_role"
                    ) "profile_data"
              ) "profile_data", (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("h"))), '[]'::JSON)
                FROM (
                    SELECT 
                        FORMAT('%s %s', u.first, u.last) "name",
                        u.email
                    FROM "event_official" eof
                    INNER JOIN "official" of 
                        ON of.official_id = eof.official_id 
                    INNER JOIN "user" u 
                        ON u.user_id = of.user_id
                    WHERE eof.event_id = e.event_id
                        AND eof.head_official IS TRUE
                        AND eof.deleted IS NULL 
                        AND eof.work_status = 'approved'
                ) "h"
              ) "heads",
                (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("cl"))), '[]'::JSON)
                FROM (
                    WITH gender_subquery AS (
                        SELECT DISTINCT
                            ec_sub.common_item_id,
                            ec_sub.member_type,
                            ARRAY_AGG(DISTINCT ec_sub."gender") AS genders
                        FROM
                            "event_clothes" ec_sub
                        WHERE
                            ec_sub.deleted IS NULL
                            AND ec_sub.event_id = e.event_id
                        GROUP BY
                            ec_sub.common_item_id, ec_sub.member_type
                    )
                    SELECT
                        ec.common_item_id,
                        COALESCE(oecs."size", ocs."size") AS "size",
                        ARRAY_AGG(DISTINCT ec.member_type) AS member_types,
                        COALESCE(gs.genders, '{}') AS genders,
                        ARRAY_TO_STRING(ARRAY_AGG(DISTINCT oecs."gender_size"), ',') as gender_size,
                        ci.title,
                        ci.details->>'size_type' AS size_type
                    FROM
                        "event_clothes" ec
                    LEFT JOIN
                        official_clothes_size ocs ON ocs.common_item_id = ec.common_item_id AND ocs.official_id = $2
                    LEFT JOIN (
                        SELECT
                            sub_oecs."size",
                            sub_ec.common_item_id,
                            sub_ec."gender" as gender_size
                        FROM
                            official_event_clothes_size sub_oecs 
                        LEFT JOIN
                            event_clothes sub_ec ON sub_ec.event_clothes_id = sub_oecs.event_clothes_id
                        WHERE
                            sub_oecs.event_id = e.event_id
                            AND sub_oecs.official_id = $2
                            AND sub_oecs."size" IS NOT NULL
                        GROUP BY
                            sub_ec.common_item_id, sub_oecs."size", sub_ec."gender"
                    ) oecs ON oecs.common_item_id = ec.common_item_id
                    LEFT JOIN
                        common_item ci ON ci.common_item_id = ec.common_item_id
                    LEFT JOIN
                        gender_subquery gs ON gs.common_item_id = ec.common_item_id AND gs.member_type = ec.member_type
                    WHERE
                        ec.event_id = e.event_id
                        AND ec.deleted IS NULL
                        AND $3 = ec.gender::text
                        AND ci.item_type = 'event_clothes'
                    GROUP BY
                        ec.common_item_id, ocs.size, oecs.size, ci.title, ci.details, gs.genders
                    ORDER BY
                        ci.details::jsonb->>'order'
                ) "cl"
              ) "clothes",
              $3 as user_gender
            FROM "event" e 
            LEFT JOIN "official" o
                ON o.official_id = $2    
            LEFT JOIN "event_official" eof 
                ON e.event_id = eof.event_id 
                AND eof.official_id = o.official_id
            LEFT JOIN "event_official_additional_role" eoar
                ON eoar.event_official_id = eof.event_official_id
            LEFT JOIN "user" u
                ON u.user_id = o.user_id
            WHERE e.deleted IS NULL
              AND e.event_id = $1`,
            [event_id, official_id, userClothingGender]
        ).then(result => {
            if(result.rows.length > 1) {
                loggers.errors_log.error(
                    'Official', official_id, 'has checked in to the event', event_id, result.rows.length, 'times');
                throw new Error('Internal Error');
            }

            let data = result.rows[0];

            if (_.isEmpty(data)) {
                res.status(200).json({ data: {}, event: {} });
            } else {
                try {
                    data.event.payment_options_for_officials
                        = OfficialsService.checkin.formatAvailablePaymentOptions(
                                data.event.official_payment_method,
                                data.role_data.payment_option
                        );
                    data.event.payment_options_for_staffers
                        = OfficialsService.checkin.formatAvailablePaymentOptions(
                                data.event.staff_payment_method,
                                data.role_data.staff_payment_option
                        );

                    if(!data.event.payment_options_for_officials.length && !data.event.payment_options_for_staffers.length) {
                        throw new Error('Payment Options Settings should not be empty');
                    }
                } catch (e) {
                    e.description = { 
                        event_id, 
                        official_id, 
                        official_payment_method: data.event.official_payment_method 
                    }

                    throw e;
                }


                delete data.event.official_payment_method;

                res.status(200).json(data);
            }            
        }).catch(res.customRespError.bind(res));
    },
    // GET /api/official/event/:event/assignments
    assignments: function (req, res) {
        let official_id = req.session.passport.user.official_id,
            event_id    = req.params.event;

        OfficialsService.assignments.getAssignmentsList(official_id, event_id)
        .then(result => {
            let data = result[0];

            if(_.isEmpty(data)) {
                res.serverError('Nothing found');
            } else {
                if(req.accepts(['json', 'html']) === 'json') {
                    res.status(200).json({ event: data });
                } else {
                    res.render('officials-schedule/official-assignments', data);
                }
            }
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // GET /api/official/events/assignments
    upcomingAssignments: function (req, res) {
        let officialID = Number(req.session.passport.user.official_id);
        let eventID    = Number(req.query.event);

        OfficialsService.assignments.getUpcomingAssignments(officialID, eventID)
            .then(data => {
                res.status(200).json(data)
            }).catch(err => {
                res.customRespError(err);
            });
    },

    // GET /api/staff/events
    staffEvents: async function(req, res) {
        try {
            const staffID = Number(req.session.passport.user.official_id);

            const events = await OfficialsService.assignments.getStaffEvents(staffID);

            res.status(200).json({ events });
        } catch (error) {
            res.customRespError(error)
        }
    },

    // get /api/event/:event/official/:official/:field(safesport|background|aau_background|aau_safesport)/ok
    setSanctioningCheckFieldOk: async function (req, res) {
        let eventID         = Number(req.params.event);
        let eventOfficialID = Number(req.params.official);
        let field           = req.params.field;
        let date            = req.query.date;
        let isHeadOfficial  = req.options.isHeadOfficial;
        let userID          = Number(req.user.user_id);

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(!eventOfficialID) {
            return res.validation('Official ID required');
        }

        if(!field) {
            return res.validation('Field name required');
        }

        if(!date) {
            return res.validation('Date required');
        }

        try {
            let updateResult = await OfficialsService.sanctioningCheckUpdate.setOk(
                {date, field}, isHeadOfficial, eventID, eventOfficialID
            );

            let historyAction;

            if(field === SANCTIONING_CHECK_FIELDS.SAFESPORT) {
                historyAction = OfficialsService.eventOfficialHistory.ACTION.SS_OK;
            } else if(field === SANCTIONING_CHECK_FIELDS.BACKGROUND) {
                historyAction = OfficialsService.eventOfficialHistory.ACTION.BG_OK;
            } else if(field === SANCTIONING_CHECK_FIELDS.AAU_SAFESPORT) {
                historyAction = OfficialsService.eventOfficialHistory.ACTION.AAU_SS_OK;
            } else if(field === SANCTIONING_CHECK_FIELDS.AAU_BACKGROUND) {
                historyAction = OfficialsService.eventOfficialHistory.ACTION.AAU_BG_OK;
            }

            await OfficialsService.eventOfficialHistory.addHistoryRow(historyAction, eventOfficialID, userID);

            res.status(200).json(updateResult)
        } catch(err) {
            res.customRespError(err)
        }
    },


    // PUT /api/official/event/:event/participation
    confirmParticipation: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const officialID = Number(req.session.passport.user.official_id);

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier '};
            }

            if (!officialID) {
                throw { validation: 'Invalid Official Identifier' };
            }

            await OfficialsService.manage.confirmOfficialParticipation({ eventID, officialID });

            res.ok();
        } catch(err) {
            res.customRespError(err);
        }
    },

    // PUT /api/official/event/:event/send-entry-qrcodes
    sendEntryQRCodes: async function(req, res) {
        
        try {
            
            const eventID = Number(req.params.event);
            const filters = req.body.filters;

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier '};
            }

            await OfficialsService.entryQRCode.sendEntryQRCodes(eventID, filters);

            res.ok();
            
        } catch(err) {
            res.customRespError(err);
        }
        
    },

    // GET /api/official/event/:event/entry-qr-code/:barcode
    showEntryQRCode: async function (req, res) {
        const $barcode = req.params.barcode,
            $eventId = req.params.event;

        if (!$eventId) {
            return res.serverError('Invalid Event Id');
        }

        if (!$barcode) {
            return res.serverError('Invalid barcode');
        }

        try {
            const data = await OfficialsService.entryQRCode.getEntryQRCodeData($eventId, $barcode);

            res.render('official/entry_qr_code', data);
        } catch (err) {
            if (err.validation) {
                res.render('500', {error: err.validation});
            } else {
                res.serverError();
            }
        }
    },
};

async function sendWithdrawEmailToEO (eventID, officialID, withdrawedOfficial, reason, role) {
    let emailSubject = `[${eventID}] ${withdrawedOfficial.event_name} - Official unassigned`;
    
    let emailToNotify = await getReceivers(eventID, role).catch(ErrorSender.defaultError.bind(ErrorSender));
    
    const workStatus = OfficialsService.checkin.getRoleWorkStatus(withdrawedOfficial, role);
    const officialInfo = `Official ${withdrawedOfficial.name} (${withdrawedOfficial.email})`;
    let emailText = `${officialInfo} unassigned from the event.`;
    
    const isAssignToMatch = await OfficialsService.assignments.checkOfficialAssignment(officialID, eventID);
    if(isAssignToMatch) {
        emailText += `\n${officialInfo} was removed from the match schedule.`;
    }
    
    emailText += `\nWithdrawal reason: ${reason}\nPrevious status: ${workStatus}`;
    
    if (!_.isEmpty(emailToNotify)) {
        for(const etn of emailToNotify) {
            await sendLetter(etn, emailText, emailSubject).catch(ErrorSender.defaultError.bind(ErrorSender))
        }
    } else {
        ErrorSender.defaultError(
            new Error(`No emails to notify officials withdrawal event: ${eventID}, official: ${officialID}`)
        );
    }
    
    async function getReceivers (eventID, role) {
        let emails;
        let isOfficial = role === OfficialsService.checkin.ROLE.OFFICIAL;

        if(isOfficial) {
            emails = await OfficialsService.getHOEmails(eventID);
        }
    
        if(_.isEmpty(emails)) {
            emails = await getEventNotificationsEmail(eventID);
        }

        return emails;
    }

    function getEventNotificationsEmail (eventID) {
        return Db.query(
            `SELECT
                COALESCE(NULLIF(e.notify_emails, ''), e.email) "email"
             FROM "event" e
             WHERE e.event_id = $1`,
            [eventID]
        ).then(({ rows }) => rows.map(({ email }) => email));
    }

    function sendLetter (to, text, subject) {
        return EmailService.sendEmail({
            from        : 'SportWrench <<EMAIL>>',
            to          : to,
            subject     : subject,
            text        : text
        }, { wrap: true });
    }
}

function sendWithdrawNotificationToStaff (eventID, withdrewOfficial) {
    return OfficialsService.sendRoleNotification(
        'withdrew', AEMService.STAFF_GROUP,  withdrewOfficial.event_official_id, eventID
    );
}

function sendWithdrawNotificationToOfficial (eventID, withdrawedOfficial) {
    return co(function* () {
        // sending notification to the official
        let _g = AEMService.OFFICIAL_GROUP;
        let _t = AEMService.triggers.getRoleTmplTypeForWorkStatus(
            AEMService.TRIGGER_ROLE_PREFIX.OFFICIAL, 'withdrew'
        );
        let _p = { event_official_id: withdrawedOfficial.event_official_id, isWithdrewNotification: true };

        yield (AEMSenderService.sendTriggerNotification(_g, _t, eventID, _p).catch(() => {}));

        // sending notification to Head Referees
        _g = AEMService.HEAD_REFEREE_GROUP;

        return (AEMSenderService.sendTriggerNotification(_g, _t, eventID, _p).catch(() => {}));
    })
}

'use strict';

/**
 * SW THS API Documentation v1.1
 * https://docs.google.com/document/d/1398CRsV3y6szM9JIHRV2u5xiWsh2G3xSKHvY86kbkgM/edit
 */

var xml = require('xml');
var crypto = require('crypto');

const co = require('co');

// Service function: returns '' if value is null
function _null(value) {
    if (value === null || value === undefined) return '';
    return value;
}

// Service function for parsing XML: returns null if value not passed
function _xml_val(value) {
    if (value && typeof value == 'object' && value.length > 0)
        return value[0];
    return null;
}

// Service function to convert UTC date to SQL format "YYYY-MM-DD HH:MM:SS"
function _utc_to_sql(timestamp) {
    if (!timestamp) return null;
    try {
        var ts = new Date(timestamp);
        return ts.toISOString().slice(0, 19).replace('T', ' ');
    } catch (err) {
        return null;
    }
}

// Service function to verify validation
// TODO: We can redo this auth check to policy
function _auth_api(req, res) {
    var url = req.url;
    var timestamp = req.get('Timestamp');
    var authorization = req.get('Authorization');

    // TODO: Validate IP address
    // THS servers:
    // Primary: **********
    // Secondary: ************

    // TODO: Validate Timestamp (+/- 65 mins)
    // It is possible one of the systems won't have the daylight savings time rules right and one side will
    // switch to or from DST at a different point in time than the other system.
    // This would cause a 60 minute difference to show up until the other system applies the same DST adjustment.

    var consumerName = 'ths';
    var hash = crypto.createHash('sha256').update(consumerName).digest('hex');
    var consumerSecret = hash.substr(0, 16); // Getting first 16 chars of sha256 hash
    var authorizationString = consumerSecret + '&' + timestamp + '&' + url;
    var authorizationHash = crypto.createHash('sha256').update(authorizationString).digest('hex');

    var success = authorization === authorizationHash;

    if (!success) {
        // TODO: Save ERROR to log with IP, etc.

        // Response XML
        var xmlString = xml({
            'response': [
                {'consumerSecret': consumerSecret},
                {'authorizationString': authorizationString},
                {'authorizationHash': authorizationHash},

                {'message': 'ERROR: Authorization failed'},
                {'success': success}
            ]
        }, {declaration: true});
        var ret = res.header('Content-Type', 'text/xml').send(xmlString);
        return ret;
    }
    return false;
}

function isBookingAllowed ({accessLevel, skip_waitlist_teams}, team) {
    if (!!team.deleted) {
        return 0;
    } else if (skip_waitlist_teams && parseInt(team.status_entry, 10) === 14) {
        return 0;
    } else if ((accessLevel === 'paid') && (parseInt(team.status_paid, 10) !== 22)) {
        return 0;
    } else if ((accessLevel === 'accepted') && (parseInt(team.status_entry, 10) !== 12)) {
        return 0;
    } else if (
        (accessLevel === 'paid_ach_pending') &&
        (Number(team.status_paid) !== 22 && !team.has_pending_ach_payment)
    ) {
        return 0;
    } else {
        return 1;
    }
}

module.exports = {

    // GET /api/ths/v1/ping
    ping: function(req, res) {
        var now = new Date(); // Get current server time
        var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format
        //var query = 'SELECT NOW()::VARCHAR db_time, EXTRACT(epoch FROM now())::VARCHAR db_timestamp';
        var query = ' SELECT NOW()::VARCHAR db_time, EXTRACT(EPOCH FROM NOW())::INTEGER - $1 "diff" ';
        var serverTimeInSeconds = parseInt(now / 1000, 10);

        Db.query(query, [serverTimeInSeconds]).then(function (result) {
            var dbTime = result.rows[0].db_time;
            var diff = result.rows[0].diff;
            dbTime = _utc_to_sql(dbTime);
            serverTime = _utc_to_sql(serverTime);

            result = 'OK';
            // 2014-12-01 11:20:56
            // 16 letters is date and time without seconds
            if ( Math.abs(diff) > 5 ) result = 'ERROR';
            var delta = new Date(dbTime) - new Date(serverTime);

            //var tsNode = parseInt(new Date()/1000, 10),
            //    tsDB = parseInt(result.rows[0].db_timestamp, 10),
            //    tsDelta = tsDB - tsNode;

            var xmlString = xml({
                'response': [
                    {'result': result},
                    {'serverTime': serverTime},
                    {'dbTime': dbTime},
                    {'delta': delta},
                    //{'tsDB': tsDB},
                    //{'tsNode': tsNode},
                    //{'tsDelta': tsDelta}
                ]
            }, {declaration: true});

            if (result !== 'OK') {
                // Logging problem
                loggers.errors_log.error('PING API ERROR. Date():', (new Date()).toString());
                loggers.errors_log.error('dbTime:' + dbTime + ' serverTime:' + serverTime 
                    + ' delta:' + delta + ' diff:' + diff);
            }

            return res.header('Content-Type', 'text/xml').send(xmlString);
        }).catch(err => {
            res.status(500).json(err.error);
        })

    },

    // GET /api/ths/v1/ping-node
    pingNode: function(req, res) {
        var now = new Date(); // Get current server time
        var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        serverTime = _utc_to_sql(serverTime);

        var result = 'OK';

        var xmlString = xml({
            'response': [
                {'result': result},
                {'serverTime': serverTime}
            ]
        }, {declaration: true});
        return res.header('Content-Type', 'text/xml').send(xmlString);
    },

    // Action "info" - used to test authentication and to get consumerSecret hash
    // GET /api/ths/v1/info
    info: function (req, res) {
        var now = new Date(); // Get current server time
        var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format
        var ip = req.get('X-Real-IP');
        var url = req.url;
        var timestamp = req.get('Timestamp');
        var authorization = req.get('Authorization');

        var consumerName = 'ths';
        var hash = crypto.createHash('sha256').update(consumerName).digest('hex');
        var consumerSecret = hash.substr(0, 16); // Getting first 16 chars of sha256 hash
        var authorizationString = consumerSecret + '&' + timestamp + '&' + url;
        var authorizationHash = crypto.createHash('sha256').update(authorizationString).digest('hex');

        var success = authorization === authorizationHash;

        // Response XML
        var xmlString = xml({
            'response': [
                {'serverTime': serverTime},
                {'timestamp': _null(timestamp)},
                {'ip': _null(ip)},
                //{ 'hash': hash },
                {'consumerSecret': consumerSecret},
                {'authorizationString': authorizationString},
                {'authorizationHash': authorizationHash},
                {'success': success}
            ]
        }, {declaration: true});
        return res.header('Content-Type', 'text/xml').send(xmlString);
    },

    // GET /api/ths/v1/eventlist
    eventlist: function (req, res) {
        // Check Authentication
        if (_auth_api(req, res)) return;
    
        let now = new Date(); // Get current server time
        let serverTime = now.toJSON(); // Convert it date string in ISO 8601 format
        let query = `
                    SELECT e.event_id, e.name, e.long_name,
                    to_char(e.date_start, 'YYYY-MM-DD HH24:MI:SS') date_start,
                    to_char(e.date_end, 'YYYY-MM-DD HH24:MI:SS') date_end,
                    e.city,
                    e.state
                    FROM event e
                    INNER JOIN housing_company hc ON hc.housing_company_id = e.housing_company_id
                    WHERE e.published = TRUE
                    AND e.teams_use_clubs_module IS TRUE
                    AND e.live_to_public IS TRUE
                    AND e.date_end > (now() AT TIME ZONE e.timezone)
                    AND e.has_status_housing = TRUE
                    AND hc.name = 'THS'
            `;
        
        Db.query(query).then(function (result) {
            var events = [];
            _.each(result.rows, function (row) {
                events.push({
                    'event': [
                        {'id': _null(row.event_id)},
                        {'name': _null(row.long_name)},
                        {'shortName': _null(row.name)},
                        {'dateStart': _null(row.date_start)},
                        {'dateEnd': _null(row.date_end)},
                        {'city': _null(row.city)},
                        {'state': _null(row.state)}
                    ]
                });
            });

            var xmlString = xml({
                'response': [
                    {'serverTime': serverTime},
                    {'events': events}
                ]
            }, {declaration: true});
            return res.header('Content-Type', 'text/xml').send(xmlString);
        }).catch(err => {
            res.status(200).json(err);
        });
    },

    // GET /api/ths/v1/clubs/:event/:timestamp
    clubs: async function (req, res) {
        const eventId = Number(req.params.event);
        const timestamp = req.params.timestamp;

        try {
            const xmlString = await THSService.getClubsData(eventId, timestamp);

            return res.header('Content-Type', 'text/xml').send(xmlString);
        } catch (err) {
            loggers.errors_log.error(err);

            return res.serverError(err);
        }
    },

    // GET /api/ths/v1/teams/:event/:timestamp
    // GET /api/ths/v1/teams/:event/:timestamp/:team
    teams: async function (req, res) {
        const eventId = req.params.event;
        const timestamp = req.params.timestamp;
        const teamId = req.params.team;

        try {
            const xmlString = await THSService.getTeamsData(eventId, timestamp, teamId);

            return res.header('Content-Type', 'text/xml').send(xmlString);
        } catch (err) {
            loggers.errors_log.error(err);

            return res.serverError(err);
        }
    },

    // GET /api/ths/v1/reservations/:event/:timestamp/:reservation
    // GET /api/ths/v1/reservations/:timestamp/:reservation
    reservations: async function (req, res) {
        const eventId = Number(req.params.event);
        const timestamp = req.params.timestamp;
        const reservationId = req.params.reservation;

        try {
            const xmlString = await THSService.getReservationsData(eventId, timestamp, reservationId);

            return res.header('Content-Type', 'text/xml').send(xmlString);
        } catch (err) {
            loggers.errors_log.error(err);

            return res.serverError(err);
        }
    },

    // GET /api/ths/v1/changes/:event/:timestamp
    changes: async function (req, res) {
        const eventId = Number(req.params.event);
        const timestamp = req.params.timestamp;
        const teamId = req.params.team;

        try {
            const xmlString = await THSService.getEventData(eventId, timestamp, teamId);

            return res.header('Content-Type', 'text/xml').send(xmlString);
        } catch (err) {
            loggers.errors_log.error(err);

            return res.serverError(err);
        }
    },

    // POST /api/ths/v1/bookings
    bookings: async function(req, res) {
        const reservations = req.body?.reservations;
        const contentType = req.get('Content-Type')

        const now = new Date();
        const serverTime = now.toJSON();

        try {
            if (contentType !== 'text/xml') {
                throw {error: 'Content-type should be text/xml'};
            }

            if (_.isEmpty(reservations)) {
                throw {error: 'XML is not passed or invalid'};
            }

            if (!reservations.reservation) {
                throw {error: 'OK'};
            }

            const acceptedReservationsNumber = await THSService.upsertReservations(reservations.reservation);

            const responseXmlString = xml({
                'response': [
                    { 'serverTime': serverTime },
                    { 'accepted': acceptedReservationsNumber },
                    { 'message': 'OK' }
                ]
            }, {declaration: true});

            return res.header('Content-Type', 'text/xml').send(responseXmlString);
        } catch (err) {
            loggers.debug_log.verbose("THS API ERROR:");
            loggers.debug_log.verbose(err);

            const errorXmlString = xml({
                'response': [
                    { 'serverTime': serverTime },
                    { 'accepted': 0 },
                    { 'teamId': err.teamId || '' },
                    { 'message': 'ERROR: ' + (err.error || '') }
                ]
            }, {declaration: true});

            return res.header('Content-Type', 'text/xml').send(errorXmlString);
        }
    },

    // ❗️Not fetched to any URL and not tested
    bookings2: function (req, res) {
        if (_auth_api(req, res)) {
            return;
        }

        let now = new Date(); 
        // Convert it date string in ISO 8601 format
        let serverTime = now.toJSON(); 

        if (req.get('Content-Type') !== 'text/xml') {
            return res.sendXML({
                response: [
                    { serverTime }, 
                    { accepted  : 0 }, 
                    { message   : 'ERROR: Content-type should be text/xml' }
                ]
            });
        }

        if ((toString.call(req.body.reservations) !== '[object Object]') || req.body.reservations.length === 0) {
            return res.sendXML({
                response: [
                    { serverTime }, 
                    { accepted: 0 }, 
                    { message: 'ERROR: XML is not passed or invalid' }
                ]
            });
        }

        let reservationsList = req.body.reservations.reservation;

        if (!Array.isArray(reservationsList) || reservationsList.length === 0) {
            return res.sendXML({
                response: [
                    { serverTime }, 
                    { accepted  : 0 }, 
                    { message   : 'OK' }
                ]
            });
        }

        let acceptedReservationsQty = 0;
        let uniqueClubIDs = new Set();

        reservationsList.reduce((prev, reservation) => {
            return prev.then(() => 
                __acceptReservation__(reservation)
                .then(clubsArr => {
                    clubsArr.forEach(clubID => {
                        uniqueClubIDs.add(clubID);
                    })
                    acceptedReservationsQty += 1;
                })
            )
        }, Promise.resolve())
        .then(() => {
            // Remove "undefined" from the Set
            uniqueClubIDs.delete();

            return __updTotals__(Array.from(uniqueClubIDs));
        })
        .then(() => {
            res.sendXML({
                response: [
                    { serverTime },
                    { accepted  : acceptedReservationsQty },
                    { message   : 'OK'}
                ]
            })
        })
        .catch(err => {
            loggers.errors_log.error(err);
            res.sendXML({
                response: [
                    { serverTime },
                    { accepted  : acceptedReservationsQty },
                    { teamId    : err.teamId },
                    { message   : `ERROR: ${err.error || err}`}
                ]
            });
        })
    }
};

function __updTotals__ (clubsList) {
    return clubsList.reduce((prev, clubID) =>
        HousingService.update_club_housing({rosterClubID: clubID}),
        Promise.resolve()
    )
}


function __acceptReservation__ (reservation) {
    return co(function* () {
        let parsedReserv = {
            ths_tentative_nights    : _xml_val(reservation.tentativenights),
            ths_confirmed_nights    : _xml_val(reservation.confirmednights),
            ths_id                  : _xml_val(reservation.thsid),
            ths_hotel_name          : _xml_val(reservation.hotelname),
            ths_hotel_status        : _xml_val(reservation.status),
            ths_loyalty             : _xml_val(reservation.loyalty),
            ths_contract_issued     : _utc_to_sql(_xml_val(reservation.contractissued)),
            ths_when_accepted       : _utc_to_sql(_xml_val(reservation.whenaccepted)),
            ths_when_canceled       : _utc_to_sql(_xml_val(reservation.whencanceled)),
            ths_modified            : _utc_to_sql(_xml_val(reservation.modified)),
            roster_team_id          : parseInt(_xml_val(reservation.id), 10),
            event_id                : parseInt(_xml_val(reservation.eventid), 10)
        }

        if (parsedReserv.event_id <= 0) {
            return Promise.reject(
                new Error(
                    `Invalid "eventid" value for reservation ${
                        parsedReserv.ths_id
                    } (team: ${parsedReserv.roster_team_id})`
                )
            );
        }

        let teamInfo = yield (getTeamInfo(parsedReserv.roster_team_id, parsedReserv.ths_id));

        if (!teamInfo) {
            return Promise.reject(new Error(`Team not found for team_id ${parsedReserv.roster_team_id}`));
        }

        // TODO: not logged reservation changes from one team to another (see "roster_team_id" field in sql query)

        yield (saveTHSHistory(parsedReserv));

        yield (upsertTHSBooking(parsedReserv));

        return [teamInfo.club_id, teamInfo.old_club_id];
    })


    function getTeamInfo (teamID, thsID) {
        let query = 
            `SELECT 
                rt."roster_club_id" "club_id", 
                rc."is_local", 
                tb."roster_team_id", 
                rc2."roster_club_id" "old_club_id", 
                rc2."is_local" "old_is_local"
            FROM roster_team rt 
            LEFT JOIN roster_club rc 
                ON rc.roster_club_id = rt.roster_club_id 
            LEFT JOIN ths_booking tb 
                ON tb.ths_id = $2 
            LEFT JOIN roster_team rt2 
                ON rt2.roster_team_id = tb.roster_team_id 
            LEFT JOIN roster_club rc2 
                ON rc2.roster_club_id = rt2.roster_club_id 
            WHERE rt."roster_team_id" = $1`;

        let params = [teamID, thsID];

        return Db.query(query, params)
        .then(result => result.rows[0] || null)
        .catch(err => {
            return Promise.reject({ 
                error   : err,
                teamId  : teamID,
                params  : params
            });
        })
    }

    function saveTHSHistory (reserv) {
        let query = squel.insert().into('ths_history').setFields(reserv);

        return Db.query(query)
        .then(() => {})
        .catch(err => {
            return Promise.reject({ 
                error   : err,
                teamId  : reserv.roster_team_id,
                params  : reserv
            });
        })
    }

    function upsertTHSBooking (reserv) {
        return Db.query(
            squel.update().table('ths_booking')
            .setFields(_.omit(reserv, 'ths_id'))
            .where('ths_id = ?', reserv.ths_id)
        )
        .then(result => {
            if (result.rowCount === 0) {
                return Db.query(
                    squel.insert().into('ths_booking')
                    .setFields(reserv)
                ).then(() => {})
            }
        })
        .catch(err => {
            return Promise.reject({ 
                error   : err,
                teamId  : reserv.roster_team_id,
                params  : reserv
            });
        })
    }
}

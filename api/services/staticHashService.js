'use strict';

const moment    = require('moment');
const argv      = require('optimist').argv;

class StaticHashService {
	constructor () {
		this._hashes = {};

		this._HEADERS = {
			sw 		: 'Content-updated-sw',
			esw 	: 'Content-updated-esw',
			swt 	: 'Content-updated-swt'
		};

		this._HEADERS_KEYS = Object.keys(this._HEADERS);
	}

	get HEADERS () {
		return this._HEADERS;
	}

	get SETTINGS_KEY () {
		return argv.staging ? 'static_hash_staging' : 'static_hash';
	}

	get HASH_DELIMETER () {
		return '-';
	}

	// covered 😄👍
	getHashes () {
		return this._hashes;
	}

	// covered 😄👍
	onSettingsChanged (data) {
		if (toString.call(data) !== '[object Object]') {
			return false;
		}
		if (data.key !== this.SETTINGS_KEY) {
			return false;
		}

		let settings = data.value;

		if (_.isEmpty(settings)) {
			return false;
		}

		loggers.debug_log.verbose('Settings changed', settings);

		let _updQty = 0;

		for (let key of this._HEADERS_KEYS) {
			let hash = settings[key];

			if (hash) {
				++_updQty;
				this._hashes[this._HEADERS[key]] = hash;
			}
		}

		return (_updQty > 0);
	}

	// covered 😄👍
	init () {
		return Db.query(
			`SELECT s."value" FROM "settings" s 
			WHERE s."key" = $1`,
			[this.SETTINGS_KEY]
		).then(res => res.rows[0] && res.rows[0].value || {})
		.then(settings => {
			let res =  this.onSettingsChanged({ key: this.SETTINGS_KEY, value: settings });
			loggers.debug_log.verbose('Static Hashes initialized');
			return res;
		});
	}

	// covered 😄👍
	getProjectHash (proj) {
		if (!/^(sw|esw|asw|swt)$/.test(proj)) {
			throw new Error(`Project "${proj}" is not supported.`);
		}

		let hashKey = this._HEADERS[proj];

		let storedHash = this._hashes[hashKey];

		if (!storedHash) {
			return null;
		}

		let _splittedHash = storedHash.split(this.HASH_DELIMETER);

		let staticHashValue = _splittedHash[0] || 'N/A';
		let generationTime 	= moment.utc(parseInt(_splittedHash[1], 10));
		generationTime		 = generationTime.isValid()
									?generationTime.format('DD MMMM YYYY HH:mm:ss z')
									:'N/A';

		return `Hash "${staticHashValue}" from ${generationTime}.`;
	}

	clear () {
		this._hashes = {};
	}
}

module.exports = new StaticHashService();

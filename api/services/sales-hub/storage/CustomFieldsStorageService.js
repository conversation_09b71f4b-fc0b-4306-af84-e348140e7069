
const AbstractStorageService = require('./AbstractStorageService');

class CustomFieldsStorageService extends AbstractStorageService {
    constructor(DbClient) {
        super(DbClient);
    }

    getEventAdditionalPurchaseFields(eventID) {
        return this.#getEventAdditionalPurchaseFieldsData(eventID);
    }

    getEventAdditionalPurchaseField(eventID, additionalFieldID) {
        return this.#getEventAdditionalPurchaseFieldsData(eventID, additionalFieldID);
    }

    async updateEventAdditionalPurchaseFields(eventID, additionalFields) {
        const query = knex('event')
            .update({ tickets_purchase_additional_fields: JSON.stringify(additionalFields) })
            .where('event_id', eventID)

        const { rowCount: updated } = await Db.query(query)

        if(!updated) {
            throw new Error('Event not found');
        }
    }

    async #getEventAdditionalPurchaseFieldsData(eventID, additionalFieldID) {
        let params = [eventID];

        const query = `SELECT COALESCE(
                                   JSONB_AGG(JSONB_BUILD_OBJECT(
                                   'field_id', data.field ->> 'field',
                                   'label', data.field ->> 'label',
                                   'required', (data.field ->> 'required')::BOOLEAN,
                                   'options', NULLIF(data.field ->> 'options', '[]')::JSONB,
                                   'type', data.field ->> 'type',
                                   'short_label', NULLIF(data.field ->> 'short_label', ''),
                                   'sort_order', index,
                                   'is_hidden', (data.field -> 'show_on' ->> 'purchase')::BOOLEAN IS FALSE,
                                   'sales_hub_custom_field_id', data.field ->> 'sales_hub_custom_field_id'
                                             )) FILTER ( WHERE data.field IS NOT NULL ),
                                   '[]'::JSONB
                   ) fields,
                   data.point_of_sales_id,
                   data.tickets_purchase_additional_fields
            FROM (SELECT field, index, epos.point_of_sales_id, e.tickets_purchase_additional_fields
                  FROM "event" e
                           LEFT JOIN JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) WITH ORDINALITY AS ARR(field, index)
                               ON ${additionalFieldID ? `field ->> 'field' = $2` : 'true'}
                           JOIN event_point_of_sales AS epos ON epos.sw_event_id = e.event_id
                  WHERE e.event_id = $1) data
            GROUP BY data.point_of_sales_id, data.tickets_purchase_additional_fields`;

        if(additionalFieldID) {
            params.push(additionalFieldID);
        }

        const { rows: [result] } = await this.DbClient.query(query, params);

        if(!_.isEmpty(result)) {
            return result;
        }
    }
}

module.exports = CustomFieldsStorageService;

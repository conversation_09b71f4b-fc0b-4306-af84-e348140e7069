const AbstractStorageService = require("./AbstractStorageService");

class SyncStorageService extends AbstractStorageService {
    constructor(DbClient) {
        super(DbClient);
    }

    async doesSyncAllowed(eventID) {
        const query = knex('event').select({
            allow_point_of_sales: knex.raw(`
                    COALESCE((tickets_settings ->> 'allow_point_of_sales')::BOOLEAN, false)
                `)
        })
            .where('event_id', eventID);

        const { rows: [event] } = await this.DbClient.query(query);

        return event.allow_point_of_sales;
    }
}

module.exports = SyncStorageService;

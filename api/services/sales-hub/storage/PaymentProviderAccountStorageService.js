
const AbstractStorageService = require('./AbstractStorageService');

class PaymentProviderAccountStorageService extends AbstractStorageService{
    constructor(DbClient) {
       super(DbClient)
    }

    #UNIQUE_STRIPE_ACCOUNT_ID = 'unique_sales_hub_payment_provider_account_stripe_account_id';
    #UNIQUE_PAYMENT_PROVIDER_ACOUNT_ID = 'unique_sales_hub_payment_provider_account_provider_account_id';

    async create(stripeAccountID, paymentProviderAccountID) {
        const query = knex('sales_hub_payment_provider_account')
            .insert({
                payment_provider_account_id: paymentProviderAccountID,
                stripe_account_id: stripeAccountID
            });

        try {
            const { rowCount } = await this.DbClient.query(query);

            if(!rowCount) {
                throw new Error('Event Point Of Sales does not created');
            }

        } catch (err) {
            if(Db.utils.isUniqueConstraintViolation(err, this.#UNIQUE_STRIPE_ACCOUNT_ID)) {
                throw { validation: `Sales Hub payment provider account with given stripe account already exists` };
            }

            if(Db.utils.isUniqueConstraintViolation(err, this.#UNIQUE_PAYMENT_PROVIDER_ACOUNT_ID)) {
                throw {
                    validation: `Sales Hub payment provider account with given provider account id already exists`
                };
            }

            throw err;
        }
    }

    async getPaymentProviderAccount(eventID, pointOfSalesType) {
        const stripeKeyFieldName = 'e.stripe_tickets_private_key';

        const query = knex('event as e')
            .select({
                paymentProviderAccountID: 'shppa.payment_provider_account_id',
                stripeAccountID: 'sa.id',
                accountIDAtStripe: 'sa.stripe_account_id'
            })
            .leftJoin('stripe_account as sa', 'sa.secret_key', stripeKeyFieldName)
            .leftJoin('sales_hub_payment_provider_account as shppa', 'shppa.stripe_account_id', 'sa.id')

            .where('e.event_id', eventID)
            .groupBy('shppa.payment_provider_account_id', 'sa.id', 'sa.stripe_account_id');

        const { rows: [paymentProviderAccount] } = await this.DbClient.query(query);

        if(_.isEmpty(paymentProviderAccount)) {
            throw new Error('Event not found');
        }

        return paymentProviderAccount;
    }
}

module.exports = PaymentProviderAccountStorageService;

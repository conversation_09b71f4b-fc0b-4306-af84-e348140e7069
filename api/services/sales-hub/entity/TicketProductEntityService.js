
class TicketProductEntityService {
    constructor(APIService, StorageService) {
        this.APIService = APIService;
        this.StorageService = StorageService;
    }

    async get(eventTicketID) {
        if(!eventTicketID) {
            throw new Error('Event Ticket ID required');
        }

        return this.StorageService.getTicketProduct(eventTicketID);
    }

    async upsert(pointOfSalesID, data) {
        const salesHubProductId = data.salesHubProductId;
        const eventTicketId = data.eventTicketId;
        const dataForUpsert = _.omit(data, ['salesHubProductId', 'eventTicketId']);

        if(salesHubProductId) {
            await this.APIService.update(pointOfSalesID, salesHubProductId, dataForUpsert);
        } else {
            const salesHubProduct = await this.APIService.create(pointOfSalesID, dataForUpsert);

            if(_.isEmpty(salesHubProduct) || !salesHubProduct?.id) {
                throw new Error('Point Of Sales ID required');
            }

            await this.StorageService.createTicketProduct(eventTicketId, salesHubProduct?.id);
        }
    }

    async getEventTicketsDataForTicketProduct(eventID, eventTicketID) {
        if(!eventID) {
            throw new Error('Event ID required');
        }

        return this.StorageService.getEventTicketsDataForTicketProduct(eventID, eventTicketID);
    }

}
module.exports = TicketProductEntityService;

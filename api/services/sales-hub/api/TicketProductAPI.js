
const AbstractAPI = require('./AbstractAPI');
const {
    createSchema: TicketProductCreationSchema,
    updateSchema: TicketProductUpdateSchema
} = require('../../../validation-schemas/sales-hub/ticket-product');

class TicketProductAPI extends AbstractAPI {

    constructor(apiKey, baseUrl) {
        super(apiKey, baseUrl);
    }

    #BASE_URL= 'point-of-sales';

    get(pointOfSalesID, productID) {
        const url = `${this.#BASE_URL}/${pointOfSalesID}/tickets/${productID}`;
        return this.sendGet(url);
    }

    create(pointOfSalesID, data) {
        const { error: validationError } = TicketProductCreationSchema.validate(data);

        if (validationError) {
            throw { validation: validationError.details[0].message };
        }

        const url = `${this.#BASE_URL}/${pointOfSalesID}/tickets`;
        return this.sendPost(url, data);
    }

    update(pointOfSalesID, productID, data) {
        const { error: validationError } = TicketProductUpdateSchema.validate(data);

        if (validationError) {
            throw { validation: validationError.details[0].message };
        }

        const url = `${this.#BASE_URL}/${pointOfSalesID}/tickets/${productID}`;
        return this.sendPut(url, data);
    }
}

module.exports = TicketProductAPI;

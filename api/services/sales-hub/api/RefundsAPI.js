
const AbstractAPI = require('./AbstractAPI');
const {
    createSchema: RefundCreationSchema,
    createFullRefundSchema: FullRefundCreationSchema,
} = require('../../../validation-schemas/sales-hub/refund');

class RefundsAPI extends AbstractAPI {

    constructor(apiKey, baseUrl) {
        super(apiKey, baseUrl);
    }

    #BASE_URL= 'refunds';

    create(data) {
        const { error: validationError } = RefundCreationSchema.validate(data);

        if (validationError) {
            throw { validation: validationError.details[0].message };
        }

        const url = `${this.#BASE_URL}`;

        return this.sendPost(url, data);
    }

    createFullRefund(data) {
        const { error: validationError } = FullRefundCreationSchema.validate(data);

        if (validationError) {
            throw { validation: validationError.details[0].message };
        }

        const url = `${this.#BASE_URL}/full`;

        return this.sendPost(url, data);
    }

}

module.exports = RefundsAPI;

const EventService = require('./ths/_EventService');
const ClubService = require('./ths/_ClubService');
const TeamService = require('./ths/_TeamService');
const BookingService = require('./ths/_BookingService');
const HistoryService = require('./ths/_HistoryService');
const UtilsService = require('./ths/_UtilsService');
const xml = require("xml");
const {ENTRY_STATUSES} = require('../constants/teams');
const {HOUSING_RESERVATIONS_INSERT_LIMIT} = require("../constants/housing");

class THSService {
    constructor(utilsService) {
        this.eventService = new EventService(utilsService);
        this.clubService = new ClubService(utilsService);
        this.teamService = new TeamService(utilsService);
        this.bookingService = new BookingService(utilsService);
        this.historyService = new HistoryService();
    }

    async getClubsData(eventId, timestamp) {
        const {event, eventRow} = await this.eventService.getEventData(eventId);

        const clubs = await this.clubService.getClubs(
            eventId,
            eventRow,
            timestamp,
            ENTRY_STATUSES.WAITLIST
        );

        const now = new Date();
        const serverTime = now.toJSON();

        return xml({
            'response': [
                {'serverTime': serverTime},
                {'event': event},
                {'clubs': clubs}
            ]
        }, {declaration: true});
    }

    async getTeamsData(eventId, timestamp, teamId) {
        const {event, eventRow} = await this.eventService.getEventData(eventId);

        const teams = await this.teamService.getTeams(
            eventId,
            eventRow,
            timestamp,
            teamId
        );

        const now = new Date();
        const serverTime = now.toJSON();

        return xml({
            'response': [
                {'serverTime': serverTime},
                {'event': event},
                {'teams': teams}
            ]
        }, {declaration: true});
    }

    async getReservationsData(eventId, timestamp, reservationId) {
        const reservations = await this.bookingService.getReservations(eventId, timestamp, reservationId);

        const now = new Date();
        const serverTime = now.toJSON();

        return xml({
            'response': [
                {'serverTime': serverTime},
                {'reservations': reservations}
            ]
        }, {declaration: true});
    }

    async getEventData(eventId, timestamp, teamId) {
        const {event, eventRow} = await this.eventService.getEventData(eventId);

        const teams = await this.teamService.getTeams(eventId, eventRow, timestamp, teamId);

        const clubs = await this.clubService.getClubs(eventId, {}, timestamp);

        const now = new Date();
        const serverTime = now.toJSON();

        return xml({
            'response': [
                {'serverTime': serverTime},
                {'event': event},
                {'teams': teams},
                {'clubs': clubs}
            ]
        }, {declaration: true});
    }

    async upsertReservations(reservations) {
        const chunks = _.chunk(reservations, HOUSING_RESERVATIONS_INSERT_LIMIT);

        for (const chunk of chunks) {
            await Promise.all(chunk.map((reservation) => this.#processReservation(reservation)));
        }

        return reservations.length;
    }

    async #processReservation(reservation) {
        const preparedReservation = this.bookingService.prepareReservation(reservation);

        this.bookingService.validateReservation(preparedReservation);

        const params = [
            preparedReservation.event_id, // $1
            preparedReservation.team_id, // $2
            preparedReservation.ths_tentative_nights, // $3
            preparedReservation.ths_confirmed_nights, // $4
            preparedReservation.ths_id, // $5
            preparedReservation.ths_hotel_name, // $6
            preparedReservation.ths_hotel_status, // $7
            preparedReservation.ths_loyalty, // $8
            preparedReservation.ths_contract_issued, // $9
            preparedReservation.ths_when_accepted, // $10
            preparedReservation.ths_when_canceled, // $11
            preparedReservation.ths_modified // $12
        ];

        let tr;
        try {
            tr = await Db.begin();

            await this.historyService.insert(tr, params);

            await this.bookingService.upsertBooking(tr, params);

            const {travcoordname, travcoordemail, travcoordphone} = reservation;
            const thsTravelingCoordinator = {travcoordname, travcoordemail, travcoordphone};

            await this.teamService.setContactsOfTravelingCoordinator(
                tr, thsTravelingCoordinator, preparedReservation.event_id, preparedReservation.team_id
            );

            await tr.commit();

            await this.#updateClubHousingStatus(preparedReservation);

        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #updateClubHousingStatus(preparedReservation) {
        const teamData = await this.teamService.getTeamData(preparedReservation.team_id, preparedReservation.ths_id);

        await HousingService.update_club_housing(
            {
                rosterClubID: teamData.club_id,
                recountMaxAcceptedNights: true
            });

        if (teamData.old_club_id && teamData.club_id !== teamData.old_club_id) {
            await HousingService.update_club_housing(
                {
                    rosterClubID: teamData.old_club_id,
                    recountMaxAcceptedNights: true
                });
        }
    }
}

module.exports = new THSService(UtilsService);


const BoothsService = require('./uncollected-fee-balance-info/booths/_BalanceInfoBoothsService');
const TicketsService = require('./uncollected-fee-balance-info/tickets/_BalanceInfoTicketsService');
const TeamsService = require('./uncollected-fee-balance-info/teams/_BalanceInfoTeamsService');

const swUtils = require('../../../lib/swUtils');

class UncollectedFeeBalanceInfoService {
    constructor (swUtils) {
        this.tickets = TicketsService;
        this.teams = TeamsService;
        this.booths = BoothsService;

        this.swUtils = swUtils;
    }

    async getBalance (eventID, paymentForType, customPaymentAmount) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        if(!paymentForType || !['tickets', 'teams', 'booths'].includes(paymentForType)) {
            throw new Error('Payment For Type invalid ' + paymentForType);
        }

        if(!_.isNumber(Number(customPaymentAmount))) {
            throw { validation: 'Custom Payment Amount Invalid' };
        }

        let balance = await this[paymentForType].getBalance(eventID);

        balance.newBalance = this.swUtils.normalizeNumber(balance.currentBalance + Number(customPaymentAmount));

        if(balance.newBalance > 0) {
            throw { validation: 'Too big amount' };
        }

        return balance;
    }

    async save (tr, balance, customPaymentID) {
        if(!customPaymentID) {
            throw { validation: 'Custom Payment ID required' };
        }

        if(_.isEmpty(balance)) {
            throw { validation: 'Balance data is empty' };
        }

        return this.saveBalanceChangeRow(tr, customPaymentID, balance);
    }

    async getCustomPaymentBalance(customPaymentID, paymentForType) {
        const query = knex('custom_payment_uncollected_fee_balance_info as balance')
            .join('custom_payment AS cp', 'balance.custom_payment_id', 'cp.custom_payment_id')
            .select('balance_details', 'sw_fee', 'current_balance_sum', 'new_balance_sum', 'cp.amount')
            .where('cp.payment_for_type', paymentForType)
            .where('cp.custom_payment_id', customPaymentID);

        const { rows: [balance] } = await Db.query(query);

        if(_.isEmpty(balance)) {
            throw new Error('Custom Payment balance does not exist');
        }

        balance.uncollected_fee_number = this.swUtils.normalizeNumber(balance.amount / balance.sw_fee);

        return _.omit(balance, ['amount']);
    }

    saveBalanceChangeRow (tr, customPaymentID, balance) {
        const query = knex('custom_payment_uncollected_fee_balance_info')
            .insert({
                custom_payment_id: customPaymentID,
                balance_details: JSON.stringify(balance.balanceDetails),
                sw_fee: balance.swFee,
                current_balance_sum: balance.currentBalance,
                new_balance_sum: balance.newBalance
            })
            .returning(['balance_details', 'sw_fee', 'current_balance_sum', 'new_balance_sum']);

        return tr.query(query).then(result => result && result.rows[0])
            .then(balanceData => {
                if(!balanceData) {
                    throw new Error('Balance Row not created');
                }

                return balanceData;
            })
    }
}

module.exports = new UncollectedFeeBalanceInfoService(swUtils);

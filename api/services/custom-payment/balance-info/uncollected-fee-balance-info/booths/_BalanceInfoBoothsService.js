
const BalanceInfoService = require('../_BalanceInfoService');

class BalanceInfoBoothsService extends BalanceInfoService {
    constructor () {
        super();
    }

    get PAYMENT_FOR_TYPE () {
        return FundsTransferService.TRANSFER_TYPES.BOOTHS;
    }

    async getTargetBalance (eventID) {
        let isBalanceMode = true;

        return BoothsService.paymentUtils.getTargetSWFee(eventID, null, isBalanceMode);
    }

    getCollectedSWFee (eventID) {
        return BoothsService.paymentStatistic.getCollectedSWFee(eventID);
    }

    async getBalance (eventID) {
        let [collectedSWFee, targetBalance] = await Promise.all([
            this.getCollectedSWFee(eventID),
            this.getTargetBalance(eventID)
        ])

        let {
            escrow: approvedOrPaidExhibitorsFee,
            statusesInformation,
            sw_fee: swFee
        } = targetBalance;

        let currentBalance = 0;

        if(approvedOrPaidExhibitorsFee > 0) {
            currentBalance = await super.getCurrentBalance(eventID, collectedSWFee, approvedOrPaidExhibitorsFee);
        }

        return {
            balanceDetails: statusesInformation,
            currentBalance: currentBalance,
            swFee
        };
    }
}

module.exports = new BalanceInfoBoothsService();


const BalanceInfoService = require('../_BalanceInfoService');

class BalanceInfoTeamsService extends BalanceInfoService{
    constructor () {
        super();
    }

    get PAYMENT_FOR_TYPE () {
        return FundsTransferService.TRANSFER_TYPES.TEAMS;
    }

    async getTargetBalance (eventID) {
        return TeamsPaymentService.getSWFeeEscrowTarget(
            eventID,
            true /* skipDisputesCounting */,
            true /* getSummands */,
            0 /* currentReceiptSWFee */,
            true /*balanceInformationMode*/
        );
    }

    getCollectedSWFee (eventID) {
        return TeamsPaymentService.getCollectedSWFee(eventID);
    }

    async getBalance (eventID) {
        const [collectedSWFee, targetBalance] = await Promise.all([
            this.getCollectedSWFee(eventID),
            this.getTargetBalance(eventID)
        ]);

        let {
            escrow: acceptedOrPaidTeamsFee,
            statusesInformation,
            summands
        } = targetBalance;


        const currentBalance = await super.getCurrentBalance(eventID, collectedSWFee, acceptedOrPaidTeamsFee);

        return {
            balanceDetails: statusesInformation,
            currentBalance: currentBalance,
            swFee: summands.sw_fee
        }
    }
}

module.exports = new BalanceInfoTeamsService();

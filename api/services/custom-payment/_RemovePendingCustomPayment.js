
const { CUSTOM_PAYMENT } = require('../../constants/payments');

class RemovePendingCustomPayment {
    constructor () {}

    async process (customPaymentID) {
        if(!customPaymentID) {
            throw { validation: 'Custom Payment ID Required' };
        }

        let paymentIntentID = await this._getPaymentIntentID(customPaymentID);

        if(!paymentIntentID) {
            throw { validation: 'Payment Not Found!' };
        }

        let tr;

        try {
            tr = await Db.begin();

            await this._cancelPendingPayment(tr, customPaymentID, paymentIntentID);

            await StripeService.paymentCard.stripeService.cancelPaymentIntent(paymentIntentID);

            await tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    _getPaymentIntentID (customPaymentID) {
        let query = knex('custom_payment AS cp')
            .select('spi.payment_intent_id AS payment_intent_id')
            .join('stripe_payment_intent AS spi', (table) => {
                table
                    .on('spi.stripe_payment_intent_id', 'cp.stripe_payment_intent_id')
                    .on(knex.raw(
                        `spi.payment_intent_status = ?`,
                        [StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.REQUIRES_PAYMENT_METHOD]
                    ))
            })
            .where('cp.custom_payment_id', customPaymentID)
            .whereIn('cp.status', ['pending', 'requires_action']);

        return Db.query(query).then(result => result && result.rows[0] && result.rows[0].payment_intent_id);
    }

    _cancelPendingPayment (tr, customPaymentID, paymentIntentID) {
        return Promise.all([
            this._updateCustomPaymentRow(tr, customPaymentID),
            this._updateStripeCustomPaymentRow(tr, paymentIntentID)
        ]);
    }

    _updateCustomPaymentRow (tr, customPaymentID) {
        let query = knex('custom_payment AS cp')
            .update({ status: CUSTOM_PAYMENT.PAYMENT_STATUS.CANCELED })
            .where('cp.custom_payment_id', customPaymentID);

        return tr.query(query).then(result => result && result.rowCount)
            .then(isUpdated => {
                if(!isUpdated) {
                    throw { validation: 'Custom Payment not updated' };
                }
            });
    }

    _updateStripeCustomPaymentRow (tr, paymentIntentID) {
        let query = knex('stripe_payment_intent AS spi')
            .update({ payment_intent_status: StripeService.paymentCard.stripeService.PAYMENT_INTENT_STATUS.CANCELED })
            .where('spi.payment_intent_id', paymentIntentID);

        return tr.query(query).then(result => result && result.rowCount)
            .then(isUpdated => {
                if(!isUpdated) {
                    throw { validation: 'Stripe Custom Payment not updated' };
                }
            });
    }
}

module.exports = new RemovePendingCustomPayment();

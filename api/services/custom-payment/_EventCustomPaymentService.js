
const PaymentNotificationService = require('./payment/_NotificationService');
const CreationService = require('./payment/_CreationService');
const CustomPaymentUtils = require('./payment/_UtilsService');
const WebhookService = require('./payment/_WebhookService');
const BalanceInfoService = require('./payment/_BalanceInfoService');

const utils = require('../../lib/swUtils');

class EventCustomPaymentService {
    constructor (
        swUtils,
        NotificationsService,
        BalanceInfoService,
        CustomPaymentUtils
    ) {
        this.swUtils = swUtils;
        this.notifications = NotificationsService;
        this.balanceInfo = BalanceInfoService;
        this.utils = CustomPaymentUtils;

        this.paymentCreation = new CreationService(
            this.balanceInfo,
            this.notifications,
            this.swUtils,
            this.utils
        );

        this.webhook = new WebhookService(this.balanceInfo, this.notifications, this.swUtils);
    }
}

module.exports = new EventCustomPaymentService(
    utils,
    PaymentNotificationService,
    BalanceInfoService,
    CustomPaymentUtils
);

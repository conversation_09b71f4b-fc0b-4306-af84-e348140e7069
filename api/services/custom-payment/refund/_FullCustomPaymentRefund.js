

class _FullCustomPaymentRefund {
    constructor (payment, source) {
        if(_.isEmpty(payment) || !_.isObject(payment)) {
            throw new Error('Payment is empty');
        }

        if(!source) {
            throw new Error('Refund source is empty');
        }

        this.payment = payment;
        this.source = source;
    }

    async proceed () {
        if(this.source === 'dashboard') {
            return this.__proceedStripeDashboardRefund();
        } else {
            throw new Error('Refund source not supported');
        }
    }

    //TODO: move to separate file when one more source would be added
    __proceedStripeDashboardRefund () {
        let customPayment = this.__prepareCustomPaymentData();

        return this.__updateCustomPaymentRow(customPayment);
    }

    __prepareCustomPaymentData () {
        let newAmount = 0
        let newMerchantFee = 0;
        let newNetProfit = 0;
        let newStatus = 'canceled';

        return {
            amount: newAmount,
            merchantFee: newMerchantFee,
            netProfit: newNetProfit,
            status: newStatus
        }
    }

    __updateCustomPaymentRow ({ amount, merchantFee, netProfit, status }) {
        let query = knex('custom_payment AS cp')
            .update({
                amount: amount,
                merchant_fee: merchantFee,
                net_profit: netProfit,
                status
            })
            .where('cp.custom_payment_id', this.payment.purchase_id);

        return Db.query(query).then(result => result && result.rowCount > 0)
            .then(updated => {
                if(!updated) {
                    throw new Error('Custom payment row not updated');
                }
            })
    }
}

module.exports = _FullCustomPaymentRefund;

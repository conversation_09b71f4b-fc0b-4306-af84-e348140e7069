const fetch = require("node-fetch");

const toBase64 = (input) => Buffer.from(input).toString('base64');


class VerticalInsuranceAPIService {

    #CLIENT_ID = sails.config.verticalInsurance.client_id;

    #CLIENT_SECRET = sails.config.verticalInsurance.client_secret;

    #API_BASE_URL= sails.config.verticalInsurance.api_base_url;

    async createCustomerClientSecret(email) {
        const path = `auth/customer/secret`
        const url = this.#generateURL(path);

        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify({ email_address: email }),
            headers: this.#prepareRequestHeaders(),
        });

        if(!response.ok) {
            const errorText = await response.text();

            loggers.errors_log.error(`API Error: ${response.status} ${response.statusText} ${errorText}`);
            throw { validation: errorText || 'Error creating customer client secret' };
        }

        return await response.json();
    }

    async createQuote(productType, data) {
        const path = `quote/${productType}`
        const url = this.#generateURL(path);

        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify(data),
            headers: this.#prepareRequestHeaders(),
        });

        const body = await response.json();
        if(!response.ok) {
            this.#handleAPIError(body, 'Error creating quote');

            return null;
        } else {
            return body;
        }
    }

    async updateQuote(quote, productType, data) {
        const path = `quote/${productType}`
        const url = this.#generateURL(path);

        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify({ quote_id: quote, ...data }),
            headers: this.#prepareRequestHeaders(),
        });

        const body = await response.json();
        if(!response.ok) {
            this.#handleAPIError(body, 'Error updating quote');

            return null;
        } else {
            return body;
        }
    }

    async payQuote(quote, stripePaymentIntentID, metadata, customerData, productType) {
        const path = `purchase/${productType}`;
        const url = this.#generateURL(path);

        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify(this.#preparePaymentData(quote, stripePaymentIntentID, metadata, customerData)),
            headers: this.#prepareRequestHeaders(),
        });

        const body = await response.json();
        if(!response.ok) {
            this.#handleAPIError(body, 'Error paying quote');

            return null;
        } else {
            return body;
        }
    }

    async payQuoteWithPaymentMethodToken(quoteID, paymentMethodToken, productType) {
        const path = `purchase/${productType}`;
        const url = this.#generateURL(path);

        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify({
                quote_id: quoteID,
                payment_method: {
                    token: paymentMethodToken
                },
            }),
            headers: this.#prepareRequestHeaders(),
        });

        const body = await response.json();
        if(!response.ok) {
            const responseData = await response.text();
            loggers.errors_log.error(`Error sending request to vertical insurance`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: preparedOptions.body,
                data: responseData,
            });
            this.#handleAPIError(body, 'Error paying quote with payment method token');

            return null;
        } else {
            return body;
        }
    }

    #generateURL(path) {
        return `${this.#API_BASE_URL}/${path}`;
    }

    #prepareRequestHeaders() {
        const headers = new fetch.Headers();
        headers.set('Authorization', `Basic ${toBase64(`${this.#CLIENT_ID}:${this.#CLIENT_SECRET}`)}`);
        headers.set('Accept', 'application/json');
        headers.set('Content-Type', 'application/json');

        return headers;
    }

    #preparePaymentData(quote, stripePaymentIntentID, metadata, customerData) {
        return {
            quote_id: quote,
            customer: customerData,
            payment_method: {
                token: `stripe:${stripePaymentIntentID}`
            },
            metadata
        };
    }

    #handleAPIError(body, message) {
        const ignoredErrors = [
            'state_not_available',
            'Invalid postal code'
        ];

        try {
            for(let ignoredError of ignoredErrors) {
                if(JSON.stringify(body).includes(ignoredError)) {
                    return;
                }
            }
        } catch (err) {}

        loggers.errors_log.error(JSON.stringify(body, null, 2));
        throw Error(body.error_description || message);
    }

}

module.exports = new VerticalInsuranceAPIService();

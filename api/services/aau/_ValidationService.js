const moment = require('moment');

class ValidationService {
    constructor (AAUService, AAUUtils) {
        this.AAUService = AAUService;
        /**
         * @type AAUUtilsService
         */
        this.Utils = AAUUtils;
    }

    get MEMBER_VALIDATION_ERROR_TEXT () {
        let errorMessage = '';

        if(this.__hasExpiredMembership) {
            errorMessage += ` AAU Member Expired`;
        }

        return errorMessage;
    }

    async processMember (memberData) {
        const membershipId = memberData.aau_primary_membership_id;
        const primaryZip = memberData.aau_primary_zip;
        const aauClubCode = memberData.aau_club_code;

        if(!membershipId) {
            throw new Error('Membership AAU Identifier required');
        }

        if(!primaryZip) {
            throw new Error('Membership Zip Code required');
        }

        let filters = {
            [this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]: membershipId,
            [this.Utils.AAU_FIELDS.ZIP_CODE]: primaryZip,
        }

        let aauMemberData = await this.AAUService.getMembersByProxy(filters);

        if(_.isEmpty(aauMemberData)) {
            throw { validation: 'AAU Member Not Found' };
        }

        let validationError = this.__validateMemberData(aauMemberData);

        if(_.isEmpty(validationError)) {
            filters[this.Utils.AAU_FIELDS.CLUB_CODE] = aauClubCode;

            const aauClubMembers = await this.AAUService.getMembersByProxy(filters);

            if(_.isEmpty(aauClubMembers)) {
                throw {
                    validation: 'No matches found in the AAU System. Please check your AAU Club Code, AAU Membership ID and Primary Zip Code on the Club tab',
                };
            }
        }

        return { memberData: aauMemberData, validationError };
    }

    async processOfficialMember (memberData) {
        const membershipId = memberData.aau_number;
        const lastName = memberData.last_name;

        if(!membershipId) {
            throw new Error('Membership AAU Identifier required');
        }

        if(!lastName) {
            throw new Error('Member Name required');
        }

        let filters = {
            [this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]: membershipId,
            [this.Utils.AAU_FIELDS.LAST_NAME]: lastName,
        }

        const aauMemberData = await this.AAUService.getMembersByProxy(filters);

        if(_.isEmpty(aauMemberData)) {
            throw { validation: 'AAU Member Not Found' };
        }

        const validationError = this.__validateMemberData(aauMemberData);

        return { memberData: aauMemberData, validationError };
    }

    __validateMemberData (aauMemberData) {
        this.__hasExpiredMembership = false;

        if(this.__isMembershipExpired(aauMemberData[this.Utils.AAU_IMPORT_FIELDS.AAU_MEMBERSHIP_ENDING_YEAR])) {
            this.__hasExpiredMembership = true;
        }

        if(this.__hasExpiredMembership) {
            return {
                validation          : this.MEMBER_VALIDATION_ERROR_TEXT,
                hasExpiredMembership: this.__hasExpiredMembership,
            };
        }
    }

    __isMembershipExpired (membershipEndingYear) {
        const currentSeason = sails.config.sw_season.current;

        return currentSeason > membershipEndingYear;
    }
}

module.exports = ValidationService;

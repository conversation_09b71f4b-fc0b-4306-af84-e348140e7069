
class QueueService {
    constructor() {}

    add (master_club_id, option) {
        return Db.query(
            'INSERT INTO "aau_queue" ("master_club_id", "option") VALUES($1, $2)',
            [master_club_id, option]
        ).then(() => {});
    }

    smartPop () {
        let query =
            `SELECT   
                aq.aau_queue_id id,
                aq.option as import_option, 
                mc.master_club_id,
                mc.aau_club_code,
                mc.aau_primary_membership_id,
                mc.aau_primary_zip
             FROM aau_queue aq  
             LEFT JOIN master_club mc   
                 ON aq.master_club_id = mc.master_club_id   
             LEFT JOIN club_owner co 
                ON co.club_owner_id = mc.club_owner_id
             WHERE aq.requested IS NULL
                AND aq.responded IS NULL
             ORDER BY aq.created ASC   
             LIMIT 1`

        return Db.query(query).then(result => result.rows[0] || null);
    }

    setRequested (id) {
        if (!id) {
            return Promise.reject(new Error('No Id passed'));
        }

        return Db.query(
            squel.update().table('aau_queue')
                .set('requested', 'NOW()')
                .where('aau_queue_id = ?', id)
        ).then(() => {});
    }

    setResponded (id, data) {
        if (!id) {
            return Promise.reject(new Error('No Id passed'));
        }

        let query =
            squel.update().table('aau_queue')
                .set('responded', 'NOW()')
                .where('aau_queue_id = ?', id);

        if(data) {
            query.set('response_body', JSON.stringify(data));
        }

        return Db.query(query);
    }

    getClubImport (masterClubID) {
        let query = squel.select().from('aau_queue', 'aq')
            .field('aq.created')
            .field('TO_CHAR(aq.requested, \'DD/MM/YYYY HH12:MI AM\')', 'requested')
            .where('aq.master_club_id = ?', masterClubID)
            .where(
                `(aq.requested IS NULL AND aq.responded IS NULL) OR
                 (aq.requested IS NOT NULL AND aq.responded IS NULL)`
            )
            .limit(1);

        return Db.query(query).then(result => result && result.rows[0]);
    }
}

module.exports = new QueueService();

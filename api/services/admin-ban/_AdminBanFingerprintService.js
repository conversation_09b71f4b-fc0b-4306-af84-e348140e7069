const {bansFingerprint} = require('../../validation-schemas/admin-ban');
const swUtils = require('../../lib/swUtils');

class _AdminBanFingerprintService {

    get UNIQUE_VIOLATION_ERR_CODE () {
        return '23505';
    }
    get UNIQUE_FINGERPRINT_CONSTRAINT () {
        return 'unique_fingerprint';
    }

    getFingerprintBanInfoBaseQuery(search) {
      const baseQuery = knex
        .from('banned_fingerprint AS bf')
        .leftJoin('purchase AS p', 'p.purchase_id', 'bf.purchase_id')
        .leftJoin('user AS u', 'u.user_id', 'p.user_id')
        .leftJoin('event AS e', 'e.event_id', 'bf.event_id');


      if(search) {
          baseQuery.where((builder) => {
            let formattedSearch = `%${search}%`;
            builder
              .where('bf.fingerprint', 'ILIKE', formattedSearch)
              .orWhere('e.name', 'ILIKE', formattedSearch)
              .orWhere('p.first', 'ILIKE', formattedSearch)
              .orWhere('u.first', 'ILIKE', formattedSearch)
              .orWhere('p.last', 'ILIKE', formattedSearch)
              .orWhere('u.last', 'ILIKE', formattedSearch);
          });
      }

      return baseQuery
    }

    getFingerprintBanInfo($page, $limit, $search) {
        const limit = $limit || 100,
              page =  $page || 1,
              search = swUtils.escapeStr($search || '')

        const query = this.getFingerprintBanInfoBaseQuery(search)
                .select('bf.fingerprint', 'bf.banned_fingerprint_id', 'bf.reason', 'bf.history', 'bf.created', {name: knex.raw(
                 'CASE WHEN u.user_id IS NOT NULL THEN FORMAT(\'%s %s\', u.first, u.last) '+
                     ' ELSE FORMAT(\'%s %s\', p.first, p.last) ' +
                 'END'
                  )}, {event_name: 'e.name'}, {total: knex.raw('count(bf.*) OVER()::INT')})
                .orderBy('bf.created')
                .limit(limit)
                .offset((page - 1) * limit);

        return Db.query(query)
            .then(result => {
                return result.rows || [];
            })
    }

    async getFingerprintBanInfoWithMetadata({ offset = 0, limit = 25, search = '' }) {
        const baseQuery = this.getFingerprintBanInfoBaseQuery(search);

        const fingerprintsQuery = baseQuery
            .clone()
            .select(
                'bf.fingerprint',
                'bf.banned_fingerprint_id',
                'bf.reason',
                'bf.history',
                'bf.created',
                {
                    name: knex.raw(
                        "CASE WHEN u.user_id IS NOT NULL THEN FORMAT('%s %s', u.first, u.last) " +
                        " ELSE FORMAT('%s %s', p.first, p.last) " +
                        'END'
                    ),
                },
                { event_name: 'e.name' }
            )
            .orderBy('bf.created', 'desc')
            .limit(limit)
            .offset(offset);

        const totalQuery = baseQuery
          .clone()
          .select(knex.raw('COUNT(*)::int AS "total"'));

        const fingerprints = await Db.query(fingerprintsQuery).then(
          (result) => {
            return result.rows || [];
          }
        );

        const total = await Db.query(totalQuery).then(
          ({ rows }) => rows[0].total
        );

        return {
          fingerprints,
          metadata: {
            total,
            hasMore: offset + limit < total,
            nextOffset: offset + limit,
          },
        };
    }

    insertFingerprintBan(fingerprint, reason) {
        const validation = bansFingerprint.validate({fingerprint, reason});
        const query = 'INSERT INTO banned_fingerprint (fingerprint, reason) VALUES ($1,$2) RETURNING fingerprint, banned_fingerprint_id, reason, history, created';
        if (validation.error) {
            throw {validationErrors: validation.error.details};
        }
        return Db.query(query, [fingerprint, reason])
            .then(result => {
                return result.rows[0];
            })
            .catch(err => {
                if(err.code === this.UNIQUE_VIOLATION_ERR_CODE && err.constraint === this.UNIQUE_FINGERPRINT_CONSTRAINT) {
                    throw new Error(`Duplicate parameter fingerprint : '${fingerprint}'`);
                }
                throw err;
            })
    }
    deleteFingerprintBan(banned_fingerprint_id) {
        const query = 'DELETE FROM banned_fingerprint WHERE banned_fingerprint_id = $1';
        if(isNaN(banned_fingerprint_id)){
            throw {validationErrors: `"Banned Fingerprint Id" must be a number`};
        }
        return Db.query(query, [banned_fingerprint_id] )
            .then(result => {
                if(result.rowCount === 0) {
                    throw new Error(`Fingerprint not found`);
                }})
    }

}
module.exports = new _AdminBanFingerprintService();

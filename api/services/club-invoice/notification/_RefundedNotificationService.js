const {PAYMENT_METHOD_NAME} = require("../../../constants/payments");

class RefundedNotificationService {
    constructor (NotificationsService) {
        this.NotificationsService = NotificationsService;
    }

    get TEMPLATE () {
        return 'club-invoice/refunded';
    }

    async send(invoiceId) {
        const invoiceData = await this.#getInvoiceData(invoiceId);

        const notificationData = this.#prepareNotificationData(invoiceData);

        return Promise.all([
            this.#notifyCD(notificationData),
            this.#notifyAdmin(notificationData),
        ]);
    }

    #notifyCD (notificationData) {
        const subject = notificationData.subject;
        const email = notificationData.director_club_email;

        if(!email) {
            return;
        }

        return this.NotificationsService.send(
            email,
            subject,
            notificationData,
            this.TEMPLATE
        );
    }

    #notifyAdmin (notificationData) {
        const subject = notificationData.subject;
        const email = notificationData.administrative_club_email;

        if(!email) {
            return;
        }

        return this.NotificationsService.send(
            email,
            subject,
            notificationData,
            this.TEMPLATE
        );
    }

    #prepareNotificationData(data) {
        const subject = `Refund for ${data.event_name} invoice`;
        const payment_method_name = data.payment_method
            ? PAYMENT_METHOD_NAME[data.payment_method.toUpperCase()] : '';

        return {
            ...data,
            subject,
            payment_method_name,
        };
    }

    async #getInvoiceData(purchaseId) {
        const query = knex('purchase as p')
            .select({
                description: 'p.notes',
                payment_method: 'p.type',
                amount_refunded: 'p.amount_refunded',
                event_name: 'e.long_name',
                event_email: 'e.email',
                administrative_club_email: 'mc.administrative_email',
                director_club_email: 'mc.director_email',
            })
            .join('event as e', 'e.event_id', 'p.event_id')
            .join('roster_club as rc', 'rc.roster_club_id', 'p.roster_club_id')
            .join('master_club as mc', 'mc.master_club_id', 'rc.master_club_id')
            .where('p.purchase_id', purchaseId);

        const {rows: [purchase] = []} = await Db.query(query);

        if(!purchase) {
            throw { validation: 'Club Invoice not found' };
        }

        return purchase;
    }
}

module.exports = RefundedNotificationService;

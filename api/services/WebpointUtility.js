'use strict';

const 
    MEMBERSHIP_NUMBER = /^[a-zA-Z]{2}[0-9]{7,7}[a-zA-Z]{2,}\d{2,}$|^[0-9]{7,7}$/;
const _ = require('lodash');

const OLD_WP_API_MEMBER_ALIASES = {
    MemberID            : 'MEMBERID',
    USAVNumber          : 'USAVNUMBER',
    Club_Code           : 'CLUB_CODE',
    First_Name          : 'FIRST_NAME',
    Member_Status       : 'MEMBER_STATUS',
    Last_Name           : 'LAST_NAME',
    Gender              : 'GENDER',
    Birth_Date          : 'BIRTH_DATE',
    Height              : 'HEIGHT',
    RegionUSAVNumber    : 'REGIONUSAVNUMBER',
    Approach            : 'APPROACH',
    Block               : 'BLOCK',
    Reach               : 'REACH',
    HandedType          : 'HANDEDTYPE',
    GradYear            : 'GRADYEAR',
    Mbr_Start_Date      : 'MBR_START_DATE',
    Mbr_Exp_Date        : 'MBR_EXP_DATE',
    MbrType             : 'MBRTYPE',
    IMPACT              : 'IMPACT',
    BGStatusID          : 'BGSTATUSID',
    BGExpDate           : 'BGEXPDATE',
    CoachStatusID       : 'COACHSTATUSID',
    SafeSport_Start_Date: 'SAFESPORT_START_DATE',
    SafeSport_End_Date  : 'SAFESPORT_END_DATE',
    SafeSport_StatusID  : 'SAFESPORT_STATUSID',
    RefCertName         : 'REFCERTNAME',
    RefEndDate          : 'REFENDDATE',
    ScoreCertName       : 'SCORECERTNAME',
    ScoreEndDate        : 'SCOREENDDATE',
    BchRefCertName      : 'BCHREFCERTNAME',
    BchRefEndDate       : 'BCHREFENDDATE'
}

const
  JUNIOR_CODES  = ['OJ', 'J', 'OY', 'OZ', 'DJ', 'SJ', 'LJ'],
  ADULT_CODES   = ['R', 'OA','CA'];

const IMPACT_TRUE_VALUES    = ['True', true, '1', 1];
const IMPACT_CERT_NAME      = 'IMPACT';

module.exports = {
    update_master_tables: function (master_club_id, items, cb) {
        let $currentSeason = parseInt(sails.config.sw_season.current, 10);

        return Promise.all(
          items.map(item => {
             let type = this.getMemberType(item.membership_code);

             let sql;
             if(type === 1) {
               sql = _get_sql('J');
             } else if (type === 2) {
                sql = _get_sql('A');
             } else {
               return null;
             }

             return Db.query(sql.update, [master_club_id, item.webpoint_parse_id, item.usav_number, $currentSeason])
             .then(result => {

                 checkMembershipStatusDifference(result, item);

                if(result.rowCount === 0) {
                    return Db.query(sql.insert, [master_club_id, item.webpoint_parse_id, $currentSeason]);
                } else {
                    return null;
                }
             })
          })
        ).then(() => {
            if(cb) {
                cb();
            }
        }).catch(err => {
           if(cb) {
              cb(err);
           } else {
              throw err;
           }
        });
    },

    getMinAge: function(birthday) {
        var bday = new Date(birthday),
            year = sails.config.sw_season.current,
            usav_age = year - bday.getFullYear();
        if (new Date(year, bday.getMonth(), bday.getDate()) >= new Date(year, 7 - 1, 1) ) {
            usav_age--;
        }
        if (usav_age < 10) usav_age = 10;
        return usav_age;
    },

    getMemberType: function (code) {
        let c = code.toUpperCase();

        if(JUNIOR_CODES.indexOf(c) >= 0) {
          return 1;
        } else if (ADULT_CODES.indexOf(c) >= 0) {
          return 2;
        } else {
          return 0;
        }
    },

    getHeightInInches: function (height) {
        // if height is number - just return it
        if (typeof height === 'number') return height;
        // if height is not defined or passed not string - nothing to do here
        if (!height || typeof height !== 'string') return 0;
        // parsing height as a string with two numbers
        var values = height.match(/(\d){1,2}/g),
            new_height = 0;

        if (values) {
            if (values.length === 2) {
                if( parseInt(values[0]) > 48 )
                    new_height = parseInt(values[0], 10);
                else
                    new_height = parseInt(values[0], 10)*12 + parseInt(values[1], 10);
            } else if (values.length === 1 && parseInt(values[0], 10) < 8) {
                new_height = parseInt(values[0], 10)*12;
            } else if (values.length === 1 && parseInt(values[0], 10) > 48) {
                new_height = parseInt(values[0], 10);
            }
        }

        return new_height;
    },

    check_queue: function (master_club_id, cb) {
        if(!master_club_id) return cb({
            text: 'No master_club_id specified'
        });

        var query = 
            'SELECT wq.created, mc.webpoint_import_agree AS agree \
            FROM webpoint_queue wq \
            LEFT JOIN master_club mc \
                ON mc.master_club_id = wq.master_club_id \
            WHERE wq.master_club_id = $1 \
            AND wq.responded IS NULL \
            LIMIT 1';
        Db.query(query, [master_club_id]).then(function (result) {
            cb(null, result.rows[0] || {});
        }).catch(err => {
            cb(err);
        })
    },

    convertPosition: function (pos) {
        switch(pos) {
            case 'Setter':
            case 'S':
                return 6;
            case 'Outside/Right Side':
            case 'OH':           
            case 'OH/RS':
            case 'Outside':
                return 5;
            case 'Middle Blocker/Hitter':
            case 'MH':
            case 'MB':
            case 'Middle':
                return 3;
            case 'Defensive Specialist':
            case 'DS':
                return 8;
            case 'Libero':
            case 'LB': 
                return 7;
            case 'RS':
            case 'Right Side':
            case 'RS/OH':
                return 4;
            default: 
                loggers.wp.error('[EXCEl IMPORT] no such position ', pos);
                return -1;
        }
    },

    parseCountry: function (c) {
        switch(c.toLowerCase()) {            
            case 'me':
            case 'mexico':
                return 'ME';    
            case 'us':
            case 'usa':
            case 'united states':
                return 'US';
            case 'pr':
            case 'puerto rico':
                return 'PR';
            case 'ca':
            case 'canada':
                return 'CA';
            case 'vi':
            case 'virgin islands':
                return 'VI';
            case 'as':
            case 'american samoa':
                return 'AS';
            case 'gu':
            case 'guam':
                return 'GU';
            case 'bs':
            case 'bahamas':
                return 'BS';
            default: return null;
        }
    },

    setPrimaryClubStaff: function (master_club_id, next) {
        if (!master_club_id) {
            // !!! DO NOT RETURN STRING AS ERROR
            return next('No master_club_id provided');
        }

        /* Set "primary" for those staffers who have only one "master_staff_role" row */

        let query = 
           `UPDATE "master_staff_role" 
            SET "primary" = TRUE  
            WHERE "master_staff_role_id" IN (  
                SELECT   
                    STRING_AGG(msr.master_staff_role_id::TEXT, '')::INTEGER  
                FROM master_staff_role msr  
                LEFT JOIN master_staff ms 
                  ON ms.master_staff_id = msr.master_staff_id 
                WHERE ms.master_club_id = $1 
                    AND msr.master_team_id IS NOT NULL 
                GROUP BY msr.master_staff_id  
                HAVING COUNT(msr.master_staff_id) = 1  
            )`;
        Db.query(query, [master_club_id]).then(function () {
            next();
        }).catch(err => {
            next(err);
        })
    },

    getMembershipNumber: function (usav) {
        return (_.first(String(usav).match(/[0-9]{7,7}/)) || null)
    },

    convertMemberWPApiResponse: function (data) {
        return getMemberDataWPApiV1(data[0]);
    },

    convertClubWPApiResponse: function (data) {
        return getClubDataWPApiV1(data);
    },

    isImpact: function (wpImpactValue) {
        return IMPACT_TRUE_VALUES.includes(wpImpactValue);
    },

    getWpCertName: function (wpImpactValue) {
        return this.isImpact(wpImpactValue) ? IMPACT_CERT_NAME : null;
    }
};

let getMemberDataWPApiV1 = function (memberData) {
    let formattedMember = getOldObjectKeys(memberData);

    return {
        "MEMBER_DATA": {
            "RESPONSE": {
                "Code": "0",
                "Msg": "OK"
            },
            "MEMBER": formattedMember
        }
    }
};

let getClubDataWPApiV1 = function (members) {
    if(members && members.length) {
        members = members.map(member => {
            return getOldObjectKeys(member);
        });
    } else {
        members = [];
    }

    return {
        "CLUBMEMBERS_DATA": {
            "RESPONSE": {
                "Code": "300",
                "Context": "Success",
                "RecordCount": String(members.length)
            },
            "MEMBER": members
        }
    }
};

function checkMembershipStatusDifference(dbItem, parserItem) {
    let dbMemberStatus     = dbItem && dbItem.rows[0] && dbItem.rows[0].membership_status;
    let parserMemberStatus = parserItem.status;

    if(dbMemberStatus !== parserMemberStatus) {
        loggers.wp.verbose(
            `PARSING: different status from WP parser for athlete ${parserItem.usav_number} (Parse ID: ${parserItem.webpoint_parse_id}).`
        );
    }
}

function getOldObjectKeys (data) {
    if(!_.isObject(data)) {
        throw new Error('Webpoint Response should be an Object');
    }

    let formattedObject = {};

    Object.keys(data).forEach(fieldName => {
        let oldFieldName = '';

        if(OLD_WP_API_MEMBER_ALIASES[fieldName]) {
            oldFieldName = OLD_WP_API_MEMBER_ALIASES[fieldName];
        } else {
            oldFieldName = fieldName;
        }

        formattedObject[oldFieldName] = data[fieldName];
    });

    return formattedObject;
}

function _get_sql (mr_type) { 
    if(mr_type === 'J') {
        return {
            check: 
                `SELECT (t.created <> t.modified) AS row_updated 
                 FROM master_athlete t 
                 WHERE t.organization_code = $1 
                    AND t.master_club_id = $2 `,
            update:
                `UPDATE master_athlete ma 
                   SET 
                         FIRST = subquery.first, 
                         LAST = subquery.last, 
                         email = subquery.email, 
                         address = subquery.addr, 
                         birthdate = subquery.birthdate, 
                         city = subquery.city, 
                         gender = subquery.gender, 
                         gradyear = subquery.gradyear, 
                         master_club_id = $1, 
                         organization_code = subquery.usav_code, 
                         phoneh       = COALESCE(NULLIF(ma.phoneh, ''), subquery.home_phone), 
                         phonem       = COALESCE(NULLIF(ma.phonem, ''), subquery.cell_phone),
                         other_phone  = COALESCE(NULLIF(ma.other_phone, ''), subquery.other_phone), 
                         state = subquery.state, 
                         zip = subquery.zip, height = subquery.height, 
                         nick = subquery.nick, address2 = subquery.address2, 
                         parent1_first = subquery.parent1_first, 
                         parent1_last = subquery.parent1_last, parent1_email = subquery.parent1_email, 
                         parent2_first = subquery.parent2_first, parent2_last = subquery.parent2_last, 
                         parent2_email = subquery.parent2_email,
                         usav_number = subquery.usav_number, season = $4
                     FROM ( 
                         SELECT  
                             FIRST, LAST, email, COALESCE(address, address2) AS addr, 
                             birthdate, city, gender, gradyear, usav_code, 
                             home_phone, cell_phone, state, zip, height, 
                             nick, address2, other_phone, parent1_first, parent1_last, 
                             parent1_email, parent2_first, parent2_last, parent2_email, 
                             status, usav_number 
                         FROM webpoint_parse 
                         WHERE webpoint_parse_id = $2 
                     ) AS subquery 
                 WHERE ma.usav_number = $3 
                    AND ma.master_club_id = $1 AND ma.deleted IS NULL
                 RETURNING ma.organization_code, ma.usav_number, ma.membership_status `,

            insert: 
                ` INSERT INTO master_athlete( 
                      FIRST, LAST, email, address, birthdate, city, gender, gradyear, 
                      master_club_id, organization_code, phoneh, phonem, state, zip, height, 
                      nick, address2, other_phone, parent1_first, parent1_last, 
                      parent1_email, parent2_first, parent2_last, parent2_email, 
                      membership_status, usav_number, season
                  ) 
                  SELECT   
                      FIRST, LAST, email, COALESCE(address, address2) AS addr,  
                      birthdate, city, gender, gradyear, $1, usav_code,  
                      home_phone, cell_phone, state, zip, height,  
                      nick, address2, other_phone, parent1_first, parent1_last, 
                      parent1_email, parent2_first, parent2_last, parent2_email, 
                      status, usav_number, $3
                  FROM webpoint_parse  
                  WHERE webpoint_parse_id = $2`
        };
    }
    return {
        check: 
            `SELECT (t.created <> t.modified) "row_updated"
             FROM master_staff t 
             WHERE t.organization_code = $1
                AND t.master_club_id = $2`,
        update:
            `UPDATE "master_staff" ms 
              SET 
                    FIRST = subquery.first, 
                    LAST = subquery.last, 
                    email = subquery.email, 
                    birthdate = subquery.birthdate, 
                    gender = subquery.gender, 
                    master_club_id = $1, 
                    organization_code = subquery.usav_code, 
                    phone  = COALESCE(NULLIF(ms.phone, ''), subquery.cell_phone), 
                    phonew = COALESCE(NULLIF(ms.phonew, ''), subquery.work_phone),  
                    phoneo = COALESCE(NULLIF(ms.phoneo, ''), subquery.other_phone), 
                    phoneh = COALESCE(NULLIF(ms.phoneh, ''), subquery.home_phone),
                    cert = subquery.cap_client, 
                    address = subquery.address, 
                    city = subquery.city, 
                    state = subquery.state, 
                    zip = subquery.zip, 
                    nick = subquery.nick, 
                    address2 = subquery.address2, 
                    bg_screening = subquery.bg_screening, 
                    bg_expire_date = subquery.bg_expire_date,  
                    chaperone_status = subquery.chaperone_status, coach_status = subquery.coach_status, 
                    usav_number = subquery.usav_number, season = $4 
                FROM (
                    SELECT  
                        birthdate, email, FIRST, gender, LAST, "cell_phone", 
                        usav_code, cap_client, address, home_phone, city, state, zip, nick, address2, work_phone, 
                        other_phone, status, bg_screening, bg_expire_date, chaperone_status, coach_status, usav_number
                    FROM webpoint_parse 
                    WHERE webpoint_parse_id = $2 
                ) "subquery"
             WHERE ms.usav_number = $3 
             AND ms.master_club_id = $1 
                AND ms.deleted IS NULL 
             RETURNING ms.organization_code, ms.membership_status `,
        insert: 
            `INSERT INTO master_staff (
                FIRST, LAST, email, birthdate, gender, master_club_id, organization_code,
                phone, cert, address, phoneh, state, zip, city, nick, address2, phonew, 
                phoneo, membership_status, bg_screening, bg_expire_date, chaperone_status, 
                coach_status, usav_number, season 
             ) 
             SELECT 
                 FIRST, LAST, email, birthdate, gender, $1, usav_code,
                 cell_phone, cap_client, address, home_phone, state, zip, city, 
                 nick, address2, work_phone, other_phone, status,  
                 bg_screening, bg_expire_date, chaperone_status, coach_status, usav_number, $3 
             FROM 
                 webpoint_parse 
             WHERE webpoint_parse_id = $2`
    };
}

'use strict';

const 
	moment 	= require('moment'),
	co 		= require('co');

const 
	stripeConnect = require('../lib/StripeConnect');

const VALID_PAYMENT_ITEMS = ['teams', 'tickets', 'booths', 'camps'];

const banService = {
	// covered 😄👍
	banEmail: function (email, reason, eventID, purchaseID, paymentFor) {
		return Promise.resolve().then(() => {
			let historyLine = this.__formatHistoryLine__(null, eventID, paymentFor);

			return Db.query(
				`WITH "existing" AS (
				 	SELECT * FROM "banned_email" 
				 	WHERE LOWER(TRIM("email")) = LOWER(TRIM($1))
				 ), "inserted" AS (
				 	INSERT INTO "banned_email" (
				 		"email", "reason", "history", "event_id", "purchase_id"
				 	)
				 	SELECT LOWER(TRIM($1)), $2, $3::JSON, ($4)::INTEGER, ($5)::INTEGER
				 	WHERE NOT EXISTS (SELECT * FROM "existing")
				 	RETURNING *
				 )
				 SELECT * FROM "existing" UNION ALL SELECT * FROM "inserted"`,
				[email, reason || null, `[${historyLine}]`, eventID, purchaseID]
			)
		}).then(result => result.rows[0] || null);
	},
	// covered 😄👍
	banFingerprint: function (fingerprint, reason, eventID, purchaseID,  paymentFor) {
		return Promise.resolve().then(() => {
			let historyLine = this.__formatHistoryLine__(null, eventID, paymentFor);

			return Db.query(
				`WITH "existing" AS (
				 	SELECT * FROM "banned_fingerprint" 
				 	WHERE LOWER(TRIM("fingerprint")) = LOWER(TRIM($1))
				 ), "inserted" AS (
				 	INSERT INTO "banned_fingerprint" (
				 		"fingerprint", "reason", "history", "event_id", "purchase_id"
				 	)
				 	SELECT LOWER(TRIM($1)), $2, $3::JSON, ($4)::INTEGER, ($5)::INTEGER 
				 	WHERE NOT EXISTS (SELECT * FROM "existing")
				 	RETURNING *
				 )
				 SELECT * FROM "existing" UNION ALL SELECT * FROM "inserted"`,
				[fingerprint, reason || null, `[${historyLine}]`, eventID, purchaseID]
			)
		}).then(result => result.rows[0] || null);
	},
	__getActionByType__: function (paymentFor) {
		return VALID_PAYMENT_ITEMS.includes(paymentFor) 
				? `dispute on existing ${paymentFor} payment` 
				: null;
	},
	__formatHistoryLine__: function (action, eventID, paymentFor) {
		let type = action || (this.__getActionByType__(paymentFor)) || (void 0);

		return JSON.stringify({
			type, 
			attempt 	: moment().utc().toISOString(),
			event 	 	: eventID
		});
	},
	checkEmail: function (email, action, eventID) {
		return co(function* () {
			let result = yield (Db.query(
				`SELECT * FROM "banned_email" WHERE LOWER(TRIM("email")) = LOWER(TRIM($1))`,
				[email]
			));
			
			let row = _.first(result.rows) || null;

			if(!(row && action)) {
				return row;
			}

			let historyLine = this.__formatHistoryLine__(action, eventID);

			let updateResult = yield (Db.query(
				`UPDATE "banned_email" 
				 SET "history" = (
				     SELECT ARRAY_TO_JSON(ARRAY_AGG(
				     			"history"."row" ORDER BY ("history"."row"->>'attempt')::TIMESTAMP DESC
				     		))
				     FROM (
				         SELECT JSON_ARRAY_ELEMENTS("history") "row"
				         FROM "banned_email" 
				         WHERE "banned_email_id" = $1
				         UNION ALL 
				         SELECT ($2)::JSON 
				     ) "history"
				 )
				 WHERE "banned_email_id" = $1
				 RETURNING *`,
				[row.banned_email_id, historyLine]
			));

			return _.first(updateResult.rows);
		}.bind(this))
	},
	checkFingerprint: function (fingerprint, action, eventID) {
		return co(function* () {
			let result = yield (Db.query(
				`SELECT * FROM "banned_fingerprint" WHERE LOWER(TRIM("fingerprint")) = LOWER(TRIM($1))`,
				[fingerprint]
			));
			
			let row = _.first(result.rows) || null;

			if(!(row && action)) {
				return row;
			}

			let historyLine = this.__formatHistoryLine__(action, eventID);

			let updateResult = yield (Db.query(
				`UPDATE "banned_fingerprint" 
				 SET "history" = (
				     SELECT ARRAY_TO_JSON(ARRAY_AGG(
				     			"history"."row" ORDER BY ("history"."row"->>'attempt')::TIMESTAMP DESC
				     		))
				     FROM (
				         SELECT JSON_ARRAY_ELEMENTS("history") "row"
				         FROM "banned_fingerprint" 
				         WHERE "banned_fingerprint_id" = $1
				         UNION ALL 
				         SELECT ($2)::JSON 
				     ) "history"
				 )
				 WHERE "banned_fingerprint_id" = $1
				 RETURNING *`,
				[row.banned_fingerprint_id, historyLine]
			));

			return _.first(updateResult.rows);
		}.bind(this))
	},
	getPurchaseEmail: function (chargeId) {
		return Db.query(
			`SELECT 
				p."email", p."event_id", p."payment_for", p."purchase_id"
			 FROM "purchase" p 
			 WHERE p.stripe_charge_id = $1`,
			[chargeId]
		).then(res => res.rows[0] || null)
	},
	// covered 😄👍
	banLostDisputes: function () {
		const REASON = 'dispute';

		let banDisputer = dispute => {
			return this.getPurchaseEmail(dispute.charge_id)
			.then(purchase => {
				if (!purchase) {
					return;
				}

				let { event_id: eventID, email: purchaseEmail, payment_for: paymentFor, purchase_id: purchaseID} = purchase;

				let tasks = [];

				if (dispute.fingerprint) {
					tasks.push(
						this.banFingerprint(dispute.fingerprint, REASON, eventID, purchaseID, paymentFor)
					)
				}

				if (dispute.email) {
					tasks.push(this.banEmail(dispute.email, REASON, eventID,purchaseID, paymentFor))
				}

				if (purchaseEmail && (purchaseEmail !== dispute.email)) {
					tasks.push(this.banEmail(purchaseEmail, REASON, eventID,purchaseID, paymentFor))
				}

				return Promise.all(tasks).then(() => {});
			})
		}

		return co(function* () {
			let lostDisputes = yield (stripeConnect.getLostDisputes());

			yield (Promise.all(
				lostDisputes.map(banDisputer)
			));
		}.bind(this));
	}
}

module.exports = banService;

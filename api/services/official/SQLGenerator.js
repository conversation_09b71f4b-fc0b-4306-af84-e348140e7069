'use strict';

const co = require('co');

const COMPUTED_VALUES = {
	schedule_name: (value) => squel.str(`(CASE WHEN "schedule_name" IS NOT NULL THEN "schedule_name" ELSE ? END)`, value)
}

class SQLGenerator {
	constructor (co) {
		this._co 			= co;
		this._EO_COLUMNS 	= null;
	}

	_getEOCols () {
		if (this._EO_COLUMNS && this._EO_COLUMNS.length) {
			return Promise.resolve(this._EO_COLUMNS);
		} else {
			return Db.query(
				`SELECT "column_name"
				 FROM information_schema.columns
				 WHERE table_schema = 'public'
				   AND table_name   = 'event_official'
				   AND "column_name" NOT IN (
				     'event_official_id', 'created', 'modified', 'event_id', 'official_id'
				   )`
			).then(result => (this._EO_COLUMNS = result.rows.map(r => r.column_name)));
		}
	}

	_genEOUpdateQuery (columns, data) {
		let sql = squel.update().table('event_official')
            .where('event_id = ?', data.event_id)
            .where('official_id = ?', data.official_id)
            .returning('"event_official_id" "id"');

        for (let _column of columns) {
        	if (typeof data[_column] !== 'undefined') {
        		let _value = data[_column];

        		sql.set(
        			_column, 
        			_.isFunction(COMPUTED_VALUES[_column])
        				?COMPUTED_VALUES[_column](_value)
        				:_value
        		);
        	} else {
        		sql.set(_column, null);
        	}
        }

        return sql;
	}

	_genEOInsertSubQuery (data, dataKeys) {
		let _keys = dataKeys || Object.keys(data);

		let sql  = squel.select();

        for (let _k of _keys) {
            // https://github.com/hiddentao/squel/issues/264
            sql.field(squel.str('?', data[_k]));
        }

        return sql;
	}

	/* contains event_id, schedule_name, official_id */
	async generateRegInfoUpdQuery (official) {
        let columns = await this._getEOCols();
        
        let officialKeys = Object.keys(official);
        
        let _updSQL = this._genEOUpdateQuery(columns, official);
        
        let _insSubSQL = this._genEOInsertSubQuery(official, officialKeys);
        _insSubSQL.where('NOT EXISTS (SELECT * FROM "upd")')
        
        let _insSQL = squel.insert().into('event_official')
            .fromQuery(officialKeys, _insSubSQL)
            .returning('"event_official_id" "id"');

        return squel.select().field('*').from('ins')
            .with('upd', _updSQL)
            .with('ins', _insSQL)
            .union(squel.select().field('*').from('upd'));
	}
	// covered 😄👍
	_generateInternalPartForAppChange (eventID, officialID, dataToSave, tableFields) {
		let initFields      = [],
            fieldsToUpdate  = [],
            params       	= [eventID, officialID];

        for (let field of tableFields) {
            if (dataToSave[field] !== undefined) {

                params.push(dataToSave[field]);
            	
            	initFields.push(`eof."${field}"`);

            	if(field === 'is_staff' && dataToSave[field]) {
                    fieldsToUpdate.push('staff_deleted = NULL')
                    const newWorkStatus = squel.case()
                        .when('staff_deleted IS NOT NULL').then(squel.str(`?::official_work_status`, OfficialsService.checkin.WORK_STATUS.PENDING))
                        .else(squel.str('staff_work_status'))
                    fieldsToUpdate.push(`staff_work_status = ${newWorkStatus}`)
                }

                if(field === 'is_official' && dataToSave[field]) {
                    fieldsToUpdate.push('deleted = NULL')
                    const newWorkStatus = squel.case()
                        .when('deleted IS NOT NULL').then(squel.str(`?::official_work_status`, OfficialsService.checkin.WORK_STATUS.PENDING))
                        .else(squel.str('work_status'))
                    fieldsToUpdate.push(`work_status = ${newWorkStatus}`)
                }

            	if (field === 'schedule_name') {
            		fieldsToUpdate.push(
            			`"${field}" = (CASE WHEN ("${
            				field
            			}" <> '') IS NOT TRUE THEN $${params.length} ELSE "${field}" END)`);
            	} else {
            		fieldsToUpdate.push(`"${field}" = $${params.length}`);
            	}

            }
        }

        return {
        	init 		: initFields, 
        	upd 		: fieldsToUpdate, 
        	params 		: params 
        };
	}

	genApplicationChangeQuery (eventID, officialID, dataToSave, tableFields) {
		let result = this._generateInternalPartForAppChange(eventID, officialID, dataToSave, tableFields);

		result.init = result.init.join(', ');
		result.upd 	= result.upd.join(', ');

		let params = result.params;

        let sql = 
            `WITH "init" AS (
                SELECT 
                    (SELECT ROW_TO_JSON("init") FROM (SELECT ${result.init}, eof."event_official_id") "init") "eof_init",
                    (SELECT 
                        ROW_TO_JSON("of_data") 
                     FROM (
                        SELECT 
                            of.usav_num, of.rank, eof.work_status,
                            FORMAT(
                                '%s %s %s %s %s, %s', of.address, of.city, of.state, of.region, of.country, of.zip
                            ) "address"
                     ) "of_data" 
                    ) "official"
                FROM "event_official" eof
                INNER JOIN "official" of 
                    ON of.official_id = eof.official_id
                WHERE eof.event_id = $1
                    AND eof.official_id = $2
            ) UPDATE "event_official" eof 
                SET ${result.upd}
             WHERE eof.event_id = $1
                AND eof.official_id = $2
                AND EXISTS (SELECT * FROM "init")
             RETURNING 
                (SELECT "official" FROM "init") "general",
                (SELECT "eof_init" FROM "init") "initial",
                (SELECT ROW_TO_JSON("updated") FROM (SELECT ${result.init}) "updated") "current"`;

        return { sql, params };
    }
}

module.exports = new SQLGenerator(co);

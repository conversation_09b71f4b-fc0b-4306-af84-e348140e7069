'use strict';

const crypto = require('crypto');
const path = require('path');
const QRCodeGenerator = require('../../lib/QRCodeGenerator');

class EntryQRCodeService {
    constructor () {
        this._qrCodeGenerator = new QRCodeGenerator({
            s3Path: '/images/official/qrcode',
            localPath: path.resolve(__dirname, '../../../../data/officials/qrcodes'),
        });
    }

    get QR_CONTENT_PREFIX () {
        return 'SWOf'
    }

    get EMAIL_TEMPLATE () {
        return 'official/checkin_barcode';
    }

    get APP_DOMAIN () {
        return sails.config.urls.main_app.baseUrl;
    }

    get DOMAIN_URL () {
        return sails.config.urls.home_page.baseUrl;
    }

    get DEFAULT_SENDER () {
        return 'SportWrench <<EMAIL>>';
    }

    get BORDER_COLOR () {
        return 'blue';
    }

    async sendEntryQRCodes(eventID, filters) {
        
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }
        
        const event = await this._getEventData(eventID);
        if (!event.official_qr_code_enable) {
            throw { validation: 'Option send qr-code to Official disabled'};
        }
        
        const officials = await this._getOfficialsData(eventID, filters);
        for(const official of officials) {
            if(!official.checkin_barcode) {
                official.checkin_barcode = await this._generateBarcode(official.official_id);
            }
            const name = `${official.first} ${official.last}`;
            official.qr_url = await this._createQRCode(eventID, official.checkin_barcode, name);
        }
        for(const official of officials) {
            await this._sendEmail(official, event);
        }
    }

    getEntryQRCodeLink(eventID, barcode) {
        return `${this.DOMAIN_URL}/api/official/event/${eventID}/entry-qr-code/${barcode}`;
    }

    geQRCodeImageName(eventID, barcode) {
        return `${this.QR_CONTENT_PREFIX}${barcode}-${eventID}.png`;
    }

    async getEntryQRCodeData(eventID, barcode) {
        const event = await this._getEventData(eventID);

        if (!event.official_qr_code_enable) {
            throw { validation: 'Option send qr-code to Official disabled'};
        }

        const {first, last, is_official_registered} = await this._getOfficialData(eventID, barcode);

        if (!is_official_registered) {
            throw { validation: 'Official not found on this Event'};
        }

        const imageFileName = this.geQRCodeImageName(eventID, barcode);
        const qrCodeFilePath = this._qrCodeGenerator.getQRCodeFilePath(imageFileName);
        const qrCodeGenerated = await FileUploadService.fileExistsInS3(qrCodeFilePath);

        return {
            ...event,
            first, last,
            event_logo: `${this.DOMAIN_URL}${event.event_logo}`,
            border_color: this.BORDER_COLOR,
            qr_url: qrCodeGenerated ? `${this.DOMAIN_URL}${qrCodeFilePath}` : null,
        };
    }

    async _createQRCode(eventID, checkinBarcode, name) {
        const imageContent = `${this.QR_CONTENT_PREFIX}${eventID}${checkinBarcode}${name}`;
        const imageFileName = this.geQRCodeImageName(eventID, checkinBarcode);
        const maxUploadAttempts = 2;

        return this._qrCodeGenerator.createQRCode(imageContent, imageFileName, maxUploadAttempts);
    }

    _getOfficialsData(eventID, filters) {
        //Generate QR codes only for Accepted officials
        filters.work_status = OfficialsService.checkin.WORK_STATUS.APPROVED;
        filters.role = OfficialsService.checkin.ROLE.OFFICIAL;

        let query = OfficialsService.getQREntryCodeListQuery(eventID, filters);

        return Db.query(query).then(r => r.rows);
    }

    _getEventData(eventID) {
        return Db.query(
            knex('event AS e')
                .column(
                    'e.event_id',
                    {
                        event_logo: knex.raw(`(
                            SELECT concat(em.file_path, '.', em.file_ext)
                            FROM event_media AS em
                            WHERE em.event_id IN (${HomePageService.DEFAULT_PLACEHOLDER_ID}, e.event_id)
                            AND em.file_type = 'main-logo'
                            ORDER BY em.event_id DESC
                            LIMIT 1 
                        )`),
                        event_name: 'e.long_name',
                        official_qr_code_enable: 'e.official_qr_code_enable'
                    },
                )
                .where('e.event_id', eventID)
                .limit(1)
        ).then(r => r.rows[0]);
    }

    _getOfficialData(eventID, barcode) {
        return Db.query(
            knex('official AS o')
                .select([
                    'u.first', 'u.last', 'u.deleted_at', 'eof.event_official_id',
                    knex.raw(`
                        CASE WHEN eof.event_official_id IS NULL THEN FALSE
                        ELSE TRUE END as "is_official_registered"
                    `),
                ])
                .innerJoin('user AS u', 'u.user_id', 'o.user_id')
                .leftJoin('event_official as eof', (join) => {
                    join.on('eof.official_id', 'o.official_id')
                        .andOn(knex.raw(`eof.event_id = ?`, eventID))
                        .andOn(knex.raw('eof.deleted IS NULL'))
                        .andOn(knex.raw('eof.is_official IS TRUE'))
                })
                .where('o.checkin_barcode', barcode)
        ).then(r => r.rows[0]);
    }

    async _generateBarcode(officialID) {
        const length = 9;
        let barcode;
        let exists;
        let iterations = 0;
        do {
            iterations++;
            if(iterations > 100) {
                throw new Error('Exceed iterations limit for barcode generation');
            }
            barcode = crypto.randomBytes(Math.ceil(length/2)).toString('hex').slice(0, length);
            exists = await Db.query(
                knex('official')
                    .select(knex.raw('1'))
                    .where('checkin_barcode', barcode)
                    .limit(1)
            ).then(r => r.rowCount > 0);
        } while(exists);
        await Db.query(
            knex('official')
                .where('official_id', officialID)
                .update('checkin_barcode', barcode)
        );

        return barcode;
    }


    async _sendEmail(official, event) {
        const subject = `${official.first} ${official.last}'s Ticket for ${event.event_name}`;

        const data = {
            ...official,
            ...event,
            event_logo: `${this.DOMAIN_URL}${event.event_logo}`,
            border_color: this.BORDER_COLOR,
            app_domain: this.APP_DOMAIN,
        };

        return EmailService.renderAndSend({
            template: this.EMAIL_TEMPLATE,
            data,
            from: this.DEFAULT_SENDER,
            to: official.email,
            subject,
        })
    }
}

module.exports = new EntryQRCodeService();

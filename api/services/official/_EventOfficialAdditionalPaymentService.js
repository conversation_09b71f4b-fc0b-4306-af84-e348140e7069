class EventOfficialAdditionalPaymentService {
    constructor() {}

    getAllowedApplyFlags() {
        return [APPLY_TO.ALL, APPLY_TO.EMPTY_CELLS];
    }

    getCategories(type) {
        return Db.query(GET_CATEGORIES_SQL(type)).then(({ rows }) => rows);
    }

    async setDefaults({ categories, eventID, applyTo, memberType }) {
        const officials = await Db.query(GET_APPROVED_EVENT_OFFICIALS(eventID, memberType)).then(({ rows }) => rows);

        const insertData = mergeOfficialsWithCategories({ officials, categories, eventID, memberType });

        const { query, values } = GET_SET_DEFAULT_SQL({ insertData, applyTo });

        return Db.query(query, values);
    }

    update({ additionalPayment, eventID }) {
        const { query, values } = GET_UPDATE_QUERY(additionalPayment, eventID)

        return Db.query(query, values);
    }
}

const EVENT_OFFICIAL_ADDITIONAL_PAYMENT_UNIQUE_CONSTRAINT = 'event_official_additional_payment_unique';
const APPLY_TO = {
    ALL: 'all',
    EMPTY_CELLS: 'emptyCells',
}

function GET_CATEGORIES_SQL(type)  {
    const query = squel.select()
        .field('official_additional_payment_category_id')
        .field('category')
        .from('official_additional_payment_category')
        .order('official_additional_payment_category_id');

    if(type === OfficialsService.payout.MEMBER_TYPE.OFFICIAL) {
        query.where('show_for_officials IS TRUE');
    } else if(type === OfficialsService.payout.MEMBER_TYPE.STAFF) {
        query.where('show_for_staff IS TRUE');
    }

    return query;
}

function GET_APPROVED_EVENT_OFFICIALS(eventID, memberType) {
    const query = squel.select()
        .field('eo.event_official_id')
        .from('event_official', 'eo')
        .where('eo.event_id = ?', eventID)

    if(memberType === OfficialsService.payout.MEMBER_TYPE.OFFICIAL) {
        query.where('eo.work_status = ?', OfficialsService.payout.WORK_STATUS.APPROVED)
    } else {
        query.where('eo.staff_work_status = ?', OfficialsService.payout.WORK_STATUS.APPROVED)
    }

    return query;
}

function GET_SET_DEFAULT_SQL({ insertData, applyTo }) {
    const sql = squel.insert()
        .into('event_official_additional_payment')
        .setFieldsRows(insertData);

    let { text: query, values } = sql.toParam();

    query += `ON CONFLICT ON CONSTRAINT ${EVENT_OFFICIAL_ADDITIONAL_PAYMENT_UNIQUE_CONSTRAINT} DO
        UPDATE SET amount = EXCLUDED.amount`

    if (applyTo === APPLY_TO.EMPTY_CELLS) {
        query += ' WHERE event_official_additional_payment.amount IN (NULL, 0)';
    }

    return { query, values };
}

function GET_UPDATE_QUERY({ amount, official_additional_payment_category_id, event_official_id, member_type }, eventID) {
    const sql = squel.insert()
        .into('event_official_additional_payment')
        .setFields({
            amount,
            event_official_id,
            official_additional_payment_category_id,
            event_id: eventID,
            member_type
        });

    let { text: query, values } = sql.toParam();

    query += `ON CONFLICT ON CONSTRAINT ${EVENT_OFFICIAL_ADDITIONAL_PAYMENT_UNIQUE_CONSTRAINT} DO
        UPDATE SET amount = EXCLUDED.amount`


    return { query, values };
}

function mergeOfficialsWithCategories({ officials, categories, eventID, memberType }) {
    const result = [];

    for (let o = 0; o < officials.length; o++) {
        for (let c = 0; c < categories.length; c++) {
            result.push(Object.assign(
                {},
                officials[o],
                categories[c],
                { event_id: eventID, member_type: memberType },
                ),
            );
        }
    }

    return result;
}

module.exports = new EventOfficialAdditionalPaymentService();



class ReceiversService {
    createReceivers (receivers) {
        if(!Array.isArray(receivers) || !receivers.length) {
            throw new Error('Receivers is empty');
        }

        let receiversData = this.__formatReceiversData(receivers);

        return this.__createReceiversRows(receiversData);
    }

    __formatReceiversData (receivers) {
        return receivers.map(receiver => this.__formatReceiverData(receiver));
    }

    __formatReceiverData (receiver) {
        if(_.isEmpty(receiver)) {
            throw new Error('Receiver is empty');
        }

        if(!receiver.ticket_coupon_id) {
            throw new Error('Ticket coupon is empty');
        }

        if(receiver.roster_team_id) {
            return _.pick(receiver, ['ticket_coupon_id', 'roster_team_id']);
        }

        if(receiver.email) {
            if(!receiver.first) {
                throw new Error('First is empty');
            }

            if(!receiver.last) {
                throw new Error('Last is empty');
            }

            return _.pick(receiver, ['ticket_coupon_id', 'email', 'first', 'last']);
        }
    }

    __createReceiversRows (receiversData) {
        let query = knex('ticket_coupon_receiver')
            .insert(receiversData)
            .returning('ticket_coupon_receiver_id');

        return Db.query(query);
    }

}

module.exports = new ReceiversService();

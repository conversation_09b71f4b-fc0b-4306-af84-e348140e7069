

class GenerationService {

    get CODE_SIZE() {
        return 8;
    }

    async processCouponGenerationTask(eventID, task) {
        const couponReceiverType = task.for;

        switch(couponReceiverType) {
            case CouponService.COUPON_RECEIVER_TYPES.TEAMS: {
               return this.__generateTeamsCoupons(eventID, task);
            }
            case CouponService.COUPON_RECEIVER_TYPES.CUSTOM: {
                return this.__generateCustomCoupons(eventID, task);
            }
            default:
                throw new Error(`Unknown coupon receiver type: ${couponReceiverType}`);
        }
    }

    async __generateTeamsCoupons (eventID, task) {
        const teams = await this.__findTeams(eventID, task.filters);
        const { counters, coupons, newCoupons } = await this.__generateCouponsForTeams(teams, task);

        if(!_.isEmpty(newCoupons)) {
            await CouponService.receiver.createReceivers(newCoupons);
        }

        if(!_.isEmpty(coupons)) {
            let couponIDs = coupons.map(c => c.ticket_coupon_id);

            counters.sentEmails = await CouponService.sending.sendCouponsMessages({
                eventID, filters: { coupons: couponIDs }, teamsRecipientTypes: task.settings.email_recipient_types
            });
        } else {
            counters.sentEmails = 0;
        }

        return counters;
    }

    async __generateCustomCoupons (eventID, task) {
        if(!eventID) {
            throw new Error('Event ID required');
        }

        let {
            settings: { event_ticket_id, quantity, send_immediately },
            receivers
        } = task;

        let generation = {
            sentEmails: 0,
            generated: 0,
            skipped: 0
        };

        let coupon = await this.__generateCoupon({eventTicketID: event_ticket_id, quantity});

        if(!_.isEmpty(coupon)) {
            let couponReceivers = receivers.map(receiver =>
                Object.assign(receiver, _.pick(coupon, ['ticket_coupon_id']))
            );

            await CouponService.receiver.createReceivers(couponReceivers);

            if(send_immediately) {
                let couponIDs = couponReceivers.map(c => c.ticket_coupon_id);

                generation.sentEmails =
                    await CouponService.sending.sendCouponsMessages({ eventID, filters: { coupons: couponIDs } });
            }

            generation.generated++;
        }

        return generation;
    }

    async __findTeams(eventID, teamFilters) {
        const filters = {...teamFilters, event: eventID, limit: null, page: null};

        const query = squel.select('roster_team_id')
            .from('roster_team', 'rt');

        SQLQueryBuilder.clubs.list(query, filters);

        return Db.query(query)
            .then(
                r => r.rows.map(
                    row => Number(row.roster_team_id)
                )
            );
    }

    async __generateCouponsForTeams(teams, task) {
        const counters = {
            generated: 0,
            skipped: 0,
        };

        let newCoupons = [],
            oldCoupons = [];

        for(const team of teams) {
            for(const ticketType of task.settings.ticket_types) {
                const { event_ticket_id: eventTicketID, quantity } = ticketType;
                if(quantity < 1) {
                    continue;
                }

                let coupon;

                if(task.mode === CouponService.GENERATION_MODES.APPEND) {
                    let existsCoupon = await this.__findTeamCoupon({rosterTeamID: team, eventTicketID});

                    if(!_.isEmpty(existsCoupon)) {
                        coupon = Object.assign(existsCoupon);

                        oldCoupons.push({
                            ticket_coupon_id: coupon.ticket_coupon_id,
                            roster_team_id: team
                        });
                        counters.skipped++;
                    }
                }

                if(_.isEmpty(coupon)) {
                    coupon = await this.__generateCoupon({eventTicketID, quantity});

                    if(coupon) {
                        newCoupons.push({
                            ticket_coupon_id: coupon.ticket_coupon_id,
                            roster_team_id: team
                        });

                        counters.generated++;
                    }
                }
            }
        }

        let coupons = [].concat(newCoupons, oldCoupons);

        return { counters, newCoupons, coupons };
    }


    __findTeamCoupon({rosterTeamID, eventTicketID}) {
        const query = knex('ticket_coupon_receiver AS tcr')
            .select('tc.ticket_coupon_id')
            .join('ticket_coupon AS tc', 'tc.ticket_coupon_id', 'tcr.ticket_coupon_id')
            .where('tcr.roster_team_id', rosterTeamID)
            .where('tc.event_ticket_id', eventTicketID)
            .groupBy('tc.ticket_coupon_id');

        return Db.query(query).then(r => r.rows[0]);
    }

    async __generateCoupon({ eventTicketID, quantity }) {
        const MAX_ATTEMPTS = 3;
        let coupon;
        for(let i = 0; i < MAX_ATTEMPTS; i++) {
            try {
                const query = knex('ticket_coupon').insert({
                    code: this._generateRandomCode(),
                    event_ticket_id: eventTicketID,
                    initial_quantity: quantity,
                    quantity: quantity,
                }).returning('ticket_coupon_id');
                coupon = await Db.query(query).then(r => r.rows[0]);
            }
            catch(err) {
                if(Db.utils.isUniqueConstraintViolation(err, 'ticket_coupon_code_uindex')) {
                    continue;
                }
                throw err;
            }
            break;
        }
        if(!coupon) {
            throw new Error('Error generating coupon');
        }
        return coupon;
    }

    _generateRandomCode() {
        let code = '';
        for(let i = 0; i < this.CODE_SIZE; i++) {
            const randomCharacter = Math.floor(Math.random() * 26 + 10).toString(36);
            code += randomCharacter;
        }
        return code;
    }
}

module.exports = new GenerationService();

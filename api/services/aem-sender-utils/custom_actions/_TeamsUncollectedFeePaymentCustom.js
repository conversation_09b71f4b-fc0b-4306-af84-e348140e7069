const MAIN_APP_URL = sails.config.urls.main_app.baseUrl;
const INCOMPLETE_PAYMENTS_TAB_ID = 2;

const getFormattedPaymentMethod = function (params) {
    let paymentMethod;

    if (params?.error?.payment_method) {
        paymentMethod = StripeService.paymentCard.stripeService.formatPaymentMethodName(
            params.error.payment_method
        );
    } else {
        paymentMethod = params.payment_method || "-";
    }

    return {
        raw: paymentMethod,
        formatted: paymentMethod
    }
}

const getConfirmationURL = function () {
    const link = `${MAIN_APP_URL}/#/events/payment-card?active_tab=${INCOMPLETE_PAYMENTS_TAB_ID}`;

    return {
        raw: link,
        formatted: link
    };
}

const ACTIONS = {
    confirm_url: getConfirmationURL,
    payment_method: getFormattedPaymentMethod,
};

module.exports = function TeamsUncollectedFeePaymentsVariablesCustomAction(variableObj, data) {
    let variableName = variableObj.field;

    let action = ACTIONS[variableName];

    if (action == null) {
        throw new Error(`Custom Action is not defined for variable '${variableName}'`);
    }

    return action(data);
}

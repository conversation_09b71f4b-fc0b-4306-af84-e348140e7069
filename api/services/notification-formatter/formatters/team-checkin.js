
const {
    TEAM_ONLINE_CHECKIN_UNCHECKED_IN,
    TEAM_ONLINE_CHECKIN_CHECKED_IN,
    TEAM_CHECKIN_NOT_CHECKED_IN,
    TEAM_CHECKIN_CHECKED_IN,
    TEAM_CHECKIN_STATUS_PENDING,
    TEAM_ONLINE_CHECKIN_SCANNED,
    TEAM_CHECKIN_STATUS_ALERT,
} = require('../../../constants/notification-actions');

module.exports = {
    [TEAM_ONLINE_CHECKIN_UNCHECKED_IN]:
        (n) => ({
            title: `Team '${n.team_name}' is Unchecked In by ${n.first} ${n.last}, and team roster is unlocked`,
        }),
    [TEAM_ONLINE_CHECKIN_CHECKED_IN]:
        (n) => ({
            title: `Team Roster ${n.team_name} is Checked In Online by ${n.first} ${n.last}`,
        }),
    [TEAM_CHECKIN_NOT_CHECKED_IN]:
        (n) => ({
            title: `Team ${n.team_name} not checked in`,
        }),
    [TEAM_CHECKIN_CHECKED_IN]:
        (n) => ({
            title: `Team ${n.team_name} checked in`,
        }),
    [TEAM_CHECKIN_STATUS_PENDING]:
        (n) => ({
            title: `Team ${n.team_name} got check-in status pending`,
        }),
    [TEAM_ONLINE_CHECKIN_SCANNED]:
        (n) => ({
            title: `Team ${n.team_name} online-checkin barcode scanned`,
        }),
    [TEAM_CHECKIN_STATUS_ALERT]:
        (n) => ({
            title: `Team ${n.team_name} got check-in status alert`,
        }),
}


class AdminMasterTeamService {
    constructor() {
    }

    async fixTeamsWithEmptyRank() {
        const noRankTeams = await this.getNoRankTeams()

        if(_.isEmpty(noRankTeams)) {
            return;
        }

        return Promise.allSettled(noRankTeams.map(({master_team_id, organization_code}) => {
            if(!organization_code || organization_code.length < 11) {
                loggers.errors_log.error(organization_code, 'is invalid');

                return;
            }

            let [, newRank] = organization_code.match(/^\d+|\d+\b|\d+(?=\w)/g);

            if(!newRank) {
                newRank = organization_code.substring(organization_code.length - 3, organization_code.length - 2);
            }

            return this.#updateMasterTeamRank(master_team_id, newRank);
        }))
    }

    async getNoRankTeams() {
        const query =
            `SELECT mt.master_team_id,
                    mt.organization_code,
                    mt.rank
             FROM master_team mt
             WHERE mt.rank IS NULL`;

        const {rows = []} = await Db.query(query);

        return rows;
    }

    #updateMasterTeamRank(masterTeamId, rank) {
        if(!rank) {
            return;
        }

        const query = knex('master_team')
            .update({rank})
            .where('master_team_id', masterTeamId);

        return Db.query(query);
    }
}

module.exports = new AdminMasterTeamService();

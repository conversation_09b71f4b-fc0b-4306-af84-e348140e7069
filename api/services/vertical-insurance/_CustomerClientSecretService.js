const { PAYMENT_STATUS } = require('../../constants/payments');

class CustomerClientSecretService {
    constructor(ApiService) {
        this.ApiService = ApiService;
    }

    async createForPurchase(purchaseId) {
        try {
            const purchase = await this.#getPurchase(purchaseId);

            const response = await this.ApiService.createCustomerClientSecret(
                purchase.email
            );

            return this.#customerClientSecretResponseToUIMapper(response);
        } catch (err) {
            loggers.errors_log.error(err);

            throw err;
        }
    }

    #customerClientSecretResponseToUIMapper(customClientSecretResponse) {
        return {
            clientSecret: customClientSecretResponse.secret,
            clientId: sails.config.verticalInsurance.client_id,
        };
    }

    async #getPurchase(purchaseId) {
        const query = knex('purchase as p')
            .select({
                email: 'p.email',
            })
            .where('p.purchase_id', purchaseId);

        const { rows: [purchase] } = await Db.query(query);

        if(_.isEmpty(purchase)) {
            throw new Error('Purchase not found');
        }

        if(purchase.status === PAYMENT_STATUS.PAID) {
            throw new Error(`Vertical Insurance is not allowed for ${purchase.status} payment. Payment must be ${PAYMENT_STATUS.PAID}`);
        }

        if(!purchase.email) {
            throw new Error('Vertical Insurance is not allowed for purchase without email');

        }

        return purchase;
    }
}

module.exports = CustomerClientSecretService;

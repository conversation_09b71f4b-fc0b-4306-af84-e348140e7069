const {
    PAYMENT_METHOD,
    CAPTURE_METHOD
} = require('../../constants/stripe/payment-intent');

const { QUOTE } = require('../../constants/vertical-insurance');


class TeamRegistrationQuotePaymentService {
    constructor(ApiService, UtilsService) {
        this.ApiService = ApiService;
        this.UtilsService = UtilsService;
    }

    #VERTICAL_INSURANCE_CONNECTED_ACCOUNT_ID = sails.config.verticalInsurance.stripe_account_id;
    #DEFAULT_CURRENCY = 'usd';

    async payQuote(quoteData, customer, stripePaymentIntentData) {
        try {
            if(_.isEmpty(stripePaymentIntentData)) {
                throw new Error('Stripe Payment Intent Data is empty');
            }

            const {
                quote_id: quoteID,
                total_amount: quoteAmount,
            } = quoteData || {};

            if(!quoteID) {
                throw new Error('Quote ID required');
            }

            if(!quoteAmount) {
                throw new Error('Quote amount required');
            }

            const paymentMethodID = this.#getPaymentMethod(stripePaymentIntentData);
            const customerID = this.#getStripeCustomer(stripePaymentIntentData);
            const customerData = this.#customerDataMapper(customer);

            const {
                id: copiedPaymentMethodID,
                type: copiedPaymentMethodType,
            } = await this.#copyPaymentMethod(customerID, paymentMethodID);

            const mandateData = await this.#getPaymentMandateData(stripePaymentIntentData);

            const paymentIntentID = await this.#createPaymentIntent(
                copiedPaymentMethodID,
                copiedPaymentMethodType,
                mandateData,
                quoteID,
                quoteAmount
            );

            const metadata = {
                parent_payment_intent_id: stripePaymentIntentData.id,
            }

            await this.ApiService.payQuote(
                quoteID,
                paymentIntentID,
                metadata,
                customerData,
                QUOTE.PRODUCT_TYPE.TEAM_REGISTRATION
            );
        } catch (err) {
            loggers.errors_log.error(err);
        }
    }

    #customerDataMapper(customerData) {
        return {
            email_address: customerData.email,
            first_name: customerData.first,
            last_name: customerData.last,
            phone_number: customerData.phone,
        };
    }

    #getPaymentMethod(stripePaymentIntentData) {
        return stripePaymentIntentData?.payment_method;
    }

    #getStripeCustomer(stripePaymentIntentData) {
        return stripePaymentIntentData?.customer;
    }

    async #copyPaymentMethod(customerID, paymentMethodID) {
        const newPaymentMethod = await StripeService.paymentCard.copyPaymentMethodToConnectedAccount(
            customerID,
            paymentMethodID,
            this.#VERTICAL_INSURANCE_CONNECTED_ACCOUNT_ID
        );

        return {
            id: newPaymentMethod?.id,
            type: newPaymentMethod?.type,
        };
    }

    async #createPaymentIntent(paymentMethodID, paymentMethodType, mandateData, quoteID, quoteAmount) {
        let paymentIntentParams = {
            amount: this.UtilsService.normalizeNumber(quoteAmount * 100),
            currency: this.#DEFAULT_CURRENCY,
            confirm: true,
            payment_method_types: [paymentMethodType],
            payment_method: paymentMethodID,
            capture_method: CAPTURE_METHOD.AUTOMATIC,
            metadata: {
                quote_id: quoteID,
            }
        };

        if(paymentMethodType === PAYMENT_METHOD.ACH) {
            paymentIntentParams.mandate_data = mandateData;
        }

        const paymentIntentOptions = {
            stripeAccount: this.#VERTICAL_INSURANCE_CONNECTED_ACCOUNT_ID,
        };

        const paymentIntent = await StripeService.paymentCard.stripeService.createPaymentIntent(
            paymentIntentParams,
            paymentIntentOptions
        );

        return paymentIntent?.id;
    }

    async #getPaymentMandateData(paymentIntent) {
        let [charge] = paymentIntent?.charges?.data || [];

        const paymentMethodDetails = charge?.payment_method_details?.[PAYMENT_METHOD.ACH];

        if(!paymentMethodDetails) return;

        const stripeMandateID = paymentMethodDetails?.mandate;

        if(stripeMandateID) {
            const mandateData = await StripeService.paymentCard.stripeService.getMandateData(stripeMandateID);

            if(!_.isEmpty(mandateData)) {
                return { customer_acceptance: mandateData?.customer_acceptance };
            }
        }
    }
}

module.exports = TeamRegistrationQuotePaymentService;



class PaymentStatisticService {
    constructor(swUtils) {
        this.swUtils = swUtils;
    }

    getEventFees (eventID) {
        return Db.query(
            `SELECT
				 	COALESCE(e."exhibitors_sw_fee", 0)::NUMERIC "exhibitors_sw_fee",
				 	COALESCE(e."stripe_exhibitors_percent", 0)::NUMERIC "stripe_exhibitors_percent"
				 FROM "event" e 
				 WHERE e."event_id" = $1`,
            [eventID]
        ).then(result => result.rows[0] || {})
            .then(data => {
                Object.keys(data).forEach(key => {
                    data[key] = parseFloat(data[key]);
                });

                if (data.stripe_exhibitors_percent === 0) {
                    data.stripe_exhibitors_percent
                        = swUtils.normalizeNumber(StripeService.DEFAULT_STRIPE_PERCENT * 100);
                }

                return data;
            })
    }

    getPaidBoothsQty (eventID, paymentType = null) {
        let query = knex('purchase AS p')
            .select({
                qty: knex.raw(`COALESCE(SUM(pb.quantity), 0) :: INT`)
            })
            .join('purchase_booth AS pb', 'pb.purchase_id', 'p.purchase_id')
            .whereRaw(`p.status = 'paid'`)
            .whereRaw(`p.payment_for = 'booths'`)
            .where('p.event_id', eventID);

        if(paymentType) {
            query.where('p.type', paymentType);
        }

        return Db.query(query).then(result => result && result.rows[0] && result.rows[0].qty || 0);
    }

    getPaidBoothsAmount (eventID, paymentType) {
        let query = knex('purchase AS p')
            .select({
                amount: knex.raw(`COALESCE(SUM(pb.amount), 0) :: INT`)
            })
            .join('purchase_booth AS pb', 'pb.purchase_id', 'p.purchase_id')
            .whereRaw(`p."status" = 'paid'`)
            .where('p.event_id', eventID)
            .where('p.type', paymentType)
            .whereRaw(`p.payment_for = 'booths'`);

        return Db.query(query).then(result => result && result.rows[0] && result.rows[0].amount || 0);
    }

    getCollectedSWFee (eventID) {
         let query = knex('purchase AS p')
             .select({
                 sw_fee: knex.raw(`
                    SUM(COALESCE(p."collected_sw_fee", 0) + COALESCE(p."additional_fee_amount", 0))::NUMERIC
                 `)
             })
             .where('p.event_id', eventID)
             .whereRaw(`p.payment_for = 'booths'`)
             .whereRaw(`p.status <> 'canceled'`)
             .whereRaw(`p.type NOT IN('check', 'waitlist')`)
             .whereRaw(`p."stripe_payment_type" = 'connect'`);

         return Db.query(query).then(result => result && result.rows[0] && result.rows[0].sw_fee || 0);
    }
}

module.exports = PaymentStatisticService;

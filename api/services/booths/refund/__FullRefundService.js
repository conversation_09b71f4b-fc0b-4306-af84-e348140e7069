const StripeConnect = require('../../../lib/StripeConnect');

class FullRefundService {
    constructor (paymentUtils) {
        /**
         * @type {PaymentCommonService}
         */
        this.paymentUtils = paymentUtils;
    }

    async process(purchaseID) {
        if(!purchaseID) {
            throw { validation: 'Invalid payment identifier passed' };
        }

        try {
            let paymentData = await this.__getPurchaseData(purchaseID);

            if(_.isEmpty(paymentData)) {
                throw { validation: 'Payment not found' }
            }

            if (paymentData.type !== this.paymentUtils.CARD_PAYMENT_TYPE) {
                throw { validation: `Unable to refund "${paymentData.type}" payment` }
            }

            await this.__processStripeRefund(paymentData);

            await this.__updateMetadata(paymentData);

            return this.__updatePurchaseRow(purchaseID);
        } catch (err) {
            throw err;
        }
    }

    __getPurchaseData (purchaseID) {
        return Db.query(
            `SELECT 
                p.stripe_charge_id "charge_id", (
                     CASE
                         WHEN e."teams_use_connect" IS TRUE 
                             THEN NULL
                         ELSE sa."secret_key"
                     END
                ) "private_key",
                e."teams_use_connect" "use_connect",
                sa."stripe_account_id",
                sc.stripe_payment_id,
                p."type"
             FROM "purchase" p
             INNER JOIN "event" e
                ON e.event_id = p.event_id
             INNER JOIN "stripe_account" sa 
                ON sa."secret_key" = e."stripe_exhibitors_private_key"
             LEFT JOIN "stripe_charge" sc
                ON sc."stripe_charge_id" = p."stripe_charge_id"   
             WHERE p."purchase_id" = $1
                AND p."payment_for" = 'booths'`,
            [purchaseID]
        ).then(result => result.rows && result.rows[0] || null);
    }

    __processStripeRefund(payment) {
        if (payment.use_connect) {
            return StripeConnect.refundClientPayment(payment, null, true);
        } else {
            return StripeService.refund(payment);
        }
    }

    __updatePurchaseRow(purchaseID) {
        return Db.query(
            `UPDATE "purchase" 
                 SET "date_refunded"    = NOW(),
                     "amount_refunded"  = COALESCE("amount_refunded", 0) + "amount",
                     "status"           = 'canceled'
                 WHERE "purchase_id" = $1
                 RETURNING "date_refunded"`,
            [purchaseID]
        ).then(result => (result.rows[0] && result.rows[0].date_refunded))
    }

    __updateMetadata(payment) {
        return StripeService.updateChargeMetadataAfterRefund(payment, {
            stripeFee: 0,
            swFee: 0,
            extraFee: 0,
            netProfit: 0
        })
    }
}

module.exports = FullRefundService;

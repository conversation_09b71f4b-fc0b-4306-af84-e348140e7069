'use strict';

const config = sails.config.applePass;
const fetch = require('node-fetch');
const moment = require('moment');
const QRTicketsUtils = require('../lib/QRTicketsGenerator');

class ApplePassService {
    constructor() {}

    get EVENT_TICKET_TEMPLATE_TYPE() {
        return 'eventTicket';
    }

    get DESCRIPTION() {
        return 'SportWrench Event Ticket';
    }

    get DEFAULT_PASS_NAME() {
        return 'Pass';
    }

    __getQRContent(paymentData) {
        return QRTicketsUtils.generateSWTContent(
            paymentData,
            paymentData.qrcode_version || undefined,
            paymentData.assigned_tickets_mode
        );
    }
    __formatPassData(paymentData) {
        const purchaseRefunded =
            paymentData.status === 'canceled'

        const payload = {
            data: {
                pass: {
                    description: this.DESCRIPTION,
                    barcodes: [
                        {
                            message: this.__getQRContent(paymentData),
                            format: config.BARCODE_CONFIG.FORMAT,
                            messageEncoding:
                                config.BARCODE_CONFIG.MESSAGE_ENCODING,
                        },
                    ],
                    voided: paymentData.deactivated || purchaseRefunded,
                    passFields: {
                        primaryFields: [
                            {
                                key: 'name',
                                label: 'EVENT',
                                value: paymentData.event_name,
                            },
                        ],
                        headerFields: [
                            {
                                key: 'start',
                                label: 'Start',
                                value: String(
                                    moment(paymentData.date_start).format(
                                        'MM/DD'
                                    )
                                ),
                            },
                        ],
                        secondaryFields: [
                            {
                                key: 'name',
                                label: `${this.DEFAULT_PASS_NAME} Holder`,
                                value: paymentData.ticket_holder,
                            },
                            {
                                key: 'ticket_label',
                                label: `${this.DEFAULT_PASS_NAME} Label`,
                                value: this.__getTicketLabels(paymentData),
                            },
                        ],
                        auxiliaryFields: [
                            {
                                key: 'Status',
                                label: `Ticket Status`,
                                value:
                                    paymentData.deactivated || purchaseRefunded
                                        ? 'Canceled'
                                        : 'Active',
                            },
                        ],
                    },
                },
                template: {
                    passStyle: this.EVENT_TICKET_TEMPLATE_TYPE,
                },
            },
        };

        if (paymentData.assigned_tickets_mode) {
            payload.data.pass.passFields.auxiliaryFields.push({
                key: 'ticket_type',
                label: 'Ticket Type',
                value: this.__getTicketTypes(paymentData),
            });
            payload.data.pass.passFields.auxiliaryFields.push({
                key: 'ticket_dates',
                label: 'Ticket Dates',
                value: this.__getTicketDates(paymentData),
            });
        }

        return { serialNumber: String(paymentData.ticket_barcode), payload };
    }

    getApplePassForTicket(paymentData) {
        const { payload, serialNumber } = this.__formatPassData(paymentData);
        return this.__generateApplePass({ serialNumber, ...payload });
    }

    updateApplePassForTicket(paymentData) {
        const { payload, serialNumber } = this.__formatPassData(paymentData);
        return this.__updateApplePass(serialNumber, payload);
    }

    __getDefaultHeaderOptions() {
        const headers = new fetch.Headers();
        headers.set('Accept', 'application/json');
        headers.set('Content-Type', 'application/json');
        headers.set(
            'Authorization',
            `${config.API_KEY_PREFIX} ${config.API_KEY}`
        );

        return headers;
    }

    async __updateApplePass(serialNumber, payload) {
        const options = {
            headers: this.__getDefaultHeaderOptions(),
            body: JSON.stringify(payload),
            method: 'PUT',
        };

        const response = await fetch(
            config.API_URL + '/pass/' + serialNumber,
            options
        );

        if (!response.ok) {
            loggers.errors_log.error(`Error updating apple pass`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: options.body,
            });
            throw Error('Error updating apple pass');
        }
    }

    async __generateApplePass(payload) {
        const options = {
            headers: this.__getDefaultHeaderOptions(),
            body: JSON.stringify(payload),
            method: 'POST',
        };

        const response = await fetch(config.API_URL + '/pass', options);

        if (!response.ok) {
            loggers.errors_log.error(`Error generating apple pass`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: options.body,
            });
            throw Error('Error creating apple pass');
        }

        return response.buffer();
    }

    __getTicketLabels(data = {}) {
        let ticketLabel = '';

        if (!data.assigned_tickets_mode) {
            data.purchase_tickets.forEach((ticket, id) => {
                let isLastTicket = id === data.purchase_tickets.length - 1;

                ticketLabel +=
                    `${ticket.quantity} ${ticket.short_label}` +
                    (isLastTicket ? '' : ', ');
            });
        } else {
            ticketLabel = data.purchase_tickets[0].ticket_label;
        }

        return ticketLabel;
    }

    __getTicketTypes(data = {}) {
        return _.capitalize(data.purchase_tickets[0].ticket_type);
    }

    __getTicketDates(data = {}) {
        let validDateLabel;
        const ticket = data.purchase_tickets[0];

        const { valid_dates } = ticket;

        if (!_.isEmpty(valid_dates)) {
            validDateLabel = valid_dates.map(date=>moment(date))
                .reduce(
                    (prev, curr) =>
                        (prev ? prev + ', ' : '') + curr.format('MM/DD'),
                    ''
                );
        } else {
            validDateLabel =
                moment(data.date_start).format('MM/DD') +
                ' - ' +
                moment(data.date_end).format('MM/DD');
        }
        return validDateLabel;
    }
}

module.exports = new ApplePassService();

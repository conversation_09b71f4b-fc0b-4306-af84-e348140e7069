const co                = require('co');
const GoogleMapsUtils   = require('../lib/GoogleMapsUtils');

// Details: https://www.npmjs.com/package/geopoint
const geoPoint = require('geopoint');

class ClubServiceGeo {
    constructor() {
        this.MASTER_CLUB_TABLE  = 'master_club';
        this.EVENT_TABLE        = 'event';
        this.TABLES = {
            [this.MASTER_CLUB_TABLE]: {
                name: 'master_club',
                pk: 'master_club_id',
            },
            [this.EVENT_TABLE]: {
                name: 'event',
                pk: 'event_id',
            },
        };
    }

    async calculateDistanceToEvent(tr, rosterClubId, masterClubId, eventId) {
        let eventCoordinates = {};
        let clubCoordinates = {};

        const eventResult = await this.getCoordinatesFromDB(tr, eventId, this.EVENT_TABLE);
        const clubResult = await this.getCoordinatesFromDB(tr, masterClubId, this.MASTER_CLUB_TABLE);

        const {coordinates: eventCoordinatesFromDB} = _.first(eventResult.rows);
        const {coordinates: clubCoordinatesFromDB} = _.first(clubResult.rows);

        if (_.isEmpty(eventCoordinatesFromDB)) {
            eventCoordinates = await this.generateCoordinates(tr, eventId, this.EVENT_TABLE);

            await this.saveCoordinates(eventCoordinates, eventId, this.EVENT_TABLE);
        }

        if (_.isEmpty(clubCoordinatesFromDB)) {
            clubCoordinates = await this.generateCoordinates(tr, masterClubId, this.MASTER_CLUB_TABLE);

            await this.saveCoordinates(clubCoordinates, masterClubId, this.MASTER_CLUB_TABLE);
        }

        const _eventCoordinates = Object.assign(eventCoordinatesFromDB, eventCoordinates);
        const _clubCoordinates  = Object.assign(clubCoordinatesFromDB, clubCoordinates);

        const _distance = this.generateDistance(_eventCoordinates, _clubCoordinates);

        await this.saveDistanceToRoster(tr, _distance, rosterClubId);
    }

    getCoordinatesFromDB(tr, id, table) {
        const _table = this.TABLES[table];

        return tr.query(`
            SELECT coordinates FROM ${_table.name}
            WHERE ${_table.pk} = $1
        `,[id]);
    }

    getAddress(tr, id, table) {
        const _table = this.TABLES[table];

        return tr.query(`
            SELECT concat_ws(' ', address, city, state, zip) AS address FROM ${_table.name}
            WHERE ${_table.pk} = $1 
        `, [id]);
    }

    saveCoordinates(coordinates, id, table) {
        const _table = this.TABLES[table];

        return Db.query(`
            UPDATE ${_table.name} SET coordinates = $1
            WHERE ${_table.pk} = $2
        `, [coordinates, id]);
    }

    saveDistanceToRoster(tr, distance, rosterClubId) {
        return tr.query(`
            UPDATE roster_club SET distance_to_event = $1
            WHERE roster_club_id = $2
        `, [distance, rosterClubId]);
    }

    async generateCoordinates(tr, id, table) {
        const result    = await this.getAddress(tr, id, table);
        const { address }  = _.first(result.rows);

        return await GoogleMapsUtils.findLocation('', address);
    }

    generateDistance(eventCoordinates, clubCoordinates) {
        const { lat: eventLat, lng: eventLng } = eventCoordinates;
        const { lat: clubLat, lng: clubLng } = clubCoordinates;

        const eventPoint = new geoPoint(eventLat, eventLng);
        const clubPoint = new geoPoint(clubLat, clubLng);

        return Math.round(eventPoint.distanceTo(clubPoint, false));
    }
}


module.exports = ClubServiceGeo;

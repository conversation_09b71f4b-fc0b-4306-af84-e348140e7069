const path = require('path');
const fs = require('fs');
const argv = require('optimist').argv;
const spawn = require('child_process').spawn;


class ExportService {
    constructor() {}

    export(dateStart, dateEnd, paymentFor, eventID) {
        let fileName = `Stats_export_${new Date().getTime()}.xlsx`;

        let filePath = path.resolve(__dirname, '..', '..', '..', '..', '..', 'export', fileName);

        return new Promise((resolve, reject) => {
            let procKiller;

            let connectionStrBase64 = new Buffer(JSON.stringify(sails.config.connections[sails.config.db.connection])).toString('base64');

            let params = [
                `--connection=${connectionStrBase64}`,
                `--path=${filePath}`,
                `--start=${dateStart}`,
                `--end=${dateEnd}`,
                `--payment_for=${paymentFor}`,
            ];

            if(eventID) {
                params.push(`--event=${eventID}`);
            }

            let xslxProc = spawn('node', [
                'admin-stripe-statistics-export.js',
                ...params
            ], {
                detached: true,
                cwd     : path.resolve(__dirname, '..', '..', '..', '..', 'sw-utils'),
                stdio   : 'pipe',
            });

            xslxProc.on('error', err => {
                if (procKiller) {
                    clearTimeout(procKiller);
                }
                reject(err);
            });

            xslxProc.stderr.on('data', function (error) {
                reject({ message: error.toString()});
            });

            xslxProc.on('close', code => {
                if (procKiller) {
                    clearTimeout(procKiller);
                }
                if (code !== 0) {
                    reject();
                } else {
                    resolve(filePath);
                }
            });

            xslxProc.unref();

            procKiller = setTimeout(() => {
                xslxProc.kill();
            }, 100 * 1000);
        })
    }
}

module.exports = new ExportService();

'use strict';

const TicketsService = require('./financial-report/__TicketsService');
const TeamsService = require('./financial-report/__TeamsService');
const ExportService = require('./financial-report/__ExportService');
const FundsTransferService = require('../FundsTransferService');
const SeasonalDataService = require('./financial-report/__SeasonalDataService');
const StatisticsLambdaFunctionService = require('./financial-report/__StatisticsLambdaFunctionService');

class FinancialReportService {

    get BASIC_TICKETS_PAYMENT_FOR () {
        return 'basic_tickets';
    }

    get TYPES_SERVICE () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: TicketsService,
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: TeamsService,
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: {}
        }
    }

    get ALLOWED_PAYMENT_FOR_TYPES () {
        return this.PAYMENT_TYPES.concat([this.BASIC_TICKETS_PAYMENT_FOR]);
    }

    get DELTAS_SERVICE () {
        return {
            [FundsTransferService.TRANSFER_TYPES.TICKETS]: TicketsService.getDeltas.bind(TicketsService),
            [FundsTransferService.TRANSFER_TYPES.TEAMS]: TeamsService.getDeltas.bind(TeamsService),
            [FundsTransferService.TRANSFER_TYPES.BOOTHS]: TeamsService.getDeltas.bind(TeamsService)
        }
    }

    get PAYMENT_TYPES () {
        return Object.values(FundsTransferService.TRANSFER_TYPES);
    }

    get seasonalData () {
        return SeasonalDataService;
    }

    getReport (dateStart, dateEnd) {
        return StatisticsLambdaFunctionService.getData(dateStart, dateEnd);
    }

    async getExport (dateStart, dateEnd, paymentFor = null, eventID = null) {
        return ExportService.export(dateStart, dateEnd, paymentFor, eventID);
    }

    async createReportJob (dateStart, dateEnd, paymentFor = this.PAYMENT_TYPES) {
        try {
            if(!dateStart) {
                throw { validation: 'Date start required' };
            }

            if(!dateEnd) {
                throw { validation: 'Date end required' };
            }

            this.__validatePaymentFor(paymentFor);

            if(this.seasonalData.isSeasonDataRequest({ dateAfter: dateStart, dateBefore: dateEnd, paymentFor})) {
                let seasonalData = await AdminStatisticsService.financialReport.seasonalData.seasonResults();

                return seasonalData.statistics;
            }

            return StatisticsLambdaFunctionService.getData(dateStart, dateEnd, paymentFor);
        } catch (err) {
            throw err;
        }
    }

    async getReportJobResult (reportID) {
        if(!reportID) {
            throw { validation: 'Report ID required' };
        }

        return StatisticsLambdaFunctionService.getJobResult(reportID)
    }

    __validatePaymentFor (paymentFor) {
        if(_.isEmpty(paymentFor) || !Array.isArray(paymentFor)) {
            throw { validation: 'Payment for is empty' }
        }

        if(paymentFor.length > this.ALLOWED_PAYMENT_FOR_TYPES.length) {
            throw { validation: 'Payment for contains excess types' }
        }

        let notValidPaymentForTypes = _.filter(
            paymentFor,
            (type) => !this.ALLOWED_PAYMENT_FOR_TYPES.includes(type)
        );

        if(notValidPaymentForTypes.length) {
            throw { validation: 'Payment for contains invalid types' }
        }
    }
}

module.exports = new FinancialReportService();

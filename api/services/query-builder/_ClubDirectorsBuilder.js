const currentSeason = sails.config.sw_season.current;

class ClubDirectorsBuilder {
    constructor() {}

    get getPreviousAndCurrentSeasons() {
        const seasons = [Number(currentSeason) - 1, Number(currentSeason)];
        return `{${seasons.join(', ')}}`;
    }
    
    emailReceiversList(receiverFiltersData) {
        let emailSelect = this.#getEmailSelection(receiverFiltersData);
        let emailWhereCondition = this.#getEmailWhereCondition(receiverFiltersData);

        return knex('master_club as mc')
            .select(
                'mc.club_name AS club_name',
                'mc.director_first AS first',
                'mc.director_last AS last',
                knex.raw(`FORMAT('%s %s', mc.director_first, mc.director_last) AS name`),
                knex.raw(`${emailSelect} AS email`)
            )
            .join('club_owner AS co', (table) => {
                table
                    .on('co.master_club_id', 'mc.master_club_id')
                    .on(knex.raw('co.active IS TRUE'))
            })
            .where(knex.raw(emailWhereCondition))
            .whereExists(
                knex('event AS e')
                    .select(1)
                    .join('roster_club AS rc', (table) => {
                        table
                            .on('rc.event_id', 'e.event_id')
                            .on('rc.master_club_id', 'mc.master_club_id')
                            .on(knex.raw(`
                                EXISTS (
                                    SELECT 1
                                    FROM "roster_team" rt
                                    WHERE rt.roster_club_id = rc.roster_club_id
                                )`)
                            )
                            .on(knex.raw(`e.season = ANY(?)`, this.getPreviousAndCurrentSeasons))
                    })
            )
            .orWhere(knex.raw(`mc.created >= (('${Number(currentSeason)}-01-01'::text)::timestamp without time zone)`));
    }

    #getEmailSelection (receiverFiltersData) {
        let emailSelect;

        if(receiverFiltersData.send_to_cd_email && receiverFiltersData.send_to_cd_admin_email) {
            emailSelect = `
                CASE 
                    WHEN mc.director_email IS NOT NULL AND mc.administrative_email IS NOT NULL
                        THEN FORMAT('%s, %s', mc.director_email, mc.administrative_email)
                    WHEN mc.administrative_email IS NOT NULL
                        THEN mc.administrative_email
                    WHEN mc.director_email IS NOT NULL
                        THEN mc.director_email
                    ELSE null
                END             
            `
        } else if(receiverFiltersData.send_to_cd_email) {
            emailSelect = 'mc.director_email'
        } else if(receiverFiltersData.send_to_cd_admin_email) {
            emailSelect = 'mc.administrative_email'
        } else {
            throw { validation: 'Invalid filters passed' };
        }

        return emailSelect;
    }

    #getEmailWhereCondition (receiverFiltersData) {
        let emailWhere;

        if(receiverFiltersData.send_to_cd_email && !receiverFiltersData.send_to_cd_admin_email) {
            emailWhere = `NULLIF(mc.director_email, '') IS NOT NULL`;
        } else if (!receiverFiltersData.send_to_cd_email && receiverFiltersData.send_to_cd_admin_email) {
            emailWhere = `NULLIF(mc.administrative_email, '') IS NOT NULL`;
        } else {
            emailWhere = `NULLIF(mc.director_email, '') IS NOT NULL OR NULLIF(mc.administrative_email, '') IS NOT NULL`;
        }

        return emailWhere;
    }
}

module.exports = new ClubDirectorsBuilder();

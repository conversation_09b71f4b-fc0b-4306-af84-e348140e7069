'use strict';

class TeamsPaymentQueryBuilder {
    constructor() {}

    get TEST_RECEIVER() {
        return SQLQueryBuilderUtils.markTestData({
            event_name              : 'Test Event Name',
            club_name               : 'Test Club Name',
            team_names              : 'Team One, Team Two',
            payment_type            : 'ach',
            payment_status          : 'canceled',  
            payment_amount          : 999,
            payment_created_date    : '2016-10-08 00:58:13.687784',
            payment_date_paid       : '2016-10-09 00:58:13.687784',
            payment_canceled_date   : '2016-10-14 00:58:13.687784',
            eo_first                : '<PERSON>',
            eo_last                 : 'Doe',
            eo_email                : '<EMAIL>',
            eo_phone                : '9876543210',
            cd_first                : 'Jane',
            cd_last                 : 'Doe',
            cd_email                : '<EMAIL>',
            cd_phone                : '0123456789',
            failure_code            : 'Insufficient Funds',
            failure_message         : 500,
        })
    }

    /**
     * For system notifications, e.g payment failure etc.
     */

    notificationReceiversList (eventID, sendersData) {
        let {
            purchaseId,
            failure_code,
            failure_message
        } = (sendersData || {});

        if (!eventID) {
            throw new Error('No Event ID');
        }

        if (!purchaseId) {
            throw new Error('No Purchase ID!');
        }

        let query = knex('v_aem_teams_payments_data AS v')
            .select('v.*', {
                first: 'cd_first',
                last: 'cd_last',
                email: knex.raw(`
                    concat_ws(
                        '${EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING}',
                         cd_email, 
                         administrative_club_email
                    )
                `),
                failure_code: knex.raw('?', failure_code ? failure_code : 'Unknown'),
                failure_message: knex.raw('?', failure_message ? failure_message : 'Unknown')
            })
            .where({
                event_id: eventID,
                purchase_id: purchaseId
            });

        return query;
    }

    getTestReceiver (eventID) {
		if (!eventID) {
            return this.TEST_RECEIVER;
        }

        const query = squel.select()
            .from('event', 'e')
            .field('e.long_name', 'event_name')
            .field('rc.club_name')
            .field(squel.str(`ARRAY_TO_STRING(ARRAY(?), ', ')`,
                squel.select()
                    .from('roster_team')
                    .field(`CONCAT(team_name)`)
                    .where('roster_club_id = rc.roster_club_id')
                    .where('deleted IS NULL')
            ), 'team_names')
            .field('p.type', 'payment_type')
            .field('p.status', 'payment_status')
            .field('p.amount', 'payment_amount')
            .field('p.created', 'payment_created_date')
            .field('p.date_paid', 'payment_date_paid')
            .field('p.canceled_date', 'payment_canceled_date')
            .field('u.first', 'eo_first')
            .field('u.last', 'eo_last')
            .field('u.email', 'eo_email')
            .field('u.phone_mob', 'eo_phone')
            .field('mc.director_first', 'cd_first')
            .field('mc.director_last', 'cd_last')
            .field('mc.director_email', 'cd_email')
            .field('mc.director_phone', 'cd_phone')
            .field(`'500'`, 'failure_code')
            .field(`'Insufficient Funds'`, 'failure_message')
            .left_join('roster_club', 'rc',
                squel.expr()
                    .and('rc.roster_club_id = ?',
                        squel.select()
                            .from('roster_club')
                            .field('roster_club_id')
                            .where('event_id = ?', eventID)
                            .order('created', false)
                            .limit(1)
                    )
            )
            .left_join('purchase', 'p', 
                squel.expr()
                    .and('p.purchase_id = ?',
                        squel.select()
                            .from('purchase')
                            .field('purchase_id')
                            .where('event_id = e.event_id')
                            .limit(1)
                    )
            )
            .left_join('event_owner', 'eo', 'eo.event_owner_id = e.event_owner_id')
            .left_join('user', 'u', 'u.user_id = eo.user_id')
            .left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .where('e.event_id = ?', eventID);

        return Db.query(query).then(({ rows: [receiver] }) => {
            if (!receiver) {
                return this.TEST_RECEIVER;
            }

            return SQLQueryBuilderUtils.replaceReceiverEmptyValuesWithTestData(receiver, this.TEST_RECEIVER);
        })
	}

}

module.exports = new TeamsPaymentQueryBuilder();

'use strict';

class HeadRefereeBuilder {
    constructor() {}

    get TEST_RECEIVER() {
        return SQLQueryBuilderUtils.markTestData({
            event_name          : 'Test Event Name',
            head_referee_name   : '<PERSON>',
            head_referee_phone  : '0000000000',
        })
    }

    notificationReceiversList(eventID, sendersData) {
        let { event_official_id: id, isWithdrewNotification } = (sendersData || {});

    if (!eventID) {
        throw new Error('No Event ID');
    }

    if (!id) {
        throw new Error('No Sender ID!');
    }
    // add query with columns matching group's variables
    /*
        [
            {"field": "event_name", "title": "Event Name", "pattern": "{event_name}"},
            {"field": "event_city", "title": "Event City", "pattern": "{event_city}"},
            {"field": "event_website", "title": "Event Website", "pattern": "{event_website}"},
            {"field": "event_email", "title": "Event Email", "pattern": "{event_email}"},
            {"field": "event_month", "title": "Event Month", "pattern": "{event_month}"},
            {"field": "official_first", "title": "Official First Name", "pattern": "{official_first}"},
            {"field": "official_last", "title": "Official Last Name", "pattern": "{official_last}"},
            {"field": "official_name", "title": "Official First and Last Name", "pattern": "{official_name}"},
            {"field": "official_email", "title": "Official Email", "pattern": "{official_email}"}
        ]
    */

    let eventOfficialExpression = squel.expr()
        .and('eof."event_id" = e."event_id"')
        .and('eof."event_official_id" = ?', id);

    if(isWithdrewNotification) {
        eventOfficialExpression
            .and('eof."deleted" IS NOT NULL')
            .and('eof.is_official IS NOT TRUE')
    } else {
        eventOfficialExpression
            .and('eof."deleted" IS NULL')
            .and('eof.is_official IS TRUE')
    }

    let query = squel.select()

        .field('e.long_name', 'event_name')
        .field('e.city', 'event_city')
        .field('e.website', 'event_website')
        .field('e.email', 'event_email')
        .field(`TO_CHAR(TO_TIMESTAMP(EXTRACT(MONTH FROM e.date_start)::TEXT, 'MM'), 'Month')`,
            'event_month')
        .field('u."first"', 'official_first')
        .field('u."last"', 'official_last')
        .field('u."email"', 'official_email')
        .field(`(u."first" || ' ' || u."last")`, 'official_name')

        // receiver data: Head Referees
        .field('v."first"', 'first')
        .field('v."last"', 'last')
        .field('v."email"', 'email')
        .field(`CONCAT(v.first, ' ', v.last)`, 'head_referee_name')
        .field('v.phone_mob', 'head_referee_phone')

        .from('event', 'e')
        .join('event_official', 'eof', eventOfficialExpression)
        .join('official', 'of', 'eof."official_id" = of."official_id"')
        .join('user', 'u', 'u."user_id" = of."user_id"')
        .join('v_head_referee_notification_receiver_data', 'v', 'v."event_id" = e."event_id"')
        .where('e."event_id" = ?', eventID)

    return query;
}

    getTestReceiver (eventID) {
		if (!eventID) {
            return this.TEST_RECEIVER;
        }

        const query = squel.select()
            .from('event', 'e')
            .field('e.long_name', 'event_name')
            .field(`FORMAT('%s %s', v.first, v.last)`, 'head_referee_name')
            .field('v.phone_mob', 'head_referee_phone')
            .left_join('v_head_referee_notification_receiver_data', 'v', 'v."event_id" = e."event_id"')
            .where('e.event_id = ?', eventID);

        return Db.query(query).then(({ rows: [receiver] }) => {
            if (!receiver) {
                return this.TEST_RECEIVER;
            }

            return SQLQueryBuilderUtils.replaceReceiverEmptyValuesWithTestData(receiver, this.TEST_RECEIVER);
        })
	}
}

module.exports = new HeadRefereeBuilder();

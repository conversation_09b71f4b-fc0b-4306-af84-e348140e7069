const {PAYMENT_STATUSES, ENTRY_STATUSES} = require('../../constants/teams');
const {DATETIME_FORMAT} = require('../../constants/db');

class TeamService {
    constructor(utilsService) {
        this.utilsService = utilsService;
    }

    async getTeams(eventId, eventSettings, timestamp, teamId) {
        const {housing_teams_access_level, skip_waitlist_teams} = eventSettings;

        let query = `
            SELECT 
                t.roster_team_id, t.roster_club_id, t.team_name, d.name division_name, t.organization_code, 
                t.deleted, t.ths_tentative_nights, t.ths_confirmed_nights, 
                TO_CHAR(t.modified, '${DATETIME_FORMAT}') modified, t.status_paid, t.status_entry,
                tst."status_value" "acceptance",
                (
                    SELECT TRUE
                    FROM purchase_team AS pt
                        JOIN purchase p 
                            ON p.purchase_id = pt.purchase_id 
                                AND p.type = 'ach' 
                                AND p.status = 'pending'
                    WHERE pt.roster_team_id = t.roster_team_id
                        AND pt.event_id = t.event_id
                        AND pt.canceled IS NULL
                )::BOOLEAN IS TRUE has_pending_ach_payment,
                t."ths_trav_coord_name", 
                t."ths_trav_coord_email",
                t."ths_trav_coord_phone" 
            FROM roster_team t 
            INNER JOIN "team_status" tst 
                ON tst."team_status_id" = t."status_entry"
            LEFT JOIN division d 
                ON d.division_id = t.division_id 
            WHERE t.event_id = $1`

        if (housing_teams_access_level === 'paid') {
            query = query + `     AND t.status_paid = ${PAYMENT_STATUSES.PAID} `;
        }
        if (housing_teams_access_level === 'accepted') {
            query = query + `     AND t.status_entry = ${ENTRY_STATUSES.ACCEPTED} `;
        }

        const params = [eventId];

        if (timestamp) {
            params.push(this.utilsService.utcToSql(timestamp));

            query = query + ' AND t.modified > $' + params.length;
        }

        if (teamId) {
            params.push(teamId);

            query = query + ' AND t.roster_team_id = $' + params.length;
        }

        const {rows: teams} = await Db.query(query, params);

        return teams.map(team => ({
            team: [
                { id                : team.roster_team_id || ''                             },
                { clubid            : team.roster_club_id || ''                             },
                { name              : team.team_name || ''                                  },
                { division          : team.division_name || ''                              },
                { teamCode          : team.organization_code || ''                          },
                { allowBooking      :
                        this.utilsService.isBookingAllowed(housing_teams_access_level, skip_waitlist_teams, team)   },
                { tentativeNights   : team.ths_tentative_nights || ''                       },
                { confirmedNights   : team.ths_confirmed_nights || ''                       },
                { modified          : team.modified || ''                                   },
                { acceptance        : team.acceptance || ''                                 },
                { travCoordName     : team.ths_trav_coord_name || ''                        },
                { travCoordEmail    : team.ths_trav_coord_email || ''                       },
                { travCoordPhone    : team.ths_trav_coord_phone || ''                       }
            ]
        }));
    }

    async getTeamData(teamId, thsId) {
        const query  =
            `SELECT
                rt.roster_club_id club_id,
                rc.is_local,
                tb.roster_team_id,
                rc2.roster_club_id old_club_id,
                rc2.is_local old_is_local
            FROM roster_team rt
            LEFT JOIN roster_club rc ON rc.roster_club_id = rt.roster_club_id
            LEFT JOIN ths_booking tb ON tb.ths_id = $2
            LEFT JOIN roster_team rt2 ON rt2.roster_team_id = tb.roster_team_id
            LEFT JOIN roster_club rc2 ON rc2.roster_club_id = rt2.roster_club_id
            WHERE rt.roster_team_id = $1`;

        const {rows: [teamData] = []} = await Db.query(query, [teamId, thsId]);

        if (!teamData) {
            throw {
                error: `Team not found for team_id: ${teamId}`,
                teamId
            }
        }

        if (teamData.roster_team_id && teamData.roster_team_id !== teamId) {
            // Reservation moved from one team to another
            loggers.debug_log.verbose(`
                THS API WARNING: Team has been changed from ${teamData.roster_team_id} to ${teamId} for reservation ID: ${thsId}
            `);
        }

        return teamData
    }

    setContactsOfTravelingCoordinator(tr, thsTravelingCoordinator, event_id, team_id) {
        const ths_trav_coord_name = this.utilsService.parseXMLValue(thsTravelingCoordinator.travcoordname),
            ths_trav_coord_email = this.utilsService.parseXMLValue(thsTravelingCoordinator.travcoordemail),
            ths_trav_coord_phone = this.utilsService.parseXMLValue(thsTravelingCoordinator.travcoordphone);

        if (ths_trav_coord_name || ths_trav_coord_email || ths_trav_coord_phone) {
            return tr.query(
                `UPDATE "roster_team"
                SET "ths_trav_coord_name" = $3,
                    "ths_trav_coord_email" = $4,
                    "ths_trav_coord_phone" = $5
                WHERE "roster_team_id" = $1 
                    AND "event_id" = $2`,
                [
                    team_id,
                    event_id,
                    ths_trav_coord_name || null,
                    ths_trav_coord_email || null,
                    ths_trav_coord_phone || null
                ]
            );
        }
    }
}

module.exports = TeamService;

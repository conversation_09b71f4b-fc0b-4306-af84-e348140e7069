const {DATETIME_FORMAT} = require('../../constants/db');
const {reservationInfoSchema} = require('../../validation-schemas/booking');

class BookingService {
    constructor(utilsService) {
        this.utilsService = utilsService;
    }

    async getReservations(eventId, timestamp, reservationId) {
        let query =
            `SELECT
                b.event_id, b.roster_team_id,
                b.ths_id, b.ths_tentative_nights, b.ths_confirmed_nights,
                b.ths_hotel_name, b.ths_hotel_status, b.ths_loyalty,
                to_char(b.ths_contract_issued, '${DATETIME_FORMAT}') contract_issued,
                to_char(b.ths_when_accepted, '${DATETIME_FORMAT}') when_accepted,
                to_char(b.ths_when_canceled, '${DATETIME_FORMAT}') when_canceled,
                to_char(b.modified, '${DATETIME_FORMAT}') modified
            FROM ths_booking b
            WHERE 1=1`;

        const params = [];

        if (eventId > 0) {
            params.push(eventId);

            query = query + ' AND b.event_id = $'+params.length;
        }

        if (timestamp) {
            params.push(this.utilsService.utcToSql(timestamp));

            query = query + ' AND b.modified > $' + params.length;
        }

        if (reservationId) {
            params.push(reservationId);

            query = query + ' AND b.ths_id = $' + params.length;
        }

        const {rows: reservations} = await Db.query(query, params);

        return reservations.map((reservation) => {
            return {
                'reservation': [
                    {'id': reservation.ths_id || ''},
                    {'eventid': reservation.event_id || ''},
                    {'teamId': reservation.roster_team_id || ''},
                    {'hotelname': reservation.ths_hotel_name || ''},
                    {'status': reservation.ths_hotel_status || ''},
                    {'loyalty': reservation.ths_loyalty || ''},
                    {'tentativenights': reservation.ths_tentative_nights || ''},
                    {'confirmednights': reservation.ths_confirmed_nights || ''},
                    {'contractissued': reservation.contract_issued || ''},
                    {'whenaccepted': reservation.when_accepted || ''},
                    {'whencanceled': reservation.when_canceled || ''},
                    {'modified': reservation.modified || ''}
                ]
            };
        });
    }

    prepareReservation(reservation) {
        return {
            ths_tentative_nights: Number(this.utilsService.parseXMLValue(reservation.tentativenights)),
            ths_confirmed_nights: Number(this.utilsService.parseXMLValue(reservation.confirmednights)),
            ths_id: this.utilsService.parseXMLValue(reservation.thsid),
            ths_hotel_name: this.utilsService.parseXMLValue(reservation.hotelname),
            ths_hotel_status: this.utilsService.parseXMLValue(reservation.status),
            ths_loyalty: Number(this.utilsService.parseXMLValue(reservation.loyalty)),
            ths_contract_issued: this.utilsService.utcToSql(this.utilsService.parseXMLValue(reservation.contractissued)),
            ths_when_accepted: this.utilsService.utcToSql(this.utilsService.parseXMLValue(reservation.whenaccepted)),
            ths_when_canceled: this.utilsService.utcToSql(this.utilsService.parseXMLValue(reservation.whencanceled)),
            ths_modified: this.utilsService.utcToSql(this.utilsService.parseXMLValue(reservation.modified)),
            team_id: Number(this.utilsService.parseXMLValue(reservation.id)),
            event_id: Number(this.utilsService.parseXMLValue(reservation.eventid)),
        }
    }

    validateReservation(data) {
        const { error } = reservationInfoSchema.validate(data);

        if (error) {
            throw {
                error: `${error.details[0].message} for reservation ${data.ths_id} with team id: ${data.team_id}`,
                teamId: data.team_id
            };
        }
    }

    async upsertBooking(tr, params) {
        const skipInsert = await this.#updateBookingRow(tr, params);

        if(!skipInsert) {
            await this.#insertBookingRow(tr, params);
        }
    }

    async #updateBookingRow(tr, params) {
        const query =
            `UPDATE ths_booking SET
                event_id = $1,
                roster_team_id = $2,
                ths_tentative_nights = $3,
                ths_confirmed_nights = $4,
                ths_hotel_name = $6,
                ths_hotel_status = $7,
                ths_loyalty = $8,
                ths_contract_issued = $9,
                ths_when_accepted = $10,
                ths_when_canceled = $11,
                ths_modified = $12
            WHERE ths_id = $5 RETURNING *`;

        const {rowCount} = await tr.query(query, params)
            .catch(err => {
                throw {
                    error: err,
                    sql: query,
                    params
                }
            });

        return rowCount > 0;
    }

    #insertBookingRow(tr, params) {
        const query =
            `INSERT INTO ths_booking (
                event_id,
                roster_team_id,
                ths_tentative_nights,
                ths_confirmed_nights,
                ths_id,
                ths_hotel_name,
                ths_hotel_status,
                ths_loyalty,
                ths_contract_issued,
                ths_when_accepted,
                ths_when_canceled,
                ths_modified)
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`;

        return tr.query(query, params)
            .catch(err => {
                throw {
                    error: err,
                    sql: query,
                    params
                }
            });
    }
}

module.exports = BookingService;


const StaffService = require('./members/_StaffService');
const AthleteService = require('./members/_AthleteService');

class MemberService {
    constructor () {
        this.staff = StaffService;
        this.athlete = AthleteService;
    }

    get STAFF_MEMBER_TYPE () {
        return TeamMembersService.STAFF;
    }

    get ATHLETE_MEMBER_TYPE () {
        return TeamMembersService.ATHLETE;
    }

    get VALID_MEMBERS () {
        return [this.ATHLETE_MEMBER_TYPE, this.STAFF_MEMBER_TYPE];
    }

    async removeFromRoster (memberID, rosterTeamID, eventID, userID, type) {
        if (!this.VALID_MEMBERS.includes(type)) {
            throw { validation: 'Invalid "type" parameter value' };
        }

        let member = await this[type].removeFromRoster(memberID, rosterTeamID, eventID, userID);

        await eventNotifications.add_notification(eventID, {
            action          : 'team.member.remove',
            roster_team_id  : rosterTeamID,
            user_id         : userID,
            comments        : `Removed ${type} ${member.first} ${member.last} (${member.usav_code}) from ${member.team_name} ${member.team_code} team's roster`
        });
    }

}

module.exports = new MemberService();

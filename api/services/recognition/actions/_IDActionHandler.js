'use strict';

const RekognitionService = require('../_RekognitionService');
const AbstractActionHandler = require('./_AbstractActionHandler');

class IDActionHandler extends AbstractActionHandler {
    getAction() {
        return RekognitionService.VERIFICATION_ACTIONS.ID;
    }

    __validateRekognitionResponse(response) {
        super.__validateRekognitionResponse(response);

        const IDUserInfo = this.__parseUserInfo(response.data);

        const isSameUser =
            IDUserInfo.first_name?.toLowerCase() === user.first?.toLowerCase() &&
            IDUserInfo.last_name?.toLowerCase() === user.last?.toLowerCase();

        if (!isSameUser) {
            throw { validation: "The name on the ID and account didn't match" };
        }
    }

    __parseUserInfo(data) {
        if(this.__isApiV2Response(data)){
            return data[0]
        }

        return data
    }

    __isApiV2Response(data) {
        return Array.isArray(data)
    }
}

module.exports = IDActionHandler;

[{"type": "text", "field": "first_name", "label": "First Name", "show_on": {"receipt": true, "purchase": true, "payment_list": true}, "required": true, "short_label": "Player First", "outer_dependency": {"usav": {"required_fields": ["first_name", "last_name", "usav_code", "birthdate", "gender"]}}}, {"type": "text", "field": "last_name", "label": "Last Name", "show_on": {"receipt": true, "purchase": true, "payment_list": true}, "required": true, "short_label": "Player Last", "outer_dependency": {"usav": {"required_fields": ["first_name", "last_name", "usav_code", "birthdate", "gender"]}}}, {"type": "select", "field": "gender", "label": "Gender", "options": {"male": "Male", "female": "Female"}, "show_on": {"receipt": true, "purchase": true, "payment_list": false}, "required": true, "short_label": "", "outer_dependency": {"usav": {"required_fields": ["first_name", "last_name", "usav_code", "birthdate", "gender"]}}}, {"type": "date-sel", "field": "birthdate", "label": "Birthdate", "show_on": {"receipt": true, "purchase": true, "payment_list": false}, "required": true, "short_label": "", "outer_dependency": {"usav": {"required_fields": ["first_name", "last_name", "usav_code", "birthdate", "gender"]}}}, {"type": "usav", "field": "usav_code", "label": "USAV Membership Number", "options": {}, "show_on": {"receipt": false, "purchase": true, "payment_list": true}, "required": true, "short_label": "", "outer_dependency": {"usav": {"required_fields": ["first_name", "last_name", "usav_code", "birthdate", "gender"]}}}]
const {TEAM_ROSTER_STAFF_CHECKIN_ACTIVATED, TEAM_ROSTER_STAFF_CHECKIN_DEACTIVATED} = require("../../constants/notification-actions");
const {NOTIFICATION_ACTION_DESCRIPTION} = require("../../constants/event-history");
class EventHistoryService {

    async get (eventId, filters) {
        const eventHistoryData = await this.#getEventHistoryData(eventId, filters);

        return this.#prepareHistoryData(eventHistoryData);
    }

    async #getEventHistoryData(eventId, filters) {
        const FORMAT = 'MM/DD/YYYY';
        const { search, from, to, has_notes, type } = filters;

        const query = knex('event_change as ec')
            .select(
                'ec.event_change_id',
                'ec.action',
                'ec.comments',
                'ec.event_email_id',
                'sj.email_template_id',
                'rc.club_name',
                'rt.team_name',
                'p.payment_for',

                { division_name: 'd.name' },
                { purchase_amount: 'p.amount' },
                { created: knex.raw(`to_char((ec.created::timestamptz AT TIME ZONE e.timezone), 'DD Mon YYYY HH24:mi:ss')`) }
            )
            .leftJoin('roster_club AS rc', 'rc.roster_club_id', 'ec.roster_club_id')
            .leftJoin('roster_team AS rt', 'rt.roster_team_id', 'ec.roster_team_id')
            .leftJoin('division AS d', 'd.division_id', 'ec.division_id')
            .leftJoin('purchase AS p', 'p.purchase_id', 'ec.purchase_id')
            .leftJoin('system_job AS sj', 'sj.system_job_id', 'ec.system_job_id')
            .leftJoin('event AS e', function() {
                return this.on(knex.raw(`e.event_id = ?`, [eventId]))
            })
            .where('ec.event_id', eventId)
            .whereRaw('ec.published IS TRUE')
            .whereRaw(`ec.action NOT IN ('${TEAM_ROSTER_STAFF_CHECKIN_ACTIVATED}', '${TEAM_ROSTER_STAFF_CHECKIN_DEACTIVATED}')`)
            .orderBy('ec.created', 'desc')
            .limit(1000);

        if(search) {
            query.whereRaw(`(ec.comments ILIKE ?)`, `%${search}%`);
        }

        if(type) {
            query.whereRaw(`(ec.action ILIKE ?)`, `${type}.%`);
        }

        if(from) {
            query.whereRaw(`TO_CHAR(ec.created, ?) >= ?`, [FORMAT, from])
        }

        if(to) {
            query.whereRaw(`TO_CHAR(ec.created, ?) <= ?`, [FORMAT, to])
        }

        if(has_notes) {
            query.where('ec.comments', '<>', '');
        }

        const {rows = []} = await Db.query(query);

        return rows;
    }

    #prepareHistoryData(eventHistoryData) {
        for(let i = 0; i < eventHistoryData.length; ++i) {
            const action = eventHistoryData[i].action;

            if (_.has(NOTIFICATION_ACTION_DESCRIPTION[action], 'text')) {
                eventHistoryData[i].action = NOTIFICATION_ACTION_DESCRIPTION[action].text
            }
        }

        return eventHistoryData;
    }
}

module.exports = new EventHistoryService();

'use strict';

/* jshint eqnull:true */

/* === Advanced Email Module === */
const fetch				   = require('node-fetch');
const co 				       = require('co');
const schemas 			       = require('../validation-schemas/aem');

const EmailTriggersConstructor  = require('./aem/_EventEmailTriggerService');
const AdminService              = require('./aem/_AdminService');
const CustomRecipientsService   = require('./aem/_CustomRecipients');
const EventOwnerService         = require('./event/_EventOwnerService');
const VariablesTransformer 	    = require('./aem-sender-utils/_VariablesTransformer.js')
const NewsLetterSendingService  = require('./aem/_NewsletterSendingService');

const EDIT_TMPL_SCHEMA    = schemas.editTemplate;
const CREATE_TMPL_SCHEMA 		= schemas.createTemplate;
const CREATE_ADMIN_TMPL_SCHEMA 	= schemas.createTemplateAdmin;

/* TODO: split into 3 services: main, admin, eo */

class AEMService {

	constructor () {
        this._EventEmailTriggerService = new EmailTriggersConstructor(this);
        this._AdminService = new AdminService(this);
    }

    /* === */ 
    get triggers () {
        return this._EventEmailTriggerService;
    }

    get admin () {
        return this._AdminService;
    }

    get customRecipients () {
        return CustomRecipientsService;
    }

    get newsletter () {
        return NewsLetterSendingService;
    }
    /* === */

	get OFFICIAL_GROUP () {
		return 'officials';
	}

	get TICKET_GROUP () {
		return 'tickets';
	}

	get CLUB_GROUP () {
		return 'clubs';
	}

    get CLUB_DIRECTORS_GROUP () {
        return 'clubs.directors';
    }
    
    get CLUBS_DIRECTORS_GROUP () {
        return 'clubs_directors';
    }

    get CLUB_STAFF_GROUP () {
        return 'clubs.staff';
    }

    get STAFF_GROUP () {
        return 'staff';
    }

    get CAMP_GROUP () {
        return 'camps';
    }

    get CUSTOM_LIST_GROUP () {
	    return 'custom_list';
	}

    get TICKETS_REFUNDS_GROUP () {
        return 'tickets.refunds';
    }

    get TICKETS_PAYMENTS_GROUP () {
        return 'tickets.payments';
    }

	get EXHIBITORS_GROUP () {
	    return 'exhibitors';
    }

    get BOOTHS_PAYMENTS_GROUP () {
	    return 'booths.payments';
    }

    get TEAMS_REFUNDS_GROUP () {
        return 'teams.refunds';
    }

    get MANUAL_GROUP () {
	    return 'manual';
    }

    get TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP () {
        return 'teams_uncollected_fee_payments';
    }

    get TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP () {
        return 'tickets_uncollected_fee_payments';
    }

    get UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP () {
        return 'upcoming_uncollected_fee_payments';
    }

    get TEAMS_REFUNDS_GROUP_TYPE () {
        return {
            STRIPE_DASHBOARD_REFUND: 'refund.stripe.eo',
            STRIPE_DASHBOARD_REFUND_ADMIN: 'refund.stripe.admin'
        }
    }

    get TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE () {
        return {
            FAILED: 'teams_uncollected_fee_payments.failed',
            PENDING: 'teams_uncollected_fee_payments.pending',
            REQUEST_ACTION: 'teams_uncollected_fee_payments.requires-action',
            SUCCESS: 'teams_uncollected_fee_payments.success',
        }
    }

    get TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE () {
        return {
            FAILED: 'tickets_uncollected_fee_payments.failed',
            PENDING: 'tickets_uncollected_fee_payments.pending',
            REQUEST_ACTION: 'tickets_uncollected_fee_payments.requires-action',
            SUCCESS: 'tickets_uncollected_fee_payments.success',
        }
    }

    get UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP_TYPE () {
        return {
            NOTICE: 'upcoming_uncollected_fee_payments.notice'
        }
    }

	get TICKETS_REFUNDS_GROUP_TYPE () {
	    return {
            PARTIAL_REFUND                  : 'refund.partial',
            FULL_REFUND                     : 'refund.full',
            STRIPE_DASHBOARD_REFUND         : 'refund.stripe.eo',
            STRIPE_DASHBOARD_REFUND_ADMIN   : 'refund.stripe.admin',
            REFUND_REQUEST                  : 'refund.request'
        }
    }

    get TICKETS_PAYMENTS_GROUP_TYPE () {
        return {
            ASSIGNED_TICKETS_RECEIPT: 'tickets.assigned.receipt',
            ASSIGNED_TICKETS_TICKET_WEEKEND: 'tickets.assigned.ticket.weekend',
            ASSIGNED_TICKETS_TICKET_DAILY: 'tickets.assigned.ticket.daily',
            ASSIGNED_TICKETS_TICKET_FREE: 'tickets.assigned.ticket.free',
        }
    }

	get CLUBS_GROUP_TYPE () {
        return {
            PRIMARY_STAFF_ONLINE_CHECKIN_TYPE: 'teams.online-checkin.primary-staff',
            DEFAULT_ONLINE_CHECKIN_TYPE: 'teams.online-checkin.default',
        }
    }
	
    get VIRTUAL_GROUPS_MAPPING () {
        return {
            [this.CLUB_GROUP]: [
                { group: this.CLUB_DIRECTORS_GROUP, title: 'Club Directors'},
                { group: this.CLUB_STAFF_GROUP, title: 'Team Staff'},
            ]
        };
    }

    get TRIGGER_ROLE_PREFIX () {
        return {
            OFFICIAL: 'official',
            STAFF   : 'staff'
        }
    }

	get NEWS_LETTERS_GROUP () {
	    return 'newsletters';
    }

    get HEAD_REFEREE_GROUP () {
        return 'head.referee';
	}
	
	get TEAMS_PAYMENTS_GROUP() {
		return 'teams.payments';
	}

	get MANUAL_MAILING_TYPE () {
		return 'content';
	}

	get MANUAL_TYPE () {
	    return 'manual';
    }

    get BASIC_LAYOUT_TYPE () {
        return 'layout';
    }

    get COMMON_TMPL_EO_ID () {
        return 0;
    }

	get _IMG_BASE_URL () {
		return '/images/tmpl-layout/';
	}

    get _TMPLS_SQL_QUERY () {
        /**
         * Event Owner can see:
         * · Default Trigger templates
         * · His Trigger templates (also, specific for viewed event)
         * · Manual Mailing Templates 
         */
        return (
            `SELECT 
                etg."group" "id", etg."title", etg."description", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("templates"))), '[]'::JSON)
                    FROM (

                        SELECT * FROM "v_aem_trigger_templates" "t" 
                        WHERE "t"."group" = etg."group"
                            AND "t"."is_default" IS TRUE
                            AND "t"."published" IS TRUE

                        UNION ALL 
                        
                        SELECT * FROM "v_aem_trigger_templates" "t" 
                        WHERE "t"."group" = etg."group"
                            AND "t"."event_owner_id" = $1
                            AND ("t"."event_id" = $2 OR "t"."event_id" IS NULL)
                            AND "t"."published" IS TRUE
                        
                        UNION ALL 

                        SELECT * FROM "v_aem_manual_mailing_templates" "m" 
                        WHERE "m"."group" = etg."group" 
                            AND "m"."event_owner_id" = $1
                            AND ("m"."event_id" = $2 OR "m"."event_id" IS NULL)
                            AND "m"."published" IS TRUE

                    ) "templates"
                ) "templates",
                (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("types"))), '[]'::JSON)
                    FROM (
                        SELECT ett.title, ett.type FROM email_template_type ett
                        WHERE etg.group = ett.email_template_group
                    ) "types"
                ) "types"
             FROM "email_template_group" etg
             LEFT JOIN "event" e ON e.event_id = $2
             WHERE (etg.usage_restrictions->'roles') ? 'any'
               AND NOT (etg.group = 'camps' AND e.ticket_camps_registration IS FALSE)
             ORDER by etg.position ASC;`
        );
    }

	get _BEE_EDITOR_KEYS_SQL_QUERY () {
		return (
			`SELECT 
				s."value"->>'client_id' "client_id", 
				s."value"->>'client_secret' "client_secret" 
			 FROM "settings" s
			 WHERE s."key" = 'bee'`
		);
	}

	get _TMPL_DATA_SQL_QUERY_BASE () {
		return (
			`SELECT 
				et."email_template_id" "id",
				et."recipient_type" "recipient",
				et."title",
				et."email_subject" "subject",
				et."bee_json",
				et."unlayer_json",
				et."email_template_type" "type",
				et."email_template_group" "group",
				et."email_html",
				et."email_text",
				et."is_valid",
				ett."long_title" "type_title",
				etg."title" "group_title"
			 FROM "email_template" et 
			 LEFT JOIN "email_template_type" ett 
			 	ON et."email_template_type" = ett."type"
			 LEFT JOIN "email_template_group" etg 
			 	ON etg."group" = et."email_template_group"`
		);
	}

	get _TMPL_DATA_BY_ID_SQL_QUERY () {
		return (
			this._TMPL_DATA_SQL_QUERY_BASE +
			`
			WHERE et."email_template_id" = $1
				AND et."deleted" IS NULL`
		);
	}

	get _TMPL_DATA_SQL_QUERY () {
		return (
			this._TMPL_DATA_SQL_QUERY_BASE +
			`
			 WHERE et."event_owner_id" = $1
			 	AND et."deleted" IS NULL
			 	AND (et."event_id" = $2 OR et."event_id" IS NULL)
			 	AND et."email_template_id" = $3
				AND et."email_template_type" <> 'layout'`
		);
	}

	get _BASIC_LAYOUTS_SQL_QUERY () {
		return (
			`SELECT 
				et."email_template_id" "id",
				et."title",
				et."img_name"
			 FROM "email_template" et 
			 WHERE et."email_template_type" = 'layout'
			 	AND et."published" IS TRUE
			 	AND et."deleted" IS NULL`
		);
	}

	get _GET_BASIC_LAYOUT_SQL_QUERY () {
		return (
			`SELECT 
				et."email_template_id" "id",
				et."title",
				et."bee_json",
				et."unlayer_json",
				et."email_html"
			 FROM "email_template" et 
			 WHERE et."email_template_id" = $1
			 	AND et."email_template_type" = 'layout'
			 	AND et."published" IS TRUE
			 	AND et."deleted" IS NULL`
		);
	}

    /* is used by EO */
	get _GET_TMPL_HTML_SQL_QUERY () {
		return (
            `SELECT 
                et."is_valid",
                et."email_html",
                et."email_subject",
                et."email_text",
                et."event_owner_id",
                et."sender_type",
                et."title",
                et."event_id",
                et."bee_json",
                et."img_name",
                et."email_template_type",
                et."email_template_group", 
                (et."email_template_id" = ett."default_email_template_id") "is_default",
                et."published",
                et."bee_html",
                et."unlayer_json"
             FROM "email_template" et 
             LEFT JOIN "email_template_type" ett 
                ON et."email_template_type" = ett."type"
             WHERE et."email_template_id" = $1 
                AND ( EXISTS (

                    SELECT "id" FROM "v_aem_trigger_templates" "t" 
                    WHERE "t"."id" = $1
                        AND "t"."is_default" IS TRUE
                        AND "t"."published" IS TRUE

                    UNION ALL 
                        
                    SELECT "id" FROM "v_aem_trigger_templates" "t" 
                    WHERE "t"."id" = $1
                        AND ("t"."event_owner_id" = $2 OR "t"."event_owner_id" IS NULL)
                        AND "t"."published" IS TRUE
                        
                    UNION ALL 

                    SELECT "id" FROM "v_aem_manual_mailing_templates" "m" 
                    WHERE "m"."id" = $1
                        AND "m"."event_owner_id" = $2
                        AND "m"."published" IS TRUE

            ) OR (et."email_template_type" = $3 AND et."email_template_group" = $4 AND et.event_owner_id = $2) )`
		);
	}

	get _TMPL_COPY_PREFIX () {
		return 'Copy of:';
	}

    virtualGroupToReal(group) {
        for (const currentGroup in this.VIRTUAL_GROUPS_MAPPING) {
            if (this.VIRTUAL_GROUPS_MAPPING[currentGroup].some(g => g.group === group)) {
                return currentGroup;
                break;
            }
        }

        return group;
    }

	_checkTmplNotNull (template) {
		if (template === null) {
			return Promise.reject({ validation: 'Template not found' });
		} else {
			return template;
		}
	}

	getTemplateByID (templateID) {
		return Db.query(this._TMPL_DATA_BY_ID_SQL_QUERY, [templateID])
		.then(result => result.rows[0] || null)
	}

	getBeeEditorUID (eventOwnerID) {
		// TODO: maybe add some random hash?
		return `sw-eo-${eventOwnerID}`;
	}

	_checkEmailGroupExists (group) {
		return Db.query(
			`SELECT etg."group"
			 FROM "email_template_group" etg
			 WHERE etg."group" = $1`,
			[group]
		).then(result => (result.rows.length > 0));
	}

	_insertEmailTemplateRow (data) {
		return Db.query(
			squel.insert().into('email_template')
			.setFields(data)
			.returning(
				`email_template_id "id", 
				unlayer_json, email_html, (
				    SELECT ROW_TO_JSON("t") 
				    FROM (
				        SELECT ett."type", ett."title"
						FROM "email_template_type" ett 
						WHERE ett."type" = "email_template"."email_template_type"
							AND ett."email_template_group" = "email_template"."email_template_group"
				    ) "t"
				) "type"`
			)
		).then(result => result.rows[0] || null);
	}

	insertEmailTemplateRows(rows) {
	    const query = squel
            .insert()
            .into('email_template')
            .setFieldsRows(rows)
            .returning(`email_template_id, event_id`);

	    return Db.query(query).then(response => response.rows);
    }

	_isTitleUnique (title, eventOwnerID, eventID, group) {
		let query = squel.select().from('email_template')
						.field('email_template_id')
					.where('LOWER(TRIM(title)) = LOWER(TRIM(?))', title)
					.where('"deleted" IS NULL')

		if (eventOwnerID) {
			query.where('event_owner_id = ?', eventOwnerID);
		}

		if (eventID) {
			query.where('event_id = ?', eventID);
		}

		if (group) {
			query.where('email_template_group = ?', group);
		}


		return Db.query(query).then(result => (result.rows.length === 0));
	}

	_findTemplateForDuplication (eventOwnerID, templateID) {
		/* ❗️ NOTE: we can't copy "layout" type, cause there is no "email_template_type" row for it */
		return Db.query(
            this._GET_TMPL_HTML_SQL_QUERY,
			[templateID, eventOwnerID, this.MANUAL_TYPE, this.MANUAL_GROUP]
		).then(result => result.rows[0] || null)
	}

	_getTmplForAssignment (eventOwnerID, eventID, templateID) {
		/* ❗️ Template should belong to EO or be a default template */
		return Db.query(
			`SELECT 
				et."is_valid",
				et."email_template_type" "type",
				et."email_template_group" "group"
			 FROM "email_template" et 
			 INNER JOIN "email_template_type" ett 
			 	ON ett."type" = et."email_template_type"
			 	AND ett."email_template_group" = et."email_template_group"
			 WHERE et."email_template_id" = $1
			 	AND et."published" IS TRUE
			 	AND (
			 		et."event_owner_id" = $2 
			 		OR ett."default_email_template_id" = et."email_template_id"
			 	)
			 	AND (
			 		et."event_id" = $3
			 		OR et."event_id" IS NULL
			 	)
			 	AND et."deleted" IS NULL`,
			[templateID, eventOwnerID, eventID]
		).then(result => result.rows[0] || null);
	}

	_upsertTriggerAssignment (templateID, eventID, tmplType, tmplGroup) {

	    if(!templateID) {
             throw new Error('Template ID required!');
        }

        if(!eventID) {
            throw new Error('Event ID required!');
        }

        if(!tmplType) {
            throw new Error('Template type required!');
        }

        if(!tmplGroup) {
            throw new Error('Template group required!');
        }

		return Db.query(
			`WITH "upd" AS (
				UPDATE "event_email_trigger" eet
				SET "email_template_id" = $1
				WHERE eet."event_id" = $2 
					AND eet."email_template_type" = $3
					AND eet."email_template_group" = $4 
				RETURNING eet."id"
			), "ins" AS (
				INSERT INTO "event_email_trigger" (
					"event_id", "email_template_id", "email_template_type", "email_template_group"
				)
				SELECT 
					($2)::INTEGER "event_id",
					($1)::INTEGER "email_template_id",
					($3)::TEXT "email_template_type",
					($4)::TEXT "email_template_group"
				WHERE NOT EXISTS (SELECT * FROM "upd")
				RETURNING "id"
			)
			SELECT * FROM "upd" UNION ALL SELECT * FROM "ins"`,
			[templateID, eventID, tmplType, tmplGroup]
		).then(result => result.rows[0] && result.rows[0].id || null);
	}

	async getBeeEditorToken () {
		const { client_id, client_secret } = await Db.query(
            this._BEE_EDITOR_KEYS_SQL_QUERY
        ).then((result) => result.rows[0] || {});

        if (!client_id) {
            throw { validation: "No Client Id" };
        }

        if (!client_secret) {
            throw { validation: "No Client Secret" };
        }

        const headers = new fetch.Headers();
		headers.set('Accept', 'application/json');
        headers.set('Content-Type', 'application/json');
		
        const options = {
			method: "POST",
            body: JSON.stringify({
				grant_type: "password",
                client_id,
                client_secret,
            }),
            headers,
        };
        
		const response = await fetch('https://auth.getbee.io/apiauth', options);
        const body = await response.json();

        if (!response.ok) {
            loggers.errors_log.error(body);
            throw Error(
                body.error_description || "Error obtaining bee editor token"
            );
        }
        return body;
	}

	// 👍 ok
	getEOTmplsList (eventOwnerID, eventID) {
		if (!Number.isInteger(eventOwnerID)) {
			return Promise.reject({ validation: 'No Access Rights' });
		}

		if (!Number.isInteger(eventID)) {
			return Promise.reject({ validation: 'Invalid Event Identifier' });
		}

        return Db.query(this._TMPLS_SQL_QUERY, [eventOwnerID, eventID])
            .then(result => result.rows);
	}

	// 👍 ok
	getTemplate (eventOwnerID, eventID, templateID) {
		return Db.query(this._TMPL_DATA_SQL_QUERY, [eventOwnerID, eventID, templateID])
		.then(result => result.rows[0] || null)
		.then(this._checkTmplNotNull.bind(this));
	}

    stripHTML (html) {
        return HtmlStripper.getText(html);
    }

	async saveTemplate (eventOwnerID, eventID, templateID, data) {
        if (!Number.isInteger(eventOwnerID)) {
            throw { validation: 'No Access Rights' };
        }

        if (!Number.isInteger(templateID)) {
            throw { validation: 'Invalid template ID' };
        }

        let validation = EDIT_TMPL_SCHEMA.validate(data);

        if (validation.error) {
            loggers.errors_log.error(validation.error);
            throw { validationErrors: validation.error.details };
        }

        let template = await this.getTemplateByID(templateID);

        if(!template) {
            throw { validation: 'Template not found' };
        }

        let prepared = validation.value;
        prepared.email_text = this.stripHTML(prepared.email_html);
        prepared.unlayer_json    = JSON.stringify(prepared.unlayer_json);
        prepared.is_valid = Boolean(prepared.email_subject);

        await this.__validateTemplateSubjectVariables(template.group, prepared.email_subject);

        return Db.query(
            squel.update().table('email_template')
                .setFields(prepared)
                .where('event_owner_id = ?', eventOwnerID)
                .where('email_template_id = ?', templateID)
        )
	}
	// 👍 ok
	createTemplate (data) {
		/* ❗️ NOTE: we can only create a template for Manual mailing */
		return co(function* () {
			let validation = CREATE_TMPL_SCHEMA.validate(data);

			if (validation.error) {
				loggers.errors_log.error(validation.error);
				return Promise.reject({ validationErrors: validation.error.details });
			}

			delete validation.value.visibility_scope;

			let prepared = validation.value;

            // NOTE: variable is called "isTypeExist", but method contains "Group" in it's name !?
			let isTypeExist = yield (this._checkEmailGroupExists(prepared.email_template_group));

			if (!isTypeExist) {
				// Should we call it another way, not "type"
				return Promise.reject({ validation: `Type ${prepared.email_template_type} does not exit.` });
			}

			let basicLayout = yield (this.getBasicLayout(prepared.email_template_id));

			if (basicLayout === null) {
				return Promise.reject({ validation: `Basic Layout not found` });
			}

			let isTitleUnique =
				yield (this._isTitleUnique(
							prepared.title, prepared.event_owner_id, prepared.event_id, prepared.email_template_group));

			if (!isTitleUnique) {
				return Promise.reject({
                    validation: `Title already exists${prepared.event_id?' For this event':''}`
                });
			}

			prepared.is_valid            = false;
			prepared.unlayer_json         = basicLayout.unlayer_json;
			prepared.email_html          = basicLayout.email_html;
			prepared.published           = true;
            /* Remove id of the Basic Layout */
			delete prepared.email_template_id;

			return yield (this._insertEmailTemplateRow(prepared));
		}.bind(this))
	}

	// 👍 ok
	getBasicLayoutsList () {
		return Db.query(this._BASIC_LAYOUTS_SQL_QUERY)
		.then(result => result.rows)
		.then(templates => templates.map(t => {
			t.img_name = this._IMG_BASE_URL + t.img_name;
			return t;
		}))
	}

	// 👍 ok
	getBasicLayout (templateID) {
		if (!Number.isInteger(templateID)) {
			return Promise.reject({ validation: 'Invalid template ID' });
		}

		return Db.query(this._GET_BASIC_LAYOUT_SQL_QUERY, [templateID])
		.then(result => result.rows[0] || null)
		.then(this._checkTmplNotNull.bind(this));
	}

	// 👍 ok
	loadEmailTypes (group) {
		if (!group) {
			return Promise.reject({ validation: 'Invalid group passed' });
		}

		return Db.query(
			`SELECT 
                ett."title", ett."type" 
             FROM "email_template_type" ett 
             WHERE ett."email_template_group" = $1`,
			[group]
		).then(result => result.rows);
	}
	// 👍 ok
    getTemplateGroupVariables(group, skipHidden) {
        if (!group) {
            return Promise.reject({validation: 'No template group passed'});
        }

        return Db.query(
            `SELECT etg."variables"
             FROM "email_template_group" etg 
             WHERE etg."group" = $1`,
            [group]
        ).then(result => result.rows[0] && result.rows[0].variables || null)
            .then((data) => {

                if (skipHidden) {
                    return data;
                }

                return this.__filterHiddenVariables(data);
            })
    }

	// 👍 ok
	eoTmplPreview (eventOwnerID, templateID) {
		if (!Number.isInteger(eventOwnerID)) {
			return Promise.reject({ validation: 'No Access Rights' });
		}

		if (!Number.isInteger(templateID)) {
			return Promise.reject({ validation: 'Invalid template ID' });
		}

		return Db.query(this._GET_TMPL_HTML_SQL_QUERY, [templateID, eventOwnerID, this.MANUAL_TYPE, this.MANUAL_GROUP])
		.then(result => result.rows[0] || null)
		.then(tmpl => {
			if (tmpl === null) {
				return Promise.reject({ validation: 'Template not found.' });
			} else if (!(tmpl.is_valid && tmpl.email_html)) {
				return Promise.reject({ validation: 'Template is not valid. Please, finish template creation first' });
			} else {
				return tmpl.email_html;
			}
		})
	}

	// 👍 ok
	duplicateTemplate (eventOwnerID, eventIds, templateID, user) {
		return co(function* () {
			if (!Number.isInteger(eventOwnerID)) {
				return Promise.reject({ validation: 'No Access Rights' });
			}

			if (!Number.isInteger(templateID)) {
				return Promise.reject({ validation: 'Invalid template ID' });
			}

			if (!eventIds && !eventIds.length) {
                return Promise.reject({ validation: 'No event(s) passed' });
            }

			let template = yield (this._findTemplateForDuplication(eventOwnerID, templateID));

			if (template === null) {
				return Promise.reject({ validation: 'Template not found' });
			}

			if (!template.is_valid) {
				return Promise.reject({ validation: 'Draft templates are not allowed to be copied' });
			}

			if (!template.published) {
				return Promise.reject({ validation: 'Not published templates are not allowed to be copied' });
			}

			template.title 			= `${this._TMPL_COPY_PREFIX} ${template.title}`;
			template.bee_json 		= JSON.stringify(template.bee_json);
            template.unlayer_json   = JSON.stringify(template.unlayer_json);

            delete template.is_default;
            delete template.event_id;
            delete template.event_owner_id;

            const rows = this.generateTemplateRows(eventIds, template, user);

			return yield (this.insertEmailTemplateRows(rows));
		}.bind(this))
	}

	// 👍 ok
	assignTmplToTrigger (eventOwnerID, eventID, templateID) {
		return co(function* () {

			let template = yield (this._getTmplForAssignment(eventOwnerID, eventID, templateID));

			if (template === null) {
				return Promise.reject({ validation: 'Template not found!' });
			}

			if (!template.is_valid) {
				return Promise.reject({ validation: 'Draft templates cannot be assigned' });
			}

			let id = yield (this._upsertTriggerAssignment(templateID, eventID, template.type, template.group));

			loggers.debug_log.verbose('Modified', `#${id}`, 'row');

			return id;
		}.bind(this))	
	}

	deleteTemplate (eventOwnerID, eventID, templateID) {
		return co(function* () {
			if (!Number.isInteger(eventOwnerID)) {
				return Promise.reject({ validation: 'No Access Rights' });
			}

			if (!Number.isInteger(templateID)) {
				return Promise.reject({ validation: 'Invalid template ID' });
			}

			let tmpl = yield (this.getTemplate(eventOwnerID, eventID, templateID));

			if (tmpl.type !== this.MANUAL_MAILING_TYPE) {
				return Promise.reject({ validation: 'Only Manual Mailing templates allowed to be deleted' });
			}

			yield (this._removeTemplate(templateID));
		}.bind(this));
	}

	/* === TEMPLATES SENDING === */

	get TEAMS_AND_CLUBS_GROUP () {
		return 'clubs';
	}

	get SWT_GROUP () {
		return 'tickets';
	}

	get OFFICIALS_GROUP () {
		return 'officials';
	}

	get EVENT_OWNERS_GROUP() {
	    return 'event.owners';
    }

	getTemplatesForSending (group, eventOwnerID, eventID) {
		if (![this.TEAMS_AND_CLUBS_GROUP, this.SWT_GROUP, this.OFFICIALS_GROUP, this.EVENT_OWNERS_GROUP, this.STAFF_GROUP, this.CLUBS_DIRECTORS_GROUP].includes(group)) {
			return Promise.reject({ validation: `Invalid Group "${group}"` });
		}

		if (!group) {
			return Promise.reject({ validation: 'No Group Passed' });
		}

		if (eventOwnerID && !Number.isInteger(eventOwnerID)) {
			return Promise.reject({ validation: 'No Access Rights' });
		}

		if (eventID && !Number.isInteger(eventID)) {
			return Promise.reject({ validation: 'Invalid event ID' });
		}

		/**
			❗️ NOTE: "is_content" property is for templates that have "{CONTENT}" variable
			in which we can pass some custom HTML. For now this logic is not implemented
		*/

		const params = [group, this.MANUAL_MAILING_TYPE];

		let query = `
		    SELECT 
				et."email_template_id" "id",
				et."email_subject" "subject",
				et."title",
				FALSE "is_content",
				et."email_template_group" "group",
				et."email_template_type" "type"
			 FROM "email_template" et 
			 WHERE et."email_template_group" = $1 
			 	AND et."email_template_type" = $2
			 	AND et."is_valid" IS TRUE
			 	AND et."published" IS TRUE
			 	AND et."deleted" IS NULL
		`;

		if (eventOwnerID && Number.isInteger(eventOwnerID)) {
            params.push(eventOwnerID);

            query += ` AND et."event_owner_id" = $${params.length}`;
        }

        if (eventID && Number.isInteger(eventID)) {
            params.push(eventID);

            query += ` AND (et."event_id" = $${params.length} OR et."event_id" IS NULL)`;
        }

		return Db.query(query, params)
            .then(result => result.rows);
	}

	_removeTemplate (templateID) {
		return Db.query(
			`UPDATE "email_template"
			 SET "deleted" = NOW()
			 WHERE "email_template_id" = $1`,
			[templateID]
		).then(result => {
			if (result.rowCount === 0) {
				return Promise.reject({ validation: 'Template not found' });
			}
		})
	}

	/* === ADMIN === */

	get _ADMIN_TMPLS_LIST_SQL_QUERY () {
        /**
         * Admin can see:
         * · default trigger templates, 
         * · not assigned (to any EO/EVENT) trigger templates, 
         * · not assigned (to any EO/EVENT) manual mailing templates
         */
		return (
			`SELECT 
                etg."group" "id", etg."title", etg."description", (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("templates"))), '[]'::JSON)
                    FROM (

                        SELECT * FROM "v_aem_admin_templates" "t" 
                        WHERE "t"."group" = etg."group"
                        
                    ) "templates"
                ) "templates",
                (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("types"))), '[]'::JSON)
                    FROM (
                        SELECT ett.title, ett.type FROM email_template_type ett
                        WHERE etg.group = ett.email_template_group
                    ) "types"
                ) "types"
             FROM "email_template_group" etg`
		);
	}

	get _ADMIN_BASIC_LAYOUTS_SQL_QUERY () {
		return (
			`SELECT * FROM "v_aem_basic_layouts"`
		);
	}

	getAdminTmplsList () {
		return Promise.all([

			Db.query(this._ADMIN_TMPLS_LIST_SQL_QUERY)
			 .then(result => result.rows),

			Db.query(this._ADMIN_BASIC_LAYOUTS_SQL_QUERY)
			 .then(result => result.rows)
			 .then(templates => ({
				id 				: 'layout',
				title 			: 'Basic Layouts',
				description 	: 'Basic Layouts',
				templates
			 }))

		])
		.then(results => (results[0].concat(results[1])))
	}

	getAdminBeeEditorUID () {
		return 'admin-yD7KXfNABi';
	}

	get _ADMIN_TMPL_PREVIEW_SQL_QUERY () {
		return (
			`SELECT 
                et."is_valid",
                et."published",
                et."email_html",
                et."bee_json",
                et."unlayer_json",
                et."email_subject",
                et."email_text",
                et."event_owner_id",
                et."sender_type",
                et."title",
                et."event_id",
                et."img_name",
                et."email_template_type",
                et."email_template_group",
                et."email_template_id",
                et."bee_html",

                at."type_title",
                at."group_title",
                at."usage_qty"

            FROM "email_template" et 
            INNER JOIN "v_aem_admin_templates" "at" 
                ON "at"."id" = "et"."email_template_id"
            WHERE et."email_template_id" = $1`
		)
	}

	adminTmplPreview (templateID) {

		if (!Number.isInteger(templateID)) {
			return Promise.reject({ validation: 'Invalid template ID' });
		}

		return Db.query(this._ADMIN_TMPL_PREVIEW_SQL_QUERY, [templateID])
		.then(result => result.rows[0] || null)
		.then(tmpl => {
			if (tmpl === null) {
				return Promise.reject({ validation: 'Template not found.' });
			} else if (!tmpl.email_html) {
				return Promise.reject({ validation: 'Template is not valid. Please, finish template creation first' });
			} else {
				return tmpl.email_html;
			}
		})
	}

	adminDuplicate (templateID) {
		return co(function* () {
			if (!Number.isInteger(templateID)) {
				return Promise.reject({ validation: 'Invalid template ID' });
			}

			let template = yield (
				Db.query(this._ADMIN_TMPL_PREVIEW_SQL_QUERY, [templateID])
				.then(result => result.rows[0] || null)
			);

			if (template === null) {
				return Promise.reject({ validation: 'Template not found' });
			}

			if (!template.published) {
				return Promise.reject({ validation: 'Not allowed to copy NOT published templates' });
			}

			template.title 			= `${this._TMPL_COPY_PREFIX} ${template.title}`;
			template.bee_json 		= JSON.stringify(template.bee_json);
            template.unlayer_json   = JSON.stringify(template.unlayer_json);
			template.published 		= false;

			delete template.is_default;
			delete template.type_title;
			delete template.group_title;
			delete template.email_template_id;
            delete template.usage_qty;

			let result = yield (this._insertEmailTemplateRow(template));

			return _.pick(result, 'id');
		}.bind(this))
	}

	getAdminTmplData (templateID) {
		if (!Number.isInteger(templateID)) {
			return Promise.reject({ validation: 'Invalid template ID' });
		}

		return Db.query(this._ADMIN_TMPL_PREVIEW_SQL_QUERY, [templateID])
		.then(result => result.rows[0] || null)
		.then(template => {
			if (template !== null) {
				return {
					id 			: template.email_template_id,
					title 		: template.title,
					subject 	: template.email_subject,
					bee_json	: template.bee_json,
					unlayer_json : template.unlayer_json,
					email_html : template.email_html,
					type 		: template.email_template_type,
					group 		: template.email_template_group,
					type_title  : (template.email_template_type === this.BASIC_LAYOUT_TYPE)
									?'Basic Layout'
									:template.type_title,
					group_title : template.group_title
				}
			} else {
				return template;
			}
		})
	}

    getTypesOfGroup (groupID, triggersOnly) {
        if (!_.isString(groupID)) {
            return Promise.reject({ validation: 'Invalid Group ID' });
        }

        let query = squel.select()
            .field('etp.title')
            .field('etp.type')
         .from('email_template_type', 'etp')
         .where('etp."email_template_group" = ?', groupID);

        if (triggersOnly) {
            query.where('etp."is_trigger" IS TRUE');
        }

        return Db.query(query).then(res => res.rows);
    }

    checkTypeExistance (groupID, typeID) {
        if (!_.isString(groupID)) {
            return Promise.reject({ validation: 'Invalid Group ID' });
        }

        if (!_.isString(typeID)) {
            return Promise.reject({ validation: 'Invalid Type ID' });
        }

        return Db.query(
            `SELECT COUNT(*) "count"
             FROM "email_template_type" ett 
             WHERE ett."email_template_group" = $1 
                AND ett."type" = $2`,
            [groupID, typeID]
        ).then(res => {
            return Boolean(res.rows[0]) && (Number(res.rows[0].count) === 1);
        });
    }

    _genUpdAdminTmplSQLQuery (templateID, data) {
        delete data.is_valid;

        let query = squel.update().table('email_template', 'et')
            .setFields(data)
            .where('et.event_owner_id IS NULL')
            .where('et.email_template_id = ?', templateID)
            .where('et.deleted IS NULL')
            /**
             * NOTE: maybe we need to use "v_aem_admin_templates" 
             * to check the availability of the template
             */
            .where(
                `et."email_template_type" = '${this.BASIC_LAYOUT_TYPE}'
                 OR EXISTS (
                    SELECT ett."type" 
                    FROM "email_template_type" ett 
                    WHERE et."email_template_type" = ett."type" 
                        AND et."email_template_group" = ett."email_template_group"
                 )`
            );

        query.set(
            `"is_valid" = (
                (et."email_template_type" = '${this.BASIC_LAYOUT_TYPE}') OR 
                (et."email_subject" IS NOT NULL AND et."email_subject" <> \'\')
            )`
        );

        return query;
    }

	updAdminTmpl (templateID, data) {
		return co(function* () {
			if (!Number.isInteger(templateID)) {
				return Promise.reject({ validation: 'Invalid template ID' });
			}

			let validation = EDIT_TMPL_SCHEMA.validate(data);

			if (validation.error) {
				loggers.errors_log.error(validation.error);
				return Promise.reject({ validationErrors: validation.error.details });
			}

			let prepared             = validation.value;
			    prepared.email_text  = this.stripHTML(prepared.email_html);
                prepared.unlayer_json    = JSON.stringify(prepared.unlayer_json);

            let sqlQuery = this._genUpdAdminTmplSQLQuery(templateID, prepared);

            return Db.query(sqlQuery).then(result => {
				if (result.rowCount === 0) {
					return Promise.reject({ validation: 'Template not found' });
				}
			})
		}.bind(this));
	}

    _validateGroupAndTypeExistance (groupID, typeID) {
        return co(function* () {
            let isGroupExist = yield (this._checkEmailGroupExists(groupID));

            if (!isGroupExist) {
                return Promise.reject({
                    validation: `Recipient Type ${groupID} does not exit.` 
                });
            }

            let isTypeExist = yield (this.checkTypeExistance(groupID, typeID));
                    
            if (!isTypeExist) {
                return Promise.reject({ validation: 'Type does not exist' });
            }
            
        }.bind(this));
    }

    /**
     * Create a Template (with Admin rights)
     * 
     * NOTE: "email_template_group" is "group", "email_template_id" is "template_id" before 
     *     transformations in the validator 
     *     
     * @param  {object} data                        - Credentials of a template to create
     * @param  {string} data.title                  - A title of the tempplate
     * @param  {string} data.email_template_group   - If a value is 'layout' (Basic Layout) - then 
     *                                              it's assumed to be a template's type. For any
     *                                              other value it's assumed to be 
     *                                              a template's group
     * @param  {string} data.email_template_id      - ID of the basic template to be inherited from
     * @return {Promise}
     */
	createAdminTemplate (data) {
		return co(function* () {
			let validation = CREATE_ADMIN_TMPL_SCHEMA.validate(data);

			if (validation.error) {
				loggers.errors_log.error(validation.error);
				return Promise.reject({ validationErrors: validation.error.details });
			}

			delete validation.value.visibility_scope;



			let prepared         = validation.value;
            let isBasicLayout    = (prepared.email_template_group === this.BASIC_LAYOUT_TYPE);

            /**
             * As stated above, for not basic layouts, a value in "email_template_group" is assumed
             * to be a template's group. 
             */
			if (!isBasicLayout) {
                if (!prepared.email_template_type) {
                    prepared.email_template_type = this.MANUAL_MAILING_TYPE;
                }

                let { email_template_group: _group, email_template_type: _type } = prepared;

				yield (this._validateGroupAndTypeExistance(_group, _type));
			} else {
                /**
                 * For Basic Layouts, a retrieved value in "emila_template_group" 
                 * is a template's type
                 */
                /**
                 * NOTE: "Basic Layout" has no relation to "email_template_type" table
                 */
				prepared.email_template_type = prepared.email_template_group;
				delete prepared.email_template_group;
			}


			let basicLayout = yield (this.getBasicLayout(prepared.email_template_id));

			if (basicLayout == null) {
				return Promise.reject({ validation: `Basic Layout not found` });
			}

			prepared.is_valid 				= false;
			prepared.unlayer_json 			= basicLayout.unlayer_json;
			prepared.email_html 			= basicLayout.email_html;
			prepared.published 				= false;
            /* Remove id of the basic layout */
			delete prepared.email_template_id;

			let created = yield (this._insertEmailTemplateRow(prepared));

			if (isBasicLayout) {
				created.type = {
					id 		: 'layout',
					title 	: 'Basic Layout'
				}
			}

			return created;
		}.bind(this))
	}

	deleteAdminTmpl (templateID) {
		return co(function* () {
			if (!Number.isInteger(templateID)) {
				return Promise.reject({ validation: 'Invalid template ID' });
			}

			let tmpl = yield (Db.query(this._ADMIN_TMPL_PREVIEW_SQL_QUERY, [templateID]).then(res => res.rows[0] || null));

			if (tmpl === null) {
				return Promise.reject({ validation: 'Template not found' });
			}

			if (!tmpl.type_title && (tmpl.email_template_type !== 'layout')) {
				return Promise.reject({ validation: 'Only Default or Basic Layouts allowed to be deleted' });
			}

			if (Number(tmpl.usage_qty) > 0) {
				return Promise.reject({ validation: 'Only not used templates are allowed to be deleted' });
			}

			yield (this._removeTemplate(templateID));
		}.bind(this));
	}

	assignTemplatesToTriggers (eventID, data) {
        return Promise.all(
            Object.keys(data).map( group => {

                return Promise.all(
                    Object.keys(data[group]).map( type => {
                        let templateID = data[group][type].email_template_id;

                        return this._upsertTriggerAssignment(templateID, eventID, type, group);
                    })
                )

            })
        );
    }

    getTemplatesAndTriggerTypes (eventID, eventOwnerID) {
        return Promise.all([
           this.triggers.getTriggerTypes(eventID),
           this.triggers.getTemplatesByGroupAndType(eventID, eventOwnerID)
        ]).then( result => {
            return {
                triggers : result[0],
                templates: result[1]
            }
        })
    }

    get EVENTS_SQL() {
        return `
            SELECT (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(data))), '[]'::JSON)
                FROM
                (
                    SELECT 
                        e.long_name, 
                        TO_CHAR(e.date_start, 'Mon DD') date_start,
                        e.event_id,
                        e.event_id = $5 "selected"
                    FROM event e
                    WHERE e.event_id = ANY ($1)
                    AND e.season = ANY ($2)
                    AND e.date_start IS NOT NULL
                    AND e.date_end IS NOT NULL
                    AND e.deleted IS NULL
                    AND is_test IS NOT TRUE
                    ORDER BY e.event_id = $5 DESC, e.date_start DESC
                ) "data"
            ) "events",
            (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(data))), '[]'::JSON)
                FROM
                (
                    SELECT g.group, g.title, NULL "event_id", NULL "list_id"
                    FROM email_template_group g
                    JOIN email_template_type t
                        ON t.email_template_group = g.group
                        AND t.type = 'content'
                    WHERE g.group <> 'newsletters'
                    AND (g.usage_restrictions->'roles') ? 'any'
                    
                    UNION ALL
                    
                    SELECT $3 "group", crl.title, crl.event_id, custom_recipients_list_id "list_id"
                    FROM custom_recipients_list crl
                    WHERE crl.event_id = ANY ($1) OR crl.event_id IS NULL AND crl.event_owner_id = ANY ($4)
                ) "data"
            ) "groups"
        `
    }

    getEmailTemplateGroup (templateID) {
	    return Db.query(
	        `SELECT et."email_template_group" "group" FROM email_template et WHERE et."email_template_id" = $1`,
            [templateID]
        ).then(result => result.rows[0] && result.rows[0].group || null);
    }

    __filterHiddenVariables (variables) {
        return variables && variables.filter(variable => !variable.is_hidden);
    }

    getEvents(events, currentSeason, eventOwnersList, currentEventId = null) {
	    const prevSeason = currentSeason - 1;
	    const seasons = [prevSeason, currentSeason];

        return Db.query(this.EVENTS_SQL, [events, seasons, this.CUSTOM_LIST_GROUP, eventOwnersList, currentEventId])
            .then(
                ({rows}) => {
                    let {events, groups} = rows[0];
                    groups = groups.reduce(
                        (result, row) => {
                            const virtualGroups = this.VIRTUAL_GROUPS_MAPPING[row.group];
                            if (virtualGroups) {
                                virtualGroups.forEach(group => {
                                    result.push({...row, ...group});
                                });
                            }
                            else {
                                result.push(row);
                            }
                            return result;
                        },
                        []
                    );
                    return {events, groups};
                }
            );
    }

    generateTemplateRows(eventIds, template, user) {
	    const rows = [];

        for (let i = 0; i < eventIds.length; i++) {
            const eventID       = eventIds[i];
            const eventOwnerID  = EventOwnerService.findId(eventID, user);

            if (!eventOwnerID) {
                throw { validation: `You don't have access to the Event` };
            }

            rows.push({
                event_id: eventID,
                event_owner_id: eventOwnerID,
                ...template
            });
        }

        return rows;
    }

    async __validateTemplateSubjectVariables (templateGroup, subject) {
        let variables = await this.getTemplateGroupVariables(templateGroup);

        VariablesTransformer.checkVariablesInTemplateSubject(subject, variables);
    }
}

module.exports = new AEMService();

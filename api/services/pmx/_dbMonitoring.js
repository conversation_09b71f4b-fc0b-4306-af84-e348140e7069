const pmx = require('pmx');
const probe = pmx.probe();
const PoolState = require('./_PoolState');

class DbMonitoring {
    get _DB_POOL_METRICS() {
        return [
            {
                key: 'total',
                name: `Total`,
            },
            {
                key: 'idle',
                name: `Idle`,
            },
            {
                key: 'waiting',
                name: `Waiting`,
            },
            {
                key: 'max',
                name: `<PERSON>`,
            },
        ];
    }

    constructor(id, db) {
        this.id = id;
        this.db = db;
        this._isPoolExtended = false;
        this._oldDbPoolSize = void 0;

        this._errors = {};


        if(PMXMonitoring.cfg.dbPool && PMXMonitoring.cfg.dbPool.enabled) {
            this._initMetrics();
            this._startPoolMonitoring();
        }

        if(PMXMonitoring.cfg.dbQueries && PMXMonitoring.cfg.dbQueries.enabled) {
            this._startQueriesMonitoring();
        }
    }

    _startPoolMonitoring() {
        this._errors.fullPool = {
            handle: handleError,
            sendNotification: PMXMonitoring.getSendNotificationFunc(
                `${this.id} DB pool is full`
            ),
            message: () => `${this.id} DB connections pool is full on ${PMXMonitoring.getInstanceId()} (size: ${this._metrics.max.val()}; waiting connections: ${this._metrics.waiting.val()})`,
        };
        this._errors.fullExtendedPool = {
            handle: handleError,
            sendNotification: PMXMonitoring.getSendNotificationFunc(
                `${this.id} DB extended pool is full`
            ),
            message: () => `${this.id} DB connections pool is full on ${PMXMonitoring.getInstanceId()} (extended size: ${this._metrics.max.val()}; waiting connections: ${this._metrics.waiting.val()})`,
        };

        this._poolState = new PoolState({
            debounceFull: PMXMonitoring.cfg.dbPool.increaseSize.delayBeforeFull,
            debounceFree: PMXMonitoring.cfg.dbPool.increaseSize.delayBeforeFree,
        });
        this._poolState.on(PoolState.EVENT_FULL, () => {
            this._errors.fullPool.handle();

            if(PMXMonitoring.cfg.dbPool.increaseSize.enabled) {
                this._extendPoolSize();
            }
        });
        this._poolState.on(PoolState.EVENT_FREE, () => {
            if(!PMXMonitoring.cfg.dbPool.increaseSize.enabled) {
                const message = `${this.id} DB connections pool is freed up on ${PMXMonitoring.getInstanceId()}`;
                loggers.debug_log.info(
                    message
                );
            }
        });
        this._poolState.on(PoolState.EVENT_EXTENDED_FULL, () => {
            this._errors.fullExtendedPool.handle();
        });
        this._poolState.on(PoolState.EVENT_CAN_RETURN, () => {
            this._returnDbPoolSize();
        });

        this.db.on('poolChange', () => {
            this._updatePoolMetrics();
            this._poolState.update(this._isPoolFull(), this._isPoolExtended, this._canReturnPoolSize(), this.db.poolStats());
        });
    }

    _updatePoolMetrics() {
        const poolStats = this.db.poolStats();
        for(const { key } of this._DB_POOL_METRICS) {
            this._metrics[key].set(poolStats[key]);
        }
    }

    _isPoolFull() {
        return (
            this._metrics.total.val() >= this._metrics.max.val()
            && this._metrics.waiting.val() > 0
        );
    }

    _canReturnPoolSize() {
        return (
            this._isPoolExtended
            && this._metrics.waiting.val() === 0
            && this._metrics.total.val() < this._oldDbPoolSize
        );
    }

    _extendPoolSize() {
        this._oldDbPoolSize = this.db.poolStats().max;
        const newSize = PMXMonitoring.cfg.dbPool.increaseSize.newSize(this._oldDbPoolSize, this.id);
        this.db.setPoolSize(newSize);
        this._isPoolExtended = true;
        loggers.debug_log.warn(`Increased ${this.id} DB connections pool size to ${newSize} on ${PMXMonitoring.getInstanceId()}`);
    }

    _returnDbPoolSize() {
        this.db.setPoolSize(this._oldDbPoolSize);
        this._isPoolExtended = false;
        const message = `${this.id} DB connections pool size returned to ${this._oldDbPoolSize} on ${PMXMonitoring.getInstanceId()}`;
        loggers.debug_log.warn(
            message
        );
        return this._oldDbPoolSize;
    }

    _startQueriesMonitoring() {
        const sqlDuration = probe.histogram({
            name: `${this.id} sql execution time (${PMXMonitoring.cfg.dbQueries.measurement})`,
            measurement: PMXMonitoring.cfg.dbQueries.measurement,
            unit: 'ms',
        });
        this.db.on(this.db.EVENT_AFTER_RUN_SQL, ({sql, duration}) => {
            sqlDuration.update(duration);
        });
    }

    _initMetrics() {
        const poolStats = this.db.poolStats();

        this._metrics = this._DB_POOL_METRICS.reduce(
            (r, options) => {
                const metric = probe.metric(
                    {
                        ..._.omit(options, ['key']),
                        value: poolStats[options.key] || 0,
                        name: `${this.id} ${options.name}`,
                    }
                );
                r[options.key] = metric;
                return r;
            },
            {}
        );
    }
}

module.exports = DbMonitoring;

function handleError() {
    const message = this.message();
    loggers.debug_log.warn(message);
    this.sendNotification(message)
        .catch(err => loggers.errors_log.error(err));
}

const moment = require('moment-timezone');

class BarcodeService {
    constructor() {}

    ACTIVATE_SQL({ ticketBarcode, historyLine }) {
        return knex('purchase').update({
            deactivated_at: null,
            deactivated_by_user_id: null,
            tickets_scan: knex.raw(`(concat_ws(chr(10), tickets_scan, (?)::TEXT))`, historyLine)
            })
            .where('ticket_barcode', ticketBarcode);
    }

    DEACTIVATE_SQL({ ticketBarcode, userID, historyLine }) {
        return knex('purchase').update({
            deactivated_at: knex.raw('NOW()'),
            deactivated_by_user_id: userID,
            tickets_scan: knex.raw(`(concat_ws(chr(10), tickets_scan, (?)::TEXT))`, historyLine)
            })
            .where('ticket_barcode', ticketBarcode);
    }

    async activate({ ticketBarcode, userName, activatedAt, reason, eventID }) {
        const timezone = await this.getEventTimezone(eventID);

        const historyLine = this.generateHistoryLine({
            action: 'Activated',
            reason: reason,
            datetime: activatedAt,
            username: userName,
            timezone
        });

        return Db.query(this.ACTIVATE_SQL({
            ticketBarcode,
            historyLine,
        })).then(() => {});
    }

    async deactivate({ ticketBarcode, userName, userID, deactivatedAt, reason, eventID }) {
        const timezone = await this.getEventTimezone(eventID);

        const historyLine = this.generateHistoryLine({
            action: 'Deactivated',
            reason: reason,
            datetime: deactivatedAt,
            username: userName,
            timezone
        });

        return Db.query(this.DEACTIVATE_SQL({
            ticketBarcode,
            userID,
            historyLine,
        })).then(() => {});
    }

    validateTicket({ action, deactivatedAt, status }) {
        if (action === TicketsService.ACTION.ACTIVATE && !deactivatedAt) {
            throw new Error('Ticket is already activated');
        }

        if (action === TicketsService.ACTION.DEACTIVATE && deactivatedAt) {
            throw new Error('Ticket is already deactivated');
        }

        if (status !== TicketsService.STATUS.PAID) {
            throw new Error('Ticket not paid');
        }
    }

    formatDate(datetime, timezone) {
    
        // Here come unix tsp, not need predefine moment date format
        const format = 'YYYY/MM/DD h:mm:ss a';
        return moment(datetime).utc().tz(timezone).format(format)
        
    }

    checkTicketDeactivateStatus(ticketBarcode) {
        const query = squel.select()
            .from('purchase', 'p')
            .field('1')
            .where('p.deactivated_at IS NOT NULL')
            .where('p.ticket_barcode = ?', ticketBarcode);

        return Db.query(query).then(response => response.rowCount !== 0);
    }

    generateHistoryLine({ reason, datetime, username, action, timezone }) {
        const _reason = reason ? `with reason "${reason}"` : '';

        return `${action} by "${username}" ${_reason} on "${this.formatDate(datetime, timezone)}" (EO acc)`
    }

    getDeactivateReason(ticketBarcode) {
        const query = squel.select()
            .from('purchase', 'p')
            .field(
                squel.select()
                    .from('purchase_history', 'ph')
                    .field('ph.notes')
                    .where('ph.purchase_id = p.purchase_id')
                    .where(`ph.action IN ('ticket.deactivated', 'ticket.activated')`)
                    .order('created', false)
                    .limit(1)
            , 'reason')
            .where('p.ticket_barcode = ?', ticketBarcode);

        return Db.query(query).then(({ rows }) => rows[0]) ;
    }

    formatDeactivateReason(reason) {
        return reason ? `with reason "${reason}"` : '';
    }

    getEventTimezone(eventID) {
        const query = squel.select()
            .from('event')
            .field('timezone')
            .where('event_id = ?', eventID);

        return Db.query(query).then(({ rows }) => rows[0].timezone);
    }
}

module.exports = new BarcodeService();

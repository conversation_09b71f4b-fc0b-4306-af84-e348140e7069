const { FEE_PAYER } = require('../../../constants/payments');
const TilledFeeService = require('../../TilledFeeService');
const AbstractSWTFeeStrategy = require('./_AbstractSWTFeeStrategy');

class TilledSWTFeeStrategy extends AbstractSWTFeeStrategy {
    __calculateCardFee(amount) {
        const { tilled_percentage, tilled_fixed } = this.settings;

        return {
            defaultFee: TilledFeeService.calculateCardFee(amount, tilled_percentage, tilled_fixed),
            customerFee: TilledFeeService.calculateCardCustomerFee(amount, tilled_percentage, tilled_fixed),
        };
    }

    __isBuyerPayingProviderFee() {
        return this.settings.tilled_fee_payer === FEE_PAYER.BUYER
    }
    
    __doesPlatformCoverProviderFee() {
        return false
    }
}

module.exports = TilledSWTFeeStrategy;

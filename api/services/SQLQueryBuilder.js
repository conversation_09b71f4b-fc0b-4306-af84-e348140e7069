'use strict';

const EventOfficial 	= require('./query-builder/_EventOfficialBuilder');
const TicketsCustomer	= require('./query-builder/_TicketsCustomerBuilder');
const Camps             = require('./query-builder/_CampsBuilder');
const HeadReferee       = require('./query-builder/_HeadRefereeBuilder');
const TeamsPayment      = require('./query-builder/_TeamsPaymentBuilder');
const EventStaff        = require('./query-builder/_EventStaffBuilder');
const EventOwners       = require('./query-builder/_EventOwnersBuilder');
const CustomRecipients  = require('./query-builder/_CustomRecipientsList');
const TicketsRefunds    = require('./query-builder/_TicketsRefundBuilder');
const CommonQueryParts  = require('./query-builder/_CommonQueryParts');
const Exhibitors        = require('./query-builder/_ExhibitorsBuilder');
const BoothPayments     = require('./query-builder/_BoothPaymentsBuilder');
const TicketPayments    = require('./query-builder/_TicketsPaymentBuilder');
const Clubs             = require("./query-builder/_ClubsBuilder");
const ClubDirectors       = require('./query-builder/_ClubDirectorsBuilder');
const TeamsRefunds = require('./query-builder/_TeamsRefundBuilder');
const TeamsUncollectedFeePayments = require('./query-builder/_TeamsUncollectedFeePaymentsBuilder');
const TicketsUncollectedFeePayments = require('./query-builder/_TicketsUncollectedFeePaymentsBuilder');
const UpcomingUncollectedFeePayments = require('./query-builder/_UpcomingUncollectedFeePaymentsBuilder');

const eventOfficial = new EventOfficial(CommonQueryParts);
const ticketsCustomer = new TicketsCustomer(CommonQueryParts);
const camps = new Camps(CommonQueryParts);
const eventStaff = new EventStaff(CommonQueryParts);
const customRecipients = new CustomRecipients(CommonQueryParts);
const exhibitors = new Exhibitors();
const boothsPayments = new BoothPayments();
const ticketPayments = TicketPayments;
const clubs = new Clubs(CommonQueryParts);

module.exports = {
    eventOfficial,
    ticketsCustomer,
    camps,
    headReferee: HeadReferee,
    teamsPayment: TeamsPayment,
    eventStaff,
    eventOwners: EventOwners,
    customRecipients,
    ticketsRefunds: TicketsRefunds,
    exhibitors,
    boothsPayments,
    ticketPayments,
    clubs,
    clubDirectors: ClubDirectors,
    teamsRefunds: TeamsRefunds,
    teamsUncollectedFeePayments: TeamsUncollectedFeePayments,
    ticketsUncollectedFeePayments: TicketsUncollectedFeePayments,
    upcomingUncollectedFeePayments: UpcomingUncollectedFeePayments,
};

'use strict';

const co = require('co');

class USAVClubService {
    constructor () {}

    getClubData (clubID, eventID) {
        if(!clubID) {
            return Promise.reject({ validation: 'Club ID Required'});
        }

        if(!eventID) {
            return Promise.reject({ validation: 'Event ID Required'});
        }

        return co(function* () {
            return yield ({
                club    : this.__getClubData__(clubID, eventID),
                teams   : this.__getTeamsData__(clubID, eventID)
            })
        }.bind(this));
    }

    __getClubData__ (clubID, eventID) {
        let query = squel.select().from('roster_club', 'rc')
            .field('rc.club_name')
            .field('rc.zip')
            .field('rc.state')
            .field('rc.address')
            .field('rc.city')
            .field('rc.country', 'country_name')
            .field('rc.code')
            .field('rc.region', 'region_name')
            .field('mc.director_first')
            .field('mc.director_last')
            .field('mc.director_email')
            .field('mc.director_phone')
            .left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .where('rc.event_id = ?', eventID)
            .where('rc.roster_club_id = ?', clubID);

        return Db.query(query).then(result => result.rows[0] || null);
    }

    __getTeamsData__ (clubID, eventID) {
        let query = squel.select().from('roster_team', 'rt')
            .field('rt.team_name')
            .field('rt.organization_code', 'team_usav')
            .field('d.short_name', "division_name")
            .field(
                `(SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("s"))), '[]'::JSON)
                FROM (
                    SELECT DISTINCT ms.master_staff_id, COALESCE(r.name, 'N/A') "role_name",
                        (ms.first || ' ' || ms.last) "name", ms.gender, ms.email, ms.master_staff_id "id",
                        replace(COALESCE(ms.phone, ms.phonew, ms.phoneh, ms.phoneo), '-', '') AS phone,
                        ms.organization_code "staff_usav"
                    FROM master_staff_role msr
                    LEFT JOIN master_staff ms
                        ON msr.master_staff_id = ms.master_staff_id
                        AND ms.deleted IS NULL
                    LEFT JOIN role r
                        ON msr.role_id = r.role_id
                    WHERE msr.master_team_id = mt.master_team_id
                ) "s")`
            , 'staff')
            .field(
                `(SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("a"))), '[]'::JSON)
                FROM (
                    SELECT (ma.first || ' ' || ma.last) "name", ma.email, ma.gender,
                       COALESCE(sp.short_name, 'N/A') "position", '#' || COALESCE(ra.jersey, ma.jersey) "uniform",
                       ma.organization_code "athlete_usav", 
                       replace(COALESCE(ma.phonem, ma.phoneh, ma.phonep), '-', '') AS phone
                    FROM roster_athlete ra
                    LEFT JOIN master_athlete ma
                        ON ma.master_athlete_id = ra.master_athlete_id
                    LEFT JOIN sport_position sp
                        ON ma.sport_position_id = sp.sport_position_id
                    WHERE ra.roster_team_id = rt.roster_team_id
                        AND ra.deleted IS NULL 
                        AND ra.deleted_by_user IS NULL
                        AND ra.event_id = rt.event_id
                ) "a")`
            , 'athletes')
            .left_join('division', 'd', 'd.division_id = rt.division_id')
            .left_join('master_team', 'mt', 'mt.master_team_id = rt.master_team_id')
            .where('rt.roster_club_id = ?', clubID)
            .where('rt.event_id = ?', eventID)
            .where('rt.deleted IS NULL');

        return Db.query(query).then(result => result.rows);
    }
}

module.exports = new USAVClubService();

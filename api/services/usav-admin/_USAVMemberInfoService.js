'use strict';

class USAVMemberInfoService {
    constructor() {}

    get TABLES() {
        return {
            'roster_athlete': this.selectAthleteInfo,
            'roster_staff_role': this.selectStaffInfo,
        };
    }

    formatResponse(response) {
        const fields = ['member', 'team', 'club'];
        const result = {};

        response.forEach((item, index) => result[fields[index]] = item.rows[0]);

        return result;
    }

    selectAthleteInfo(id) {
        return squel.select()
            .from('master_athlete', 'ma')
            .field('ma.first')
            .field('ma.last')
            .field('ma.gradyear')
            .field(`'#' || COALESCE(ra.jersey, ma.jersey)`, 'uniform')
            .field('organization_code', 'usav_code')
            .field(`'athlete'`, 'role')
            .left_join(
                'roster_athlete', 'ra',
                `ma.master_athlete_id = ra.master_athlete_id
                 AND ra.deleted is NULL
                 AND ra.deleted_by_user is NULL`
            )
            .where('ra.roster_athlete_id = ?', id);
    }

    selectStaffInfo(id) {
        return squel.select()
            .from('roster_staff_role', 'rsr')
            .field('ms.first')
            .field('ms.last')
            .field('ms.organization_code', 'usav_code')
            .field('r.name', 'role')
            .field('COALESCE(rsr.primary, msr.primary)', 'primary')
            .left_join('master_staff', 'ms', 'ms.master_staff_id = rsr.master_staff_id')
            .left_join('master_staff_role', 'msr', 'msr.master_staff_id = ms.master_staff_id')
            .left_join('role', 'r', 'COALESCE(msr.role_id, rsr.role_id) = r.role_id')
            .where(`rsr.roster_staff_role_id = ?
                    AND rsr.deleted is NULL
                    AND rsr.deleted_by_user is NULL`, id);
    }

    selectMemberInfo(id, table) {
        return this.TABLES[table](id);
    }

    selectTeamInfo(id, table) {
        return squel.select()
            .from('roster_team', 'rt')
            .field('rt.organization_code', 'usav_code')
            .field('rt.team_name', 'name')
            .field('d.name', 'division')
            .left_join('division', 'd', 'rt.division_id = d.division_id')
            .left_join(
                table, 'tn',
                `tn.roster_team_id = rt.roster_team_id
                 AND rt.deleted is NULL`
            )
            .where(`tn.${table}_id = ?`, id);
    }

    selectClubInfo(id, table) {
        return squel.select()
            .from(table, 'tn')
            .field('rc.club_name', 'name')
            .field('rc.address')
            .field('rc.city')
            .field('rc.state')
            .field('rc.zip')
            .field('c.name', 'country')
            .field('rc.code', 'usav_code')
            .field('r.name', 'region')
            .field(`mc.director_first || ' ' || mc.director_last`, 'director_name')
            .field('mc.director_email')
            .left_join(
                'roster_team', 'rt',
                `tn.roster_team_id = rt.roster_team_id
                 AND rt.deleted is NULL`
            )
            .left_join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id')
            .left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .left_join('region', 'r', 'r.region = rc.region')
            .left_join('country', 'c', 'c.code = rc.country')
            .where(`tn.${table}_id = ?`, id);
    }

    /**
     * @param id - id of the requested member
     * @param table - table name of the requested member
     */

    getInfo(id, table) {
        const isAllow = Object.keys(this.TABLES).some(t => t === table);

        if (!isAllow) {
            return Promise.reject({ validation: 'Wrong field name for member!'})
        }

        return Promise.all([
            Db.query(this.selectMemberInfo(id, table)),
            Db.query(this.selectTeamInfo(id, table)),
            Db.query(this.selectClubInfo(id, table)),
        ])
        .then(this.formatResponse)

    }
}

module.exports = new USAVMemberInfoService();

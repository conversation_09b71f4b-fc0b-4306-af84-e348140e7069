'use strict';

const co = require('co');

class TryoutService {
    constructor () {}

    get NOT_ASSIGNED () {
        return 'N/A';
    }

    get SELECT_FIELD_TYPE () {
        return 'select';
    }

    /**
     * Values of these fields are computed on the basis of entered data by a purchaser.
     * We do not store in the DB neither such fields nor their values.
     */
    get COMPUTED_EVENT_FIELDS () {
        return [{
            field: 'age',
            label: 'Age',
            type: 'computed'
        }]
    }

    getCampAthletes (eventID, campID) {
        /**
         * Comments on "barcode" column formatting: 
         * https://www.postgresql.org/docs/9.6/static/functions-formatting.html#FUNCTIONS-FORMATTING-NUMERIC-TABLE
         *
         * FM prefix means: fill mode (suppress trailing zeroes and padding blanks)
         * "0" specifies a digit position that will always be printed, even if it contains a leading/trailing zero. 
         * "9" also specifies a digit position, but if it is a leading zero then it will be replaced by a space, 
         *     while if it is a trailing zero and fill mode (FM) is specified then it will be deleted.
         */
        let query =
            `SELECT
              e.event_id "event", COALESCE((
                SELECT JSON_AGG(JSON_BUILD_OBJECT(
                    'field', "fields"->>'field', 
                    'label', "fields"->>'label', 
                    'type', "fields"->>'type', 
                    'options', "fields"->'options'
                )) 
                FROM JSONB_ARRAY_ELEMENTS(e.tickets_purchase_additional_fields) "fields"
                WHERE ("fields"->'show_on'->>'purchase')::BOOLEAN IS TRUE
             ), '[]'::JSON) "fields", (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("athlete"))), '{}' :: JSON)
                FROM (
                    SELECT
                        TO_CHAR(p.ticket_barcode::INT, 'FM000-000-000') "barcode",
                        COALESCE(p.tickets_additional, '{}' :: JSON)::JSONB || JSONB_BUILD_OBJECT('age', v_age."age") "data", (
                          SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("c"))), '{}' :: JSON)
                          FROM (SELECT et."event_camp_id" "camp_id") c
                        ) "camps"
                    FROM purchase p
                    INNER JOIN purchase_ticket pt
                        ON pt.purchase_id = p.purchase_id
                        AND pt.canceled IS NULL
                    INNER JOIN event_ticket et
                        ON et.event_ticket_id = pt.event_ticket_id
                    JOIN "v_swt_participant_age" v_age 
                        ON v_age."purchase_id" = p."purchase_id"
                        AND v_age."event_camp_id" = et."event_camp_id"
                        AND v_age."age_type" = 'camp'
                    WHERE p.event_id = e.event_id
                        AND p.canceled_date IS NULL
                        AND p.payment_for = 'tickets'
                        AND p.is_ticket IS TRUE
                        AND p.type IS DISTINCT FROM $3
                        AND p.status = 'paid'
                        AND et.event_camp_id = $2
              ) "athlete"
            ) "athletes"
            FROM event "e"
            WHERE e.event_id = $1`;

        return co(function* () {
            if(!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            if(!campID) {
                throw { validation: 'Invalid Camp Identifier' };
            }

            const WL = SWTPaymentsService.WAITLIST_METHOD;

            let data = yield Db.query(query, [eventID, campID, WL])
                .then(result => result.rows[0] || null);

            if (data == null) {
                throw { validation: 'Event not found!' }
            }

            data.fields = data.fields.concat(this.COMPUTED_EVENT_FIELDS);

            let formattedData = this.__formatCampsResponse(data.athletes, data.fields);

            return {
                event   : data.event,
                athletes: formattedData,
            }

        }.bind(this))
    }

    __formatCampsResponse (athletes, athleteInfoFieldsList) {
        if (_.isEmpty(athletes)) {
            return athletes;
        }

        let __formatAthleteInfo = athleteInfo => {
            return athleteInfoFieldsList.reduce((formattedInfo, field) => {
                let { field: propName, label, type, options } = field;

                let value = athleteInfo[propName];

                /**
                 * NOTE: same logic is implemented in views/tickets/receipt.ejs
                 */
                value = (type === this.SELECT_FIELD_TYPE && !_.isEmpty(options)) 
                                ? options[value] 
                                : value

                formattedInfo[label] = value || this.NOT_ASSIGNED;

                return formattedInfo;
            }, {});
        };

        let __formatCampsRespObj = athleteCamps => {
            return athleteCamps.reduce((camps, { camp_id }) => {
                camps[camp_id] = true;
                return camps;
            }, {});
        }

        let result = {};

        athletes.forEach(athlete => {

            let needsFormatting = (!_.isEmpty(athlete.data) && athleteInfoFieldsList.length);

            result[athlete.barcode] = {
                data    : needsFormatting ? __formatAthleteInfo(athlete.data) : {},
                camps   : __formatCampsRespObj(athlete.camps)
            }
        });

        return result;
    }
}

module.exports = new TryoutService();

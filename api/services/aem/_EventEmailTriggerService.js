'use strict';

const TriggerEmailTemplateService = require('./_TriggerEmailTemplateService');

/**
 * Works with assignments of templates to certain event's actions
 * 
 * @param {object} AEMService
 */
function EventEmailTriggerService (AEMService) {
    this._AEMService = AEMService;
}

Object.defineProperty(EventEmailTriggerService.prototype, 'DEFAULT_EVENT_ID', {
    value           : 0,
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'APPLIED_ACTION', {
    value           : 'applied',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'ACCEPTED_ACTION', {
    value           : 'accepted',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'WAITLISTED_ACTION', {
    value           : 'waitlisted',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'DECLINED_ACTION', {
    value           : 'declined',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'WITHDREW_ACTION', {
    value           : 'withdrew',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'TEAMS_PAYMENT_ACH_CANCELED_ACTION', {
    value           : 'teams.ach.canceled',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'TEAMS_PAYMENT_PENDING_ACTION', {
    value           : 'teams.any.pending',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'TEAMS_PAYMENT_SUCCESSFUL_ACTION', {
    value           : 'teams.any.paid',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'CAMPS_REGISTRATION_WAITLISTED_ACTION', {
    value           : 'waitlist.registration',
    writable        : false,
    configurable    : false
});

Object.defineProperty(EventEmailTriggerService.prototype, 'CAMPS_PARTICIPATION_CANCELLATION_ACTION', {
    value           : 'participation.canceled',
    writable        : false,
    configurable    : false
});

EventEmailTriggerService.prototype.getRoleTmplTypeForWorkStatus = function (group, workStatus) {

    if(![AEMService.TRIGGER_ROLE_PREFIX.OFFICIAL, AEMService.TRIGGER_ROLE_PREFIX.STAFF].includes(group)) {
        return null;
    }

    let prefix = group + '.';

    switch (workStatus) {
        case 'enter':
            return prefix + this.APPLIED_ACTION;
        case 'approved': 
            return prefix + this.ACCEPTED_ACTION;
        case 'waitlisted': 
            return prefix + this.WAITLISTED_ACTION;
        case 'declined': 
            return prefix + this.DECLINED_ACTION;
        case 'withdrew': 
            return prefix + this.WITHDREW_ACTION;
        default:
            return null;
    }
}

EventEmailTriggerService.prototype.getPaymentsTmplForPaymentType = function (payment_for, type, status) {
    if(payment_for ==='teams' && type === 'ach' && status === 'canceled') {
        return this.TEAMS_PAYMENT_ACH_CANCELED_ACTION;
    }

    if(payment_for ==='teams' && type === 'any' && status === 'pending') {
        return this.TEAMS_PAYMENT_PENDING_ACTION;
    }

    if(payment_for ==='teams' && type === 'any' && status === 'paid') {
        return this.TEAMS_PAYMENT_SUCCESSFUL_ACTION;
    }

    return null;
}

EventEmailTriggerService.prototype.getTemplate = function (group, type, eventID) {
    if (!_.isString(group)) {
        return Promise.reject({ validation: 'Invalid Group' });
    }

    if (!_.isString(type)) {
        return Promise.reject({ validation: 'Invalid Type' });
    }

    if (!_.isNumber(eventID) || eventID <= 0) {
        return Promise.reject({ validation: 'Invalid Event ID' });
    }

    return Db.query(
        `SELECT
            et."email_template_id" "id",
            (tmpl_trigger."event_id" = $4) "is_default",
        
            et."email_subject" "subject",
            et."bee_json",
            et."email_template_type" "type",
            et."email_template_group" "group",
            et."email_html" "html",
            et."email_text" "text"
        FROM "event_email_trigger" tmpl_trigger 
        INNER JOIN "email_template" et 
            ON et."email_template_id"       = tmpl_trigger."email_template_id" 
            AND et."email_template_group"   = tmpl_trigger."email_template_group" 
            AND et."email_template_type"    = tmpl_trigger."email_template_type"
            AND et."is_valid" IS TRUE
            AND et."deleted" IS NULL
            AND et."published" IS TRUE
        INNER JOIN "email_template_type" ett 
            ON ett."type"                   = tmpl_trigger."email_template_type"
            AND ett."email_template_group"  = tmpl_trigger."email_template_group"
            AND ett."is_trigger" IS TRUE
        WHERE tmpl_trigger."email_template_group"   = $1 
            AND tmpl_trigger."email_template_type"  = $2 
            AND tmpl_trigger."event_id" IN ($4, $3)
        ORDER BY tmpl_trigger."event_id" DESC
        LIMIT 1`,
        [group, type, eventID, this.DEFAULT_EVENT_ID]
    ).then(res => res.rows[0] || null)
    .then(template => TriggerEmailTemplateService.getTemplateCC(template, eventID));
}

EventEmailTriggerService.prototype.getTriggerTypes = function (eventID) {

    if (!_.isNumber(eventID) || eventID <= 0) {
        return Promise.reject({ validation: 'Invalid Event ID' });
    }

    return Db.query(
        `SELECT * FROM (
              SELECT
              etg."group" "group_name", (
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("tmpl_type")))
                FROM (
                  SELECT
                    DISTINCT ON (ett."type")
                    ett.title,    
                    (tr."event_id" = 0) "is_default",
                    ett."type" "type_name",
                    tr."email_template_id",
                    tr."event_id"
                  FROM "email_template_type" ett
                  LEFT JOIN "event_email_trigger" tr
                    ON  tr."email_template_group" = ett."email_template_group"
                    AND tr."email_template_type"  = ett."type"
                    AND tr."event_id" IN ($2, $1)
                  LEFT JOIN "event" e ON e.event_id = $1
                  WHERE etg."group" = ett."email_template_group"
                    AND ett.is_trigger IS TRUE
                    AND NOT (etg.group = 'camps' AND e.ticket_camps_registration IS FALSE)
                  ORDER BY ett."type", tr."event_id" DESC NULLS LAST
                ) "tmpl_type"
              ) "types"
            FROM "email_template_group" etg
            WHERE (etg.usage_restrictions->'roles') ? 'any'
            GROUP BY etg."group"
            ) AS all_data WHERE all_data.types is not null`,
        [eventID, this.DEFAULT_EVENT_ID]
    ).then( result => {

        if(!_.isEmpty(result.rows)) {
            return this.__formatArrayToObject(result.rows);
        }

        return result.rows;
    })
}

EventEmailTriggerService.prototype.getTemplatesByGroupAndType = function (eventID, eventOwnerID) {

    if (!_.isNumber(eventID) || eventID <= 0) {
        return Promise.reject({ validation: 'Invalid Event ID' });
    }

    if (!_.isNumber(eventOwnerID) || eventOwnerID <= 0) {
        return Promise.reject({ validation: 'Invalid Event Owner ID' });
    }

    return Db.query(
        `SELECT * FROM (
          SELECT etg.group "group_name",
          (
            SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("tmpl_types")))
              FROM (
                SELECT ett.type "type_name",
                  (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("templates")))
                    FROM (
                      SELECT et.email_template_id, et.title,
                        (et.email_template_id = ett.default_email_template_id) is_default
                      FROM email_template et
                      WHERE (et.event_id = $1 OR et.event_id IS NULL)
                        AND (
                            et.event_owner_id = $2 
                            OR et.event_owner_id = $3
                            OR et.event_owner_id IS NULL
                        )
                        AND et.deleted IS NULL
                        AND et.email_template_type = ett.type
                        AND et.email_template_group = etg."group"
                        AND et.published IS TRUE
                        AND et.is_valid IS TRUE
                    ) templates
                  ) templates
                FROM email_template_type ett
                WHERE ett.email_template_group = etg.group
                  AND ett.is_trigger IS TRUE
                  AND (etg.usage_restrictions->'roles') ? 'any'
                  AND NOT (etg.group = 'camps' AND e.ticket_camps_registration IS FALSE)
                ORDER BY ett.type
              ) tmpl_types
          ) "types"
        FROM email_template_group etg
        LEFT JOIN "event" e ON e.event_id = $1
        ) all_data WHERE all_data."types" IS NOT NULL`,
        [eventID, eventOwnerID, this._AEMService.COMMON_TMPL_EO_ID]
    ).then( result => {

        if(!_.isEmpty(result.rows)) {
            let addTmplsToResult = true;
            return this.__formatArrayToObject(result.rows, addTmplsToResult);
        }

        return result.rows;
    })
}

EventEmailTriggerService.prototype.__formatArrayToObject = function (data, addTmplsToResult) {
    let result = {};

    data.forEach(group => {

        let { group_name: grName, types: grTypes } = group;

        if (!(grName && grTypes && grTypes.length)) {
            return;
        }

        let grResult = result[grName] || (result[grName] = {});

        grTypes.forEach(type => {

            let { type_name: tName, templates } = type;

            if (addTmplsToResult) {

                if (templates && templates.length) {
                    let tResult = grResult[tName] || (grResult[tName] = {});

                    templates.forEach(tmpl => {
                        let tmplID = tmpl.email_template_id;

                        if (tmplID) {
                            tResult[tmplID] = tmpl;
                        }
                    })
                }
                

            } else {
                grResult[tName] = type;
            }

        });
    });

    return result;
};

module.exports = EventEmailTriggerService;




 

const {
    apiUrl: JUSTIFI_API_URL,
    clientId,
    clientSecret,
} = sails.config.justifi;

const NUMBER_REG_EXP = '^[0-9]+$';
const DESC_ALLOWED_LETTERS = '^[a-zA-Z0-9()_\\-!#$%^&,.|\\][]+$';

class JustifiError extends Error {
    constructor(message, status, code = null, path = null) {
        super(message);
        this.code = code;
        this.path = path;
        this.status = status;
        this.name = 'JustifiError';
    }
}
class JustifiService {
    _accessToken = null;
    _expiresAt = 0; // 0 → expired

    get FEE_PERCENTAGE() {
        return 0.029;
    }

    get FEE_FIXED() {
        return 30;
    }

    static TOKEN_TTL = 12 * 60 * 60 * 1_000;

    get PAYMENT_METHODS() {
        return {
            CARD: 'card',
            ACH: 'bank_account',
        };
    }

    get CODE_ERRORS() {
        return {
            NAME_ALREADY_TAKEN: 'name_already_taken',
        };
    }

    calculateDefaultFee(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        if (amount === 0) {
            return 0;
        }

        return Math.round(amount * feePercentage + feeFixed);
    }

    calculateTotalIncludingFees(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        if (amount === 0) {
            return 0;
        }

        return swUtils.normalizeNumber(
            (amount + feeFixed) / (1 - feePercentage)
        );
    }

    calculateCustomerFee(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        return (
            this.calculateTotalIncludingFees(amount, feePercentage, feeFixed) -
            amount
        );
    }

    async upsertSubAccount({ businessId, eventOwnerId }) {
        const subAccount = await this.getSubAccountByBusinessId({ businessId });

        const business = await this.getBusinessById({ businessId });

        const data = {
            id_at_justifi: subAccount.subAccountId,
            email: business.email,
            name: business.legalName,
            is_test: this.#isTestEnvironment(),
            hidden: false,
            event_owner_id: eventOwnerId,
        };

        return this.#saveJustifiSubAccount(data);
    }

    async #saveJustifiSubAccount(data) {
        return Db.query(
            knex('justifi_sub_account')
                .insert(data)
                .onConflict('id_at_justifi')
                .merge({
                    email: data.email,
                    name: data.name,
                    is_test: data.is_test,
                    hidden: data.hidden,
                })
                .returning('*')
        ).then((res) => {
            const row = res.rows[0];

            if (row.event_owner_id !== data.event_owner_id) {
                throw {
                    validation:
                        'Specified Justifi sub-account already exists for another Event Owner',
                };
            }

            return row;
        });
    }

    statementDescriptorIsNotValid(statementDescriptor) {
        if (typeof statementDescriptor !== 'string') {
            return;
        }

        let doesNotContainsForbiddenLetters = new RegExp(
            DESC_ALLOWED_LETTERS
        ).test(statementDescriptor);
        let containsOnlyNumbers = new RegExp(NUMBER_REG_EXP).test(
            statementDescriptor
        );

        return !doesNotContainsForbiddenLetters || containsOnlyNumbers;
    }

    async getSubAccountByBusinessId({ businessId }) {
        const response = await this._makeRequest(
            `/v1/sub_accounts?business_id=${businessId}`,
            'GET'
        );

        if (!response.data || response.data.length === 0) {
            throw new JustifiError('Sub-account not found', 404);
        }

        const subAccount = response.data[0];

        return {
            subAccountId: subAccount.id,
            businessId: subAccount.business_id,
            email: subAccount.email,
            name: subAccount.name,
        };
    }

    async getBusinessById({ businessId }) {
        const response = await this._makeRequest(
            `/v1/entities/business/${businessId}`,
            'GET'
        );

        return {
            businessId: response.data.id,
            legalName: response.data.legal_name,
            phone: response.data.phone,
            email: response.data.email,
        };
    }

    async createCheckout({
        subAccountId,
        amount,
        statementDescriptor,
        description,
    }) {
        const response = await this._makeRequest(
            '/v1/checkouts',
            'POST',
            {
                amount,
                statement_descriptor: statementDescriptor,
                description,
            },
            {
                'Sub-Account': subAccountId,
            }
        );

        const checkoutWebToken = await this.#createWebTokenForCheckout({
            checkoutId: response.data.id,
            subAccountId,
        });

        return {
            checkoutId: response.data.id,
            token: checkoutWebToken,
        };
    }

    async updateCheckout({ checkoutId, amount, applicationFee }) {
        const response = await this._makeRequest(
            `/v1/checkouts/${checkoutId}`,
            'PATCH',
            {
                amount,
                application_fees: {
                    card: {
                        amount: applicationFee,
                    },
                },
            }
        );

        const checkoutWebToken = await this.#createWebTokenForCheckout({
            checkoutId: response.data.id,
            subAccountId: response.data.account_id,
        });

        return {
            checkoutId: response.data.id,
            token: checkoutWebToken,
        };
    }

    async createBusiness(
        { email, legalName },
        retryOnDuplicateLegalName = true
    ) {
        try {
            const response = await this._makeRequest(
                '/v1/entities/business',
                'POST',
                {
                    email,
                    legal_name: legalName,
                }
            );

            return {
                businessId: response.data.id,
            };
        } catch (err) {
            if (
                err instanceof JustifiError &&
                err.code === this.CODE_ERRORS.NAME_ALREADY_TAKEN &&
                retryOnDuplicateLegalName
            ) {
                const newLegalName = this.#makeLegalNameUnique(legalName);

                return this.createBusiness(
                    { email, legalName: newLegalName },
                    false
                );
            }
        }
    }

    #makeLegalNameUnique(legalName) {
        return `${legalName} ${Math.floor(Math.random() * 1000)}`;
    }

    async createWebTokenForBusiness({ businessId }) {
        const response = await this._makeRequest(
            '/v1/web_component_tokens',
            'POST',
            {
                resources: [`write:business:${businessId}`],
            }
        );

        return response.access_token;
    }

    async #createWebTokenForCheckout({ checkoutId, subAccountId }) {
        const response = await this._makeRequest(
            '/v1/web_component_tokens',
            'POST',
            {
                resources: [
                    `write:checkout:${checkoutId}`,
                    `write:tokenize:${subAccountId}`,
                ],
            }
        );

        return response.access_token;
    }

    async getAccessToken(forceRefresh = false) {
        const now = Date.now();
        if (!forceRefresh && this._accessToken && now < this._expiresAt) {
            return this._accessToken;
        }

        const res = await fetch(`${JUSTIFI_API_URL}/oauth/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                client_id: clientId,
                client_secret: clientSecret,
            }),
        });

        if (!res.ok) {
            const errBody = await res.text().catch(() => '');
            throw new JustifiError(
                `Justifi auth failed (${res.status}): ${
                    errBody || res.statusText
                }`,
                res.status
            );
        }

        const { access_token } = await res.json();
        if (!access_token) {
            throw new Error('Justifi auth: empty access_token received');
        }

        this._accessToken = access_token;
        this._expiresAt = now + JustifiService.TOKEN_TTL;

        return this._accessToken;
    }

    async _makeRequest(path, method = 'GET', body = null, headers = {}) {
        const execute = async () => {
            const token = await this.getAccessToken();
            return fetch(`${JUSTIFI_API_URL}${path}`, {
                method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    ...headers,
                },
                body: body !== null ? JSON.stringify(body) : undefined,
            });
        };

        let res = await execute();

        if (res.status === 401) {
            await this.getAccessToken(true); // force refresh

            res = await execute();

            if (res.status === 401) {
                const errPayload = await this._extractErrorPayload(res);
                throw new JustifiError(
                    `Unauthorized after token refresh: ${errPayload.message}`,
                    401,
                    errPayload.code,
                    errPayload.path
                );
            }
        }

        if (!res.ok) {
            const errPayload = await this._extractErrorPayload(res);
            throw new JustifiError(
                errPayload.message,
                res.status,
                errPayload.code,
                errPayload.path
            );
        }

        return res.json();
    }

    async _extractErrorPayload(res) {
        try {
            const json = await res.clone().json();
            const err = json.error || json;
            return {
                message: err.message || res.statusText,
                code: err.code ?? null,
                path: err.param ?? null,
            };
        } catch (_) {
            const text = await res.text().catch(() => '');
            return { message: text || res.statusText, code: null, path: null };
        }
    }

    #isTestEnvironment() {
        return clientId.startsWith('test_');
    }
}

module.exports = new JustifiService();

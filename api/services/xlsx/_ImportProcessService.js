const spawn = require('node:child_process').spawn;
const path = require('node:path');

const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

const { USAV_SANC_BODY } = require('../../constants/teams');
const { US_COUNTRY_CODE, CA_COUNTRY_CODE } = require('../../constants/common');

class ImportProcessService {
    #IMPORTER_PATH = path.resolve(__dirname, '..', '..', '..', 'sw-utils');

    UPLOAD_DIR = path.resolve(__dirname, '..', '..', '..', '..', 'uploads/rosterImport');

    async run($masterClubId, $clubOwnerId, fileStream, season) {
        const { code, region, country, male, female, coed } = await this._getClubDataForImport($masterClubId, $clubOwnerId);

        this._validateClubData(code, region, country);

        const genders = this._getClubGenders(male, female, coed);

        return new Promise((resolve, reject) => {
            let errorMsg = '';

            const proc = spawn('node', [
                /**
                 * from "node --v8-options":
                 * --max_old_space_size -  max size of the old space (in Mbytes). sets the
                 *     maximum size of the old object heap
                 */
                '--max_old_space_size=50',
                'club_roster_import.js',
                `--club=${$masterClubId}`,
                `--owner=${$clubOwnerId}`,
                `--code=${code}`,
                `--region=${region}`,
                `--country=${country}`,
                `--season=${season}`,
                `--path=${this.UPLOAD_DIR}`,
                `--genders=${JSON.stringify(genders)}`,
                `--conn=${Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64')}`
            ], {
                cwd: this.#IMPORTER_PATH,
                stdio: 'pipe'
            }).on('error', err => {
                reject({validation: (err && err.toString()) || errorMsg});
            }).on('close', exitCode => {
                switch (exitCode) {
                    case 0:
                        resolve();
                        break;
                    case 2:
                        reject({validation: 'Empty File'});
                        break;
                    default:
                        let msg;
                        try {
                            msg = JSON.parse(errorMsg)
                        } catch (e) {
                            msg = errorMsg
                        }
                        reject(msg || new Error('Internal Error'));
                }
            });

            proc.stderr.on('data', function (error) {
                errorMsg += error;
            });

            proc.stdout.on('data', function (msg) {
                if (msg) {
                    loggers.debug_log.verbose(msg.toString())
                }
            });

            try {
                fileStream.pipe(proc.stdin);
            } catch (error) {
                reject(error.validation ? error : {validation: error.message})
            }
        })
    }

    async _getClubDataForImport($masterClubId, $clubOwnerId) {
        const {rows: [row]} = await Db.query(
            `SELECT LOWER(TRIM(mc.code))                        "code",
                    LOWER(TRIM(mc.region))                      "region",
                    LOWER(TRIM(mc.country))                     "country",
                    mc."has_male_teams"                         "male",
                    mc."has_female_teams"                       "female",
                    mc."has_coed_teams"                         "coed",
                    (${this.isXLSXImportAllowedQuery()}) "allow_xlsx_import"
            FROM "master_club" mc
            WHERE mc.master_club_id = $1
                AND mc.club_owner_id = $2`,
            [$masterClubId, $clubOwnerId]
        );

        if (row && !row.allow_xlsx_import) {
            throw {
                validation: 'Not a foreign Club'
            }
        }

        return row;
    }

    _getClubGenders(male, female, coed) {
        const genders = [];

        if (male) {
            genders.push('m');
        }
        if (female) {
            genders.push('f');
        }
        if (coed) {
            genders.push('c');
        }

        if (!genders.length) {
            throw {
                validation: 'Club has no genders'
            }
        }

        return genders;
    }

    _validateClubData(code, region, country) {
        if (!code) {
            throw {
                validation: 'Club Code not found'
            }
        }

        if (!region) {
            throw {
                validation: 'Club Region not found'
            }
        }

        if (!country) {
            throw {
                validation: 'Club Country not found'
            }
        }
    }

    isXLSXImportAllowedQuery () {
        return `SELECT (
                CASE
                    WHEN mc.country NOT IN ('${US_COUNTRY_CODE}', '${CA_COUNTRY_CODE}') AND mc.country IS NOT NULL
                      THEN TRUE
                    WHEN mc.country = '${US_COUNTRY_CODE}' AND ${USAV_SANC_BODY.JVA} = ANY (ARRAY_AGG(DISTINCT e.sport_sanctioning_id)) 
                      THEN TRUE
                    WHEN mc.country = '${US_COUNTRY_CODE}' AND ${USAV_SANC_BODY.NINE_MAN} = ANY (ARRAY_AGG(DISTINCT mcs.sport_sanctioning_id))
                      THEN TRUE 
                    WHEN ${USAV_SANC_BODY.OTHER} = ANY (ARRAY_AGG(DISTINCT e.sport_sanctioning_id))
                         AND ${USAV_SANC_BODY.NONE} = ANY (ARRAY_AGG(DISTINCT mcs.sport_sanctioning_id))
                      THEN TRUE      
                    ELSE FALSE
                    END
               )
               FROM roster_club rc
                 LEFT JOIN event e
                   ON e.event_id = rc.event_id
                   AND e.date_start > (NOW() AT TIME ZONE e.timezone)
                 LEFT JOIN master_club_sanctioning mcs
                   ON rc.master_club_id = mcs.master_club_id 
               WHERE rc.master_club_id = mc.master_club_id AND rc.deleted IS NULL`;
    }
}

module.exports = new ImportProcessService();

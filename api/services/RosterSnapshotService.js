'use strict';

const swUtils = require('../lib/swUtils');
const { USAV_SANC_BODY } = require('../constants/teams');
const { US_COUNTRY_CODE } = require('../constants/common');

module.exports = {
    removeRoster: function(roster_team_id, cb) {
        return Db.begin()
        .then(tr => {
            return Promise.all([

                 tr.query(
                    `UPDATE "roster_athlete" 
                     SET "deleted" = now(), "roster_team_id" = NULL 
                     WHERE roster_team_id = $1 AND "deleted" IS NULL`, 
                     [roster_team_id]
                ).then(result => {
                    return result.rowCount;
                }),

                tr.query(
                    `UPDATE "roster_staff_role" rsr
                     SET "deleted" = NOW(), "primary" = (
                        CASE 
                            WHEN rsr."primary" IS TRUE THEN NULL
                            ELSE  rsr."primary"
                        END 
                     ) FROM (
                        SELECT 
                            "rsr".roster_staff_role_id, "rsr".primary, "rt".event_id
                        FROM "roster_staff_role" rsr 
                        INNER JOIN "roster_team" rt 
                            ON rt.roster_team_id = rsr.roster_team_id
                        WHERE rsr.roster_team_id = $1 
                            AND rsr.deleted IS NULL
                     ) "old" 
                     WHERE rsr.roster_staff_role_id = old.roster_staff_role_id
                     RETURNING rsr.master_staff_id, old.primary, old.event_id`, 
                     [roster_team_id]
                ).then(result => {
                    /* Why do we need this? */
                    let dropRedefiedValues  = [];

                    result.rows.forEach(roleRow => {
                        if(roleRow.primary) {
                            dropRedefiedValues.push(
                                tr.query(
                                    `UPDATE "roster_staff_role" rsr 
                                        SET "primary" = NULL 
                                     WHERE rsr."master_staff_id" = $1 
                                        AND rsr."roster_team_id" <> $2
                                        AND EXISTS (
                                            SELECT rt.roster_team_id 
                                            FROM "roster_team" rt 
                                            INNER JOIN "event" e 
                                                ON e.event_id = rt.event_id
                                            INNER JOIN "division" d 
                                                ON d.division_id = rt.division_id
                                            WHERE rt.roster_team_id = rsr.roster_team_id 
                                                AND rt.event_id = $3
                                                AND rt.locked IS NOT TRUE
                                        )`,
                                    [roleRow.master_staff_id, roster_team_id, roleRow.event_id]
                                )
                            );
                        }
                    });

                    return (
                        (dropRedefiedValues.length)
                                ?Promise.all(dropRedefiedValues)
                                :Promise.resolve()
                    ).then(function () {
                        return result.rowCount;
                    });
                })
            ]).then(function (updatedRowsCount) {
                return tr.query(UPDATE_ROSTER_COUNT, [roster_team_id])
                .then(function () {
                    return updatedRowsCount;
                });
            }).then(function (updatedRowsCount) {
                return tr.commit()
                .then(function () {
                    return updatedRowsCount;
                });
            }).catch(function (err) {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }
                throw err;
            });
        }).then(function (updatedRowsCount) {
            if(cb) {
                cb(null, updatedRowsCount[0], updatedRowsCount[1]);
            } else {
                return updatedRowsCount
            }
        }).catch(function (err) {
            if(cb) {
                cb(err);
            } else {
                throw err;
            }
        })
    },
    // covered 😄👍
    upsertRosterAthletes: function (masterClubId, masterAthleteId, masterTeamId, disableQtyUpdate, skipLocked) {
        /*
            Returned row contains:
            1. Event Club for specified club id (only for upcoming events)
            2. event athlete object with "deadline_passed" variable which is false 
            when it is allowed to change this athlete
            3. event team identifier which shows whether the team (passed at the third function parameter) is
            available on the event;
        */
        return Db.query(
            `SELECT ma.usav_number, ma.aau_membership_id
            FROM master_athlete ma
            WHERE ma.master_athlete_id = $1`,
            [masterAthleteId]
        ).then(function (result) {
            const athlete = _.first(result.rows);

            if (_.isEmpty(athlete)) {
                throw {
                    validation: 'Athlete Not Found'
                };
            }

            return athlete;
        }).then(athlete => {
            const sanctioningEventWhereSql = __getSanctioningEventWhereSql(athlete);

            return Db.query(
                `SELECT
                    rc.roster_club_id,
                    rc.event_id, (
                        SELECT ROW_TO_JSON("event_athlete")
                        FROM (
                            SELECT ra.*, rt.locked, (
                                COALESCE(d.roster_deadline, e.roster_deadline) < (NOW() AT TIME ZONE e.timezone)
                            ) "deadline_passed", rt.team_name
                            FROM "roster_athlete" ra
                            LEFT JOIN "roster_team" rt
                                ON rt.roster_team_id = ra.roster_team_id
                            LEFT JOIN "division" d
                                ON d.division_id = rt.division_id
                            WHERE ra.master_athlete_id = $2
                                AND ra.event_id = e.event_id
                        ) "event_athlete"
                    ) "athlete",
                    rt.roster_team_id, rt.team_name, co.user_id,
                    (
                        SELECT ROW_TO_JSON(_master_athlete)
                        FROM (
                            SELECT FORMAT('%s %s', ma.first, ma.last) "member_name", ma.organization_code
                            FROM master_athlete ma
                            WHERE ma.master_athlete_id = $2
                        ) _master_athlete
                    ) AS "member"
                    FROM "roster_club" rc
                    INNER JOIN "event" e
                        ON e.event_id = rc.event_id
                        AND e.date_end > (NOW() AT TIME ZONE e.timezone)
                    LEFT JOIN "roster_team" rt
                        ON rt.master_team_id = $3
                        AND rt.event_id = e.event_id
                    LEFT JOIN "club_owner" co
                        ON co.master_club_id = $1
                    WHERE rc.master_club_id = $1
                        AND rc.deleted IS NULL
                        ${sanctioningEventWhereSql ? sanctioningEventWhereSql : ''}
                    ORDER BY e.date_end DESC`,
                [masterClubId, masterAthleteId, masterTeamId]
            );
        }).then(function (result) {
            return result.rows || [];
        }).then(function (eventClubs) {
            return Promise.all(
                eventClubs.map(function (club) {
                    return __upsertClubAthlete(club, masterAthleteId, skipLocked)
                })
            ).then(function (result) {
                let modifiedAthletes = [];
                
                let existsResults =  result.filter(a => a !== null);
                loggers.debug_log.verbose(
                    'Modified', existsResults.length, 'roster athletes rows of', result.length
                );
                
                if(existsResults.length) {
                    modifiedAthletes = _.map(existsResults, 'modifiedEventAthletes');
                    
                    return Promise.all(
                        existsResults.map(obj => __addMemberChangeHistoryRow({
                            title: obj.historyData.title,
                            action: obj.historyData.action,
                            club: obj.historyData.club
                        }))
                    ).then(() => modifiedAthletes);
                }
                
                return modifiedAthletes;
            }).then(function (modifiedEventAthletes) {
                let result = {
                    member      : modifiedEventAthletes,
                    eventClubs  : eventClubs
                };

                if(modifiedEventAthletes.length && !disableQtyUpdate) {
                    let needToExtractIds = true;

                    return __setAthletesQty(modifiedEventAthletes, needToExtractIds).then(() => result);
                }

                return result;
            })
        })
    },
    // covered 😄👍
    upsertRosterStaffRoles: function (masterClubId, masterStaffId, disableQtyUpdate, skipLocked) {
        return Db.query(
            `SELECT ms.usav_number, ms.aau_membership_id
            FROM master_staff ms
            WHERE ms.master_staff_id = $1`,
            [masterStaffId]
        ).then(function (result) {
            const staff = _.first(result.rows);

            if (_.isEmpty(staff)) {
                throw {
                    validation: 'Staff Not Found'
                };
            }

            return staff;
        }).then(staff => {
            const sanctioningEventWhereSql = __getSanctioningEventWhereSql(staff);

            return Db.query(
                `SELECT
                    rc.event_id, rc.roster_club_id, rt.roster_team_id, rt.master_team_id,
                    ROW_TO_JSON(msr) "master_role", ROW_TO_JSON(rsr) "roster_role", rt.team_name, co.user_id,
                    (
                        SELECT ROW_TO_JSON(_staff)
                        FROM (
                            SELECT FORMAT('%s %s', ms.first, ms.last) "member_name", ms.organization_code
                            FROM master_staff ms
                            WHERE ms.master_staff_id = $2
                        ) _staff
                    ) AS member
                FROM "roster_club" rc
                INNER JOIN "event" e
                    ON e.event_id = rc.event_id
                    AND e.date_end > (NOW() AT TIME ZONE e.timezone)
                INNER JOIN "roster_team" rt
                    ON rt.roster_club_id = rc.roster_club_id
                    AND rt.event_id = e.event_id
                LEFT JOIN "division" d
                    ON d.division_id = rt.division_id
                LEFT JOIN "master_staff_role" msr
                    ON msr.master_team_id = rt.master_team_id
                    AND msr.master_staff_id = $2
                LEFT JOIN "roster_staff_role" rsr
                    ON rsr.master_team_id = rt.master_team_id
                    AND rsr.roster_team_id = rt.roster_team_id
                    AND rsr.master_staff_id = $2
                LEFT JOIN "club_owner" co
                    ON co.master_club_id = $1
                WHERE rc.master_club_id = $1
                    AND rc.deleted IS NULL
                    ${!skipLocked ? 'AND rt.locked IS NOT TRUE' : ''}                   
                    ${sanctioningEventWhereSql ? sanctioningEventWhereSql : ''}
                ORDER BY e.date_end DESC`,
                [masterClubId, masterStaffId]
            );
        }).then(function (result) {
            return result.rows || []
        }).then(function (eventClubs) {
            return Promise.all(
                eventClubs.map(club => __upsertClubStaff(club, skipLocked))
            ).then(function (result) {
               let modifiedStaffers = [];
                
                let existsResults =  result.filter(a => a !== null);
                loggers.debug_log.verbose('Modified', existsResults.length,
                    'roster staff role rows of', result.length);
                
                if(existsResults.length) {
                    modifiedStaffers = _.map(existsResults, 'upsertResultRows');
                    
                    return Promise.all(
                        existsResults.map(obj => __addMemberChangeHistoryRow({
                            title: obj.historyData.title,
                            action: obj.historyData.action,
                            club: obj.historyData.club
                        }))
                    ).then(() => modifiedStaffers);
                }
                
                return modifiedStaffers;
            }).then(function (modifiedEventStaff) {

                let result = {
                    member      : modifiedEventStaff,
                    eventClubs  : eventClubs
                }


                if(modifiedEventStaff.length && !disableQtyUpdate) {
                    let needToExtractIds = true;

                    return __setStaffersQty(modifiedEventStaff, needToExtractIds)
                    .then(() => result);
                }

                return result;
            })
        })
    },
    makeRosterSnapshot: function (masterTeamId, rosterTeamId, eventId, season, cb, skipLocked) {
        var self = this;

        return Db.query(
            `SELECT
                e.sport_sanctioning_id,
                rc.country "team_club_country"
            FROM "roster_team" rt
            INNER JOIN "roster_club" rc ON rt.roster_club_id = rc.roster_club_id AND rc.deleted IS NULL
            INNER JOIN "event" e ON rc.event_id = e.event_id
            WHERE e.event_id = $1
                AND rt.roster_team_id = $2`,
            [eventId, rosterTeamId]
        ).then(result => {
            const event = _.first(result.rows);
            if (_.isEmpty(event)) {
                throw {
                    validation: 'Event or Team Not Found'
                };
            }
            return event;
        }).then(event => {
            let athletesQuery =
                `SELECT ma.master_athlete_id, ma.master_club_id, ma.master_team_id
                 FROM "master_athlete" ma
                 WHERE ma.master_team_id = $1
                   AND ma.deleted IS NULL
                   AND ma.season = $2`;

            if (event.sport_sanctioning_id === USAV_SANC_BODY.USAV && event.team_club_country === US_COUNTRY_CODE) {
                athletesQuery += ` AND ma.usav_number IS NOT NULL`;
            } else if (event.sport_sanctioning_id === USAV_SANC_BODY.JVA || event.sport_sanctioning_id === USAV_SANC_BODY.AAU) {
                athletesQuery += ` AND (ma.aau_membership_id IS NOT NULL OR ma.usav_number IS NULL)`;
            } else if(event.sport_sanctioning_id !== USAV_SANC_BODY.OTHER && event.sport_sanctioning_id !== USAV_SANC_BODY.USAV) {
                athletesQuery += ` AND (ma.usav_number IS NULL AND ma.aau_membership_id IS NULL)`;
            }

            let staffersQuery =
                `SELECT DISTINCT ms.master_staff_id, ms.master_club_id
                 FROM "master_staff" ms
                          INNER JOIN "master_staff_role" msr ON msr.master_staff_id = ms.master_staff_id
                     AND msr.master_team_id = $1
                 WHERE ms.deleted IS NULL
                   AND ms.season = $2`;

            if (event.sport_sanctioning_id === USAV_SANC_BODY.USAV && event.team_club_country === US_COUNTRY_CODE) {
                staffersQuery += ` AND ms.usav_number IS NOT NULL`;
            } else if (event.sport_sanctioning_id === USAV_SANC_BODY.JVA || event.sport_sanctioning_id === USAV_SANC_BODY.AAU) {
                staffersQuery += ` AND (ms.aau_membership_id IS NOT NULL OR ms.usav_number IS NULL)`;
            } else if(event.sport_sanctioning_id !== USAV_SANC_BODY.OTHER && event.sport_sanctioning_id !== USAV_SANC_BODY.USAV) {
                staffersQuery += ` AND (ms.usav_number IS NULL AND ms.aau_membership_id IS NULL)`;
            }

            return Promise.all([
                Db.query(athletesQuery, [masterTeamId, season])
                    .then(function (result) {
                        return (result.rows || [])
                    }),
                Db.query(staffersQuery, [masterTeamId, season])
                    .then(function (result) {
                        return (result.rows || [])
                    })
            ]);
        }).then(function (result) {
            var athletesList    = result[0],
                staffList       = result[1];
            return __runMembersUpsert.bind(self)(athletesList, staffList, skipLocked).
            then(__updateEventTeamsMembersCount);
        }).then(function () {
            if(cb) cb()
        }).catch(function (err) {
            if(cb) {
                cb(err)
            }
            else {
                throw err;
            }
        })
    },
    async clubSnapshot(masterClubId, season, options) {
        const clubTeamsList = await Db.query(
            `SELECT (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("a"))), '[]'::JSON)
                FROM (
                    SELECT ma.master_athlete_id, ma.master_club_id, ma.master_team_id
                    FROM "master_athlete" ma 
                    WHERE ma.master_team_id = mt.master_team_id
                        AND ma.deleted IS NULL
                        AND ma.season = mt.season
                ) "a"
            ) "athletes", (
                SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("s"))), '[]'::JSON)
                FROM (
                    SELECT DISTINCT ms.master_staff_id, ms.master_club_id
                    FROM "master_staff" ms 
                    INNER JOIN "master_staff_role" msr 
                        ON msr.master_staff_id = ms.master_staff_id
                        AND msr.master_team_id = mt.master_team_id
                    WHERE ms.deleted IS NULL 
                        AND ms.season = mt.season
                ) "s"
            ) "staff"
            FROM "master_club" mc 
            INNER JOIN "master_team" mt 
                ON mt.master_club_id = mc.master_club_id 
                AND mt.deleted IS NULL 
                AND mt.season = $2
            WHERE mc.master_club_id = $1`,
            [masterClubId, season]
        ).then(r => r.rows);
        if(_.isEmpty(clubTeamsList)) {
            throw {
                validation: 'Club Not found.'
            }
        }
        const members = [];
        for(const team of clubTeamsList) {
            const upsertResult = await __runMembersUpsert.bind(this)(team.athletes, team.staff);
            for(const member of upsertResult) {
                members.push(member);
            }
        }
        await __updateEventTeamsMembersCount(members);
    },

    __generateCheckBlock: function (eventsList, teamsList) {
        if (Array.isArray(eventsList) && (eventsList.length > 0)) {
            let eventsIDs = swUtils.numArrayToString(eventsList, null, true);

            return (
                `AND rt.event_id IN (${eventsIDs})
                 AND (
                    (
                     COALESCE(d.roster_deadline, e.roster_deadline) 
                     BETWEEN (NOW() AT TIME ZONE e.timezone) 
                     AND ((NOW() AT TIME ZONE e.timezone) + INTERVAL '10 minutes' )
                    ) 
                )`
            );
        } else if (Array.isArray(teamsList) && (teamsList.length > 0)) {
            let teamsIDs = swUtils.numArrayToString(teamsList, null, true);

            return (
                `AND rt."roster_team_id" IN (${teamsIDs})
                 AND rt."locked" IS TRUE`
            );
        } else {
            throw new Error(`Either events' identifiers or teams' ones required`)
        }
    },

    // Covered 👍
    __copyMasterValuesForTeams: function (params) {
        return Promise.resolve().then(() => {
            let {eventsList, teamsList, season} = params;

            if (!Number.isInteger(season) || (season < 2015)) {
                throw new Error('Invalid season');
            }

            let checkBlock = this.__generateCheckBlock(eventsList, teamsList);


            let __updRosterAthleteSQL = () => {
                let query = 
                    `UPDATE "roster_athlete" ra
                     SET "jersey"               = COALESCE(ra."jersey", filtered_ra."jersey"),
                         "sport_position_id"    = COALESCE(ra."sport_position_id", filtered_ra."sport_position_id"),
                         "role"                 = COALESCE(ra."role", filtered_ra."role")
                     FROM (
                        SELECT
                            ra."roster_athlete_id",
                            ma."jersey", 
                            ma."sport_position_id", 
                            ma."master_athlete_id",
                            ma."role"
                        FROM "roster_athlete" ra 
                        INNER JOIN "event" e
                            ON e.event_id = ra.event_id
                        INNER JOIN "master_athlete" ma 
                            ON ma.master_athlete_id = ra.master_athlete_id
                            AND ma."season" = $1
                        WHERE ra.deleted IS NULL
                            AND ra.deleted_by_user IS NULL 
                            AND (
                                ra."jersey" IS NULL OR 
                                ra."sport_position_id" IS NULL OR 
                                (ra."role" IS NULL AND e."sport_sanctioning_id" = ${USAV_SANC_BODY.NINE_MAN})
                            )
                            AND EXISTS (
                                SELECT rt.roster_team_id
                                FROM "roster_team" rt 
                                INNER JOIN "division" d 
                                    ON d.division_id = rt.division_id
                                INNER JOIN "event" e 
                                    ON e.event_id = rt.event_id 
                                WHERE rt.roster_team_id = ra.roster_team_id
                                ${checkBlock}
                            )
                     ) "filtered_ra"
                     WHERE ra."roster_athlete_id" = filtered_ra."roster_athlete_id"
                     RETURNING ra.roster_athlete_id, ra.jersey, ra.sport_position_id`;

                return query;
            }

            let __updRosterStaffRoleSQL = () => {
                let query = 
                `UPDATE "roster_staff_role" rsr
                 SET "role_id" = COALESCE(NULLIF(rsr.role_id, 0), msr.role_id),
                     "primary" = COALESCE(rsr.primary, msr.primary)
                 FROM (
                     SELECT msr.role_id, msr.primary, msr.master_staff_id, msr.master_team_id
                     FROM "master_staff_role" msr 
                     INNER JOIN "master_staff" ms
                         ON ms.master_staff_id = msr.master_staff_id 
                         AND ms.season = $1
                 ) "msr"
                 WHERE rsr.master_team_id = msr.master_team_id
                     AND rsr.master_staff_id = msr.master_staff_id
                     AND rsr.deleted IS NULL 
                     AND rsr.deleted_by_user IS NULL
                     AND (NULLIF(rsr."role_id", 0) IS NULL OR rsr."primary" IS NULL)
                     AND EXISTS (
                         SELECT rt.roster_team_id
                         FROM "roster_team" rt 
                         INNER JOIN "division" d 
                             ON d.division_id = rt.division_id
                         INNER JOIN "event" e 
                             ON e.event_id = rt.event_id 
                         WHERE rt.roster_team_id = rsr.roster_team_id
                         ${checkBlock}
                     )
                 RETURNING rsr.roster_staff_role_id, rsr.role_id, rsr.primary`;

                return query;
            }

            let sqlParams = [season];

            return {
                updateRosterAthletesQuery   : __updRosterAthleteSQL(),
                updateRosterStaffRolesQuery : __updRosterStaffRoleSQL(),
                sqlParams
            }
        }).then(queries => {
            return Db.begin()
            .then(tr => {
                return Promise.all([
                    tr.query(queries.updateRosterAthletesQuery, queries.sqlParams),
                    tr.query(queries.updateRosterStaffRolesQuery, queries.sqlParams)
                ]).then(result => {
                    return tr.commit().then(() => {
                        let [{ rows: { length: athletes } }, { rows: { length: staff } }] = result;

                        loggers.debug_log.verbose(
                            'Modified:', 
                                athletes, '"roster_athlete" rows,', 
                                staff, '"roster_staff_role" rows'
                        );

                        return {athletes, staff};
                    });
                })
            })
        })
    },

    // Covered 👍
    copyMasterValues: function (season) {
        return Db.query(
            `SELECT e.event_id
             FROM "event" e
             WHERE (e.date_end > NOW() AT TIME ZONE e.timezone)`
        ).then(res => {
            let {rows} = res;

            if (rows.length === 0) {
                return Promise.resolve(null);
            } else {
                return this.__copyMasterValuesForTeams({
                    eventsList: rows.map(r => Number(r.event_id)),
                    season
                })
            }
        });
    },

    // Covered 👍
    copyTeamsMembersValuesToRoster: function (teamsList, season) {
        return this.__copyMasterValuesForTeams({
            teamsList, season
        });
    },


    withdrawByUser: function (memberId, masterClubId, eventId, type) {
        return Db.query(
            (type === 'athlete')
            ?`UPDATE "roster_athlete" ra 
              SET "deleted_by_user" = NOW() 
              WHERE ra.roster_athlete_id = $1 
              AND ra.deleted_by_user IS NULL 
              AND EXISTS ( 
                  SELECT rt.roster_team_id 
                  FROM "roster_team" rt 
                  INNER JOIN "event" e 
                      ON e.event_id = rt.event_id 
                  INNER JOIN "division" d 
                      ON d.division_id = rt.division_id 
                  LEFT JOIN master_team mt  
                      ON mt.master_team_id = rt.master_team_id 
                  INNER JOIN master_club mc  
                      ON mc.master_club_id = mt.master_club_id 
                      AND mc.master_club_id = $2 
                  WHERE rt.roster_team_id = ra.roster_team_id 
                      AND rt.event_id = $3 
                      AND rt.deleted IS NULL 
                      AND rt.locked IS NOT TRUE
                      AND (
                        CASE WHEN rt.reg_method = $4
                            THEN TRUE
                            ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        END    
                      )  
                      AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone) 
                      AND e.registration_method = 'club'  
                      AND e.live_to_public IS TRUE 
                      AND e.published IS TRUE
                      AND e.teams_use_clubs_module IS TRUE
              ) 
              RETURNING "roster_team_id"`
            :`UPDATE "roster_staff_role" rsr  
             SET "deleted_by_user" = NOW() 
             WHERE rsr.roster_staff_role_id = $1 
             AND rsr.deleted_by_user IS NULL  
             AND EXISTS (   
                 SELECT rt.roster_team_id   
                 FROM roster_team rt   
                 INNER JOIN "event" e 
                     ON e.event_id = rt.event_id   
                 INNER JOIN "division" d   
                     ON d.division_id = rt.division_id   
                 INNER JOIN master_team mt    
                     ON mt.master_team_id = rt.master_team_id   
                     AND mt.master_team_id = rsr.master_team_id 
                 INNER JOIN master_club mc    
                     ON mc.master_club_id = mt.master_club_id   
                     AND mc.master_club_id = $2 
                 WHERE rt.roster_team_id = rsr.roster_team_id   
                     AND rt.event_id = $3 
                     AND rt.deleted IS NULL   
                     AND (
                        CASE WHEN rt.reg_method = $4
                            THEN TRUE
                            ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        END    
                     )   
                     AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone) 
                     AND rt.locked IS NOT TRUE
                     AND e.registration_method = 'club'    
                     AND e.live_to_public IS TRUE   
                     AND e.published IS TRUE
                     AND e.teams_use_clubs_module IS TRUE
             )   
             RETURNING "roster_team_id"`,
            [memberId, masterClubId, eventId, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK]
        ).then(function (result) {
            var removedMember = _.first(result.rows);
            if(_.isEmpty(removedMember)) {
                throw {
                    validation: `Failed: Member not found has been removed or Roster is Locked`
                }
            }
            return removedMember;
        }).then(function (removedMember) {
            return Db.query(
                UPDATE_ROSTER_COUNT,
                [removedMember.roster_team_id]
            ).then(function () {
                return removedMember;
            })
        })
    },
    reinstateMember: function (memberId, masterClubId, eventId, type) {
        return Db.query(
            (type === 'athlete')
            ?`UPDATE "roster_athlete" ra 
              SET "deleted_by_user" = NULL 
              WHERE ra.roster_athlete_id = $1 
              AND ra.deleted_by_user IS NOT NULL 
              AND EXISTS ( 
                  SELECT rt.roster_team_id 
                  FROM "roster_team" rt 
                  INNER JOIN "event" e 
                      ON e.event_id = rt.event_id 
                  INNER JOIN "division" d 
                      ON d.division_id = rt.division_id 
                  LEFT JOIN master_team mt  
                      ON mt.master_team_id = rt.master_team_id 
                  INNER JOIN master_club mc  
                      ON mc.master_club_id = mt.master_club_id 
                      AND mc.master_club_id = $2 
                  WHERE rt.roster_team_id = ra.roster_team_id 
                      AND rt.event_id = $3 
                      AND rt.deleted IS NULL 
                      AND rt.locked IS NOT TRUE
                      AND (
                        CASE WHEN rt.reg_method = $4
                            THEN TRUE
                            ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        END    
                      )     
                      AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone) 
                      AND e.registration_method = 'club'  
                      AND e.live_to_public IS TRUE 
                      AND e.published IS TRUE
                      AND e.teams_use_clubs_module IS TRUE
              ) 
              RETURNING "roster_team_id"`
            :`UPDATE "roster_staff_role" rsr  
             SET "deleted_by_user" = NULL
             WHERE rsr.roster_staff_role_id = $1 
             AND rsr.deleted_by_user IS NOT NULL  
             AND EXISTS (   
                 SELECT rt.roster_team_id   
                 FROM roster_team rt   
                 INNER JOIN "event" e 
                     ON e.event_id = rt.event_id   
                 INNER JOIN "division" d   
                     ON d.division_id = rt.division_id   
                 INNER JOIN master_team mt    
                     ON mt.master_team_id = rt.master_team_id   
                     AND mt.master_team_id = rsr.master_team_id 
                 INNER JOIN master_club mc    
                     ON mc.master_club_id = mt.master_club_id   
                     AND mc.master_club_id = $2 
                 WHERE rt.roster_team_id = rsr.roster_team_id   
                     AND rt.event_id = $3 
                     AND rt.deleted IS NULL   
                     AND rt.locked IS NOT TRUE
                     AND (
                        CASE WHEN rt.reg_method = $4
                            THEN TRUE
                            ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        END    
                     )     
                     AND COALESCE(d.roster_deadline, e.roster_deadline) >= (NOW() AT TIME ZONE e.timezone)
                     AND e.registration_method = 'club'    
                     AND e.live_to_public IS TRUE   
                     AND e.published IS TRUE
                     AND e.teams_use_clubs_module IS TRUE
             )   
             RETURNING "roster_team_id"`,
            [memberId, masterClubId, eventId, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK]
        ).then(function (result) {
            var reinstatedMember = _.first(result.rows);
            if(_.isEmpty(reinstatedMember)) {
                throw {
                    validation: `Failed: Member not found has been removed or Roster is locked`
                }
            }
            return reinstatedMember
        }).then(function (reinstatedMember) {
            return Db.query(
                UPDATE_ROSTER_COUNT,
                [reinstatedMember.roster_team_id]
            ).then(function () {
                return reinstatedMember;
            })
        })
    },
    findBlockedEvents: function (masterClubId, masterAthleteId, masterStaffId) {
        if(!masterClubId) {
            return Promise.reject('Club Identifier required');
        }

        let raBlock = 
            `SELECT DISTINCT
                e.event_id, e.long_name "name",
                TO_CHAR(
                    COALESCE(d.roster_deadline, e.roster_deadline), 
                    'Mon DD, YYYY, HH12:MI AM'
                ) "deadline"
             FROM "roster_athlete" ra 
             INNER JOIN "master_athlete" ma 
                ON ma.master_athlete_id = ra.master_athlete_id
             INNER JOIN "event" e 
                ON e.event_id = ra.event_id
             INNER JOIN "roster_team" rt 
                 ON ra.roster_team_id = rt.roster_team_id 
                 AND ra.event_id = e.event_id 
                 AND ra.deleted IS NULL
             INNER JOIN "division" d 
                 ON d.division_id = rt.division_id
                 AND d.event_id = e.event_id
             WHERE ma.master_club_id = $1
                 ${!!masterAthleteId?'AND ra.master_athlete_id = $2':''}
                 AND ra.deleted IS NULL
                 AND rt.locked IS TRUE
                 AND e.date_end > (NOW() AT TIME ZONE e.timezone)`;

        let rsBlock = 
            `SELECT DISTINCT
                 e.event_id, e.long_name "name",
                 TO_CHAR(
                    COALESCE(d.roster_deadline, e.roster_deadline), 
                    'Mon DD, YYYY, HH12:MI AM'
                ) "deadline"
             FROM "roster_staff_role" rsr 
             INNER JOIN "master_staff" ms 
                 ON ms.master_staff_id = rsr.master_staff_id
             INNER JOIN "roster_team" rt 
                 ON rt.roster_team_id = rsr.roster_team_id 
                 AND rt.deleted IS NULL
             INNER JOIN "event" e 
                 ON e.event_id = rt.event_id 
             INNER JOIN "division" d 
                 ON d.division_id = rt.division_id
                 AND d.event_id = e.event_id
             WHERE "ms".master_club_id = $1
                 ${!!masterStaffId?'AND "rsr".master_staff_id = $2':''}
                 AND rsr.deleted IS NULL
                 AND rt.locked IS TRUE
                 AND e.date_end > (NOW() AT TIME ZONE e.timezone)`


        let sql, params = [masterClubId];

        if(!(masterAthleteId || masterStaffId)) {
            sql = `SELECT DISTINCT * FROM (${raBlock} UNION ALL ${rsBlock}) "data"`;
        } else if (!!masterAthleteId) {
            sql = raBlock;
            params.push(masterAthleteId);
        } else if (!!masterStaffId) {
            sql = rsBlock;
            params.push(masterStaffId);
        }

        return Db.query(sql, params)
        .then(result => {
            return result.rows;
        });
    }
}

function getArrayOfRosterTeamIds (arr) {
    return arr.reduce((res, item) => {
        if (item.roster_team_id) {
            res.push(item.roster_team_id);
        }

        if (item.old_roster_team_id) {
            res.push(item.old_roster_team_id)
        }
        return res;
    }, []);
}

function __setStaffersQty (modifiedRosterStaffRoleRows, needToExtractIds = false) {
    return Promise.resolve().then(() => {
        let teamsIDs = needToExtractIds
            ? getArrayOfRosterTeamIds(modifiedRosterStaffRoleRows)
            : modifiedRosterStaffRoleRows;

        if (teamsIDs.length === 0) {
            return Promise.resolve();
        }

        teamsIDs = _.uniq(teamsIDs);

        return Db.query(
            `UPDATE "roster_team" rt 
             SET "roster_staff_count" = (
                 SELECT COUNT(rsr.*) 
                 FROM "roster_staff_role" rsr   
                 WHERE rsr.roster_team_id = rt.roster_team_id   
                    AND rsr.deleted IS NULL
                    AND rsr.deleted_by_user IS NULL
             )  
             WHERE rt.roster_team_id IN(${teamsIDs.join(', ')})`
        )
    })
    .catch(err => {
        loggers.errors_log.error(err);
    })
}

function __setAthletesQty (modifiedRosterAthleteRows, needToExtractIds = false) {
    return Promise.resolve().then(() => {
        let teamsIDs = needToExtractIds
            ? getArrayOfRosterTeamIds(modifiedRosterAthleteRows)
            : modifiedRosterAthleteRows;

        if (teamsIDs.length === 0) {
            return Promise.resolve();
        }

        teamsIDs = _.uniq(teamsIDs);

        return Db.query(
            `UPDATE "roster_team" rt 
             SET "roster_athletes_count" = (
                 SELECT COUNT(ra.*) 
                 FROM "roster_athlete" ra   
                 WHERE ra.roster_team_id = rt.roster_team_id   
                    AND ra.deleted IS NULL
                    AND ra.deleted_by_user IS NULL
             )  
             WHERE rt.roster_team_id IN(${teamsIDs.join(', ')})`
        )
    })
    .catch(err => {
        loggers.errors_log.error(err);
    })
}

function __upsertClubAthlete (eventClub, masterAthleteId, skipLocked) {
    /*
        Cases:
        1. Athlete is present on the event and the deadline has not passed:
            a. Team is present
                    Change athlete's team if the team's division allows the change 
                    and team value differs from Athlete's team
            b. Team is NOT present - Remove athlete's team id value
        2. Athlete is NOT present on the event:
            a.Team is Present
                Create event athlete if the team's division allows the creation
            b. Team is NOT present - DO NOTHING
        
        ELSE DO nothing
    */

    return new Promise(function (resolve) {
        var rosterTeamId    = eventClub.roster_team_id,
            rosterAthlete   = eventClub.athlete,
            isRosterLocked  = eventClub.athlete && eventClub.athlete.locked;
        
        var sql, params = [], historyTitle, historyAction;
        if(rosterAthlete && !isRosterLocked) {
            if(rosterTeamId) {
                sql =
                    `WITH "old_team" AS (
                        SELECT "roster_team_id" "id" FROM "roster_athlete"
                        WHERE "roster_athlete_id" = $2
                     )
                     UPDATE "roster_athlete" ra 
                     SET "roster_team_id"   = $1,
                         "deleted"          = NULL,
                         "deleted_by_user"  = NULL 
                     WHERE ra.roster_athlete_id = $2
                        AND (ra.roster_team_id IS DISTINCT FROM ($1)::INTEGER)
                        AND EXISTS (
                            SELECT rt.roster_team_id
                            FROM "roster_team" rt 
                            INNER JOIN "division" d 
                                ON d.division_id = rt.division_id
                            INNER JOIN "event" e 
                                ON e.event_id = rt.event_id 
                            WHERE rt.roster_team_id = $1
                                AND (
                                    CASE WHEN rt.reg_method = $3
                                        THEN TRUE
                                        ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                                    END    
                                )
                                AND e.registration_method = 'club'
                                ${skipLocked ? '' : 'AND rt.locked IS NOT TRUE'}
                                AND e.live_to_public IS TRUE
                                AND e.published IS TRUE
                                AND e.teams_use_clubs_module IS TRUE
                        )
                     RETURNING (SELECT "id" FROM "old_team") "old_roster_team_id", ra."roster_team_id"`;
                // NOTE: we return both ids: of the team the athlete was removed from and of the team 
                // the athlete was added to. This is because we need to recount number of athletes
                // in both teams
                params.push(rosterTeamId, rosterAthlete.roster_athlete_id, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK);
                historyTitle = 'Moved Athlete';
                historyAction = 'team.member.add';
            } else {
                sql =
                `UPDATE "roster_athlete" ra
                SET "roster_team_id" = NULL 
                FROM roster_team "rt"
                WHERE ra."roster_athlete_id" = $1 
                    AND rt.roster_team_id = ra.roster_team_id
                    ${skipLocked ? '' : 'AND rt.locked IS NOT TRUE'}
                 RETURNING rt."roster_team_id"`; // Note that we use "roster_team" to return the "id"
                params.push(rosterAthlete.roster_athlete_id);
                historyTitle = 'Removed Athlete';
                historyAction = 'team.member.remove';
                eventClub.roster_team_id = eventClub.athlete?.roster_team_id;
                eventClub.team_name = eventClub.athlete?.team_name;
            }
        } else if(!rosterAthlete && rosterTeamId) {
            sql =
                `INSERT INTO "roster_athlete" (
                    "event_id", "master_athlete_id", "roster_team_id"
                )
                SELECT rt.event_id, ($2)::INTEGER, rt.roster_team_id
                FROM "roster_team" rt 
                INNER JOIN "division" d 
                    ON d.division_id = rt.division_id
                INNER JOIN "event" e 
                    ON e.event_id = rt.event_id 
                WHERE rt.roster_team_id = $1
                    AND (
                        CASE WHEN rt.reg_method = $3
                            THEN TRUE
                            ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                        END    
                    )
                    AND e.registration_method = 'club'
                    AND e.live_to_public IS TRUE
                    ${skipLocked ? '' : 'AND rt.locked IS NOT TRUE'}
                    AND e.published IS TRUE
                    AND e.teams_use_clubs_module IS TRUE
                RETURNING "roster_team_id"`;
            params.push(rosterTeamId, masterAthleteId, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK);
            historyTitle = 'Added Athlete';
            historyAction = 'team.member.add';
        }
        resolve({
            sql     : sql,
            params  : params,
            changeHistoryData: {
                title: historyTitle,
                action: historyAction,
                club: eventClub
            }
        });
    }).then(function (sqlData) {
        if(!sqlData.sql) {
            return null;
        }
        return Db.query(sqlData.sql, sqlData.params)
        .then(function (result) {
            if(_.first(result.rows)) {
                return ({
                    modifiedEventAthletes: _.first(result.rows),
                    historyData: sqlData.changeHistoryData
                })
            }
            return null;
        })
    })
}

function __upsertClubStaff (eventClub, skipLocked) {
    var masterRole = eventClub.master_role,
        rosterRole = eventClub.roster_role;

    /*
        1. Master Role Present
            a. Roster role present 
                Drop "deleted" & "deleted_by_user" values. 
                (NOTES: we use "DELETE" operator to remove master_staff_role rows, 
                so it is not need to check whether msr.deleted field was modified)
            b. Roster role NOT present
                Create roster role
        2. Master Role NOT Present
            a. Roster role present
                Set "deleted" field to roster role
            b. Roster role NOT present
                Do nothing
    */

    return new Promise(function (resolve) {
        var sql, params = [], historyTitle, historyAction;
        if(masterRole) {
            if(rosterRole) {
                sql = 
                    `UPDATE "roster_staff_role" rsr 
                     SET "deleted" = NULL,
                         "deleted_by_user" = NULL 
                     WHERE rsr.roster_staff_role_id = $1
                        AND (
                            "deleted" IS NOT NULL OR 
                            "deleted_by_user" IS NOT NULL
                        ) AND EXISTS (
                            SELECT rt.roster_team_id
                            FROM "roster_team" rt 
                            INNER JOIN "division" d 
                                ON d.division_id = rt.division_id
                            INNER JOIN "event" e 
                                ON e.event_id = rt.event_id 
                            WHERE rt.roster_team_id = rsr.roster_team_id
                                AND (
                                    CASE WHEN rt.reg_method = $2
                                        THEN TRUE
                                        ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                                    END    
                                )
                                AND COALESCE(d.roster_deadline, e.roster_deadline) 
                                                                >= (NOW() AT TIME ZONE e.timezone) 
                                AND e.registration_method = 'club'
                                AND e.live_to_public IS TRUE
                                AND e.published IS TRUE
                                AND e.teams_use_clubs_module IS TRUE
                     )
                     RETURNING rsr."roster_team_id"`;
                params.push(rosterRole.roster_staff_role_id, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK);
                historyTitle = 'Moved Staff';
                historyAction = 'team.member.add';
            } else {
                sql = 
                    `INSERT INTO "roster_staff_role" (
                        "master_staff_id", "master_team_id", "roster_team_id", "role_id",
                        "primary"
                    ) 
                    SELECT 
                        ($2)::INTEGER, rt.master_team_id, rt.roster_team_id, 0, (
                            CASE 
                                WHEN (
                                        SELECT 
                                            COALESCE(COUNT(rsr.*), 0) 
                                        FROM "roster_staff_role" rsr 
                                        INNER JOIN "roster_team" other_rt 
                                            ON other_rt.roster_team_id = rsr.roster_team_id
                                            AND other_rt.event_id = rt.event_id
                                        WHERE rsr.master_staff_id = ($2)::INTEGER
                                            AND rsr.roster_team_id <> $1
                                            AND rsr.deleted IS NULL 
                                            AND rsr.deleted_by_user IS NULL
                                            AND rsr.primary IS NOT NULL
                                    ) > 0
                                    THEN FALSE
                                ELSE NULL
                            END 
                        ) "primary"
                    FROM "roster_team" rt 
                    INNER JOIN "division" d 
                        ON d.division_id = rt.division_id
                    INNER JOIN "event" e 
                        ON e.event_id = rt.event_id 
                    WHERE rt.roster_team_id = $1
                        AND (
                            CASE WHEN rt.reg_method = $3
                                THEN TRUE
                                ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                            END    
                        )
                        ${skipLocked ? '' : 'AND rt.locked IS NOT TRUE'}
                        AND e.registration_method = 'club'
                        AND e.live_to_public IS TRUE
                        AND e.published IS TRUE
                        AND e.teams_use_clubs_module IS TRUE
                        AND NOT EXISTS (
                            SELECT * FROM "roster_staff_role" rsr
                            WHERE rsr.master_staff_id = ($2)::INTEGER
                                AND rsr.master_team_id = rt.master_team_id 
                                AND rsr.roster_team_id = rt.roster_team_id
                        )
                    RETURNING "roster_team_id"`;
                params.push(eventClub.roster_team_id, masterRole.master_staff_id, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK);
                historyTitle = 'Added Staff';
                historyAction = 'team.member.add';
            }
        } else {
            if(rosterRole) {
                sql = 
                    `UPDATE "roster_staff_role" rsr 
                     SET "deleted" = NOW()
                     WHERE rsr.roster_staff_role_id = $1
                        AND rsr.deleted IS NULL
                        AND EXISTS (
                            SELECT rt.roster_team_id
                            FROM "roster_team" rt 
                            INNER JOIN "division" d 
                                ON d.division_id = rt.division_id
                            INNER JOIN "event" e 
                                ON e.event_id = rt.event_id 
                            WHERE rt.roster_team_id = rsr.roster_team_id
                                AND (
                                    CASE WHEN rt.reg_method = $2
                                        THEN TRUE
                                        ELSE e.date_reg_open <= (NOW() AT TIME ZONE e.timezone)
                                    END    
                                )
                                ${skipLocked ? '' : 'AND rt.locked IS NOT TRUE'}
                                AND e.registration_method = 'club'
                                AND e.live_to_public IS TRUE
                                AND e.published IS TRUE
                                AND e.teams_use_clubs_module IS TRUE
                     )
                     RETURNING rsr."roster_team_id"`;
                params.push(rosterRole.roster_staff_role_id, CheckInRosterService.ROSTER_TEAM_REG_METHOD.PRIVATE_LINK);
                historyTitle = 'Removed Staff';
                historyAction = 'team.member.remove';
            }
        }
        resolve({
            sql     : sql,
            params  : params,
            changeHistoryData: {
                title: historyTitle,
                action: historyAction,
                club: eventClub
            }
        });
    }).then(function (sqlData) {
        if(!sqlData.sql) return null;
        return Db.query(sqlData.sql, sqlData.params)
        .then(function (result) {
            if(_.first(result.rows)) {
                return ({
                    upsertResultRows: _.first(result.rows),
                    historyData: sqlData.changeHistoryData
                })
            }
            return null;
        }).catch(function (error) {
            // 23505 - is unique_violation code
            // for more details: http://www.postgresql.org/docs/9.3/static/errcodes-appendix.html
            if(error && (parseInt(error.code, 10) === 23505)) {
                loggers.errors_log.error('Snapshot Error: Unique Event Staffer constraint failed', error)
                return null;
            } else {
                throw error;
            }
        })
    })
}

function __runMembersUpsert (athletesList, staffList, skipLocked) {
    var self                        = this,
        disableMembersQtyRecount    = true;

    return Promise.all([
        Promise.all(
            athletesList.map(function (athlete) {
                return self.upsertRosterAthletes(
                    athlete.master_club_id, athlete.master_athlete_id, athlete.master_team_id, 
                                                                            disableMembersQtyRecount, skipLocked)
                .then(upsertResult => upsertResult.member);
            })
        ), Promise.all(
            staffList.map(function (staff) {
                return self.upsertRosterStaffRoles(
                    staff.master_club_id, staff.master_staff_id, disableMembersQtyRecount, skipLocked)
                .then(upsertResult => upsertResult.member);
            })
        )
    ]).then(function (result) {
        var athletsTeams    = _.flattenDeep(result[0]),
            staffTeams      = _.flattenDeep(result[1]);

        return athletsTeams.concat(staffTeams);
    }).catch(function (err) {
        loggers.errors_log.error(err);
        throw err;
    })
}

function __updateEventTeamsMembersCount (eventTeams) {
    let uniqueTeamsList     = getArrayOfRosterTeamIds(eventTeams);
    let needToExtractIds    = false;

    loggers.debug_log.verbose(
        'Recount Teams Members call, (', uniqueTeamsList.length, ') teams passed. Unique Teams:',
        uniqueTeamsList.length
    );

    if(!uniqueTeamsList.length)
        return Promise.resolve([]);

    return Promise.all([
        __setAthletesQty(uniqueTeamsList, needToExtractIds),
        __setStaffersQty(uniqueTeamsList, needToExtractIds)
    ]);
}

function __addMemberChangeHistoryRow ({ title, action, club }) {
    const member        = club.member,
        teamName        = club.team_name,
        userId          = club.user_id,
        eventId         = club.event_id,
        rosterTeamId    = club.roster_team_id;
    
    let comments = action === 'team.member.add'
        ? `${title} ${member.member_name} to [${teamName}] (${member.organization_code})`
        : `${title} ${member.member_name} from [${teamName}] (${member.organization_code}) team's roster`
    
    const lockActionTypes = ['team.roster.lock.deadline', 'team.roster.lock.online-checkin', 'team.roster.lock.eo'];
    return Db.query(`
            INSERT INTO event_change(action, roster_team_id, user_id, event_id, comments)
            SELECT $1, $2, $3, $4, $5
            WHERE EXISTS (
                SELECT 1 FROM event_change
                WHERE event_id = $4 AND roster_team_id = $2
                  AND action = ANY($6)
            );
        `, [action, rosterTeamId, userId, eventId, comments, lockActionTypes])
        .catch(err => {
        loggers.errors_log.error(err);
    });
}

function __getSanctioningEventWhereSql ({ usav_number, aau_membership_id }) {
    const SANCTIONING = [USAV_SANC_BODY.OTHER];

    if (_.isNumber(usav_number)) {
        SANCTIONING.push(USAV_SANC_BODY.USAV);
    }

    if ((_.isString(aau_membership_id) && aau_membership_id) || !_.isNumber(usav_number)) {
        SANCTIONING.push(USAV_SANC_BODY.AAU, USAV_SANC_BODY.JVA);
    }

    if ((!_.isString(aau_membership_id) || !aau_membership_id) && !_.isNumber(usav_number)) {
        SANCTIONING.push(USAV_SANC_BODY.NINE_MAN);
    }

    return ` AND (
        e.sport_sanctioning_id IN (${swUtils.numArrayToString(SANCTIONING)})
        OR (
            e.sport_sanctioning_id = ${USAV_SANC_BODY.USAV}
                AND rc.country <> '${US_COUNTRY_CODE}'
        )
    )`;
}

var UPDATE_ROSTER_COUNT =
`UPDATE "roster_team" rt
SET "roster_athletes_count" = (
    SELECT COUNT(ra.*) 
    FROM roster_athlete ra 
    WHERE ra.roster_team_id = rt.roster_team_id 
        AND ra.deleted IS NULL 
        AND ra.deleted_by_user IS NULL
), "roster_staff_count" = (
    SELECT COUNT(rsr.*) 
    FROM roster_staff_role rsr 
    WHERE rsr.roster_team_id = rt.roster_team_id
        AND rsr.deleted IS NULL
        AND rsr.deleted_by_user IS NULL
)
WHERE rt.roster_team_id = $1
RETURNING rt.roster_team_id, rt.roster_athletes_count, rt.roster_staff_count`;

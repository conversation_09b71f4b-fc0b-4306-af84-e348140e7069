
const APIService = require('./VerticalInsuranceAPIService');
const UtilsService = require('../lib/swUtils');
const TeamRegistrationQuoteService = require('./vertical-insurance/_TeamRegistrationQuoteService');
const TeamRegistrationQuotePaymentService = require('./vertical-insurance/_TeamRegistrationQuotePaymentService');
const CustomerClientSecretService = require('./vertical-insurance/_CustomerClientSecretService');
const TicketRefundQuotePaymentService = require('./vertical-insurance/_TicketRefundQuotePaymentService');

class VerticalInsuranceService {
    constructor() {
        this.teamRegistrationQuote = new TeamRegistrationQuoteService(APIService);
        this.teamRegistrationQuotePayment = new TeamRegistrationQuotePaymentService(APIService, UtilsService);
        this.customerClientSecret = new CustomerClientSecretService(APIService);
        this.ticketRefundQuotePayment = new TicketRefundQuotePaymentService(APIService, UtilsService);
    }
}

module.exports = new VerticalInsuranceService();

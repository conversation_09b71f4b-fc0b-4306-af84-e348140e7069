
const swUtils = require('../../../../lib/swUtils');
const ConnectRefund = require('./ConnectRefund');
const DirectPaymentRefund = require('./DirectRefund');

const { CUSTOM_PAYMENT } = require('../../../../constants/payments');

class Refund {
    constructor(webhookData) {
        this.webhook = webhookData;

        let paymentType;

        try {
            paymentType = this.webhook.data.object.metadata.purchase_type;
        } catch (err) {
            throw { text: 'Purchase type not found in metadata' };
        }

        this.refund = this.#getRefundType(paymentType);
    }

    async process () {
        try {
            let { amount, chargeID } = await this.validateWebhook();

            await this.refund.process(amount, chargeID);
        } catch (err) {
            if(!_.isUndefined(err.retry) && err.retry === false) {
                loggers.errors_log.error(err);
            } else {
                throw err;
            }
        }
    }

    async validateWebhook () {
        let charge = this.webhook.data.object;

        if(charge.object !== 'charge') {
            throw { text: 'Wrong object passed', retry: false };
        }

        const chargeID = await this.#getStripeChargeId(charge);
        const $amount = swUtils.normalizeNumber(charge.amount_refunded / 100);

        let purchase = await this.refund.getPurchaseData(chargeID);

        if(!purchase) {
            throw { text: 'Purchase not found', retry: false };
        }

        purchase.amount = swUtils.normalizeNumber(purchase.amount);
        purchase.amount_refunded = swUtils.normalizeNumber(purchase.amount_refunded || 0);

        if(purchase.status === 'canceled' || purchase.amount_refunded === $amount) {
            loggers.debug_log.verbose({ message:'Refund is already processed', purchase });
            throw { text: 'Refund is already processed', retry: false };
        }

        if ($amount < purchase.amount_refunded) {
            throw { text: 'Refund amount from stripe is less than amount in purchase table', retry: false };
        }

        if ($amount > purchase.amount + purchase.amount_refunded) {
            throw { text: 'Stripe tried to refund more than client paid', retry: false };
        }

        return { amount: $amount, chargeID };
    }

    #getRefundType (paymentType) {
        if(paymentType !== CUSTOM_PAYMENT.PURCHASE_TYPE) {
            return new ConnectRefund(this.webhook);
        } else {
            return new DirectPaymentRefund(this.webhook);
        }
    }

    async #getStripeChargeId(charge) {
        let id = charge.id;

        if(this.#isDestinationPaymentCharge(charge)) {
            return this.#getStripeChargeIdByPaymentID(id);
        }

        return id;
    }

    #isDestinationPaymentCharge(charge) {
        const paymentType = charge?.metadata?.purchase_type;

        return !charge.destination &&
            !charge.on_behalf_of &&
            charge.id.startsWith('py') &&
            paymentType !== CUSTOM_PAYMENT.PURCHASE_TYPE;
    }

    async #getStripeChargeIdByPaymentID(stripePaymentID) {
        const charge = await Db.query(
            `SELECT ch.stripe_charge_id
                FROM "stripe_charge" ch
                WHERE ch.stripe_payment_id = $1`,
            [stripePaymentID]
        ).then((result) => (result && result.rows[0]) || null);

        if(!charge) throw { text: 'Payment not found' };

        return charge.stripe_charge_id;
    }
}

module.exports = Refund;

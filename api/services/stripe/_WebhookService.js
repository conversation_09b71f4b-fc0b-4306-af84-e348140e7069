'use strict';

const moment = require('moment-timezone');

const { PAYMENT_INTENT: PAYMENT_INTENT_EVENTS } = require('./../../constants/stripe/webhook-events');
const { CUSTOM_PAYMENT } = require('./../../constants/payments');
const DisputeService = require('./webhook/__DisputeService');
const ChargeService = require('./webhook/__ChargeService');
const RefundService = require('./webhook/__RefundService');
const AccountService = require('./_AccountService');
const QueueService = require('./webhook/__QueueService');
const TransferService = require('./webhook/__TransferService');
const PaymentMethodService = require('./webhook/__PaymentMethodService');

class WebhookService {
    constructor(StripeConnect) {
        this.dispute = DisputeService;
        this.charge = new ChargeService(StripeConnect);
        this.refund = RefundService;
        this.account = AccountService;
        this.queue = new QueueService();
        this.stripeConnect = StripeConnect;
        this.transfer = new TransferService(StripeConnect);
        this.paymentMethod = new PaymentMethodService();
    }

    async processWebhook(webhookData) {
        await this.__saveStripeEventRow(webhookData);

        if(
            this.__stripeAccountNotAllowedToProcess(webhookData) ||
            this.__stripeEventNotAllowedToProcess(webhookData))
        {
            return ;
        }

        await this.queue.addJob(webhookData);
    }

    // TODO what should be here??
    __stripeEventNotAllowedToProcess(stripeEventData) {
        const eventType = stripeEventData.type;
        const isPaymentIntentWebhookEvent = Object.values(PAYMENT_INTENT_EVENTS).includes(eventType);

        if(isPaymentIntentWebhookEvent) {
            if(this.__isUncollectedFeePayment(stripeEventData)) {}
        }
    }

    __prepareEventDataForSaving(webhookObj) {
        const eventData = webhookObj.data && webhookObj.data.object || {};

        return {
            event_id    : webhookObj.id,
            type        : webhookObj.type,
            data        : JSON.stringify(webhookObj),
            stripe_id   : eventData.id || null,
            amount      : (eventData.amount / 100) || null,
            status      : eventData.status
        }
    }

    async __saveStripeEventRow(webhookObj) {
        const prepared = this.__prepareEventDataForSaving(webhookObj);

        await Db.query(
            knex('stripe_event').insert(prepared)
        );
    }

    formatStripeDate(date, timezone) {
        return (!timezone ? moment(date * 1000) : moment(date * 1000).tz(timezone)).format('MM/DD/YYYY hh:mm a');
    }

    __stripeAccountNotAllowedToProcess(stripeEventData) {
        const eventType = stripeEventData.type;

        if (['charge.succeeded', 'charge.failed', 'charge.refunded'].includes(eventType) === false) {
            return false;
        }

        const charge          = stripeEventData.data.object;
        const destination     = charge.destination;
        const on_behalf_of    = charge.on_behalf_of;
        const connected_acc   = stripeEventData.account;

        if (this.isUaProject(stripeEventData)) {
            loggers.debug_log.verbose(`project.toLowerCase() === 'ua'`);
            return true;
        }

        if(this.__isUncollectedFeePayment(stripeEventData) || this.__isAdditionalFeePayment(stripeEventData)) {
            return false;
        }

        if(this.isCustomPayment(stripeEventData)) {
            loggers.debug_log.verbose(`purchase_type === 'custom'`);
            return true;
        }

        if(!connected_acc && !destination && !on_behalf_of) {
            loggers.debug_log.verbose(`!connected_acc && !destination && !on_behalf_of`, {destination, on_behalf_of});
            return true;
        }
    }

    isUaProject(stripeEventData) {
        const metadata = this.__extractPaymentIntentEventMetadata(stripeEventData);
        const project = metadata && metadata.project || '';

        return project.toLowerCase() === 'ua';
    }

    isCustomPayment(stripeEventData) {
        const metadata = this.__extractPaymentIntentEventMetadata(stripeEventData);

        return metadata?.purchase_type === CUSTOM_PAYMENT.PURCHASE_TYPE
    }

    async __signatureCheck(rawBody, signature) {
        const stripeInstance = await this.stripeConnect.getInstance();
        const secret = this.__getWebhookSecret(rawBody)

        return stripeInstance.webhooks.constructEvent(rawBody, signature, secret);
    }

    __getWebhookSecret(rawBody) {
        const webhookData = JSON.parse(rawBody);

        return  webhookData.account ?
            sails.config.stripe_api.webhook_secret_connect :
            sails.config.stripe_api.webhook_secret_platform;
    }

    __isUncollectedFeePayment(stripeEventData) {
        const metadata = this.__extractPaymentIntentEventMetadata(stripeEventData);

        const paymentFor = metadata?.payment_for;

        return paymentFor === CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE;
    }

    __isAdditionalFeePayment(stripeEventData) {
        const metadata = this.__extractPaymentIntentEventMetadata(stripeEventData);

        const paymentFor = metadata?.payment_for;

        return paymentFor === CUSTOM_PAYMENT.PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE;
    }

    __extractPaymentIntentEventMetadata(stripeEventData) {
        return stripeEventData?.data?.object?.metadata;
    }
}

module.exports = WebhookService;

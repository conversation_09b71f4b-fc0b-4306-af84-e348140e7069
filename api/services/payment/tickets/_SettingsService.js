'use strict';

const {POINT_OF_SALES_TYPE} = require('../../../constants/sales-hub');

class SettingsService {
    constructor () {}

    get PIN_FIELDS() {
        return ['tickets_lite_pin', 'tickets_buy_pin', 'tickets_admin_pin'];
    }

    get _IDENTICAL_PIN_EVENTS_QUERY() {
        return `
        WITH current_event AS (
            SELECT * FROM event 
                WHERE event_id = $1
        )
        SELECT EXISTS(
            SELECT 1 FROM event AS e 
            WHERE e.season = $2
            AND e.event_id <> $1
            AND (e.tickets_lite_pin = $3
                OR e.tickets_buy_pin = $4 
                OR e.tickets_admin_pin = $5)
            AND (current_event.date_start < e.date_end AND current_event.date_end > e.date_start)
            AND e.allow_ticket_sales IS TRUE 
            AND e.deleted IS NULL 
            AND e.is_test IS FALSE
        )
        FROM current_event
        `
    }

    async getEventPins (eventID) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        const { rows: [ pins ] } = await Db.query(
            squel.select()
                .from('event')
                .fields(this.PIN_FIELDS)
                .where('event_id = ?', eventID)
        );

        if(typeof pins === 'undefined') {
            throw { validation: 'Event not found' };
        }

        return pins;
    }

    async updateEventPins(eventID, pins) {
        if(!eventID) {
            throw { validation: 'Event ID required' };
        }

        const current_season = sails.config.sw_season.current

        const identicalPinEvent = await Db.query(
            this._IDENTICAL_PIN_EVENTS_QUERY,
            [
                eventID,
                current_season,
                pins.tickets_lite_pin,
                pins.tickets_buy_pin,
                pins.tickets_admin_pin,
            ]
        ).then(({ rows }) => rows[0] || null);

        if (identicalPinEvent?.exists) {
            throw {
                validation: `Please change pin, it is not secure`,
            };
        }

        const result = await Db.query(
            knex("event")
                .update(_.pick(pins, this.PIN_FIELDS))
                .where("event_id", eventID)
        );

        if(result.rowCount === 0) {
            throw { validation: 'Event not found' };
        }

        return pins;
    }

    async updateTicketsSettings(eventID, settings) {
        this._handleTicketingModeSettings(settings);

        const ticketSettings = await this._updateTicketSettingsInDB(eventID, settings);

        await this._handlePointOfSalesModeSettingsUpdate(eventID, settings);

        return ticketSettings;
    }

    async _updateTicketSettingsInDB(eventID, settings) {
        const serializedSettings = JSON.stringify(settings);
        const updateQuery = knex('event')
            .update({
                tickets_settings: knex.raw(`COALESCE(tickets_settings, '{}'::JSONB) || ?::JSONB`, serializedSettings),
            })
            .returning('tickets_settings')
            .where('event_id', eventID);
        const updateResult = await Db.query(updateQuery);
        if(updateResult.rowCount === 0) {
            throw { validation: 'Event not found' };
        }
        return updateResult.rows[0].tickets_settings;
    }

    _handleTicketingModeSettings(settings) {
        const { require_recipient_name_for_each_ticket: mode } = settings;
        if(typeof mode === 'undefined') return;
        if(mode === false) {
            settings.require_coupon = false;
        }
    }

    async _handlePointOfSalesModeSettingsUpdate(eventID, settings) {
        const { allow_point_of_sales: mode } = settings;
        if(mode) {
            await SalesHubService.sync.process(eventID, POINT_OF_SALES_TYPE.TICKETS);
        }

    }
}

module.exports = new SettingsService();

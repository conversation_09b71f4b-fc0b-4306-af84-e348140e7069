'use strict';

const co = require('co');

class ParticipationService {
    constructor () {}

    get TICKETS_PAYMENT_FOR_TYPE () {
        return 'tickets';
    }

    get CHECK_PAYMENT_TYPE () {
        return 'check';
    }

    get CARD_PAYMENT_TYPE () {
        return 'card';
    }

    get PAYMENT_STATUS () {
        return {
            PAID    : 'paid',
            PENDING : 'pending',
            CANCELED: 'canceled'
        }
    }

    get REGISTRATION_STATUS () {
        return {
            CANCELED: 'canceled',
            ACTIVE  : 'active'
        }
    }

    get CANCELED_PARTICIPATION_ACTION () {
        return 'participation.canceled';
    }

    cancelParticipation (eventID, purchaseTickets, barcode) {
        return co(function* () {
            if(!eventID) {
                throw { validation: 'Event ID required' };
            }

            if(!barcode) {
                throw { validation: 'Ticket barcode required' };
            }

            if(!_.isArray(purchaseTickets) || !purchaseTickets.length) {
                throw { validation: 'Tickets list required' };
            }

            let purchase = yield this.__getPurchase(eventID, barcode);

            if(_.isEmpty(purchase)) {
                throw { validation: 'Purchase not found' };
            }

            let purchaseTicketsArr = this.__formatTicketsListToArrayAndValidate(purchaseTickets);

            return this.__proceedCancellation(eventID, purchaseTicketsArr, purchaseTickets, purchase);

        }.bind(this));
    }

    __formatTicketsListToArrayAndValidate (purchaseTickets) {
        return purchaseTickets.map(ticket => {
            if(!ticket.purchase_ticket_id) {
                throw { validation: 'Tickets ID required' };
            }

            if(!ticket.camp_name) {
                throw { validation: 'Camp Name required' };
            }

            return Number(ticket.purchase_ticket_id);
        });
    }

    __proceedCancellation (eventID, purchaseTicketsArr, purchaseTickets, purchase) {
        let tr                  = null;

        return co(function* () {
            tr = yield Db.begin();

            let updatedTickets = yield this.__updatePurchaseTicketsRow(purchaseTicketsArr, tr);

            if(updatedTickets !== purchaseTicketsArr.length) {
                throw new Error('Not all camps updated');
            }

            yield Promise.all(
                purchaseTickets.map(ticket => this.__updatePurchaseHistory(eventID, ticket, purchase.purchase_id, tr))
            );

            if(updatedTickets === purchase.available_camps_count) {
                yield this.__updatePurchaseRow(purchase.purchase_id, tr)
            }

            yield tr.commit();

            return purchase;
        }.bind(this))
            .catch(err => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }

                throw err;
            })
    }

    __getPurchase (eventID, barcode) {
        let query = squel.select().from('purchase', 'p')
            .field('p.purchase_id')
            .field(
                `(COUNT(pt.*) FILTER (WHERE pt.registration_status = '${this.REGISTRATION_STATUS.ACTIVE}')) :: INTEGER`,
                'available_camps_count'
            )
            .join('purchase_ticket', 'pt', 'pt.purchase_id = p.purchase_id')
            .join('event', 'e', 'e.event_id = p.event_id')
            .where('p.payment_for = ?', this.TICKETS_PAYMENT_FOR_TYPE)
            .where('e.ticket_camps_registration IS TRUE')
            .where(`
                CASE 
                    WHEN p.type = '${this.CHECK_PAYMENT_TYPE}' 
                        THEN p.status IN ('${this.PAYMENT_STATUS.PAID}', '${this.PAYMENT_STATUS.PENDING}')
                    WHEN p.type = '${this.CARD_PAYMENT_TYPE}'
                        THEN TRUE
                    ELSE FALSE
                END             
            `)
            .where('p.event_id = ?', eventID)
            .where('p.ticket_barcode = ?', barcode)
            .where('p.canceled_date IS NULL')
            .where('p.is_ticket IS TRUE')
            .group('p.purchase_id');

        return Db.query(query).then(result => result.rows[0] || null);
    }

    __updatePurchaseTicketsRow (purchaseTickets, tr) {
        let query = squel.update().table('purchase_ticket', 'pt')
            .set('registration_status', this.REGISTRATION_STATUS.CANCELED)
            .set('available', 0)
            .where(`pt.purchase_ticket_id IN (${purchaseTickets.join()})`)
            .where('pt.available > 0')
            .where('pt.registration_status <> ?::ticket_registration_status', this.REGISTRATION_STATUS.CANCELED);

        return tr.query(query).then(result => result.rowCount);
    }

    __updatePurchaseHistory (eventID, ticket, purchaseID, tr) {
        let query = squel.insert().into('purchase_history', 'ph')
            .set('purchase_ticket_id', Number(ticket.purchase_ticket_id))
            .set('purchase_id', purchaseID)
            .set('action', this.CANCELED_PARTICIPATION_ACTION)
            .set('description', `Participation in ${ticket.camp_name} has been canceled`);

        return tr.query(query);
    }

    __updatePurchaseRow (purchaseID, tr) {
        let query = squel.update().table('purchase', 'p')
            .set('registration_status', this.REGISTRATION_STATUS.CANCELED)
            .where('p.purchase_id = ?', purchaseID)
            .where('p.registration_status <> ?::ticket_registration_status', this.REGISTRATION_STATUS.CANCELED);

        return tr.query(query);
    }
}

module.exports = new ParticipationService();

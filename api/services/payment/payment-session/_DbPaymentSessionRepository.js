const { PAYMENT_SESSION_STATUS } = require('../../../constants/payments');
const BasePaymentSessionRepository = require('./BasePaymentSessionRepository');

class DbPaymentSessionRepository extends BasePaymentSessionRepository {
    async createSession(sessionData, { tr } = {}) {
        const { purchaseTeams, ...session } = sessionData;

        if (!tr) {
            throw new Error('createSession must run with transaction')
        }

        const sessionCreateQuery = knex('payment_session')
            .insert(session)
            .returning('*');

        const paymentSession = await tr
            .query(sessionCreateQuery)
            .then(({ rows }) => rows[0]);

        const sessionTeams = await this.__saveSessionTeams(
            purchaseTeams?.map((team) => ({
                ...team,
                payment_session_id: paymentSession.payment_session_id,
            })),
            tr
        );

        return {
            ...paymentSession,
            purchaseTeams: sessionTeams,
        };
    }

    async updateSession(sessionData, { tr } = {}) {
        if (!tr) {
            throw new Error('updateSession must run with transaction')
        }

        const { purchaseTeams, ...session } = sessionData;

        const sessionCreateQuery = knex('payment_session')
            .update(session)
            .where(
                'provider_payment_intent_id',
                sessionData.provider_payment_intent_id
            )
            .returning('*');

        const paymentSession = await tr
            .query(sessionCreateQuery)
            .then(({ rows }) => rows[0]);

        const sessionTeams = await this.__replacePaymentSessionTeams(
            purchaseTeams?.map((team) => ({
                ...team,
                payment_session_id: paymentSession.payment_session_id,
            })),
            tr
        );

        return {
            ...paymentSession,
            purchaseTeams: sessionTeams,
        };
    }

    async findByProviderPaymentIntentId(provider_payment_intent_id) {
        const query = this.__getBaseQuery().where(
            'provider_payment_intent_id',
            provider_payment_intent_id
        );

        return Db.query(query).then(({ rows }) => rows[0]);
    }

    async findPendingByUserId(user_id) {
        const query = this.__getBaseQuery()
            .where('user_id', user_id)
            .where('status', PAYMENT_SESSION_STATUS.PENDING);

        return Db.query(query).then(({ rows }) => rows);
    }

    async clearSession({
        provider_payment_intent_id,
        user_id,
        payment_provider,
    }, { tr }) {

        const paymentSessionID = await this.__removePaymentSession({
            provider_payment_intent_id,
            user_id,
            payment_provider,
        }, { tr });

        if(paymentSessionID) {
            await this.__deletePaymentSessionTeams(paymentSessionID, tr);
        }
    }

    async __removePaymentSession({
         provider_payment_intent_id,
         user_id,
         payment_provider
    }, { tr } = {}) {
        if (!tr) {
            throw new Error('__removePaymentSession must run with transaction');
        }

        if(!provider_payment_intent_id) {
            throw new Error('Provider payment intent ID required');
        }

        if(!user_id) {
            throw new Error('User ID required');
        }

        if(!payment_provider) {
            throw new Error('Payment provider required');
        }

        const query = knex('payment_session')
            .delete()
            .where('provider_payment_intent_id', provider_payment_intent_id)
            .where('user_id', user_id)
            .where('payment_provider', payment_provider)
            .returning('payment_session_id')

        return tr.query(query).then(({ rows }) => rows[0]?.payment_session_id);
    }

    async __saveSessionTeams(sessionTeams, tr) {
        if (!tr) {
            throw new Error('__saveSessionTeams must run within transaction');
        }

        if (!sessionTeams?.length) {
            return [];
        }

        const teamsSaveQuery = knex('payment_session_team')
            .insert(sessionTeams)
            .onConflict(['payment_session_id', 'roster_team_id'])
            .merge()
            .returning('*');

        return tr.query(teamsSaveQuery).then(({ rows }) => rows[0]);
    }

    async __replacePaymentSessionTeams(sessionTeams, tr) {
        if (!tr) {
            throw new Error('__saveSessionTeams must run within transaction');
        }

        if (!sessionTeams?.length) {
            return [];
        }

        await this.__deletePaymentSessionTeams(sessionTeams[0].payment_session_id, tr);
        return this.__saveSessionTeams(sessionTeams, tr);
    }

    async __deletePaymentSessionTeams(paymentSessionID, tr) {
        if (!tr) {
            throw new Error('__saveSessionTeams must run within transaction');
        }

        if (!paymentSessionID) {
            throw new Error('Payment session ID required');
        }

        const query = knex('payment_session_team')
            .delete()
            .where('payment_session_id', paymentSessionID);

        await tr.query(query);
    }

    __getBaseQuery() {
        return knex('payment_session as ps')
            .select({
                payment_session_id: 'ps.payment_session_id',
                amount: 'ps.amount',
                provider_payment_intent_id: 'ps.provider_payment_intent_id',
                payment_provider: 'ps.payment_provider',
                status: 'ps.status',
                event_id: 'ps.event_id',
                user_id: 'ps.user_id',
                data: 'ps.data',
                created: 'ps.created',
                modified: 'ps.modified',
                purchaseTeams: knex.raw(`json_agg(pst)`),
            })
            .leftJoin(
                'payment_session_team as pst',
                'pst.payment_session_id',
                'ps.payment_session_id'
            )
            .groupBy('ps.payment_session_id');
    }
}

module.exports = new DbPaymentSessionRepository();

const StripePendingPaymentProcessor = require("../../teams/payment-processor/stripe/_StripePendingPaymentProcessor");
const AbstractPaymentStrategy = require("../_AbstractPaymentStrategy");
const { trackJobProgress } = require("../../../../../worker/utils/progress-tracker");

class ProcessingPaymentStrategy extends AbstractPaymentStrategy {
  async execute() {
    const paymentIntent = this.webhookData.data.object;
  
    trackJobProgress(30);

    if (_.isEmpty(this.sessionPayment)) {

      if (this.paymentData.payment_intent_status === 'requires_action') {
        await StripePendingPaymentProcessor.updatePendingPayment(this.paymentData, this.webhookData);
        trackJobProgress(80);
      }

    } else if (this.sessionPayment.data.payment.type === 'ach') {
      await StripePendingPaymentProcessor.saveUSBankAccountPendingPayment(this.webhookData, this.sessionPayment);
      trackJobProgress(80);
    }
  }
}

module.exports = ProcessingPaymentStrategy;
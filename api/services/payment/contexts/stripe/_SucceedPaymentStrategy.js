const StripeSuccessPaymentProcessor = require("../../teams/payment-processor/stripe/_StripeSuccessPaymentProcessor");
const StripeSuccessPendingPaymentProcessor = require("../../teams/payment-processor/stripe/_StripeSuccessPendingPaymentProcessor");
const AbstractPaymentStrategy = require("../_AbstractPaymentStrategy");
const { trackJobProgress } = require("../../../../../worker/utils/progress-tracker");

class SucceededPaymentStrategy extends AbstractPaymentStrategy {
  async execute() {
    const paymentIntent = this.webhookData.data.object;
    const charge = paymentIntent?.charges?.data?.[0];

    trackJobProgress(40);

    if (_.isEmpty(this.sessionPayment)) {
      if (_.isEmpty(charge)) {
        throw new Error('Payment Intent charge not found');
      }

      if(_.isObject(this.paymentData) && !_.isEmpty(this.paymentData)) {
        await StripeSuccessPaymentProcessor.validatePayment(this.webhookData, this.paymentData);
        trackJobProgress(60);

        await StripeSuccessPendingPaymentProcessor.saveSuccessPendingPayment(this.paymentData, this.webhookData);
        trackJobProgress(80);
      }

    } else {
      if(_.isObject(this.sessionPayment?.data?.payment) && !_.isEmpty(this.sessionPayment?.data?.payment)) {
        await StripeSuccessPaymentProcessor.validatePayment(this.webhookData, this.sessionPayment.data.payment);
        trackJobProgress(60);

        await StripeSuccessPaymentProcessor.saveSuccessPayment(this.webhookData, this.sessionPayment);
        trackJobProgress(80);
      }
    }
  }
}

module.exports = SucceededPaymentStrategy;

const swUtils = require("../../../../lib/swUtils");
const BalanceInformationService = require("../../../teams-payments/BalanceInformationService");
const TeamsPaymentNotificationsService = require("../notification/_TeamsPaymentNotificationsService");
const BasePaymentProcessor = require("./_BasePaymentProcessor");

class AbstractSuccessPaymentProcessor extends BasePaymentProcessor {
    async saveSuccessPayment(webhookData, paymentSession) {
        const { data: { recountedReceipt, settings, fees, payment }, event_id } = paymentSession;

        let BalanceInformation = new BalanceInformationService();
        await TeamsPaymentService.recordBalanceInformation('before', Number(event_id), 0, BalanceInformation);

        let tr;
        let purchaseId;

        try {
            tr = await Db.begin();

            if (payment.changePaymentType) {
                purchaseId =
                    await this.teamsPaymentDataService.changePurchaseType(tr, {
                        paymentSession,
                        webhookData,
                    });
            } else {
                purchaseId =
                    await this.teamsPaymentDataService.savePurchaseData(tr, {
                        paymentSession,
                        webhookData,
                    });
            }

            await this.__updateBalance(tr, purchaseId, BalanceInformation, payment, recountedReceipt, fees);

            await this.teamsPaymentDataService.savePaymentData(tr, { payment, webhookData, settings });

            if(!_.isEmpty(paymentSession?.data?.payment?.verticalInsuranceQuote)) {
                await VerticalInsuranceService.teamRegistrationQuotePayment.payQuote(
                    paymentSession?.data?.payment?.verticalInsuranceQuote,
                    paymentSession?.data?.payment?.user,
                    webhookData?.data?.object,
                )
            }
            
            await this.__removePaymentSession({
                tr,
                provider_payment_intent_id: paymentSession.provider_payment_intent_id,
                user_id: paymentSession.user_id,
            })

            await this.__savePaymentCard(tr, webhookData) 

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }

        await TeamsPaymentNotificationsService.sendSuccessPaymentNotification(
            Object.assign({}, payment, { purchase_id: purchaseId })
        );

        await this.__updatePaymentMetadata(webhookData, Number(event_id), purchaseId);
    }

    __updatePaymentMetadata(webhookData, eventId, purchaseId) {
        const metadata = {
            purchase_id: purchaseId,
            link_to_purchase: TeamsPaymentService.metadata.getPurchasePage(eventId, purchaseId)
        };

        return this.teamsPaymentDataService.updatePaymentMetadata(webhookData, metadata);

    }

    async __updateBalance(tr, purchaseID, service, payment, recountedReceipt, fees) {
        await TeamsPaymentService.recordBalanceInformation(
            'after', Number(payment.event_id), recountedReceipt.swFee, service, tr
        );

        let { sw_fee, extra_fee } = fees.swFeeDetails.details;

        service.sw_fee = swUtils.normalizeNumber(sw_fee + extra_fee);

        return service.updateBalanceInformation(tr, purchaseID);
    }

    async __savePaymentCard(tr, webhookData) {
        throw new Error('__savePaymentCard is not implemented');
    }

    async __removePaymentSession(params) {
        throw new Error('__removePaymentSession is not implemented');
    }
}

module.exports = AbstractSuccessPaymentProcessor

const { PAYMENT_PROVIDER } = require("../../../../constants/payments");
const AbstractTeamsPaymentSessionService = require("./_AbstractTeamsPaymentSessionService");

class JustifiTeamsPaymentSessionService extends AbstractTeamsPaymentSessionService{
    __getPaymentIntentId (payment) {
        return payment.justifi_checkout_id
    }

    get paymentProviderType() {
        return PAYMENT_PROVIDER.JUSTIFI
    }

}

module.exports = new JustifiTeamsPaymentSessionService();

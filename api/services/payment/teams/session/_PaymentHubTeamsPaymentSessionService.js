const { PAYMENT_PROVIDER } = require("../../../../constants/payments");
const AbstractTeamsPaymentSessionService = require("./_AbstractTeamsPaymentSessionService");

class PaymentHubTeamsPaymentSessionService extends AbstractTeamsPaymentSessionService{
    __getPaymentIntentId (payment) {
        return payment.payment_hub_payment_intent_id
    }

    get paymentProviderType() {
        return PAYMENT_PROVIDER.PAYMENT_HUB
    }

}

module.exports = new PaymentHubTeamsPaymentSessionService();

const JustifiTeamsPaymentSessionService = require('./session/_JustifiTeamsPaymentSessionService');
const PaymentHubTeamsPaymentSessionService = require('./session/_PaymentHubTeamsPaymentSessionService');
const StripeTeamsPaymentSessionService = require('./session/_StripeTeamsPaymentSessionService');

class TeamsPaymentSessionService {
    get stripe() {
        return StripeTeamsPaymentSessionService
    }

    get paymentHub() {
        return PaymentHubTeamsPaymentSessionService
    }

    get justifi() {
        return JustifiTeamsPaymentSessionService
    }
}

module.exports = new TeamsPaymentSessionService();

const swUtils = require('../../../../lib/swUtils');
const moment = require('moment');

const { FEE_PAYER } = require('../../../../constants/payments');

class AbstractTeamsPaymentDataService {
    __getPaymentStatus(paymentData) {
        throw new Error('__getPaymentStatus not implemented');
    }

    __mapWebhookDataToPaymentRow(paymentData) {
        throw new Error('__mapPaymentDataToPaymentRow not implemented')
    }

    async savePaymentData(tr, payment, webhookData) {
        throw new Error('savePaymentData not implemented')
    }

    async updatePaymentData(tr, paymentDataId, dataForUpdate) {
        throw new Error('updatePaymentData not implemented')
    }

    async cancelPendingPurchase(tr, webhookData) {
        throw new Error('cancelPendingPurchase not implemented')
    }

    async getPendingPurchaseRow(paymentDataId) {
        throw new Error('getPendingPurchaseRow not implemented')
    }

    async updatePaymentMetadata(paymentDataId, metadata) {
        throw new Error('updatePaymentMetadata not implemented')
    }

    __mapWebhookDataToPurchaseHistory(webhookData) {
        throw new Error('__mapWebhookDataToPurchaseHistory not implemented')
    }

    async savePurchaseData(tr, { paymentSession, webhookData }) {
        const { purchaseTeams, data: { payment, recountedReceipt, settings, fees }} = paymentSession;

        const paymentStatus = this.__getPaymentStatus(webhookData);

        const purchaseID = await this.createPurchaseRow(
            tr, payment, recountedReceipt, fees, settings, webhookData
        );

        await TeamsPaymentService.processReceiptTeams(
            tr, purchaseID, payment.event_id, settings.reg_fee, purchaseTeams, paymentStatus
        );

        let namesOfTeams = TeamsPaymentService.getTeamsNamesStr(purchaseTeams);

        await TeamsPaymentService.insertPurchaseHistoryRow(
            tr, purchaseID, namesOfTeams, payment.user.user_id, recountedReceipt.total
        );

        return purchaseID;
    }

    __calculateNetProfitSubtract(settings, takenFees) {
        return TeamsPaymentService.__getFeesToSubtractFromNetProfit__(settings, takenFees);
    }

    async changePurchaseType(tr, { paymentSession, webhookData }) {
        const { purchaseTeams, data: { payment, recountedReceipt, settings, fees }} = paymentSession;

        const {
            event_id,
            purchase_id,
            type: paymentType,
            user,
            changePaymentType: changeFromType,
            declinedTeams = [],
        } = payment;

        const paymentStatus = this.__getPaymentStatus(webhookData);

        const dataForUpdate = this.preparePurchaseData(
            payment,
            recountedReceipt,
            fees,
            settings,
            webhookData
        );

        await this.updatePurchaseRow(tr, purchase_id, dataForUpdate);

        await TeamsPaymentService.typeChangeHistoryRow(
            tr,
            purchase_id,
            user.user_id,
            recountedReceipt.total,
            changeFromType,
            paymentType
        );

        await (TeamsPaymentService.updateTeamRows(
            tr, purchaseTeams, paymentStatus, event_id, purchase_id
        ));

        if (declinedTeams.length > 0) {
            let { canceledQty, changedPaidStatusQty } =
                await TeamsPaymentService.processDeclinedTeams(
                    tr,
                    purchase_id,
                    declinedTeams
                );
            loggers.debug_log.verbose('Canceled', canceledQty, 'declined teams');
            loggers.debug_log.verbose('Changed paid status', changedPaidStatusQty, 'declined teams');
        }

        return purchase_id
    }

    createPurchaseRow(
        tr,
        payment,
        recountedReceipt,
        fees, settings,
        paymentData
    ) {
        let dataForInsert = this.preparePurchaseData(
            payment,
            recountedReceipt,
            fees,
            settings,
            paymentData
        );

        return tr.query(knex('purchase AS p').insert(dataForInsert).returning('purchase_id'))
            .then(result => result?.rows?.[0]?.purchase_id);
    }

    preparePurchaseData(
        payment,
        recountedReceipt,
        fees,
        settings,
        paymentData
    ) {

        const paymentStatus = this.__getPaymentStatus(paymentData);

        const takenFees = fees?.swFeeDetails?.details;
        let netProfitSubtract = this.__calculateNetProfitSubtract(settings, takenFees)

        if(this.__getProviderFeePayer(settings) === FEE_PAYER.BUYER) {
            recountedReceipt.total += Number(takenFees.provider_fee);
            netProfitSubtract += Number(takenFees.provider_fee);
        }

        return {
            event_id: payment.event_id,
            amount: recountedReceipt.total,
            club_owner_id: payment.club_owner_id,
            type: payment.type,
            user_id: payment.user.user_id,
            phone: payment.user.phone,
            net_profit: swUtils.normalizeNumber(recountedReceipt.total - netProfitSubtract),
            date_paid: moment(),
            received_date: moment(),
            additional_fee_amount: fees.extraFee,
            collected_sw_fee: fees.swFee,
            status: paymentStatus,
            roster_club_id: settings.roster_club_id,
            payment_for: 'teams',
            ...this.__mapWebhookDataToPaymentRow(paymentData),
            ...this.__mapProviderFields({ settings, payment, fees }),
        };
    }

    __mapProviderFields({ settings, payment }) {
        throw new Error('__getProviderFields not implemented');
    }

    __getProviderFeePayer(settings) {
        throw new Error('__getProviderFeePayer not implemented')
    }

    async updatePurchaseRow(tr, purchaseID, dataForUpdate) {
        let query = knex('purchase AS p')
            .update(dataForUpdate)
            .where('p.purchase_id', purchaseID);

        let updated = await tr.query(query).then(result => result?.rowCount === 1);

        if (!updated) {
            throw new Error('Purchase row does not updated: ' + purchaseID);
        }
    }

    async setRosterTeamPaymentStatusCancelled(tr, purchaseID) {
        return this.__updateRosterTeamStatus(tr, TeamsPaymentService.PAYMENT_STATUS.NONE, purchaseID);
    }

    async setRosterTeamPaymentStatusPaid(tr, purchaseID) {
        return this.__updateRosterTeamStatus(tr, TeamsPaymentService.PAYMENT_STATUS.PAID, purchaseID);
    }

    async __updateRosterTeamStatus(tr, status, purchaseID) {
        let query =
            `UPDATE roster_team rt
             SET status_paid = $1
             FROM purchase_team pt 
             WHERE rt.roster_team_id = pt.roster_team_id AND pt.purchase_id = $2`;

        let updated = await tr.query(query, [status, purchaseID])
            .then(result => !!result?.rowCount);

        if (!updated) {
            throw new Error('Roster Team not updated');
        }
    }

    async insertHistoryRow(tr, { purchaseId, webhookData, action, description }) {
        let query = knex('purchase_history')
            .insert({ purchase_id: purchaseId, action, description, ...this.__mapWebhookDataToPurchaseHistory(webhookData) });

        return tr.query(query);
    }

    async updatePurchaseTeamRow(tr, purchaseID, dataToUpdate) {
        let query = knex('purchase_team AS pt')
            .update(dataToUpdate)
            .where('pt.purchase_id', purchaseID);

        let updated = await tr.query(query).then(result => !!result?.rowCount);

        if (!updated) {
            throw new Error('Purchase Team not updated');
        }
    }
}

module.exports = AbstractTeamsPaymentDataService;

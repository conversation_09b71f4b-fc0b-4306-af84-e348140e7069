'use strict';

const co = require('co');

function SendWaitlistedNotificationService() {}


SendWaitlistedNotificationService.prototype.sendWaitlistedStatusEmail = function (ticket) {
    return co(function* () {
        let emailData = yield this.__getDataForWaitlistNotification(ticket.event_ticket_id);

        if(!emailData) {
            throw new Error('Event not found!');
        }

        return EmailService.renderAndSend({
            template: 'tickets/eo_notifications/tickets_waitlist_notification',
            data: {
                camp_name        : emailData.camp_name,
                ticket_name      : emailData.ticket_name,
                purchases_count  : ticket.payments_count + 1,
                tickets_max_count: ticket.waitlist_switching_count
            },
            from: 'SportWrench <<EMAIL>>',
            to: emailData.email,
            subject: `Camp ${emailData.camp_name} moved to waitlist`
        });
    }.bind(this));
};

SendWaitlistedNotificationService.prototype.__getDataForWaitlistNotification = function (eventTicketID) {
    let query = `SELECT
                  ec.name "camp_name",
                  et.label "ticket_name",
                  e.email
                FROM event_ticket et
                LEFT JOIN event_camp ec
                  ON ec.event_camp_id = et.event_camp_id
                LEFT JOIN event e
                  ON e.event_id = et.event_id
                WHERE et.event_ticket_id = $1`;

    return Db.query( query, [eventTicketID] ).then( result => result.rows[0] );
};

module.exports = new SendWaitlistedNotificationService();

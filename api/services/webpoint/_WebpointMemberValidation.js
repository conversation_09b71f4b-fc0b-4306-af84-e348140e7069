'use strict';

const co        = require('co');
const moment    = require('moment');

const SafeSportService  = require('../../lib/SafeSportService');

class WebpointMemberValidation {
    constructor () {}

    get GENDER_REG_EXP () {
        return /^(?:male|female|unspecified)$/;
    }

    get USAV_NUMBER_REGEX () {
        return /[0-9]{7,7}/;
    }

    get WP_RESPONSE_GENDER () {
        return {
            MALE: 'M'
        }
    }

    get CURRENT_MEMBER_STATUS () {
        return 'Current';
    }

    get WP_RESPONSE_BIRTHDATE_FORMAT () {
        return 'M/D/YYYY';
    }

    get WP_MEMBER_VALIDATION_ERROR_TEXT () {
        let errorMessage = '';

        if(this.__errorFields.length) {
            errorMessage = `Entered information doesn’t match USAV data for your USAV Membership. 
	            Please check these fields: ${this.__errorFields.join(', ')}.`
        }

        if(this.__hasExpiredMembership) {
            errorMessage += ` Webpoint Member Status not "Current"`;
        }

        return errorMessage;
    }

    get FRONTEND_FIELD_NAMES() {
        return {
            first: 'director_first',
            last: 'director_last',
            directorUsavCode: 'director_usav_code',
            gender: 'director_gender',
            birthdate: 'director_birthdate',
            region: 'region',
            clubUsavCode: 'usav_code',
        }
    }

    /**
     * Compares passed member data with Webpoint response data.
     * Throws an exception if data not equal.
     *
     * @param {Object}  memberData                   - An object containing members's data (see below)
     * @param {string}  memberData.usav_code         - Member's USAV number
     * @param {string=} memberData.first             - Member's first name
     * @param {string=} memberData.last              - Member's last name
     * @param {string=} memberData.club_code         - Member's club code
     * @param {string=} memberData.region            - Member's region
     * @param {string=} memberData.gender            - Member's gender ('male' or 'female')
     * @param {Object=} memberData.birthdate         - An object containing members's birthdate data (see below)
     * @param {string}  memberData.birthdate.date    - Member's birthdate date
     * @param {string}  memberData.birthdate.format  - Member's birthdate format (e.g. 'YYYY-MM-DD', 'MM/DD/YYYY')
     * @returns {Object}                             - Parsed Webpoint member data and raw Webpoint member data
     */
    async validateUsavMember (memberData) {
        const ss = new SafeSportService(Db);

        let memberID = _.first(memberData.usav_code.match(this.USAV_NUMBER_REGEX));

        let wpMemberData = await WebpointDataReceiver.v2.getMember(memberID);

        let member = ss.extractMemberFromWebpointResponse(wpMemberData);

        if(!member) {
            throw { validation: 'USAV code is not valid!' };
        }

        let validationError = this.__validateMemberData(memberData, member);

        return { rawWpData: wpMemberData, parsedWpData: member, validationError };
    }

    __validateMemberData (memberData, webpointData) {
        this.__hasExpiredMembership = false;

        this.__errorFields = [];
        this.__frontendErrorFiedls = [];


        if (memberData.gender && !this.GENDER_REG_EXP.test(memberData.gender)) {
            throw { validation: 'Gender should be "male" or "female" or "unspecified"' };
        }

        if (memberData.usav_code !== webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.USAVNUMBER]) {
            this.__errorFields.push('"USAV Club Director Membership number"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.directorUsavCode);
        }

        if(memberData.first && !this.__isNameEqual(memberData.first, webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.FIRST_NAME])) {
            this.__errorFields.push('"First Name"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.first);
        }

        if(memberData.last && !this.__isNameEqual(memberData.last, webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.LAST_NAME])) {
            this.__errorFields.push('"Last Name"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.last);            
        }

        if(memberData.gender && !this.__isGenderEqual(memberData.gender, webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.GENDER])) {
            this.__errorFields.push('"Gender"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.gender);
        }

        if(memberData.birthdate && !this.__isBirthdateEqual(memberData.birthdate, webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.BIRTH_DATE])) {
            this.__errorFields.push('"Birthdate"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.birthdate);
        }

        if(memberData.club_code && memberData.club_code !== webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.CLUB_CODE]) {
            this.__errorFields.push('"Club USAV Code"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.clubUsavCode);
        }

        if(memberData.region && !this.__regionIsValid(memberData.region, webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.USAVNUMBER])) {
            this.__errorFields.push('"Region"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES.region);
        }

        if(!this.__isWebpointMemberStatusCurrent(webpointData[WebpointService.WEBPOINT_RESPONSE_KEY.MEMBER_STATUS])) {
            this.__hasExpiredMembership = true;
        }

        if(this.__errorFields.length || this.__hasExpiredMembership) {
            return {
                validation          : this.WP_MEMBER_VALIDATION_ERROR_TEXT,
                hasExpiredMembership: this.__hasExpiredMembership,
                notValidFields: this.__frontendErrorFiedls,
            };
        }
    }

    __regionIsValid (dbRegion, wpUSAV) {
        let wpRegion = this.__getWPRegion(wpUSAV);

        return wpRegion && (wpRegion === dbRegion);
    };

    __isWebpointMemberStatusCurrent (memberStatus) {
        return memberStatus === this.CURRENT_MEMBER_STATUS;
    }

    __isNameEqual (stringOne, stringTwo) {
        return String(stringOne).toUpperCase() === String(stringTwo).toUpperCase();
    }

    __getWPRegion (wpUSAV) {
        return wpUSAV && wpUSAV.substring(0,2);
    }

    __isBirthdateEqual (dbBirthdate, wpBirthdate) {
        wpBirthdate = moment(wpBirthdate, this.WP_RESPONSE_BIRTHDATE_FORMAT).format('DD/MM/YYYY');
        dbBirthdate = moment(dbBirthdate.date, dbBirthdate.format).format('DD/MM/YYYY');

        return wpBirthdate === dbBirthdate;
    };

    __isGenderEqual (dbGender, wpGender) {
        wpGender = (wpGender === this.WP_RESPONSE_GENDER.MALE) ? 'male' : 'female';

        return wpGender === dbGender;
    };
}

module.exports = new WebpointMemberValidation();

"use strict";

const FormattersService = require('./notification-formatter/_NotificationFormattersService');

const {
    TEAM_ENTERED_ON_EVENT,
    TEAM_DELETED_FROM_EVENT,
    TEAM_STATUS_CHANGED_TO_DECLINED,
    R<PERSON><PERSON>TRATION_PAID_BY_CARD,
    REG<PERSON>TRATION_PAID_BY_CHECK,
    TEAM_STATUS_CHANGED_TO_ACCEPTED,
    REG<PERSON><PERSON>ATION_PAYMENT_REFUNDED,
    REGISTRATION_PAYMENT_REFUNDED_PARTIALLY,
    REGISTRATION_PAYMENT_CANCELLED,
    TEAM_ONLINE_CHECKIN_CHECKED_IN,
    TEAM_CHANGED_DIVISION,
} = require('../constants/notification-actions');

class NotificationFormatter {
    constructor (FormattersService) {
        this.formatters = FormattersService.formatters;
    }

    get NOTIFICATION_TARGET_EVENT () {
        return {
            NAME: 'event',
            ACTIONS: [
                TEAM_ENTERED_ON_EVENT,
                TEAM_DELETED_FROM_EVENT,
                TEAM_STATUS_CHANGED_TO_DECLINED,
                REGISTRATION_PAID_BY_CARD,
                REGISTRATION_PAID_BY_CHECK,
                TEAM_STATUS_CHANGED_TO_ACCEPTED,
                REGISTRATION_PAYMENT_REFUNDED,
                REGISTRATION_PAYMENT_REFUNDED_PARTIALLY,
                REGISTRATION_PAYMENT_CANCELLED,
                TEAM_ONLINE_CHECKIN_CHECKED_IN,
                TEAM_CHANGED_DIVISION
            ]
        };
    }

    get NOTIFICATION_TARGET_CLUB () {
        return {
            NAME: 'club',
        };
    }

    format (notifications, target = this.NOTIFICATION_TARGET_EVENT.NAME) {
        if(!notifications) return;

        let actionsRequiresRemoving = [];
        if (target === this.NOTIFICATION_TARGET_EVENT.NAME) {
            actionsRequiresRemoving = actionsRequiresRemoving.concat(this.#filterEventNotification(notifications));
        }

        for(let notification of notifications) {
            const { action } = notification;

            if(_.isFunction(this.formatters[action])) {
                const {
                    title,
                    comments,
                } = this.formatters[action](notification);

                if(title) {
                    notification.title = title;
                    // rewrite notification.comments with the value from formatter
                    notification.comments = comments;
                }
            } else {
                if (target === this.NOTIFICATION_TARGET_EVENT.NAME || target === this.NOTIFICATION_TARGET_CLUB.NAME) {
                    actionsRequiresRemoving.push(notification);
                } else {
                    notification.title = notification.action;
                }
            }
        }

        for (let notificationToRemove of actionsRequiresRemoving) {
            let index = notifications.indexOf(notificationToRemove);
            if(index >= 0) {
                notifications.splice(index, 1);
            }
        }

        return notifications;
    }

    #filterEventNotification(data) {
        return data.filter((item) => {
            return !this.NOTIFICATION_TARGET_EVENT.ACTIONS.includes(item.action);
        });
    }

}

module.exports = new NotificationFormatter(FormattersService);

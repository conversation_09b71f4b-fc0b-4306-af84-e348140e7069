angular.module('SportWrench').controller('NoRankTeamsController', NoRankTeamsController);

function NoRankTeamsController ($scope, $http) {
    $scope.teams = [];
    _load();

    $scope.fix = function () {
         $http.get('/api/admin/club/teams/norank/fix').success(function (data) {
            _load();
        })
    }

    function _load () {
        $http.get('/api/admin/club/teams/norank').success(function (data) {
            $scope.teams = data.teams;
        })
    }
}

<form class="form-horizontal" name="$ctrl.credForm" ng-submit="$ctrl.submit()" novalidate>
	<fieldset ng-disabled="$ctrl.sending">
	    <div class="form-group">
	        <label class="col-sm-2 control-label">Username</label>
	        <div class="col-sm-10">
	            <input type="text" class="form-control" ng-model="$ctrl.data.username" required>
	        </div>
	    </div>
	    <div class="form-group">
	        <label class="col-sm-2 control-label">Password</label>
	        <div class="col-sm-10">
	            <input type="password" class="form-control" ng-model="$ctrl.data.pswd" required>
	        </div>
	    </div>
	    <div class="form-group">
	        <label class="col-sm-2 control-label">Club ID</label>
	        <div class="col-sm-10">
	            <input type="number" class="form-control" ng-model="$ctrl.data.club_id" required>
	        </div>
	    </div>
	    <div class="form-group">
	        <div class="col-sm-offset-2 col-sm-10">
	            <div class="checkbox">
	                <label>
	                    <input type="checkbox" ng-model="$ctrl.data.agree"> Agree
	                </label>
	            </div>
	        </div>
	    </div>
	    <div class="form-group">
	        <div class="col-sm-offset-2 col-sm-10">
	            <button type="submit" class="btn btn-default">Run Parser</button>
	        </div>
	    </div>
    </fieldset>
</form>

angular.module('SportWrench').component('wpCredentialsForm', {
	templateUrl 	: 'public/usav/parser/form.html',
	bindings 		: {
		onSubmit 		: '&sbtm',
	},
	controller 		: ['toastr', WPCredentialsFormController]
});

function WPCredentialsFormController (toastr) {
	var _self = this;

	this.sending = false;

	this.wpData = {};

	this.submit = function () {
		if (this.credForm.$invalid) {
			toastr.warning('Invalid Form Data');
			return;
		}

		if (this.sending) {
			return;
		}

		this.sending = true;

		this.onSubmit({
			data: this.wpData
		}).finally(function () {
			_self.sending = false;
			_self.credForm.$setPristine();
		})
	}
}
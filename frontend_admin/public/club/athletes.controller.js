angular.module('SportWrench').controller('ClubAthletesController', ClubAthletesController);


function ClubAthletesController ($scope, $http, $uibModal) {
    $scope.athletes = [];
    $scope.roles = [];
    $scope.search = {};

    _loadRoles(); 

    $scope.findAthletes = function () {
        $http({
            method: 'GET',
            url: '/api/admin/club/master/athlete/find?' + $.param($scope.search)
        }).success(function (data) {
            $scope.athletes = data.athletes;
        })
    }

    $scope.openOptionsModal = function (athlete) {
        var _roles = $scope.roles;
        var find_athletes = $scope.findAthletes;
        $uibModal.open({
            templateUrl: 'public/club/athlete_options/club.athlete.modal.html',
            controller: function ($scope, $uibModalInstance) {  
                $scope.athlete = athlete;              
                $scope.teams = [];
                $scope.roles = _roles;

                $scope.data_changed = false;

                _loadClubTeams(athlete.master_club_id, function (teams) {
                    $scope.teams = teams;
                });

                $scope.move_to_staff = function (data, cb) {
                    if(!data) return;
                    data.master_athlete_id = $scope.athlete.master_athlete_id;
                    
                    $http({
                        method: 'POST',
                        url: '/api/admin/club/master/athlete/tostaff',
                        data: data
                    }).success(function () {
                        $scope.data_changed = true;
                        if(cb) return cb();
                    });
                }

                $scope.close = function () {                    
                    $uibModalInstance.close();
                }

                function _onClose () {
                    if($scope.data_changed)
                        find_athletes();
                }

                $uibModalInstance.result.then(_onClose, _onClose);
            }
        });
    }

    function _moveToStaff (params, cb) {
        $http({
            method: 'POST',
            url: '/api/admin/club/master/athlete/tostaff',
            data: params
        }).success(function () {
            console.log('OK');
        }).finally(function () {
            return cb();
        })
    }

    function _loadClubTeams (club_id, cb) {
        if(!club_id) return;
        var _teams = [];
        $http.get('/api/admin/club/' + club_id + '/teams')
        .success(function (data) {
            _teams = data.teams;
        }).finally(function () {
            return cb(_teams);
        })
    }

    function _loadRoles () {
        $http.get('/api/admin/club/staff/roles')
            .success(function (data) {
                $scope.roles = data.roles;
            })
    }
}

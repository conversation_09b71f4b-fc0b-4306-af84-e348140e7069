<div class="container">
    <p class="lead text-center">Copy production DB</p>
    <form name="$ctrl.form" class="form-horizontal" ng-submit="$ctrl.submit()" novalidate>
        <div ng-class="$ctrl.ctrlCls('event_id')">
            <label class="col-sm-offset-3 col-sm-2 control-label">Event ID:</label>
            <div class="col-sm-3">
                <input type="number" name="event_id" class="form-control" ng-model="$ctrl.data.event_id" required>
            </div>
        </div>
        <div ng-class="$ctrl.ctrlCls('pswd')">
            <label class="col-sm-offset-3 col-sm-2 control-label">Prod Pswd:</label>
            <div class="col-sm-3">
                <input type="password" name="pswd" class="form-control" ng-model="$ctrl.data.pswd" required>
            </div>
        </div>
        <div class="form-group">
        	<div class="col-sm-offset-3 col-sm-5" ng-if="$ctrl.error">
        		<uib-alert type="danger text-center">{{$ctrl.error}}</uib-alert>
        	</div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-3" ng-if="$ctrl.tablesInProgress">
                <ul>
                    <li ng-repeat="table in $ctrl.allTables">
                        <i class="gl-success fa fa-check" aria-hidden="true" ng-if="$ctrl.isReady(table)"></i> {{table}}
                    </li>
                </ul>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-5" ng-if="$ctrl.withErrorTables.length">
                <uib-alert type="danger text-center">Tables that are not copied: {{$ctrl.tablesNotCopied}}</uib-alert>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-5" ng-if="$ctrl.isRunning">
                <uib-progressbar type="success" value="$ctrl.progressValue" max="$ctrl.allTables.length">
                    {{$ctrl.progressValue}} of {{$ctrl.allTables.length}}
                </uib-progressbar>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-5 col-sm-3">
                <button type="submit" class="btn btn-primary pull-right" ng-if="!$ctrl.isRunning">Copy!</button>
            </div>
        </div>
    </form>
</div>

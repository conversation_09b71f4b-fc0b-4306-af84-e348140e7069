angular.module('SportWrench').component('usersList', {
    templateUrl 	: 'public/users/users-list/users-list.html',
    bindings 		: {},
    controller 		: UsersList
});

UsersList.$inject = ['UsersService', 'ngTableParams', 'toastr', 'HOME_PAGE_URL'];

function UsersList(UsersService, ngTableParams, toastr, HOME_PAGE_URL) {
    let self = this;

    this.users          = [];
    this.search         = '';
    this.defaultSort    = false;
    this.filters        = {
        page    : 1,
        limit   : 20
    };

    this.utils = { totalRows: 0 };

    this.dataIsLoading = false;

    let getData = function ($q, params) {
        let _params = {};
        let filter  = params.filter();
        let orderBy = params.orderBy();

        if (filter) {
            _params = _.clone(filter);
        }

        if(orderBy && orderBy.length && !self.defaultSort) {
            _params.order  = orderBy[0].substr(1);
            _params.revert = orderBy[0].charAt(0) === '-';
        }

        return UsersService.getUsersList(_params).then(function(users) {
            self.users              = users;
            self.defaultSort        = false;
            self.utils.totalRows    = users[0] && users[0].total_rows || 0;
            self.dataIsLoading      = false;

            return users;
        })
    };

    this.usersTable = new ngTableParams({
        page        : 1,
        count       : 20,
        sorting     : {},
        filter      : self.filters
    }, {
        total       : 0,
        counts      : [],
        filterDelay : 0,
        getData     : getData
    });

    this.columnClass = function (column) {
        return {
            'text-center sortable'  : true,
            'sort-asc'              : self.usersTable.isSortBy(column, 'asc'),
            'sort-desc'             : self.usersTable.isSortBy(column, 'desc')
        };
    };

    this.sort = function (column) {
        self.filters.page = 1;
        self.usersTable.sorting(column, self.usersTable.isSortBy(column, 'asc') ? 'desc'
            : self.usersTable.isSortBy(column, 'desc') ? self.setSortDefault() : 'asc');
    };

    this.setSortDefault = function () {
        self.defaultSort = true;
        return 'text-center sortable';
    };

    this.filterSearch     = function () {
        if(self.usersTable.settings().$loading) return;

        self.filters.search   = self.search;
        self.filters.page     = 1;
    };

    this.loginAsUser = function (id) {
        let user = self.users[id];

        if(!user.activated) {
            toastr.warning('User is not activated');
            return;
        }

        UsersService.loginAs(user.email, user.users_token).then((user) => {
            UsersService.addUserDataToStorage(user);

            let link = angular.element(`<a href="${HOME_PAGE_URL}" target="_blank"></a>`);
            angular.element(document.body).append(link);
            link[0].click();
            link.remove();
        });
    }
}

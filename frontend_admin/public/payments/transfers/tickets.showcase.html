<table class="table table-condensed tickets-showcase-table">
    <thead>
        <tr>
            <th></th>
            <th>Transactions</th>
            <th ng-repeat="t in data.ticket_types | orderBy:'order'">{{t.label}}</th>
            <th>Amount</th>
            <th>Stripe Fee</th>         
            <th>SW</th>
            <th>Balance</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="title">
                <span uib-tooltip="Web Site before the Event Date Start" class="helper-descr">Online Prior</span>
            </td>
            <td ng-bind="data.prior.tr"></td>
            <td ng-repeat="t in data.prior.types | orderBy:'order'">{{t.total}} <span class="text-grey" title="Not Scanned" uib-tooltip="Not Scanned">({{t.not_scanned}})</td>
            <td ng-bind="data.prior.amount | currency"></td>
            <td ng-bind="data.prior.stripe | currency"></td>
            <td ng-bind="data.prior.sw | currency"></td>
            <td ng-bind="data.prior.balance | currency"></td>
        </tr>
        <tr>
            <td class="title">
                <span uib-tooltip="Web Site during the Event" class="helper-descr">Online During</span>
            </td>
            <td ng-bind="data.during.tr"></td>
            <td ng-repeat="t in data.during.types | orderBy:'order'">{{t.total}} <span class="text-grey" title="Not Scanned" uib-tooltip="Not Scanned">({{t.not_scanned}})</td>
            <td ng-bind="data.during.amount | currency"></td>
            <td ng-bind="data.during.stripe | currency"></td>
            <td ng-bind="data.during.sw | currency"></td>
            <td ng-bind="data.during.balance | currency"></td>
        </tr>
        <tr>
            <td class="title">
                <span uib-tooltip="via TicketGuru" class="helper-descr">Event Credit</span>
            </td>
            <td ng-bind="data.api.tr"></td>
            <td ng-repeat="t in data.api.types | orderBy:'order'" ng-bind="t.total"></td>
            <td ng-bind="data.api.amount | currency"></td>
            <td ng-bind="data.api.stripe | currency"></td>
            <td ng-bind="data.api.sw | currency"></td>
            <td ng-bind="data.api.balance | currency"></td>
        </tr>
        <tr>
            <td class="title green">Credit subtotal</td>
            <td ng-bind="data.subtotals.tr"></td>
            <td 
                class="green" 
                ng-repeat="t in data.subtotals.types | orderBy:'order'"
                >{{t.total}} <span class="helper-descr text-grey" title="Not Scanned" uib-tooltip="Not Scanned">({{t.not_scanned}})
            </td>
            <td class="green" ng-bind="data.subtotals.amount | currency"></td>
            <td  class="green" ng-bind="data.subtotals.stripe | currency"></td>
            <td class="green" ng-bind="data.subtotals.sw | currency"></td>
            <td id="credit-balance">
                (<var class="text-primary">CRB</var>)
                <span class="green" ng-bind="data.subtotals.balance | currency"></span>
            </td>
        </tr>
        <tr>
            <td class="title">Event Cash</td>
            <td ng-bind="data.cash.tr"></td>
            <td ng-repeat="t in data.cash.types | orderBy:'order'" ng-bind="t.total"></td>
            <td id="cash-amount">
                (<var class="text-warning">CN</var>)
                <span ng-bind="data.cash.amount | currency"></span>
            </td>
            <td ng-bind="data.cash.stripe | currency"></td>
            <td id="sw-fee-cash">
                (<var class="text-warning">CF</var>)
                <span ng-bind="data.cash.sw | currency"></span>
            </td>
            <td ng-bind="data.cash.balance | currency"></td>
        </tr>
        <tr class="green">
            <td class="title">Credit + Cash Total</td>
            <td ng-bind="data.totals.tr"></td>
            <td ng-repeat="t in data.totals.types | orderBy:'order'" ng-bind="t.total"></td>
            <td ng-bind="data.totals.amount | currency"></td>
            <td ng-bind="data.totals.stripe | currency"></td>
            <td ng-bind="data.totals.sw | currency"></td>
            <td ng-bind="data.totals.balance | currency"></td>
        </tr>
        <tr>
            <td class="title">Credit Card Net Profit (Amount SW sends to Stripe)</td>
            <td colspan="{{4 + utils.typesQty}}"></td>
            <td>
                (<var 
                    class="text-primary"
                    ng-mouseover="hightlightElem('credit-balance')"
                    ng-mouseleave="fadeElem('credit-balance')">CRB</var>) 
                <span> - </span>
                (<var class="text-warning"
                    ng-mouseover="hightlightElem('sw-fee-cash')"
                    ng-mouseleave="fadeElem('sw-fee-cash')"
                    >CF</var>)
                <span> = </span>
                (<var class="text-danger">N</var>) <b class="green" ng-bind="data.net | currency"></b> 
            </td>        
        </tr>
        <tr class="bg-warning">
            <td class="title">Event Total Net Profit (includes on-site cash)</td>
            <td colspan="{{utils.typesQty + 4}}"></td>
            <td colspan="2">
                (<var 
                    class="text-danger">N</var>)  
                <span> + </span>
                (<var 
                    class="text-warning"
                    ng-mouseover="hightlightElem('cash-amount')"
                    ng-mouseleave="fadeElem('cash-amount')">CN</var>)  
                <span> = </span>
                <span ng-bind="data.event_total_net_profit | currency"></span>
            </td>
        </tr>
    </tbody>
</table>

angular.module('SportWrench').directive('managedAccountModal', function (StripeTransfersService) {
	return {
		restrict: 'E',
		scope: {
			accountId: '@'
		},
		templateUrl: 'public/payments/transfers/account-modal.html',
		link: function (scope) {
			var mode = scope.accountId?'edit':'create';

			scope.utils = {
				header: mode
			}

			scope.data = {
				account: {}
			}

			scope.uploadDocs = function () {
				var file = _.first(angular.element('[name="docs-scan"]')[0].files)
				StripeTransfersService.uploadDocs(scope.accountId, file)
				.success(function () {

				})
				.error(function () {
					
				})
			}

			scope.remove = function () {
				StripeTransfersService.removeAccount(scope.accountId)
				.success(function () {

				})
				.error(function () {
					
				})
			}

			scope.submit = function () {
				(
					(mode === 'edit')
						?__update()
						:__create()
				).success(function () {

				}).error(function () {

				}).finally(function () {

				})
			}

			function __create () {

			}

			function __update () {

			}
		}
	}
})
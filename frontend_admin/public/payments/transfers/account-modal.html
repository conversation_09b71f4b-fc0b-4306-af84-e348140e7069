<div class="modal-header">
	<h4 class="text-center capitalize">{{utils.header}} Account</h4>
</div>
<div class="modal-body">
	<tabset>
		<tab heading="General">
			<em>Coming soon...</em>
			<!--form class="form-horizontal row-space" name="accountForm">
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Country</label>
					<div class="col-sm-6">
						<select name="country" class="form-control">
							<option value="US" selected>United States</option>
							<option value="CA">Canada</option>
						</select>
					</div>
				</div>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Email</label>
					<div class="col-sm-6">
						<input type="text" name="email" class="form-control">
					</div>
				</div>
				<legend>External Account</legend>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Type</label>
					<div class="col-sm-6">
						<select name="type">
							<option value=""></option>
						</select>
					</div>
				</div>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Account Number</label>
					<div class="col-sm-6">
						<input type="text" name="account_number" class="form-control">
					</div>
				</div>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Account Holder Type</label>
					<div class="col-sm-6">
						<input type="text" name="account_holder_type" class="form-control">
					</div>
				</div>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Account Name</label>
					<div class="col-sm-6">
						<input type="text" name="name" class="form-control">
						<p class="help-block">The name of the person or business that owns the bank account</p>
					</div>
				</div>
				<div class="form-group validation-required">
					<label class="control-label col-sm-4">Routing Number</label>
					<div class="col-sm-6">
						<input type="text" name="routing_number" class="form-control">
					</div>
				</div>
				<legend>Transfer Schedule</legend>
				<div class="form-group">
					<label class="control-label col-sm-4">Interval</label>
					<div class="col-sm-6">
						<select class="form-control" name="">
							<option value="manual" selected>Manual</option>
							<option value="daily">Daily</option>
							<option value="weekly">Weekly</option>
							<option value="monthly">Monthly</option>
						</select>
						<p class="help-block">How frequently funds will be paid out</p>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-sm-4">Delay Days</label>
					<div class="col-sm-6">
						<select 
							class="form-control" 
							name=""
							>
							<option value="minimum" selected>Minimum (based on Country)</option>
							<option value="1">1</option>
							<option value="2">2</option>
							<option value="3">3</option>
							<option value="4">4</option>
							<option value="5">5</option>
							<option value="6">6</option>
							<option value="7">7</option>
						</select>
						<p class="help-block">The number of days charges for the account will be held before being paid out</p>
					</div>
				</div>
				<button class="btn btn-primary" ng-click="submit()">Save</button>
			</form-->
		</tab>
		<tab heading="Scan Uploading">
			<div class="well row-space">
				<form name="scanUpload" class="form-horizontal">
					<div class="form-group">
						<label class="control-label col-sm-4">Choose a Scan</label>
						<div class="col-sm-6">
							<input 
			                    type="file"
			                    name="docs-scan"
			                    class="form-control"
			                    required
		                    >
						</div>
					</div>
					<button class="col-sm-offset-4 btn btn-primary" ng-click="uploadDocs()">Upload</button>
				</form>
			</div>
		</tab>
	</tabset>
</div>
<div class="modal-footer">
	<button class="btn btn-default pull-right" ng-click="$parent.$close()">Close</button>
</div>
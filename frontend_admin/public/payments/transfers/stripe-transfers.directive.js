angular.module('SportWrench').directive('stripeTransfers', function (StripeTransfersService, $uibModal) {
	return {
		restrict: 'E',
		scope: {},
		templateUrl: 'public/payments/transfers/stripe-transfers.html',
		link: function (scope) {
			scope.utils = {
				selectedAccount 	: {},
				accountsList 		: [],
				eventsList 			: [],
				eventsLoading 		: false,
				accountsLoading 	: false,
				statisticsLoading 	: false,
				transferInProgress 	: false,
				error 				: '',
				transferType		: 'stripe',
				swAcc 				: {}
			};
			scope.transfer = {};

			__loadManaged();

			scope.migrate = function () {
				StripeTransfersService.recipientsMigrate()
				.success(function () {
					__loadManaged();
				})
			}

			scope.createTranser = function () {
				if(scope.transfersForm.$invalid) return;
				scope.utils.transferInProgress = true;
				scope.utils.error = '';
				if(scope.transfer.date_sent) {
					scope.transfer.date_sent = __stringifyDate(scope.transfer.date_sent);
				}
				(
					(scope.utils.transferType === 'stripe') 
						?StripeTransfersService.createTransfer(scope.transfer)
						:StripeTransfersService.saveCheckTransfer(scope.transfer)
				).then(function () {
					scope.transfer.amount = null;
					scope.transfer.description = '';
					scope.transfer.check_num = '';
				}, function (resp) {
					scope.utils.error = resp && resp.data && (resp.data.validation || resp.data.error)
				}).then(function () {
					scope.utils.transferInProgress = false;
					scope.swAccBalance();
					scope.loadEventStatistics();
				})
			}

			scope.accountLabel = function (acc) {
                var accName = (acc.display_name || acc.business_name || acc.person_name || acc.id);

                if (acc.email) {
                    return (accName + ' (' + acc.email + ')');
                } else {
                    return accName;
                }
			}

			scope.setAccount = function () {
				if(scope.utils.selectedAccount) {
					scope.transfer.account_id = scope.utils.selectedAccount.id;
					__loadAccountEvents(scope.transfer.account_id);
				} else {
					scope.transfer.account_id = null;
					scope.utils.eventsList = [];
				}
			}

			scope.showAccountInfo = function () {
				return scope.transfer.account_id
			};

			scope.loadEventStatistics = function () {
				if(!scope.transfer.event_id) return;
				scope.utils.statisticsLoading = true;
				return StripeTransfersService.eventStatistics(scope.transfer.event_id)
				.success(function (data) {
					scope.utils.eventStatistics = data;
					return data;
				})
				.finally(function () {
					scope.utils.statisticsLoading = false;
				});
			}

			scope.openAccountModal = function (id) {
				$uibModal.open({
					template: '<managed-account-modal account-id="' + (id || '') + '"></managed-account-modal>'
				}).result.then(function () {

				})
			}

			scope.swAccBalance = function () {
				scope.utils.balanceInfoLoading = true;
				StripeTransfersService.swAccBalance()
				.success(function (data) {
					scope.utils.swAcc = data.balance;
				}).finally(function () {
					scope.utils.balanceInfoLoading = false;
				})
			}

			scope.onTransferTypeChange = function () {
				if(scope.utils.transferType === 'check') {
					scope.transfer.date_sent = new Date();
				} else {
					scope.transfer.date_sent = null;
				}
			}

			scope.swAccBalance();

			function __loadAccountEvents (accountId) {
				scope.utils.eventsLoading = true;
				StripeTransfersService.eventsList(accountId)
				.success(function (data) {
					scope.utils.eventsList = data.events
				}).finally(function () {
					scope.utils.eventsLoading = false
				})
			}

			function __loadManaged () {
				scope.utils.accountsLoading = true;
				scope.utils.selectedAccount = null;
				scope.transfer.account_id = null;
				StripeTransfersService.getManagedAccounts()
				.success(function (data) {
					scope.utils.accountsList = data.accounts;
				}).finally(function () {
					scope.utils.accountsLoading = false;
				})
			}

			function __stringifyDate (d) {
				return (
					d.getFullYear() + '/' + 
					(d.getMonth() + 1) + '/' + 
					d.getDate()
				)
			}
		}
	}
})

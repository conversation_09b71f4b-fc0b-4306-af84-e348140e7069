<form class="form-horizontal" name="transfersForm">
	<legend>
		<div class="col-sm-offset-3">Recipient Details</div>
	</legend>
	<div class="form-group validation-required">
		<label class="col-sm-offset-1 col-sm-2 control-label">Account:</label>
		<div class="col-sm-5">
			<select 
				name="account" 
				class="form-control"
				ng-model="utils.selectedAccount"
				ng-change="setAccount()"
				ng-options="acc as accountLabel(acc) for acc in utils.accountsList"
				ng-show="!utils.accountsLoading"
				required>
				<option value="">Choose an Account ...</option>
			</select>
			<spinner active="utils.accountsLoading"></spinner>
		</div>
	</div>
	<div class="row" ng-if="showAccountInfo()">
		<div class="col-sm-offset-3 col-sm-7">
			<h4>Account Info: <i class="fa fa-pencil-square-o pointer" ng-if="utils.selectedAccount.managed " ng-click="openAccountModal(utils.selectedAccount.id)"></i></h4>
			<managed-account-info
				account="utils.selectedAccount"
			></managed-account-info>
		</div>
	</div>
	<div class="form-group validation-required">
		<label class="col-sm-offset-1 col-sm-2 control-label">Event:</label>
		<div class="col-sm-5">
			<select 
				name="event" 
				class="form-control"
				ng-model="transfer.event_id"
				ng-options="e.id as e.name for e in utils.eventsList"
				ng-change="loadEventStatistics()"
				ng-show="!utils.eventsLoading"
				required>
				<option value="">Choose an Event ...</option>
			</select>
			<spinner active="utils.eventsLoading"></spinner>
		</div>
	</div>
	<div class="row row-space" ng-if="transfer.event_id">
		<div class="col-sm-12">
			<h4 class="col-sm-offset-3">Tickets Statistics</h4>
			<tickets-showcase
			    stat-data="utils.eventStatistics"
				ng-if="!utils.statisticsLoading"
			></tickets-showcase>
			<div class="col-sm-offset-3" ng-if="utils.statisticsLoading">
				<spinner active="true"></spinner>
			</div>
		</div>
	</div>
	<legend>
		<div class="col-sm-offset-3">Make Payout</div>
	</legend>
	<div class="form-group">
		<label class="col-sm-offset-1 col-sm-2 control-label">Transfer Type:</label>
		<div class="col-sm-5">
			<label class="radio-inline">
				<input type="radio" name="transfer_type" ng-model="utils.transferType" value="stripe" ng-change="onTransferTypeChange()"> Stripe
			</label>
			<label class="radio-inline">
				<input type="radio" name="transfer_type" ng-model="utils.transferType" value="check" ng-change="onTransferTypeChange()"> Check
			</label>
		</div>
	</div>
	<div class="form-group validation-required" ng-if="utils.transferType === 'check'">
		<label class="col-sm-offset-1 col-sm-2 control-label">Check Number:</label>
		<div class="col-sm-5">
			<input 
				type="text" 
				class="form-control"
				ng-model="transfer.check_num"
				name="check_num"
				required>
		</div>
	</div>
	<div class="form-group validation-required" ng-if="utils.transferType === 'check'">
		<label class="col-sm-offset-1 col-sm-2 control-label">Date Sent:</label>
		<div class="col-sm-5 pointer" ng-click="utils.datePickerOpened = !utils.datePickerOpened">
			<input 
				type="text" 
				class="form-control white-ro"
				ng-model="transfer.date_sent"
				datepicker-popup="MM/dd/yyyy"
				close-on-date-selection="true"
				is-open="utils.datePickerOpened"
				placeholder="MM/DD/YYYY"
				required
				readonly>
		</div>
	</div>
	<div class="form-group validation-required">
		<label class="col-sm-offset-1 col-sm-2 control-label">{{(utils.transferType === 'check')?'Check Amount':'Amount to transfer'}}</label>
		<div class="col-sm-2">
			<div class="input-group">
      			<div class="input-group-addon">$</div>
				<input 
					name="amount" 
					type="number" 
					step="0.01"
					min="1"
					class="form-control"
					ng-model="transfer.amount"
					required>
			</div>
		</div>
		<div class="col-sm-6">
			<div ng-if="!utils.balanceInfoLoading">
				<label class="control-label">SW Acc Balance:</label> {{utils.swAcc.available | currency}} <span class="text-grey">(pending {{utils.swAcc.pending | currency}})</span>
			</div>
			<spinner active="utils.balanceInfoLoading"></spinner>
		</div>
	</div>
	<div class="form-group validation-required">
		<label class="col-sm-offset-1 col-sm-2 control-label">Description:</label>
		<div class="col-sm-5">
			<input 
				name="description" 
				type="text" 
				class="form-control"
				ng-model="transfer.description"
				required>
			<p class="help-block">EO will see it in Statistics and Stripe Transfer Info</p>
		</div>
	</div>
	<uib-alert type="danger text-center" ng-if="utils.error">{{utils.error}}</uib-alert>
	<button 
		class="col-sm-offset-3 btn btn-primary" 
		ng-click="createTranser()" 
		ng-if="!utils.transferInProgress">{{(utils.transferType === 'check')?'Add Check Info':'Send Money'}}</button>
	<spinner active="utils.transferInProgress"></spinner>
</form>

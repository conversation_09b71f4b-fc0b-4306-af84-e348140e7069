angular.module('SportWrench').component('officialBarcodeScanHistory', {
    templateUrl: 'components/member-info/info-blocks/official-scan-history/official-scan-history.html',
    bindings: {
        scanHistory: '<'
    },
    controller: Component,
});

Component.$inject = ['OFFICIAL_SCAN_HISTORY_ACTION_TYPE'];

function Component(OFFICIAL_SCAN_HISTORY_ACTION_TYPE) {
    this.scanTextFormat = {
        [OFFICIAL_SCAN_HISTORY_ACTION_TYPE.SCAN]: 'Scanned on <%= date_scanned %>',
        [OFFICIAL_SCAN_HISTORY_ACTION_TYPE.REENTRY]: 'Re-entry for #<%= barcode %> on <%= date_scanned %>',
    }

    this.generateScanText = function (scan) {
        const html = this.scanTextFormat[scan.action_type];
        const compiled = _.template(html);

        return compiled(scan);
    }
}

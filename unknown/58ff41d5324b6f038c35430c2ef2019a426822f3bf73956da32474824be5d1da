angular.module('SportWrench')
    .controller('sales.ExhibitorsTicketsController', ExhibitorsTicketsController );

function ExhibitorsTicketsController (
    $scope, exhibitorsTickets, ticketsService, $stateParams, $timeout, toastr, CAMPS_TICKETS_FILTER_NAME, PAYMENT_TYPE,
    $uibModal, INTERNAL_ERROR_MSG, TICKETS_FILTER_NAME, PAYMENT_STATUS, ExhibitorTicketService
) {
    const ctrl= this;

    this.payments               = exhibitorsTickets.payments;
    this.ticket_types           = exhibitorsTickets.ticket_types;
    this.column_show            = exhibitorsTickets.column_show;

    this.filters    = {
        sort        : 'purchased',
        limit       : 100,
        page        : 1,
        revert      : true
    };

    this.utils          = {
        total_rows          : exhibitorsTickets.total_rows,
        eventId             : $stateParams.event,
    };

    this.check = {
        all     : false,
        items   : [],
        count   : 0
    };

    this.isWaitlistPayment = function (payment) {
        return payment.type === PAYMENT_TYPE.WAITLIST;
    };

    this.isPendingPayment = function (payment) {
        return payment.status === PAYMENT_STATUS.PENDING;
    };

    this.rowClass = function (row) {
        return {
            'bg-warning'    : row.type === 'waitlist',
            'text-grey'     : row.status === 'canceled',
        };
    };

    this.sort = function (column) {
        this.filters.sort = column;
        this.filters.revert = !this.filters.revert;
        reloadData();
    };

    this.changePage = function (page) {
        this.filters.page = page;
        reloadData();
    }

    this.ticketModal = function (p) {
        $uibModal.open({
            template: '<exhibitors-tickets-modal barcode="' + p.unform_barcode + '"></exhibitors-tickets-modal>',
            size: 'lg'
        });
    }

    this.paginationText = function () {
        const from = (this.filters.page - 1) * this.filters.limit;

        return ( from + 1 ) + ' - ' + (from + this.payments.length) +
                    ' of ' + this.utils.total_rows
    }
    this.utils.paginationText = this.paginationText();

    function getAllFilters () {
        return Object.assign({}, ctrl.filters, ctrl.utils.filters);
    }

    this.showColumn = function(columnName) {
        // if column not in data.column_show - show column
        if(this.column_show) {
            if ( !this.column_show.hasOwnProperty(columnName)) return true;

            return this.column_show[columnName];
        } else {
            return false;
        }
    }

    this.agesHTML = function (age) {
        return age.age_for.reduce((acc, curr) =>  acc += '<p>' + curr + '</p>', '');
    };

    this.filtersChanged = function (filters, search, updateWithoutReload = false) {
        this.utils.filters = Object.assign({}, filters, { search });

        this.filters.page = 1;

        if(!updateWithoutReload) {
            reloadData();
        }
    };

    function reloadData() {
        if($scope.data.utils.filters.search) {
            $scope.data.filters.page = 1;
        }

        let filters = getAllFilters();

        ticketsService.exhibitorsTickets($stateParams.event, filters).then(function (resp) {
            $scope.data.payments = resp.data.payments;
            $scope.data.ticket_types = resp.data.ticket_types;
            $scope.data.utils.total_rows = resp.data.total_rows;
            $scope.data.utils.paginationText = $scope.data.paginationText();
            $scope.data.column_show = resp.data.column_show;
        });
    }

    $scope.openGenerateFreeTicketModal = function() {
        let isWeekendExist = false;

        $scope.data.ticket_types.forEach(ticket => {
            if (ticket.ticket_type === 'weekend') {
                isWeekendExist = true;
            }
        })

        const ticketType = isWeekendExist ? 'weekend': 'daily';
        ExhibitorTicketService.openGenerateExhibitorTicketModal(ticketType, reloadData);
    }

    $scope.showFreeTicketButton = function() {
        if(_.isEmpty($scope.event)) {
            return false;
        }

        const rules = [
            $scope.event.is_tickets_purchase_open,
            $scope.event.assigned_tickets_mode === true,
            $scope.event.enable_exhibitor_tickets,
        ];

        return rules.every(rule => rule);
    }
}

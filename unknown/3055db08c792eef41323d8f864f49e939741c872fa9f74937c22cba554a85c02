'use strict';

const swUtils = require('../../lib/swUtils');
const SportEngineUtilsService = require('../../lib/SEUtilsService');

const moment= require('moment');
const updateAthleteSchema= require('../../validation-schemas/club-schemas').master_athlete;


var FIND_ATHLETE_SQL =
`SELECT
    ( 
        SELECT ROW_TO_JSON(current_athlete)
        FROM ( 
            SELECT 
                ma.first, ma.last, ma.email, ma.address, ma.age, INITCAP(ma.ethnicity::TEXT) AS ethnicity,
                ma.city, ma.gender,  to_char(ma.birthdate, 'MM/DD/YYYY HH24:MI:SS') birthdate, ma.role,
                ma.gradyear, ma.master_athlete_id, ma.sport_position_id, 
                ma.organization_code, ma.phoneh, ma.phonem, ma.state, ma.zip, ma.jersey, ma.aau_jersey, 
                ma.country, ma.phonep, ma.height, mt.master_team_id, 
                ma.nick, ma.address2, ma.other_phone, ma.parent1_first, ma.parent1_last, 
                ma.parent1_email, ma.parent2_first, ma.parent2_last, ma.parent2_email, 
                ma.membership_status, mt.team_name, INITCAP(ma.seasonality) "seasonality",
                ma.usav_number, ma.aau_membership_id
            FROM master_athlete ma 
            LEFT JOIN master_team mt ON ma.master_team_id = mt.master_team_id AND mt.season = $3
            WHERE ma.master_athlete_id = $1
        ) current_athlete 
    ) AS athlete, 
    ( 
        SELECT coalesce(array_to_json(array_agg(row_to_json(current_states))), '[]'::json) 
        FROM ( 
            SELECT s.state, s.name 
            FROM state s 
            WHERE s.country = ma.country 
        ) current_states 
    ) AS states, 
    ( 
        SELECT coalesce(array_to_json(array_agg(row_to_json(current_roles))), '[]'::json) 
        FROM ( 
            SELECT r.athlete_role_id AS id, INITCAP(r.name) AS name
            FROM athlete_role r
        ) current_roles
    ) AS roles, 
    ( 
        SELECT coalesce(array_to_json(array_agg(row_to_json(current_positions))), '[]'::json) 
        FROM ( 
            SELECT sp.short_name AS NAME, sp.sport_position_id AS id 
            FROM sport_position sp 
            WHERE sp.sport_id = 2 
        ) current_positions 
    ) AS positions, 
    (  
        SELECT coalesce(array_to_json(array_agg(row_to_json(available_teams))), '[]'::json)  
        FROM (  
            SELECT mt.master_team_id "id", mt.team_name AS NAME, mt.organization_code "code", mt.gender 
            FROM master_team mt 
            WHERE mt.master_club_id = ma.master_club_id 
            AND (mt.gender::text = ma.gender::text OR mt.gender = 'coed' or ma.gender = 'non-binary') 
            AND mt.deleted IS NULL  
            AND mt.season = $3 
            AND ( (ma.age <= mt.age OR mt.age = 0)  OR ( 
                    (ma.age = 19 OR ma.age = 20) 
                    AND ma.gradyear::TEXT = ($3)::TEXT
                    AND mt.age = 18 
                ) 
            ) 
            ORDER BY mt.organization_code 
        ) available_teams  
    ) AS teams
 FROM master_athlete ma 
 WHERE ma.master_athlete_id = $1 and ma.master_club_id = $2`;

// V2 
module.exports = {
    // get /api/v2/club/master_athlete
    all: async (req, res)=> {
        let limit = parseInt(req.query.limit, 10);
        let offset = parseInt(req.query.offset, 10);
        
        if(!limit || swUtils.isNumberLessThanZero(limit)) {
            limit = 25;
        }
        if(!offset || swUtils.isNumberLessThanZero(offset)) {
            offset = 0;
        }
        
        const masterClubId = Number(req.session.passport.user.master_club_id);

        if(!masterClubId) {
            return res.forbidden('Not a club owner');
        }

        const params = {
            masterClubId,
            season: Number(req.query.season),
            gender: req.query.gender,
            search: swUtils.escapeStr(req.query.search || ''),
            sanctionedBody: (req.query.sanctionedBody || []).map(Number),
            seasonality: req.query.seasonality,
            order: req.query.order,
            revert: req.query.revert,
            age: req.query.age,
            assign: req.query.assign,
            teamIds: req.query.team,
            limit,
            offset,
        }

        try {
            const athletes = await TeamMembersService.getMasterAthletes(params);

            return res.status(200).json(athletes);
        } catch (e) {
            res.customRespError(e)
        }
    },
    // /api/v2/club/master_athlete/move_to_team
    moveToTeam: function (req, res) {
        let $master_club_id         = parseInt(req.session.passport.user.master_club_id, 10),
            $athletes               = req.body.athletes,
            $master_team_id         = parseInt(req.body.master_team_id, 10) || null,
            $currentSeason          = sails.config.sw_season.current;

        if(!$master_club_id)        return res.forbidden('Not a club owner');  
        if(!_.isArray($athletes))   return res.validation('Expecting athletes to be an integers array');

        Promise.resolve().then(() => {
            let athletesListStr = swUtils.numArrayToString($athletes);

            if (!athletesListStr) {
                throw { validation: 'Invalid Identifiers passed!' };
            }

            let query;

            if($master_team_id) {
            query =                        
                `WITH oldVal AS (
                    SELECT ma.master_athlete_id, ma.master_team_id 
                    FROM "master_athlete" ma 
                    WHERE ma.master_athlete_id IN(${athletesListStr})
                )
                UPDATE master_athlete ma 
                 SET master_team_id = ( 
                        COALESCE((  
                          SELECT mt.master_team_id  
                          FROM master_team mt  
                          WHERE mt.deleted IS NULL  
                            AND mt.season = $2::INTEGER 
                            AND mt.master_team_id = $1::INTEGER 
                            AND mt.master_club_id = ma.master_club_id  
                            AND (
                                mt.gender::TEXT = ma.gender::TEXT OR mt.gender = 'coed' OR ma.gender = 'non-binary'
                            )
                            AND (
                                mt.seasonality = ma.seasonality
                                OR ma.seasonality IS NULL
                                OR (
                                    mt.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.FULL}'
                                        AND ma.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.REGIONAL}'
                                )
                            ) 
                        ), ma.master_team_id) 
                    )  
                 WHERE master_athlete_id IN (  
                    SELECT ma.master_athlete_id  
                    FROM master_athlete ma  
                    INNER JOIN master_team mt  
                        ON mt.master_team_id = $1::INTEGER  
                    WHERE ma.master_athlete_id IN (${athletesListStr}) 
                        AND ( 
                            (ma.age <= mt.age OR mt.age = 0) 
                            OR ( 
                                (ma.age = 19 OR ma.age = 20) 
                                AND ma.gradyear::TEXT = $2::TEXT 
                                AND mt.age = 18
                            ) 
                            OR (ma.age = 18 AND ma.gradyear::TEXT = ($2::INT + 1)::TEXT AND mt.age = 17)    
                        ) 
                        AND ma.deleted IS NULL
                 )                                
                 RETURNING master_athlete_id "id", NULLIF(
                    ma.master_team_id, (
                        SELECT master_team_id "oldId" FROM oldVal 
                        WHERE oldVal.master_athlete_id = ma.master_athlete_id
                    )
                 ) "updatedTo", FORMAT('%s %s', ma.first, ma.last) "name"`;
            } else  {
                query = 
                squel.update().table('master_athlete', 'ma')
                    .where(`master_athlete_id IN (${athletesListStr})`)
                    .set('master_team_id', null)
                    .returning(`master_athlete_id "id", FORMAT('%s %s', ma.first, ma.last) "name"`);
            }

            return query;
        })
        .then(sql => Db.query(sql, [$master_team_id, $currentSeason]).then(result => result.rows))
        .then(updatedAthletes => {
            if ($master_team_id) {
                updatedAthletes = updatedAthletes.filter(a => a.updatedTo);
            }

            if (updatedAthletes.length === 0) {
                loggers.debug_log.verbose('No athletes updated');
                return updatedAthletes;
            }

            let upsertFunc  = RosterSnapshotService.upsertRosterAthletes.bind(RosterSnapshotService, $master_club_id);

            // NOTE: don't use splitArray to array of athletes when UPDATE/INSERT/DELETE on roster_athlete table.
            // It will create parallel queries and wristbands count trigger will not see all
            // changes in roster_athlete table.
            return updatedAthletes.reduce((prev, athlete) =>
                prev.then(() => upsertFunc(athlete.id, $master_team_id)),
                Promise.resolve()
            ).then(() => updatedAthletes);
        }).then(function (updatedAthletes) {
            let names   = updatedAthletes.map(a => a.name).join(', '),
                message;

            switch(updatedAthletes.length) {
                case 0:
                    message = 'No Athletes were ';
                    break;
                case 1:
                    message = `The Athlete ${names} was successfully `;
                    break;
                default:
                    message = `The Athletes ${names} were successfully `;
            }

            if($master_team_id !== null) {
                message += 'moved to the Team.'
            } else {
                message += 'removed from the Team.'
            }

            return message;
        }).then(msg => {
            res.status(200).json({ msg });
        }).catch(res.customRespError.bind(res))
    },    
    // put /api/v2/club/master_athlete/:id/update
    updateAthlete: function (req, res) {
        var master_athlete_id   = parseInt(req.params.id, 10),  
            master_club_id      = parseInt(req.session.passport.user.master_club_id, 10),
            club_owner_id       = parseInt(req.session.passport.user.club_owner_id, 10),
            masterAthlete                = req.body.athlete,
            $currentSeason      = sails.config.sw_season.current;

        if(!club_owner_id)
            return res.forbidden('Not a club owner');
        if(!master_athlete_id) 
            return res.validation('No Athlete Identifier provided');
        if(_.isEmpty(masterAthlete))
            return res.validation('Empty athlete object passed. Nothing to update');

        new Promise(function validate (resolve, reject) {
            if(masterAthlete.jersey) masterAthlete.jersey = parseInt(masterAthlete.jersey, 10);
            if(masterAthlete.aau_jersey) masterAthlete.aau_jersey = parseInt(masterAthlete.aau_jersey, 10);
            let validation = updateAthleteSchema.validate(masterAthlete);
            if(validation.error) {
                reject({ validationErrors: validation.error.details });
            } else {
                resolve(validation.value);
            }

        }).then(function genSQL () {
            
            var bdate, query;
            if(masterAthlete.birthdate) {
                
                bdate = moment.utc(masterAthlete.birthdate, 'MM/DD/YYYY');
                masterAthlete.age = WebpointUtility.getMinAge(bdate.toDate());
                masterAthlete.birthdate = bdate.format('YYYY-MM-DD HH:mm:ss');

                if(!masterAthlete.age) {
                    throw {
                        validation: 'Invalid Birthdate'
                    }
                }
                
            }
            query = squel.update({ 
                autoQuoteTableNames: false, 
                replaceSingleQuotes: true, 
                singleQuoteReplacement: "''" 
            })
                .table('master_athlete', 'ma')
                .setFields(_.omit(masterAthlete, 'master_team_id', 'seasonality'))
                
                .where('master_athlete_id = ?', master_athlete_id)
                .where('master_club_id = ?', master_club_id)
                .returning(
                    `master_team_id::INT, gender, gradyear, 
                     master_athlete_id, first, last, jersey, aau_jersey, sport_position_id, 
                     (SELECT INITCAP(ar.name) FROM athlete_role ar WHERE ar.athlete_role_id = role) AS role,
                     email, address, to_char(birthdate, 'MM/DD/YYYY HH24:MI:SS') birthdate, 
                     organization_code, usav_number, aau_membership_id, phoneh, phonem, age, zip, state, phonep, city, (
                         SELECT mt.team_name FROM "master_team" mt 
                         WHERE mt.master_team_id = ma.master_team_id AND mt.deleted IS NULL
                     ) "team_name", (
                        SELECT it."master_team_id" = ma."master_team_id" FROM "initial_team" it
                     ) "same_team", INITCAP(ma.seasonality) AS seasonality`
                );

            if(masterAthlete.master_team_id !== undefined) {
                if(masterAthlete.master_team_id === -1) {
                    query.set('master_team_id', null);
                } else {
                    query.set(
                        `master_team_id = COALESCE((
                            SELECT mt.master_team_id
                            FROM master_team mt
                            WHERE mt.deleted IS NULL
                                AND mt.season = ${$currentSeason}
                                AND mt.master_team_id = ${masterAthlete.master_team_id}
                                AND mt.master_club_id = ma.master_club_id 
                                AND (mt.gender::text = ma.gender::TEXT OR mt.gender = 'coed' or ma.gender = 'non-binary')
                                AND (mt.seasonality = ma.seasonality
                                    OR ma.seasonality IS NULL
                                    OR (mt.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.FULL}'
                                        AND ma.seasonality = '${SportEngineUtilsService.SEASONALITY_TAG.POSSIBLE_VALUES.REGIONAL}'
                                    )
                                )
                                AND ( 
                                     (ma.age <= mt.age OR mt.age = 0) OR 
                                     ( 
                                        (ma.age = 19 OR ma.age = 20) 
                                        AND ma.gradyear::text = ${$currentSeason}::text 
                                        AND mt.age = 18
                                    ) 
                                ) 
                        ), ma.master_team_id)`
                    )
                }
            }
            return `WITH "initial_team" AS (
                        SELECT ma.master_team_id, mt.team_name
                        FROM "master_athlete" ma 
                        LEFT JOIN "master_team" mt 
                           ON mt.master_team_id = ma.master_team_id
                        WHERE "ma"."master_athlete_id" = $1
                            AND "ma"."master_club_id" = $2
                    ) ${query.toString()}`
        }).then(function (sql) {
            return Db.query(sql, [master_athlete_id, master_club_id])
            .then(function (result) {
                var updatedAthlete = _.first(result.rows);

                if(_.isEmpty(updatedAthlete)) {
                    throw {
                        validation: 'Athlete not found'
                    }
                } else {
                    return updatedAthlete
                }
            })
        }).then(function (updatedAthlete) {
            return (
                (!updatedAthlete.same_team)
                    ?RosterSnapshotService.upsertRosterAthletes(
                        master_club_id, master_athlete_id, updatedAthlete.master_team_id
                     )
                    :Promise.resolve()
            ).then(function () {
                return updatedAthlete
            })
        }).then(function (updatedAthlete) {
            updatedAthlete.contacts = TeamMembersService.renderContacts(updatedAthlete);
            return updatedAthlete;
        }).then(function (athlete) {
            return res.status(200).json({ athlete });
        }).catch(function (err) {
            res.customRespError(err)
        })
    },
    // get /api/v2/club/athletes/positions
    sportPositions: function (req, res) {
        var query = 
            'SELECT \
                format( \
                        \'{%s}\',   \
                        string_agg(  \
                                    format( \
                                            \'"%s":%s\',  \
                                            to_json(sp.sport_position_id), to_json(sp.short_name)),  \
                                    \',\')  \
                    )::json AS positions \
            FROM sport_position sp';
        Db.query(query).then(function (result) {
            res.status(200).json(_.first(result.rows) || []);
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/v2/club/master_athlete/:id
    findAthlete: function (req, res) {
        var $master_club_id         = parseInt(req.session.passport.user.master_club_id, 10),
            $master_athlete_id      = parseInt(req.params.id, 10),
            $currentSeason          = sails.config.sw_season.current;

        if(!$master_club_id) {
            return res.validation('No club created');
        }
        if(!$master_athlete_id) {
            return res.validation('Invalid athlete identifier passed');
        }

        Promise.all([
            Db.query(
                FIND_ATHLETE_SQL, 
                [$master_athlete_id, $master_club_id, $currentSeason]
            ),
            RosterSnapshotService.findBlockedEvents($master_club_id, $master_athlete_id)
        ]).then(function (results) {
            let athleteRes      = results[0],
                blockedEvents   = results[1],
                data            = _.first(athleteRes.rows),
                athlete         = data && data.athlete || {};

            if(_.isEmpty(athlete)) {
                res.status(200).json({});
            } else {
                athlete.height = WebpointUtility.getHeightInInches(athlete.height);
                res.status(200).json({
                    athlete         : athlete,
                    states          : data.states,
                    positions       : data.positions,
                    teams           : data.teams,
                    roles           : data.roles,
                    blocked_events  : blockedEvents
                });
            }
        }).catch(function (err) {
            res.customRespError(err);
        })
    },
    // delete /api/v2/club/master_athlete
    remove: async function (req, res) {
        const masterClubId= parseInt(req.session.passport.user.master_club_id, 10);
        const athleteIds = req.body.athletes;

        if (!masterClubId) return res.forbidden('Not a club owner');

        try {
            const message = await TeamMembersService.removeAthletesFromClub(masterClubId, athleteIds);

            return res.status(200).json({ message });
        } catch (err) {
            return res.customRespError(err);
        }
    }
}

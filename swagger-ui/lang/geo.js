'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"ყურადღება: აღარ გამოიყენება",
    "Implementation Notes":"იმპლემენტაციის აღწერა",
    "Response Class":"რესპონს კლასი",
    "Status":"სტატუსი",
    "Parameters":"პარამეტრები",
    "Parameter":"პარამეტრი",
    "Value":"მნიშვნელობა",
    "Description":"აღწერა",
    "Parameter Type":"პარამეტრის ტიპი",
    "Data Type":"მონაცემის ტიპი",
    "Response Messages":"პასუხი",
    "HTTP Status Code":"HTTP სტატუსი",
    "Reason":"მიზეზი",
    "Response Model":"რესპონს მოდელი",
    "Request URL":"მოთხოვნის URL",
    "Response Body":"პასუხის სხეული",
    "Response Code":"პასუხის კოდი",
    "Response Headers":"პასუხის ჰედერები",
    "Hide Response":"დამალე პასუხი",
    "Headers":"ჰედერები",
    "Try it out!":"ცადე !",
    "Show/Hide":"გამოჩენა/დამალვა",
    "List Operations":"ოპერაციების სია",
    "Expand Operations":"ოპერაციები ვრცლად",
    "Raw":"ნედლი",
    "can't parse JSON.  Raw result":"JSON-ის დამუშავება ვერ მოხერხდა.  ნედლი პასუხი",
    "Example Value":"მაგალითი",
    "Model Schema":"მოდელის სტრუქტურა",
    "Model":"მოდელი",
    "Click to set as parameter value":"პარამეტრისთვის მნიშვნელობის მისანიჭებლად, დააკლიკე",
    "apply":"გამოყენება",
    "Username":"მოხმარებელი",
    "Password":"პაროლი",
    "Terms of service":"მომსახურების პირობები",
    "Created by":"შექმნა",
    "See more at":"ნახე ვრცლად",
    "Contact the developer":"დაუკავშირდი დეველოპერს",
    "api version":"api ვერსია",
    "Response Content Type":"პასუხის კონტენტის ტიპი",
    "Parameter content type:":"პარამეტრის კონტენტის ტიპი:",
    "fetching resource":"რესურსების მიღება",
    "fetching resource list":"რესურსების სიის მიღება",
    "Explore":"ნახვა",
    "Show Swagger Petstore Example Apis":"ნახე Swagger Petstore სამაგალითო Api",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"სერვერთან დაკავშირება ვერ ხერხდება.  შეამოწმეთ access-control-origin.",
    "Please specify the protocol for":"მიუთითეთ პროტოკოლი",
    "Can't read swagger JSON from":"swagger JSON წაკითხვა ვერ მოხერხდა",
    "Finished Loading Resource Information. Rendering Swagger UI":"რესურსების ჩატვირთვა სრულდება. Swagger UI რენდერდება",
    "Unable to read api":"api წაკითხვა ვერ მოხერხდა",
    "from path":"მისამართიდან",
    "server returned":"სერვერმა დააბრუნა"
});


exports.up = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."stripe_payment_method" ADD COLUMN IF NOT EXISTS "offline_failed_attempts_count" INT NOT NULL DEFAULT 0;
        COMMENT ON COLUMN "public"."stripe_payment_method"."offline_failed_attempts_count"
            IS 'Count of failed offline payment attempts';
        ALTER TABLE "public"."stripe_payment_method" ADD COLUMN IF NOT EXISTS "offline_is_active" BOOLEAN NOT NULL DEFAULT TRUE;
        COMMENT ON COLUMN "public"."stripe_payment_method"."offline_is_active"
            IS 'Payment method status';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."stripe_payment_method" DROP COLUMN "offline_failed_attempts_count";
        ALTER TABLE "public"."stripe_payment_method" DROP COLUMN "offline_is_active";
    `);
};

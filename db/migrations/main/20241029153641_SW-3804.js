
exports.up = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."official"
            ADD COLUMN IF NOT EXISTS "aau_modified" TIMESTAMP DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_data" JSONB DEFAULT NULL,
            ADD COLUMN IF NOT EXISTS "aau_bg_screening" CHARACTER VARYING(20),
            ADD COLUMN IF NOT EXISTS "aau_bg_expire_date" TIMESTAMP WITHOUT TIME ZONE,
            ADD COLUMN IF NOT EXISTS "aau_expire_date" TIMESTAMP WITHOUT TIME ZONE,
            ADD COLUMN IF NOT EXISTS "aau_profile_completed_at" TIMESTAMP WITHOUT TIME ZONE,
            ADD COLUMN IF NOT EXISTS "aau_safesport_end_date" TIMESTAMP WITHOUT TIME ZONE,
            ADD COLUMN IF NOT EXISTS "aau_safesport_statusid" CHARACTER VARYING(20);
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."official"
            DROP COLUMN IF EXISTS "aau_modified",
            DROP COLUMN IF EXISTS "aau_data",
            DROP COLUMN IF EXISTS "aau_bg_screening",
            DROP COLUMN IF EXISTS "aau_bg_expire_date",
            DROP COLUMN IF EXISTS "aau_expire_date",
            DROP COLUMN IF EXISTS "aau_profile_completed_at",
            DROP COLUMN IF EXISTS "aau_safesport_end_date",
            DROP COLUMN IF EXISTS "aau_safesport_statusid";
    `)
};


exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO public.member_type_rules 
            (field_name, member_type, rule_data, is_active) 
        VALUES 
            ('membership_definition_id', 'athlete', '11ee20f2-860a-1b2e-ac01-8e22f6e13dea', true),
            ('membership_definition_id', 'staff', '11ee20f1-066f-3864-b37e-5684723906a8', true);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."member_type_rules" WHERE rule_data = '11ee20f2-860a-1b2e-ac01-8e22f6e13dea';
        DELETE FROM "public"."member_type_rules" WHERE rule_data = '11ee20f1-066f-3864-b37e-5684723906a8';
    `)
};

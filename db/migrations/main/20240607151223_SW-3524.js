exports.up = function(knex) {
    return knex.raw(`
        ALTER TYPE public.custom_payment_for ADD VALUE 'lost_dispute_fee_failed_ach_fee';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        --- ALTER TABLE COLUMN TO TEXT ---
        ALTER TABLE public."custom_payment" ALTER "payment_for" TYPE TEXT;
        
        --- RECREATE ENUM TYPE ---
        DROP TYPE IF EXISTS public.custom_payment_for;
        CREATE TYPE public.custom_payment_for AS ENUM('uncollected_fee', 'other');

        --- ALTER TABLE COLUMN TO enum ---
        ALTER TABLE public."custom_payment" ALTER payment_for TYPE custom_payment_for USING "payment_for"::custom_payment_for;
    `);
};

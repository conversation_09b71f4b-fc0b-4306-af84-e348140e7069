exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO "public"."email_template_group" ("group", title, description, variables, usage_restrictions, position)
        VALUES (
            'tickets_uncollected_fee_payments',
            'Tickets Uncollected Fee Payments',
            'Templates for Uncollected Fee Payments for Tickets',
            '[
                {
                    "field": "event_name",
                    "description": "Event Long Name",
                    "title": "Event Name",
                    "pattern": "{event_name}",
                    "is_available_for_subject": true,
                    "custom_action": false
                },
                {
                    "field": "stripe_error_message",
                    "description": "Last Payment Error",
                    "title": "Payment Error Message",
                    "pattern": "{stripe_error_message}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "payment_net_amount",
                    "description": "Paid Uncollected fee amount",
                    "title": "Payment Net Amount",
                    "pattern": "{payment_net_amount}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "payment_merchant_fee",
                    "description": "Stripe Fee amount paid",
                    "title": "Stripe Fee paid for Payment",
                    "pattern": "{payment_merchant_fee}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "payment_method",
                    "description": "Payment Method: Card / ACH",
                    "title": "Payment Method",
                    "pattern": "{payment_method}",
                    "is_available_for_subject": false,
                    "custom_action": true
                },
                {
                    "field": "confirm_url",
                    "description": "URL to the page with payment confirmation",
                    "title": "Payment Confirm URL",
                    "pattern": "{payment_confirm_url}",
                    "is_available_for_subject": false,
                    "custom_action": true
                },
                {
                    "field": "payment_total_amount",
                    "description": "Total Payment Amount",
                    "title": "Total Payment Amount",
                    "pattern": "{payment_total_amount}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "uncollected_tickets_count",
                    "description": "Uncollected Tickets Count - for cash and check",
                    "title": "Uncollected Tickets Count",
                    "pattern": "{uncollected_tickets_count}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "sw_fee",
                    "description": "Event SW Fee",
                    "title": "Event SW Fee",
                    "pattern": "{sw_fee}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "card_last_4",
                    "description": "Card Last 4 Number",
                    "title": "Card Last 4 Number",
                    "pattern": "{card_last_4}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "event_short_name",
                    "description": "Event Short Name",
                    "title": "Event Short Name",
                    "pattern": "{event_short_name}",
                    "is_available_for_subject": false,
                    "custom_action": false
                },
                {
                    "field": "season",
                    "description": "Event Season",
                    "title": "Event Season",
                    "pattern": "{season}",
                    "is_available_for_subject": false,
                    "custom_action": false
                }
            ]',
            '{ "roles": ["god_mode"] }',
            17
        );
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        DELETE FROM "email_template_group" WHERE "group" = 'tickets_uncollected_fee_payments';
    `);
};

exports.up = function(knex) {
  return knex.raw(`
    ALTER TABLE "public"."master_athlete"
        ADD COLUMN "safesport_end_date" TEXT COLLATE "pg_catalog"."default";
    ALTER TABLE "public"."master_athlete"
        ADD COLUMN "safesport_start_date" TEXT COLLATE "pg_catalog"."default";
    ALTER TABLE "public"."master_athlete"
        ADD COLUMN "safesport_status_id" TEXT COLLATE "pg_catalog"."default";
    
    COMMENT ON COLUMN "public"."master_athlete"."safesport_end_date" IS 'Athletes Safesport certification end date';
    COMMENT ON COLUMN "public"."master_athlete"."safesport_start_date" IS 'Athletes Safesport certification start date';
    COMMENT ON COLUMN "public"."master_athlete"."safesport_status_id"
        IS 'Athletes Safesport certification ID (1 or 2). 1 - not valid, 2 - valid';
        
    UPDATE "public"."event" AS "e"
    SET "team_members_validation" = "e"."team_members_validation" || '{ "validate_athlete_ss": true }'
    WHERE "e"."sport_sanctioning_id" = 3 AND "e"."team_members_validation" ->> 'validate_athlete_ss' IS NULL;    
  `);
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."master_athlete" DROP COLUMN "safesport_end_date";
        ALTER TABLE "public"."master_athlete" DROP COLUMN "safesport_start_date";
        ALTER TABLE "public"."master_athlete" DROP COLUMN "safesport_status_id";
        
        UPDATE "public"."event" AS "e"
        SET "team_members_validation" = "e"."team_members_validation"::JSONB - 'validate_athlete_ss'
        WHERE "e"."sport_sanctioning_id" = 3 AND "e"."team_members_validation" ->> 'validate_athlete_ss' IS NOT NULL;
    `)
};

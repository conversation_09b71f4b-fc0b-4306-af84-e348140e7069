
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Create new template group for teams refunds -----------------------------------------------------------------
        INSERT INTO public.email_template_group ("group", title, description, variables, usage_restrictions,
                                                 position)
        VALUES ('teams.refunds', 'Refunds for teams',
                '<i>Email templates related to teams refunds that can be sent automatically</i>', '[
            {
              "field": "event_name",
              "title": "Event Name",
              "pattern": "{event_name}",
              "is_available_for_subject": true
            },
            {
              "field": "club_name",
              "title": "Club Name",
              "pattern": "{club_name}",
              "is_available_for_subject": true
            },
            {
              "field": "team_names",
              "title": "Team Name(s)",
              "pattern": "{team_names}"
            },
            {
              "field": "amount_refunded",
              "title": "Amount Refunded",
              "pattern": "{amount_refunded}"
            },
            {
               "field": "total_amount",
               "title": "Total Purchase Amount",
               "pattern": "{total_amount}"
            },
            {
               "field": "adjusted_total_amount",
               "title": "Adjusted Total Amount",
               "pattern": "{adjusted_total_amount}"
            },
            {
              "field": "date_refunded",
              "title": "Date Issued",
              "pattern": "{date_refunded}"
            },
            {
              "field": "payment_type",
              "title": "Payment Type",
              "pattern": "{payment_type}"
            },
            {
              "field": "payment_status",
              "title": "Payment Status",
              "pattern": "{payment_status}"
            },
            {
              "field": "payment_amount",
              "title": "Payment Amount",
              "pattern": "{payment_amount}"
            },
            {
              "field": "payment_created_date",
              "title": "Payment Created Date",
              "pattern": "{payment_created_date}"
            },
            {
              "field": "payment_date_paid",
              "title": "Payment Date Paid",
              "pattern": "{payment_date_paid}"
            },
            {
              "field": "payment_canceled_date",
              "title": "Payment Canceled Date",
              "pattern": "{payment_canceled_date}"
            },
            {
               "field": "eo_stripe_account",
               "title": "EO Stripe Account ID",
               "pattern": "{eo_stripe_account}"
            },
            {
              "field": "eo_first",
              "title": "Event Owner First",
              "pattern": "{eo_first}",
              "is_available_for_subject": true
            },
            {
              "field": "eo_name",
              "title": "Event Owner First and Last Name",
              "pattern": "{eo_name}",
              "is_available_for_subject": true
            },
            {
              "field": "eo_last",
              "title": "Event Owner Last",
              "pattern": "{eo_last}",
              "is_available_for_subject": true
            },
            {
              "field": "eo_email",
              "title": "Event Owner Email",
              "pattern": "{eo_email}"
            },
            {
              "field": "eo_phone",
              "title": "Event Owner Phone",
              "pattern": "{eo_phone}"
            },
            {
              "field": "cd_first",
              "title": "Club Director First",
              "pattern": "{cd_first}"
            },
            {
              "field": "cd_last",
              "title": "Club Director Last",
              "pattern": "{cd_last}",
              "is_available_for_subject": true
            },
            {
              "field": "cd_email",
              "title": "Club Director Email",
              "pattern": "{cd_email}",
              "is_available_for_subject": true
            },
            {
              "field": "cd_phone",
              "title": "Club Director Phone",
              "pattern": "{cd_phone}"
            },
            {
              "field": "social_icons",
              "title": "Social Icons",
              "pattern": "{social_icons}",
              "custom_action": true
            },
            {
              "field": "facebook_icon",
              "title": "Facebook Icon",
              "pattern": "{facebook_icon}",
              "custom_action": true
            },
            {
              "field": "twitter_icon",
              "title": "Twitter Icon",
              "pattern": "{twitter_icon}",
              "custom_action": true
            },
            {
              "field": "instagram_icon",
              "title": "Instagram Icon",
              "pattern": "{instagram_icon}",
              "custom_action": true
            },
            {
              "field": "snapchat_icon",
              "title": "Snapchat Icon",
              "pattern": "{snapchat_icon}",
              "custom_action": true
            },
            {
              "field": "card_last_4",
              "title": "Last four digits card",
              "pattern": "{card_last_4}"
            }
          ]', '{
            "roles": [
              "any"
            ]
          }', 15);
        --------------------------------------------------------------------------------------------------------------
        
        
        -- Create new template type for teams payment refund from Stripe dashboard for admin -------------------------
        INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                default_email_template_id)
        VALUES ('refund.stripe.admin', 'teams.refunds', 'Stripe Dashboard Refund (to SW Admin)',
                '<em>Refund From Stripe Dashboard (to SW Admin)</em>', 'Stripe Dashboard Refund (to SW Admin)', true, 0);
        --------------------------------------------------------------------------------------------------------------
        
        
        -- Create new template type for teams payment refund from Stripe dashboard for EO ----------------------------
        INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                default_email_template_id)
        VALUES ('refund.stripe.eo', 'teams.refunds', 'Stripe Dashboard Refund',
                '<em>Refund From Stripe Dashboard</em>', 'Stripe Dashboard Refund', true, 0);
        --------------------------------------------------------------------------------------------------------------
        
        
        -- Create new email trigger for teams payment refund from Stripe dashboard for admin -------------------------
        INSERT INTO public.event_email_trigger
        (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('refund.stripe.admin', 'teams.refunds', 0, 0);
        --------------------------------------------------------------------------------------------------------------
                  
        
        -- Create new email trigger for teams payment refund from Stripe dashboard for EO ----------------------------
        INSERT INTO public.event_email_trigger
        (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('refund.stripe.eo', 'teams.refunds', 0, 0);
        --------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE
        FROM email_template_type
        WHERE type IN ('refund.stripe.eo', 'refund.stripe.admin')
          AND email_template_group = 'teams.refunds';
          
        DELETE
        FROM event_email_trigger
        WHERE email_template_type IN ('refund.stripe.eo', 'refund.stripe.admin')
          AND email_template_group = 'teams.refunds';
          
        DELETE
        FROM email_template_group
        WHERE "group" = 'teams.refunds';
    `)
};

exports.up = function(knex) {
    return knex.raw(String.raw `
            UPDATE "email_template" set 

            "email_html" = '
<!DOCTYPE html>
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">

<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
    <style>
        * {
            box-sizing: border-box
        }
        
        body {
            margin: 0;
            padding: 0
        }
        
        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: inherit !important
        }
        
        #MessageViewBody a {
            color: inherit;
            text-decoration: none
        }
        
        p {
            line-height: inherit
        }
        
        @media (max-width:660px) {
            .row-content {
                width: 100% !important
            }
            .column .border,
            .mobile_hide {
                display: none
            }
            table {
                table-layout: fixed !important
            }
            .stack .column {
                width: 100%;
                display: block
            }
            .mobile_hide {
                min-height: 0;
                max-height: 0;
                max-width: 0;
                overflow: hidden;
                font-size: 0
            }
            .desktop_hide,
            .desktop_hide table {
                display: table !important;
                max-height: none !important
            }
        }
    </style>
</head>

<body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none">
    <table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff">
        <tbody>
            <tr>
                <td>
                    <table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:12px">{event_logo}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-2" width="16.666666666666668%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:15px;padding-left:0;padding-top:15px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-3" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;text-align:right;mso-line-height-alt:16.8px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:14px;text-align:right;mso-line-height-alt:16.8px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            <span style="font-size:26px;"><strong>{event_name}&nbsp;</strong></span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            {event_dates_info} &nbsp;&nbsp;</p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:0;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px;padding-top:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#9b9b9b;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px"><span style="font-size:12px;">Daily pass good
                                                                                for entire day on available date listed
                                                                                below</span></p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="divider_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-bottom:10px;padding-left:10px;padding-right:10px">
                                                                <div align="center">
                                                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0">
                                                                        <tr>
                                                                            <td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #bbb">
                                                                                <span>&#8202;</span>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td>
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px">
                                                                            <span style="font-size:16px;">Present this
                                                                                QR code on a mobile device along with a
                                                                                <span
                                                                                    style="background-color:#ffffff;color:#337ab7;"><strong>Government
                                                                                        photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding
                                                                            ID. Minors without ID must accompany adults to enter.
                                                                            </span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td>
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            <span style="font-size:20px;">{ticket_holder_name}</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-left:5px;padding-right:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:0;padding-left:0;padding-top:5px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-2" width="50%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            {qr_code_image}</p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:0">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-2" width="50%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:5px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            {apple_wallet_icon}</p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px;padding-top:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;mso-line-height-alt:32.4px">
                                                                            <span style="font-size:18px;color:#9b9b9b;">Participant
                                                                                information:</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                    <table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Available
                                                                                Dates:</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="spacer_block" style="height:0;line-height:0;font-size:1px">&#8202;</div>
                                                </td>
                                                <td class="column column-2" width="58.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                    <table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{valid_dates}</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                    <table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Pass
                                                                                barcode:</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Email:</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Phone:</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Zip
                                                                                Code:</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Purchased:</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:25.2px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">Price:</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                </td>
                                                <td class="column column-2" width="58.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                    <table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-left:10px;padding-right:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{ticket_barcode}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{payer_email}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{payer_phone}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{payer_zip}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{payment_date_time}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:25.2px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
                                                                            <span style="font-size:16px;">{ticket_price}&nbsp;</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                    <table class="text_block desktop_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word;mso-hide:all;display:none;max-height:0;overflow:hidden">
                                                        <tr>
                                                            <td style="padding-bottom:10px;padding-left:10px;padding-right:10px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:21px;color:#555;line-height:1.5;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Available
                                                                                Dates: {valid_dates}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Pass barcode:
                                                                                {ticket_barcode}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Email:
                                                                                {payer_email}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Phone:
                                                                                {payer_phone}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Zip Code:
                                                                                {payer_zip}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Purchased:
                                                                                {payment_date_time}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:21px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:16px;mso-line-height-alt:24px">
                                                                            <span style="font-size:16px;">Price:
                                                                                {ticket_price}&nbsp;</span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td>
                                                                <div style="font-family:Arial,sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;font-family:''Helvetica Neue'',Helvetica,Arial,sans-serif;mso-line-height-alt:16.8px;color:#555;line-height:1.2">
                                                                        <p style="margin:0"><span style="color:#9b9b9b;font-size:18px;">All
                                                                                ticket holders on this purchase:</span>
                                                                        </p>
                                                                        <p style="margin:0"><em><span
                                                                                    style="color:#9b9b9b;font-size:12px;">(Click
                                                                                    on a name to access each ticket if
                                                                                    you do not receive the individual QR
                                                                                    code ticket email)</span></em></p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-13" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-2" width="50%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px">
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:14px;text-align:center">
                                                                            {tickets_links}</p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                                <td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                                                        <tr>
                                                            <td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px">
                                                                <div></div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table class="row row-14" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
                        <tbody>
                            <tr>
                                <td>
                                    <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640">
                                        <tbody>
                                            <tr>
                                                <td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0">
                                                    <table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
                                                        <tr>
                                                            <td>
                                                                <div style="font-family:sans-serif">
                                                                    <div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif">
                                                                        <p style="margin:0;font-size:12px"><span style="font-size:18px;color:#9b9b9b;">Important
                                                                                Information:</span></p>
                                                                        <p style="margin:0;font-size:12px"><span style="font-size:14px;">{tickets_receipt_descr}</span>
                                                                        </p>
                                                                        <p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px">
                                                                            &nbsp;</p>
                                                                        <p style="margin:0;font-size:12px;text-align:center">
                                                                            <span style="font-size:14px;">{social_icons} </span>
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
    <!-- End -->
</body>

</html>',
            
    "email_text" = '{event_logo}
{event_name} {event_dates_info}   Daily pass good for entire day on available date listed below
Present this QR code on a mobile device along with a Government photo ID to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.{ticket_holder_name}{qr_code_image}
{apple_wallet_icon}Participant information:
Available Dates:  {valid_dates}
Pass barcode:Email:Phone:Zip Code:Purchased: Price:  {ticket_barcode}{payer_email}{payer_phone}{payer_zip}{payment_date_time} {ticket_price}   Available Dates: {valid_dates}
Pass barcode: {ticket_barcode}Email: {payer_email}Phone: {payer_phone}Zip Code: {payer_zip}
Purchased: {payment_date_time} Price: {ticket_price}  All ticket holders on this purchase:(Click on a name to access each ticket if you do not receive the individual QR code ticket email)
{tickets_links}Important Information:{tickets_receipt_descr}
{social_icons}',
            
            "bee_json" = '
            {
                "page": {
                  "body": {
                    "type": "mailup-bee-page-proprerties",
                    "content": {
                      "style": {
                        "color": "#000000",
                        "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                      },
                      "computedStyle": {
                        "align": "center",
                        "linkColor": "#0000FF",
                        "messageWidth": "640px",
                        "messageBackgroundColor": "#FFFFFF"
                      }
                    },
                    "webFonts": [],
                    "container": {
                      "style": {
                        "background-color": "#ffffff"
                      }
                    }
                  },
                  "rows": [
                    {
                      "type": "two-columns-3-9-empty",
                      "uuid": "862a63c2-2d85-49c9-a9bf-a11543177d88",
                      "columns": [
                        {
                          "uuid": "19c06ebf-11c7-4290-966e-ea5b45d69d85",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "d6e3df38-3d68-45e6-bf48-15feaa48e0cd",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event''s Logo Image\">{event_logo}</code></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 5
                        },
                        {
                          "uuid": "113c5b2a-3d84-46a1-bd7b-e774b09120cf",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "15px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "15px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 2
                        },
                        {
                          "uuid": "60d6be53-1d65-43ce-841a-4e7cd6b5d771",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "aa2e4e69-409a-46e9-8d7e-6bdc11c85faf",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 26px; line-height: 31px;\" data-mce-style=\"font-size: 26px; line-height: 31px;\"><strong>{event_name}&nbsp;</strong></span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{event_dates_info} &nbsp;&nbsp;</p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 5
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "00562602-fcd1-49d3-bab9-028e1956a7fa",
                      "columns": [
                        {
                          "uuid": "ba852ef2-f803-4101-96eb-675f32a7920b",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "e000d369-e46d-4729-b54e-d0e71045f4ee",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Daily pass good for entire day on available date listed below</span></p></div>",
                                  "style": {
                                    "color": "#9b9b9b",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "50c455da-69a7-4629-8961-987e389cf029",
                      "columns": [
                        {
                          "uuid": "df2aa1f9-6abe-41b8-81de-1ad199dde115",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-divider",
                              "uuid": "ef18d6a5-984d-4d8c-8ee6-5d861be30faf",
                              "descriptor": {
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "divider": {
                                  "style": {
                                    "width": "100%",
                                    "border-top": "1px solid #BBBBBB"
                                  }
                                },
                                "computedStyle": {
                                  "align": "center",
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "9001a3d9-eac2-4e78-9190-6d9c08942a0f",
                      "columns": [
                        {
                          "uuid": "a700934a-402f-484c-9fe9-2c297a5e10bb",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "277c6edd-f3bb-4743-82c2-632a33af40ea",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Present this QR code on a mobile device along with a <span style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\" data-mce-style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\"><strong>Government photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "c710a258-34fa-4d18-bd51-7928747a93e1",
                      "columns": [
                        {
                          "uuid": "78e09595-6a01-4d05-9b90-06f95456ce55",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "ab786759-f5f1-44bb-bd34-a70a13a18c67",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">{ticket_holder_name}</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "8f19dd21-3c22-4df8-8cc2-b2896afad221",
                      "columns": [
                        {
                          "uuid": "4ed08b5e-7e08-4980-b272-cc59d30a6564",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "5px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "5px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        },
                        {
                          "uuid": "cfb9bfac-3251-4a88-925d-77fa1690c62a",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "ecc2faaa-bd35-47fa-8a52-6dbbc1c64a7a",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{qr_code_image}</p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "5px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 6
                        },
                        {
                          "uuid": "9fa36f9c-f04f-46e5-891e-3b3f15e33264",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "*************-4d15-a4f8-bda3c575fa84",
                      "columns": [
                        {
                          "uuid": "11c26808-c646-43c2-8ade-6ef30225f965",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "0px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        },
                        {
                          "uuid": "b47fd3a5-3fe2-4cf0-895e-e8f69ebcc0b7",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "bcc8c36a-d1ef-4f11-8120-95153044208a",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{apple_wallet_icon}</p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 6
                        },
                        {
                          "uuid": "e57d21fb-7776-4f27-b789-49c3a53341a5",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "3e4d3fd7-5233-40d7-a3de-bf6097568836",
                      "columns": [
                        {
                          "uuid": "7ba1cb1e-69d3-4945-8f7f-225f6d10fe2a",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "4a7742e0-914f-46c3-b4a3-9ed9b11fc793",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 14px; line-height: 25px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 25px; word-break: break-word;\"><span style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\" data-mce-style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\">Participant information:</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "180%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "two-columns-3-9-empty",
                      "uuid": "ddd9f27d-2f48-4d32-8054-fd10c8d16f28",
                      "columns": [
                        {
                          "uuid": "ed3abe4a-8434-4692-8b8b-cdc3b311f45b",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "0px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "a14bbdca-06fa-4271-8079-4bc319d109d7",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Available Dates:</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "180%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": true,
                                  "hideContentOnDesktop": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 5
                        },
                        {
                          "uuid": "8e4603bc-5b80-4b4b-9b19-e3eaf1039874",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "ed9ea857-9298-4e00-9b9a-27e119e6b2fa",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{valid_dates}</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "180%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": true,
                                  "hideContentOnDesktop": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 7
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "two-columns-3-9-empty",
                      "uuid": "3bdcd663-aeb8-4b8b-bfcf-3799ce6581d3",
                      "columns": [
                        {
                          "uuid": "ed3abe4a-8434-4692-8b8b-cdc3b311f45b",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "a4922a37-3a89-4204-a10c-abc5136e0853",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Pass barcode:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Email:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Phone:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Zip Code:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Purchased:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Price:</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "180%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": true,
                                  "hideContentOnDesktop": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 5
                        },
                        {
                          "uuid": "8e4603bc-5b80-4b4b-9b19-e3eaf1039874",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "5c68847a-5e50-431c-a5d2-a105ae7a7c6e",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_email}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_phone}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_zip}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_price}&nbsp;</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "180%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "0px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": true,
                                  "hideContentOnDesktop": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 7
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "94fb84e4-2582-472f-8d69-548cd7331c31",
                      "columns": [
                        {
                          "uuid": "964cc474-7c93-434a-b7b3-2e6def7d2bbf",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "a2f89c02-dd22-443b-b5ae-2ac06cb56343",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Available Dates: {valid_dates}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Pass barcode: {ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Email: {payer_email}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Phone: {payer_phone}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Zip Code: {payer_zip}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Purchased: {payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Price: {ticket_price}&nbsp;</span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "150%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "0px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false,
                                  "hideContentOnDesktop": true
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "1c9ccffd-094c-4a4f-8aa5-b0480213315a",
                      "columns": [
                        {
                          "uuid": "f1813730-f126-45c0-b70f-af9e0869f738",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "e66ed712-c131-445f-8a02-d0a40a12e963",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\" data-mce-style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\">All ticket holders on this purchase:</span></p><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><em><span style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\" data-mce-style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></em></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "''Helvetica Neue'', Helvetica, Arial, sans-serif",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "three-columns-empty",
                      "uuid": "dcc3c7fe-181f-48ca-866a-7cceb2f094a6",
                      "columns": [
                        {
                          "uuid": "36fccea6-ecbd-4313-9cea-b284f479ff31",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        },
                        {
                          "uuid": "9124d5d7-6457-40c4-8851-66da056cf25a",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "95bc0deb-c452-41f5-a614-e330a1aff047",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 6
                        },
                        {
                          "uuid": "baa627e6-3205-48a1-b6f0-4d81b8b9da88",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [],
                          "grid-columns": 3
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "500px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    },
                    {
                      "type": "one-column-empty",
                      "uuid": "d670fb00-97c6-4f67-80dc-d868ba849587",
                      "columns": [
                        {
                          "uuid": "4d936506-f768-48a3-9015-67d00d95a043",
                          "style": {
                            "border-top": "0px solid transparent",
                            "border-left": "0px solid transparent",
                            "padding-top": "5px",
                            "border-right": "0px solid transparent",
                            "padding-left": "0px",
                            "border-bottom": "0px solid transparent",
                            "padding-right": "0px",
                            "padding-bottom": "5px",
                            "background-color": "transparent"
                          },
                          "modules": [
                            {
                              "type": "mailup-bee-newsletter-modules-text",
                              "uuid": "b66b40ee-d2af-4a70-8ea5-1cee45c1f591",
                              "descriptor": {
                                "text": {
                                  "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\" data-mce-style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">Important Information</code>:</span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">{tickets_receipt_descr}</code></span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">&nbsp;</code></span></p></div>",
                                  "style": {
                                    "color": "#555555",
                                    "font-family": "inherit",
                                    "line-height": "120%"
                                  },
                                  "computedStyle": {
                                    "linkColor": "#0000FF"
                                  }
                                },
                                "style": {
                                  "padding-top": "10px",
                                  "padding-left": "10px",
                                  "padding-right": "10px",
                                  "padding-bottom": "10px"
                                },
                                "computedStyle": {
                                  "hideContentOnMobile": false
                                }
                              }
                            }
                          ],
                          "grid-columns": 12
                        }
                      ],
                      "content": {
                        "style": {
                          "color": "#000000",
                          "width": "640px",
                          "background-color": "#FFFFFF",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        },
                        "computedStyle": {
                          "rowColStackOnMobile": true,
                          "rowReverseColStackOnMobile": false
                        }
                      },
                      "container": {
                        "style": {
                          "background-color": "transparent",
                          "background-image": "none",
                          "background-repeat": "no-repeat",
                          "background-position": "top left"
                        }
                      }
                    }
                  ],
                  "title": "BF-basic-newsletter",
                  "template": {
                    "name": "template-base",
                    "type": "basic",
                    "version": "0.0.1"
                  },
                  "description": "BF-basic-newsletter"
                },
                "comments": {}
              }
            ' 
              WHERE email_template_type = 'tickets.assigned.ticket.daily' 
              AND email_template_group='tickets.payments'
        `)
};

exports.down = function(knex) {
    return knex.raw(String.raw `
    UPDATE "email_template" set
    "email_html" = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
    <head>
     <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
     <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
     <meta name="viewport" content="width=device-width">
     <!--[if !mso]><!-->
     <meta http-equiv="X-UA-Compatible" content="IE=edge">
     <!--<![endif]-->
     <title></title>
     <!--[if !mso]><!-->
     <!--<![endif]-->
     <style type="text/css">
      body {
       margin: 0;
       padding: 0;
      }
    
      table,
      td,
      tr {
       vertical-align: top;
       border-collapse: collapse;
      }
    
      * {
       line-height: inherit;
      }
    
      a[x-apple-data-detectors=true] {
       color: inherit !important;
       text-decoration: none !important;
      }
     </style>
     <style type="text/css" id="media-query">
      @media (max-width: 660px) {
    
       .block-grid,
       .col {
        min-width: 320px !important;
        max-width: 100% !important;
        display: block !important;
       }
    
       .block-grid {
        width: 100% !important;
       }
    
       .col {
        width: 100% !important;
       }
    
       .col_cont {
        margin: 0 auto;
       }
    
       img.fullwidth,
       img.fullwidthOnMobile {
        width: 100% !important;
       }
    
       .no-stack .col {
        min-width: 0 !important;
        display: table-cell !important;
       }
    
       .no-stack.two-up .col {
        width: 50% !important;
       }
    
       .no-stack .col.num2 {
        width: 16.6% !important;
       }
    
       .no-stack .col.num3 {
        width: 25% !important;
       }
    
       .no-stack .col.num4 {
        width: 33% !important;
       }
    
       .no-stack .col.num5 {
        width: 41.6% !important;
       }
    
       .no-stack .col.num6 {
        width: 50% !important;
       }
    
       .no-stack .col.num7 {
        width: 58.3% !important;
       }
    
       .no-stack .col.num8 {
        width: 66.6% !important;
       }
    
       .no-stack .col.num9 {
        width: 75% !important;
       }
    
       .no-stack .col.num10 {
        width: 83.3% !important;
       }
    
       .video-block {
        max-width: none !important;
       }
    
       .mobile_hide {
        min-height: 0px;
        max-height: 0px;
        max-width: 0px;
        display: none;
        overflow: hidden;
        font-size: 0px;
       }
    
       .desktop_hide {
        display: block !important;
        max-height: none !important;
       }
    
       .img-container.big img {
        width: auto !important;
       }
      }
     </style>
    </head>
    
    <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #F7F7F7;">
     <!--[if IE]><div class="ie-browser"><![endif]-->
     <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #F7F7F7; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#F7F7F7" valign="top">
      <tbody>
       <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top;" valign="top">
         <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#F7F7F7"><![endif]-->
         <div style="background-color:transparent;">
          <div class="block-grid three-up" style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="266" style="background-color:#FFFFFF;width:266px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num5" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 265px; width: 266px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 12px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 14px;">
                 <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;">{event_logo}</p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="106" style="background-color:#FFFFFF;width:106px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:15px; padding-bottom:15px;"><![endif]-->
            <div class="col num2" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 106px; width: 106px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:15px; padding-bottom:15px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="266" style="background-color:#FFFFFF;width:266px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num5" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 265px; width: 266px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: right; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: right; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                 <p style="margin: 0; font-size: 26px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 31px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 26px;"><strong>{event_name}&nbsp;</strong></span></p>
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">{event_dates_info} &nbsp;&nbsp;</p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:0px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:0px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#9b9b9b;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #9b9b9b; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 12px;">Daily pass good for entire day on available date listed below</span></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
                <tbody>
                 <tr style="vertical-align: top;" valign="top">
                  <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 0px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;" valign="top">
                   <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px solid #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
                    <tbody>
                     <tr style="vertical-align: top;" valign="top">
                      <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
                     </tr>
                    </tbody>
                   </table>
                  </td>
                 </tr>
                </tbody>
               </table>
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 16px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 19px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Present this QR code on a mobile device along with a <span style="background-color: #ffffff; color: #337ab7;"><strong>Government photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.</span></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 20px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 20px;">{ticket_holder_name}</span></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid three-up" style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 5px; padding-left: 5px; padding-top:5px; padding-bottom:0px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:0px; padding-right: 5px; padding-left: 5px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="320" style="background-color:#FFFFFF;width:320px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num6" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 318px; width: 320px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 5px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:5px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">{qr_code_image}</p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid three-up" style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:0px; padding-bottom:5px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:0px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="320" style="background-color:#FFFFFF;width:320px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num6" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 318px; width: 320px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:0px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">{apple_wallet_icon}</p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:10px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.8; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 25px;">
                 <p style="margin: 0; font-size: 18px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 32px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 18px; color: #9b9b9b;">Participant information:</span></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid mixed-two-up" style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="266" style="background-color:#FFFFFF;width:266px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num5" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 265px; width: 266px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <div class="mobile_hide">
                <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
                <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:0px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
                 <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.8; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 25px;">
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Available Dates:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Pass barcode:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Email:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Phone:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Zip Code:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Purchased:</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; text-align: right; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Price:</span></p>
                 </div>
                </div>
                <!--[if mso]></td></tr></table><![endif]-->
               </div>
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="373" style="background-color:#FFFFFF;width:373px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num7" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 371px; width: 373px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <div class="mobile_hide">
                <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
                <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:0px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
                 <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.8; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 25px;">
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{valid_dates}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{ticket_barcode}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{payer_email}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{payer_phone}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{payer_zip}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{payment_date_time}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.8; word-break: break-word; mso-line-height-alt: 29px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">{ticket_price}&nbsp;</span></p>
                 </div>
                </div>
                <!--[if mso]></td></tr></table><![endif]-->
               </div>
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if !mso]><!-->
               <div class="desktop_hide" style="mso-hide: all; display: none; max-height: 0px; overflow: hidden;">
                <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
                <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.5;padding-top:0px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                 <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.5; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 21px;">
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Available Dates: {valid_dates}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Pass barcode: {ticket_barcode}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Email: {payer_email}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Phone: {payer_phone}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Zip Code: {payer_zip}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Purchased: {payment_date_time}</span></p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                  <p style="margin: 0; font-size: 16px; line-height: 1.5; word-break: break-word; mso-line-height-alt: 24px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 16px;">Price: {ticket_price}&nbsp;</span></p>
                 </div>
                </div>
                <!--[if mso]></td></tr></table><![endif]-->
               </div>
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Arial, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:''Helvetica Neue'', Helvetica, Arial, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; font-family: ''Helvetica Neue'', Helvetica, Arial, sans-serif; color: #555555; mso-line-height-alt: 17px;">
                 <p style="margin: 0; line-height: 1.2; word-break: break-word; font-size: 18px; mso-line-height-alt: 22px; margin-top: 0; margin-bottom: 0;"><span style="color: #9b9b9b; font-size: 18px;">All ticket holders on this purchase:</span></p>
                 <p style="margin: 0; line-height: 1.2; word-break: break-word; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;"><em><span style="color: #9b9b9b; font-size: 12px;">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></em></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid three-up" style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="320" style="background-color:#FFFFFF;width:320px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num6" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 318px; width: 320px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 14px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 17px;">
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; text-align: center; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;">{tickets_links}</p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td><td align="center" width="160" style="background-color:#FFFFFF;width:160px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num3" style="display: table-cell; vertical-align: top; max-width: 320px; min-width: 159px; width: 160px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <div style="background-color:transparent;">
          <div class="block-grid " style="min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; Margin: 0 auto; background-color: #FFFFFF;">
           <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
            <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
            <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
             <div class="col_cont" style="width:100% !important;">
              <!--[if (!mso)&(!IE)]><!-->
              <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
               <!--<![endif]-->
               <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
               <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
                <div class="txtTinyMce-wrapper" style="font-size: 12px; line-height: 1.2; color: #555555; font-family: Tahoma, Verdana, Segoe, sans-serif; mso-line-height-alt: 14px;">
                 <p style="margin: 0; font-size: 18px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 22px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 18px; color: #9b9b9b;">Important Information:</span></p>
                 <p style="margin: 0; font-size: 14px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 14px;">{tickets_receipt_descr}</span></p>
                 <p style="margin: 0; font-size: 12px; line-height: 1.2; word-break: break-word; mso-line-height-alt: 14px; margin-top: 0; margin-bottom: 0;">&nbsp;</p>
                 <p style="margin: 0; font-size: 14px; text-align: center; line-height: 1.2; word-break: break-word; mso-line-height-alt: 17px; margin-top: 0; margin-bottom: 0;"><span style="font-size: 14px;">{social_icons}&nbsp;</span></p>
                </div>
               </div>
               <!--[if mso]></td></tr></table><![endif]-->
               <!--[if (!mso)&(!IE)]><!-->
              </div>
              <!--<![endif]-->
             </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
            <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
           </div>
          </div>
         </div>
         <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        </td>
       </tr>
      </tbody>
     </table>
     <!--[if (IE)]></div><![endif]-->
    </body>
    
    </html>',    
    "email_text" = '{event_logo}
{event_name}
{event_dates_info}
Daily pass good for entire day on available date listed below
Present this QR code on a mobile device along with a Government photo ID to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.
{ticket_holder_name}
{qr_code_image}
{apple_wallet_icon}
Participant information:
Available Dates:
Pass barcode:
Email:
Phone:
Zip Code:
Purchased:
Price:
{valid_dates}
{ticket_barcode}
{payer_email}
{payer_phone}
{payer_zip}
{payment_date_time}
{ticket_price}
Available Dates: {valid_dates}
Pass barcode: {ticket_barcode}
Email: {payer_email}
Phone: {payer_phone}
Zip Code: {payer_zip}
Purchased: {payment_date_time}
Price: {ticket_price}
All ticket holders on this purchase:
(Click on a name to access each ticket if you do not receive the individual QR code ticket email)
{tickets_links}
Important Information:
{tickets_receipt_descr}
{social_icons}',
    
    "bee_json" = '{
        "page": {
          "body": {
            "type": "mailup-bee-page-proprerties",
            "content": {
              "style": {
                "color": "#000000",
                "font-family": "Tahoma, Verdana, Segoe, sans-serif"
              },
              "computedStyle": {
                "align": "center",
                "linkColor": "#0000FF",
                "messageWidth": "640px",
                "messageBackgroundColor": "#FFFFFF"
              }
            },
            "webFonts": [],
            "container": {
              "style": {
                "background-color": "#ffffff"
              }
            }
          },
          "rows": [
            {
              "type": "two-columns-3-9-empty",
              "uuid": "862a63c2-2d85-49c9-a9bf-a11543177d88",
              "columns": [
                {
                  "uuid": "19c06ebf-11c7-4290-966e-ea5b45d69d85",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "d6e3df38-3d68-45e6-bf48-15feaa48e0cd",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event''s Logo Image\">{event_logo}</code></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 5
                },
                {
                  "uuid": "113c5b2a-3d84-46a1-bd7b-e774b09120cf",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "15px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "15px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 2
                },
                {
                  "uuid": "60d6be53-1d65-43ce-841a-4e7cd6b5d771",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "aa2e4e69-409a-46e9-8d7e-6bdc11c85faf",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 26px; line-height: 31px;\" data-mce-style=\"font-size: 26px; line-height: 31px;\"><strong>{event_name}&nbsp;</strong></span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{event_dates_info} &nbsp;&nbsp;</p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 5
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "00562602-fcd1-49d3-bab9-028e1956a7fa",
              "columns": [
                {
                  "uuid": "ba852ef2-f803-4101-96eb-675f32a7920b",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "0px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "e000d369-e46d-4729-b54e-d0e71045f4ee",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Daily pass good for entire day on available date listed below</span></p></div>",
                          "style": {
                            "color": "#9b9b9b",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "0px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "50c455da-69a7-4629-8961-987e389cf029",
              "columns": [
                {
                  "uuid": "df2aa1f9-6abe-41b8-81de-1ad199dde115",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-divider",
                      "uuid": "ef18d6a5-984d-4d8c-8ee6-5d861be30faf",
                      "descriptor": {
                        "style": {
                          "padding-top": "0px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "divider": {
                          "style": {
                            "width": "100%",
                            "border-top": "1px solid #BBBBBB"
                          }
                        },
                        "computedStyle": {
                          "align": "center",
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "9001a3d9-eac2-4e78-9190-6d9c08942a0f",
              "columns": [
                {
                  "uuid": "a700934a-402f-484c-9fe9-2c297a5e10bb",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "277c6edd-f3bb-4743-82c2-632a33af40ea",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Present this QR code on a mobile device along with a <span style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\" data-mce-style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\"><strong>Government photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "c710a258-34fa-4d18-bd51-7928747a93e1",
              "columns": [
                {
                  "uuid": "78e09595-6a01-4d05-9b90-06f95456ce55",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "ab786759-f5f1-44bb-bd34-a70a13a18c67",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">{ticket_holder_name}</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "8f19dd21-3c22-4df8-8cc2-b2896afad221",
              "columns": [
                {
                  "uuid": "4ed08b5e-7e08-4980-b272-cc59d30a6564",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "5px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "5px",
                    "padding-bottom": "0px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                },
                {
                  "uuid": "cfb9bfac-3251-4a88-925d-77fa1690c62a",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "ecc2faaa-bd35-47fa-8a52-6dbbc1c64a7a",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{qr_code_image}</p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "5px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 6
                },
                {
                  "uuid": "9fa36f9c-f04f-46e5-891e-3b3f15e33264",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "*************-4d15-a4f8-bda3c575fa84",
              "columns": [
                {
                  "uuid": "11c26808-c646-43c2-8ade-6ef30225f965",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "0px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                },
                {
                  "uuid": "b47fd3a5-3fe2-4cf0-895e-e8f69ebcc0b7",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "bcc8c36a-d1ef-4f11-8120-95153044208a",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{apple_wallet_icon}</p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "0px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 6
                },
                {
                  "uuid": "e57d21fb-7776-4f27-b789-49c3a53341a5",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "3e4d3fd7-5233-40d7-a3de-bf6097568836",
              "columns": [
                {
                  "uuid": "7ba1cb1e-69d3-4945-8f7f-225f6d10fe2a",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "4a7742e0-914f-46c3-b4a3-9ed9b11fc793",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 14px; line-height: 25px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 25px; word-break: break-word;\"><span style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\" data-mce-style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\">Participant information:</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "180%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "0px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "two-columns-3-9-empty",
              "uuid": "ddd9f27d-2f48-4d32-8054-fd10c8d16f28",
              "columns": [
                {
                  "uuid": "ed3abe4a-8434-4692-8b8b-cdc3b311f45b",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "a14bbdca-06fa-4271-8079-4bc319d109d7",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Available Dates:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Pass barcode:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Email:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Phone:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Zip Code:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Purchased:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Price:</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "180%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "0px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "0px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": true,
                          "hideContentOnDesktop": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 5
                },
                {
                  "uuid": "8e4603bc-5b80-4b4b-9b19-e3eaf1039874",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "ed9ea857-9298-4e00-9b9a-27e119e6b2fa",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{valid_dates}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_email}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_phone}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_zip}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_price}&nbsp;</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "180%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "0px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "0px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": true,
                          "hideContentOnDesktop": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 7
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "94fb84e4-2582-472f-8d69-548cd7331c31",
              "columns": [
                {
                  "uuid": "964cc474-7c93-434a-b7b3-2e6def7d2bbf",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "a2f89c02-dd22-443b-b5ae-2ac06cb56343",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Available Dates: {valid_dates}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Pass barcode: {ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Email: {payer_email}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Phone: {payer_phone}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Zip Code: {payer_zip}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Purchased: {payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Price: {ticket_price}&nbsp;</span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "150%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "0px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false,
                          "hideContentOnDesktop": true
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "1c9ccffd-094c-4a4f-8aa5-b0480213315a",
              "columns": [
                {
                  "uuid": "f1813730-f126-45c0-b70f-af9e0869f738",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "e66ed712-c131-445f-8a02-d0a40a12e963",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\" data-mce-style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\">All ticket holders on this purchase:</span></p><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><em><span style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\" data-mce-style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></em></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "''Helvetica Neue'', Helvetica, Arial, sans-serif",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "three-columns-empty",
              "uuid": "dcc3c7fe-181f-48ca-866a-7cceb2f094a6",
              "columns": [
                {
                  "uuid": "36fccea6-ecbd-4313-9cea-b284f479ff31",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                },
                {
                  "uuid": "9124d5d7-6457-40c4-8851-66da056cf25a",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "95bc0deb-c452-41f5-a614-e330a1aff047",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 6
                },
                {
                  "uuid": "baa627e6-3205-48a1-b6f0-4d81b8b9da88",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [],
                  "grid-columns": 3
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "500px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            },
            {
              "type": "one-column-empty",
              "uuid": "d670fb00-97c6-4f67-80dc-d868ba849587",
              "columns": [
                {
                  "uuid": "4d936506-f768-48a3-9015-67d00d95a043",
                  "style": {
                    "border-top": "0px solid transparent",
                    "border-left": "0px solid transparent",
                    "padding-top": "5px",
                    "border-right": "0px solid transparent",
                    "padding-left": "0px",
                    "border-bottom": "0px solid transparent",
                    "padding-right": "0px",
                    "padding-bottom": "5px",
                    "background-color": "transparent"
                  },
                  "modules": [
                    {
                      "type": "mailup-bee-newsletter-modules-text",
                      "uuid": "b66b40ee-d2af-4a70-8ea5-1cee45c1f591",
                      "descriptor": {
                        "text": {
                          "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\" data-mce-style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">Important Information</code>:</span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">{tickets_receipt_descr}</code></span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">&nbsp;</code></span></p></div>",
                          "style": {
                            "color": "#555555",
                            "font-family": "inherit",
                            "line-height": "120%"
                          },
                          "computedStyle": {
                            "linkColor": "#0000FF"
                          }
                        },
                        "style": {
                          "padding-top": "10px",
                          "padding-left": "10px",
                          "padding-right": "10px",
                          "padding-bottom": "10px"
                        },
                        "computedStyle": {
                          "hideContentOnMobile": false
                        }
                      }
                    }
                  ],
                  "grid-columns": 12
                }
              ],
              "content": {
                "style": {
                  "color": "#000000",
                  "width": "640px",
                  "background-color": "#FFFFFF",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                },
                "computedStyle": {
                  "rowColStackOnMobile": true,
                  "rowReverseColStackOnMobile": false
                }
              },
              "container": {
                "style": {
                  "background-color": "transparent",
                  "background-image": "none",
                  "background-repeat": "no-repeat",
                  "background-position": "top left"
                }
              }
            }
          ],
          "title": "BF-basic-newsletter",
          "template": {
            "name": "template-base",
            "type": "basic",
            "version": "0.0.1"
          },
          "description": "BF-basic-newsletter"
        },
        "comments": {}
      }' 
      WHERE email_template_type = 'tickets.assigned.ticket.daily' 
      AND email_template_group='tickets.payments'
      AND event_id IS NULL
    `)
}

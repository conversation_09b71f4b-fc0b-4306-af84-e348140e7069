/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO member_type_rules
            (field_name, member_type, rule_data, is_active) 
        VALUES 
            ('membership_definition_id', 'staff', '11ef2a9c-4541-c39e-8914-8649eb9692a5', true),
            ('membership_definition_id', 'athlete', '11ef2a9a-ad25-a20c-8911-8649eb9692a5', true);
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns {Knex.SchemaBuilder}
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."member_type_rules" WHERE rule_data = '11ef2a9c-4541-c39e-8914-8649eb9692a5';
        DELETE FROM "public"."member_type_rules" WHERE rule_data = '11ef2a9a-ad25-a20c-8911-8649eb9692a5';
    `)
};

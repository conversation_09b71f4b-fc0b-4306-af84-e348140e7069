/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TYPE user_action_type ADD VALUE 'payment_method_remove';
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE user_action ALTER COLUMN action TYPE TEXT USING action::text;

        DROP TYPE user_action_type;

        CREATE TYPE user_action_type AS ENUM ('login', 'logout');
    `)
};

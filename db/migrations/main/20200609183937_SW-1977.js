
exports.up = function(knex) {
    return knex.schema.raw(
        `CREATE TABLE ticket_coupon
        (
            ticket_coupon_id GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            created TIMESTAMP DEFAULT NOW() NOT NULL,
            modified T<PERSON><PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NULL,
            code TEXT NOT NULL,
            event_ticket_id INT NOT NULL,
            roster_team_id INT,
            initial_quantity INT NOT NULL,
            quantity INT NOT NULL
        );
        CREATE UNIQUE INDEX ticket_coupon_code_uindex ON public.ticket_coupon (code);
        CREATE TRIGGER update_ticket_coupon_modified
          BEFORE UPDATE
          ON ticket_coupon
          FOR EACH ROW EXECUTE PROCEDURE
          update_modified_column();`
    );
};

exports.down = function(knex) {
    return knex.schema.raw(`DROP TABLE ticket_coupon`);
};


exports.up = function(knex) {
    return knex.schema.raw(`
        INSERT INTO event_exhibitor_invoice AS eei (event_exhibitor_id, purchase_id, event_dates, booths, comment)
        SELECT ee.event_exhibitor_id, p.purchase_id, ee.event_dates, ee.booths, ee.comment
        FROM event_exhibitor ee
        INNER JOIN "event" as "e" ON e.event_id = ee.event_id
        INNER JOIN "purchase" as "p" ON
            p.event_id = ee.event_id AND p.payment_for = 'booths' AND p.sponsor_id = ee.sponsor_id
        WHERE e.season >= 2024;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw()
};

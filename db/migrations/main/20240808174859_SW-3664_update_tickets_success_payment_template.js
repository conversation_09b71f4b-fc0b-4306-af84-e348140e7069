
exports.up = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text =
                'A successful charge has been made to the {payment_method} ending in {card_last_4}
                for the payment of {event_short_name} SW Ticket Fees.
                Payment details:
                SW Ticket Fee Total: {payment_net_amount}
                Merchant Processing Fee: {payment_merchant_fee}
                Total Charge: {payment_total_amount}
                Accounting details:
                SW Ticket Fee Charged: {sw_fee}
                # of Uncollected Ticket Fees: {uncollected_tickets_count}
                Copyright © SportWrench Inc. {current_year}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">    <div style="text-align: left">
                <p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of <b>{event_short_name}</b> SW Ticket Fees.</p><p><span style="margin-bottom: 6px; display: block"><b>Payment details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Total: {payment_net_amount}</span><span style="margin-bottom: 6px; display: block">Merchant Processing Fee: {payment_merchant_fee}</span><span style="margin-bottom: 6px; display: block">Total Charge: {payment_total_amount}</span></p><p>
                <span style="margin-bottom: 6px; display: block"><b>Accounting details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Charged: {sw_fee}</span><span style="margin-bottom: 6px; display: block"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span>
                </p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "49653f72-0cd0-48a8-991e-ca3ffd6f6385",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "4cbaecbf-b91f-44c6-8da4-e572af34e50f",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "f5fbea0c-47f3-47f5-85c7-b2fc0621bd7b",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of <b>{event_short_name}</b> SW Ticket Fees.</p><p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Total: {payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Processing Fee: {payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total Charge: {payment_total_amount}</span></p><p><span style=\"margin-bottom: 6px; display: block\"><b>Accounting details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Charged: {sw_fee}</span><span style=\"margin-bottom: 6px; display: block\"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span></p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "f3835c30-9e25-4915-b2d3-b7b74155dee2",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "7389cc6b-df87-496f-9900-a5cf255c58c3",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "83adf697-2d15-4798-b6f5-fd008c42a203",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "bc1cc2b5-a82a-4cab-86f7-7f837d53e3c1",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "93ab5b57-6039-4194-81e0-2a7bb03f2539",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "f24f9cdb-7630-4b3c-bc0c-64a50ffb0cd1",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: inherit;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: inherit;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "a8431641-2002-4f5d-bf0b-22837d81c53f",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "136fc7fb-ad37-4e05-8d11-fdcf836c2196",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "3c171abe-dcef-43f9-94f2-3408bf765444",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "72px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.success');
    `);
};

exports.down = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text =
                'A successful charge has been made to the {payment_method} ending in {card_last_4}
                for the payment of {event_short_name} SW Ticket Fees.
                Payment details:
                SW Ticket Fee Total: {payment_net_amount}
                Merchant Processing Fee: {payment_merchant_fee}
                Total Charge: {payment_total_amount}
                Accounting details:
                SW Ticket Fee Charged: {sw_fee}
                # of Uncollected Ticket Fees: {uncollected_tickets_count}
                Copyright © SportWrench Inc. {season}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">    <div style="text-align: left">
                <p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of <b>{event_short_name}</b> SW Ticket Fees.</p><p><span style="margin-bottom: 6px; display: block"><b>Payment details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Total: {payment_net_amount}</span><span style="margin-bottom: 6px; display: block">Merchant Processing Fee: {payment_merchant_fee}</span><span style="margin-bottom: 6px; display: block">Total Charge: {payment_total_amount}</span></p><p>
                <span style="margin-bottom: 6px; display: block"><b>Accounting details:</b></span><span style="margin-bottom: 6px; display: block">SW Ticket Fee Charged: {sw_fee}</span><span style="margin-bottom: 6px; display: block"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span>
                </p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "49653f72-0cd0-48a8-991e-ca3ffd6f6385",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "4cbaecbf-b91f-44c6-8da4-e572af34e50f",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "f5fbea0c-47f3-47f5-85c7-b2fc0621bd7b",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p>A successful charge has been made to the {payment_method} ending in {card_last_4} for the payment of <b>{event_short_name}</b> SW Ticket Fees.</p><p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Total: {payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Processing Fee: {payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total Charge: {payment_total_amount}</span></p><p><span style=\"margin-bottom: 6px; display: block\"><b>Accounting details:</b></span><span style=\"margin-bottom: 6px; display: block\">SW Ticket Fee Charged: {sw_fee}</span><span style=\"margin-bottom: 6px; display: block\"># of Uncollected Ticket Fees: {uncollected_tickets_count}</span></p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "f3835c30-9e25-4915-b2d3-b7b74155dee2",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "7389cc6b-df87-496f-9900-a5cf255c58c3",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "83adf697-2d15-4798-b6f5-fd008c42a203",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "bc1cc2b5-a82a-4cab-86f7-7f837d53e3c1",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "93ab5b57-6039-4194-81e0-2a7bb03f2539",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "f24f9cdb-7630-4b3c-bc0c-64a50ffb0cd1",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: inherit;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: inherit;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center; word-break: break-word;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "a8431641-2002-4f5d-bf0b-22837d81c53f",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "136fc7fb-ad37-4e05-8d11-fdcf836c2196",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "3c171abe-dcef-43f9-94f2-3408bf765444",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "72px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'        
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.success');
    `);
};

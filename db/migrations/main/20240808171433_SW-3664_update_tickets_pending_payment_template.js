
exports.up = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text =
                '{event_name}
                Payment for "{event_name}" is pending.
                You will get notified when payment settles.
                Payment details:
                Payment method: {payment_method}
                Amount: {payment_net_amount}
                Merchant Fee: {payment_merchant_fee}
                Total: {payment_total_amount}
                Copyright © SportWrench Inc. {current_year}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">     <div style="text-align: left">
                        <p style="font-weight: bold; font-size: 18px;"><b>{event_name}</b></p>
                        <p>
                          Payment for "{event_name}" is pending.
                        </p>
                        <p>You will get notified when payment settles.</p>
                        <p>
                            <span style="margin-bottom: 6px; display: block"><b>Payment details:</b><span>
                            <span style="margin-bottom: 6px; display: block">Payment method: {payment_method}</span>
                            <span style="margin-bottom: 6px; display: block">Amount: {payment_net_amount}</span>
                            <span style="margin-bottom: 6px; display: block">Merchant Fee: {payment_merchant_fee}</span>
                            <span style="margin-bottom: 6px; display: block">Total: {payment_total_amount}</span>
                        </span></span></p>
                      </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "7959561d-4efc-448a-8404-8cb9a3317792",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "f35f1860-0b05-4bbb-859e-e6e52b056c7a",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "20f784c2-6e0c-44fc-a079-3280bfad5dae",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p style=\"font-weight: bold; font-size: 18px;\"><b>{event_name}</b></p><p>Payment for \"{event_name}\" is pending.</p><p>You will get notified when payment settles.</p><p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b><span><span style=\"margin-bottom: 6px; display: block\">Payment method: {payment_method}</span><span style=\"margin-bottom: 6px; display: block\">Amount: {payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Fee: {payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total: {payment_total_amount}</span></span></span></p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "ba4d83ad-002c-4468-974d-8305f7438e7d",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "74fd1847-7a92-423b-a015-add0bbda9a6e",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "9f7e3756-48f0-4c10-a173-53d82c4b0637",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "71924043-cfc1-4dc4-b04e-68e9ed31c755",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "51b6523a-b1e7-4119-b2f3-683ad4684f1a",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "1c658980-889a-4977-a182-32c4cefb739a",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:12px;line-height:14px;\" data-mce-style=\"font-size:12px;line-height:14px;\">Copyright © SportWrench Inc. {current_year}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "802818b3-33c0-4bfb-b038-cc3358c094fb",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "fdb14232-aa6a-4405-8701-785cdf27fdbe",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "2593f3c9-d922-45eb-921c-2a66e8f43189",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "72px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.pending');
    `);
};

exports.down = function(knex) {
    return knex.raw(String.raw`
        UPDATE email_template
        SET
            email_text =
                '{event_name}
                Payment for "{event_name}" is pending.
                You will get notified when payment settles.
                Payment details:
                Payment method: {payment_method}
                Amount: {payment_net_amount}
                Merchant Fee: {payment_merchant_fee}
                Total: {payment_total_amount}
                Copyright © SportWrench Inc. {season}. All rights reserved',
            email_html =
                '<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
                *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}.image_block img+div{display:none} @media (max-width:660px){.mobile_hide{display:none}.row-content{width:100%!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
                </style></head><body class="body" style="background-color:#f7f7f7;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#f7f7f7"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody>
                <tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table 
                class="html_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div style="font-family:Tahoma,Verdana,Segoe,sans-serif;text-align:center" align="center">     <div style="text-align: left">
                        <p style="font-weight: bold; font-size: 18px;"><b>{event_name}</b></p>
                        <p>
                          Payment for "{event_name}" is pending.
                        </p>
                        <p>You will get notified when payment settles.</p>
                        <p>
                            <span style="margin-bottom: 6px; display: block"><b>Payment details:</b><span>
                            <span style="margin-bottom: 6px; display: block">Payment method: {payment_method}</span>
                            <span style="margin-bottom: 6px; display: block">Amount: {payment_net_amount}</span>
                            <span style="margin-bottom: 6px; display: block">Merchant Fee: {payment_merchant_fee}</span>
                            <span style="margin-bottom: 6px; display: block">Total: {payment_total_amount}</span>
                        </span></span></p>
                      </div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block block-1" width="100%" border="0" cellpadding="5" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad"><div class="alignment" align="center"><table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px dotted #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
                cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block block-1" width="100%" border="0" cellpadding="0" 
                cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td class="pad" style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class style="font-size:12px;font-family:Tahoma,Verdana,Segoe,sans-serif;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2"><p style="margin:0;font-size:14px;text-align:center;mso-line-height-alt:16.8px">
                <span style="font-size:12px;">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px;margin:0 auto" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;padding-bottom:5px;padding-top:5px;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="image_block block-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
                style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="pad" style="width:100%;padding-right:0;padding-left:0"><div class="alignment" align="center" style="line-height:10px"><div style="max-width:70.4px"><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"><img src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" style="display:block;height:auto;border:0;width:100%" width="70.4" alt="Logo" 
                title="Logo" height="auto"></a></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>',
            bee_json =
                '{
                    "page": {
                        "body": {
                            "type": "mailup-bee-page-properties",
                            "content": {
                                "style": {
                                    "color": "#000000",
                                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                                },
                                "computedStyle": {
                                    "linkColor": "#0000FF",
                                    "messageWidth": "640px",
                                    "messageBackgroundColor": "#FFFFFF"
                                }
                            },
                            "webFonts": [],
                            "container": {
                                "style": {
                                    "background-color": "#F7F7F7"
                                }
                            }
                        },
                        "rows": [
                            {
                                "type": "one-column-empty",
                                "uuid": "7959561d-4efc-448a-8404-8cb9a3317792",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "f35f1860-0b05-4bbb-859e-e6e52b056c7a",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-html",
                                                "uuid": "20f784c2-6e0c-44fc-a079-3280bfad5dae",
                                                "locked": false,
                                                "descriptor": {
                                                    "html": {
                                                        "html": "<div style=\"text-align: left\"><p style=\"font-weight: bold; font-size: 18px;\"><b>{event_name}</b></p><p>Payment for \"{event_name}\" is pending.</p><p>You will get notified when payment settles.</p><p><span style=\"margin-bottom: 6px; display: block\"><b>Payment details:</b><span><span style=\"margin-bottom: 6px; display: block\">Payment method: {payment_method}</span><span style=\"margin-bottom: 6px; display: block\">Amount: {payment_net_amount}</span><span style=\"margin-bottom: 6px; display: block\">Merchant Fee: {payment_merchant_fee}</span><span style=\"margin-bottom: 6px; display: block\">Total: {payment_total_amount}</span></span></span></p></div>"
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnAmp": false,
                                                        "hideContentOnHtml": false,
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "ba4d83ad-002c-4468-974d-8305f7438e7d",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "74fd1847-7a92-423b-a015-add0bbda9a6e",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-divider",
                                                "uuid": "9f7e3756-48f0-4c10-a173-53d82c4b0637",
                                                "locked": false,
                                                "descriptor": {
                                                    "style": {
                                                        "padding-top": "5px",
                                                        "padding-left": "5px",
                                                        "padding-right": "5px",
                                                        "padding-bottom": "5px"
                                                    },
                                                    "divider": {
                                                        "style": {
                                                            "width": "100%",
                                                            "border-top": "1px dotted #BBBBBB"
                                                        }
                                                    },
                                                    "computedStyle": {
                                                        "align": "center"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "71924043-cfc1-4dc4-b04e-68e9ed31c755",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "51b6523a-b1e7-4119-b2f3-683ad4684f1a",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-text",
                                                "uuid": "1c658980-889a-4977-a182-32c4cefb739a",
                                                "locked": false,
                                                "descriptor": {
                                                    "text": {
                                                        "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size:12px;line-height:14px;font-family:inherit;\" data-mce-style=\"font-size:12px;line-height:14px;font-family:inherit;\"><p style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\" data-mce-style=\"font-size:14px;line-height:16px;text-align:center;word-break:break-word;\"><span style=\"font-size:12px;line-height:14px;\" data-mce-style=\"font-size:12px;line-height:14px;\">Copyright © SportWrench Inc. {season}. All rights reserved</span></p></div>",
                                                        "style": {
                                                            "color": "#555555",
                                                            "font-family": "inherit",
                                                            "line-height": "120%"
                                                        },
                                                        "computedStyle": {
                                                            "linkColor": "#0000FF"
                                                        }
                                                    },
                                                    "style": {
                                                        "padding-top": "0px",
                                                        "padding-left": "10px",
                                                        "padding-right": "10px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "hideContentOnMobile": false,
                                                        "hideContentOnDesktop": false
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            },
                            {
                                "type": "one-column-empty",
                                "uuid": "802818b3-33c0-4bfb-b038-cc3358c094fb",
                                "empty": false,
                                "locked": false,
                                "synced": false,
                                "columns": [
                                    {
                                        "uuid": "fdb14232-aa6a-4405-8701-785cdf27fdbe",
                                        "style": {
                                            "border-top": "0px solid transparent",
                                            "border-left": "0px solid transparent",
                                            "padding-top": "5px",
                                            "border-right": "0px solid transparent",
                                            "padding-left": "0px",
                                            "border-bottom": "0px solid transparent",
                                            "padding-right": "0px",
                                            "padding-bottom": "5px",
                                            "background-color": "transparent"
                                        },
                                        "modules": [
                                            {
                                                "type": "mailup-bee-newsletter-modules-image",
                                                "uuid": "2593f3c9-d922-45eb-921c-2a66e8f43189",
                                                "locked": false,
                                                "descriptor": {
                                                    "image": {
                                                        "alt": "Logo",
                                                        "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                                                        "href": "https://sportwrench.com",
                                                        "prefix": "",
                                                        "target": "_self",
                                                        "percWidth": 11
                                                    },
                                                    "style": {
                                                        "width": "100%",
                                                        "padding-top": "0px",
                                                        "padding-left": "0px",
                                                        "padding-right": "0px",
                                                        "padding-bottom": "0px"
                                                    },
                                                    "computedStyle": {
                                                        "class": "center",
                                                        "width": "72px"
                                                    }
                                                }
                                            }
                                        ],
                                        "grid-columns": 12
                                    }
                                ],
                                "content": {
                                    "style": {
                                        "color": "#000000",
                                        "width": "640px",
                                        "background-color": "#FFFFFF",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    },
                                    "computedStyle": {
                                        "verticalAlign": "top",
                                        "hideContentOnMobile": false,
                                        "rowColStackOnMobile": true,
                                        "hideContentOnDesktop": false,
                                        "rowReverseColStackOnMobile": false
                                    }
                                },
                                "container": {
                                    "style": {
                                        "background-color": "transparent",
                                        "background-image": "none",
                                        "background-repeat": "no-repeat",
                                        "background-position": "top left"
                                    }
                                }
                            }
                        ],
                        "title": "BF-basic-newsletter",
                        "template": {
                            "name": "template-base",
                            "type": "basic",
                            "version": "2.0.0"
                        },
                        "description": "BF-basic-newsletter"
                    },
                    "comments": {}
                }'   
        WHERE email_template_id =
              (SELECT default_email_template_id FROM email_template_type WHERE type = 'tickets_uncollected_fee_payments.pending');
    `);
};

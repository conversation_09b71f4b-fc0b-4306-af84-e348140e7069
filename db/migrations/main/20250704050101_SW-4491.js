exports.up = function up(knex) {
    return knex.schema.raw(`
        CREATE TABLE IF NOT EXISTS justifi_sub_account
        (
            justifi_sub_account_id  INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
            created                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            modified                TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
            id_at_justifi           TEXT                                NOT NULL UNIQUE,
            email                   TEXT                                NOT NULL,
            name                    TEXT                                NOT NULL,
            is_test                 BOOLEAN   DEFAULT FALSE             NOT NULL,
            hidden                  BOOLEAN   DEFAULT FALSE             NOT NULL,
            event_owner_id          INT                                 NOT NULL,
            CONSTRAINT justifi_sub_account_id_at_justifi_uq
              UNIQUE (id_at_justifi)
        );
  
        CREATE TRIGGER "update_justifi_sub_account_modified"
            BEFORE UPDATE
            ON "public"."justifi_sub_account"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
    `);
};

exports.down = function down(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS justifi_sub_account;
    `);
};

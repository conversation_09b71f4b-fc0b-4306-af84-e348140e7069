
exports.up = function(knex) {
    return knex.raw(`
        UPDATE event
        SET team_members_validation = COALESCE(team_members_validation, '{}'::JSONB) || '{"one_team_per_staffer_required": true}'
        WHERE teams_use_clubs_module IS TRUE
          AND allow_teams_registration IS TRUE;
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE event
        SET team_members_validation = COALESCE(team_members_validation, '{}'::JSONB) - 'one_team_per_staffer_required'
        WHERE (COALESCE(team_members_validation, '{}'::JSONB) ->> 'one_team_per_staffer_required') IS NOT NULL;
    `)
};

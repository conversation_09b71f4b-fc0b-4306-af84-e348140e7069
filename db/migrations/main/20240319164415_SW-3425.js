
exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TYPE "public"."official_scan_history_types" AS ENUM('scan', 'reentry');

        CREATE TABLE IF NOT EXISTS "public"."official_scan_history" (
            "official_scan_history_id" INT GENERATED ALWAYS AS IDENTITY,
            "created"                  TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"                 TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "event_id"                 INTEGER NOT NULL,
            "barcode"                  TEXT NOT NULL,
            "action_type"              official_scan_history_types,
            PRIMARY KEY("official_scan_history_id")
        );
        
        CREATE INDEX IF NOT EXISTS "official_scan_history_event_id_barcode_index" 
            ON "public"."official_scan_history" ("event_id", "barcode");
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."official_scan_history";
        DROP TYPE IF EXISTS "public"."official_scan_history_types";
    `);
};

exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "recognition_verification" -----------------------------------------------------------------------
        CREATE TABLE "public"."recognition_verification"
            (
                "recognition_verification_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                "created"                 TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
                "modified"                TIMESTAMP WITH TIME ZONE DEFAULT NULL,
                "user_id"                 INTEGER NOT NULL,
                "last_action"             TEXT DEFAULT NULL,
                "session_id"              TEXT UNIQUE NOT NULL,
                "is_verified"             BOOLEAN DEFAULT FALSE
            );
        ----------------------------------------------------------------------------------------------------------------
           
        -- Add indexes to "recognition_verification" --------------------------------------------------------------------------
            CREATE INDEX recognition_verification_user_id_index ON public."recognition_verification" USING btree (user_id);
        -- -------------------------------------------------------------------------------------------------------------
        

        --- Add modified trigger to recognition_verification table ---------------------------------------------------------
        CREATE TRIGGER "update_recognition_verification_modified"
            BEFORE UPDATE
            ON "public"."recognition_verification"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        ----------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."recognition_verification";
    `)
};

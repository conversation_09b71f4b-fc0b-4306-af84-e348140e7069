
exports.up = function(knex) {
    return knex.schema.raw(`
        -- his is a fix to passing test 
        ALTER TABLE official_event_clothes_size
        DROP CONSTRAINT official_event_clothes_size_official_id_fkey;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
      -- restore foreign key
        ALTER TABLE official_event_clothes_size
        ADD CONSTRAINT official_event_clothes_size_official_id_fkey 
        FOREIGN KEY (official_id) REFERENCES official(official_id);
    `);
};

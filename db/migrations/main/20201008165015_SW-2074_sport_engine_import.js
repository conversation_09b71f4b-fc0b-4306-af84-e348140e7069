
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TYPE "sportengine_import_option" ---------------------------------------------------------------------
        CREATE TYPE "public"."sportengine_import_option" AS ENUM( 'default', 'insert' );
        COMMENT ON TYPE  "public"."sportengine_import_option" IS 'What actions SportEngine import should do.
        default - insert/update master&roster rows
        insert - only insert not existing rows';
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- CREATE TABLE "sportengine_queue" ----------------------------------------------------------------------------
        CREATE TABLE "public"."sportengine_queue"
        (
            "sportengine_queue_id" INT GENERATED ALWAYS AS IDENTITY,
            "created"              TIMESTAMP WITH TIME ZONE  DEFAULT NOW() NOT NULL,
            "modified"             TIMESTAMP WITH TIME ZONE  DEFAULT NULL,
            "master_club_id"       INTEGER                                 NOT NULL,
            "requested"            TIMESTAMP WITHOUT TIME ZONE,
            "response_body"        TEXT,
            "responded"            TIMESTAMP WITHOUT TIME ZONE,
            "option"               sportengine_import_option DEFAULT 'default'
        );
        
        COMMENT ON COLUMN "public"."sportengine_queue"."master_club_id" IS 'Master CLub ID';
        COMMENT ON COLUMN "public"."sportengine_queue"."requested" IS 'Datetime when SportsEngine API requested';
        COMMENT ON COLUMN "public"."sportengine_queue"."response_body" IS 'SportsEngine API response body';
        COMMENT ON COLUMN "public"."sportengine_queue"."requested" IS 'Datetime when SportsEngine API responded';
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- Add indexes to "sportengine_queue" --------------------------------------------------------------------------
        CREATE INDEX "sportengine_queue_master_club_id_index" ON "public"."sportengine_queue" ( "master_club_id" );
        CREATE INDEX "sportengine_queue_requested_index" ON "public"."sportengine_queue" ( "requested" );
        CREATE INDEX "sportengine_queue_responded_index" ON "public"."sportengine_queue" ( "responded" );
        CREATE INDEX "sportengine_queue_created_index" ON "public"."sportengine_queue" ( "created" );
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- Add modified trigger to sportengine_queue table -------------------------------------------------------------
        CREATE TRIGGER "update_sportengine_queue_modified" BEFORE UPDATE
        ON "public"."sportengine_queue" FOR EACH ROW EXECUTE PROCEDURE
        update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."sportengine_queue";
        DROP TYPE IF EXISTS "public"."sportengine_import_option";
    `)
};

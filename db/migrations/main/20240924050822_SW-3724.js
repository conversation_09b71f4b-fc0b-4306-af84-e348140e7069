exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE payment_session_team
        DROP CONSTRAINT payment_session_team_payment_session_id_fkey,
        ADD CONSTRAINT payment_session_team_payment_session_id_fkey
        FOREIGN KEY (payment_session_id) 
        REFERENCES payment_session(payment_session_id) 
        ON DELETE CASCADE;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE payment_session_team
        DROP CONSTRAINT payment_session_team_payment_session_id_fkey,
        ADD CONSTRAINT payment_session_team_payment_session_id_fkey
        FOREIGN KEY (payment_session_id) 
        REFERENCES payment_session(payment_session_id);
    `);
};


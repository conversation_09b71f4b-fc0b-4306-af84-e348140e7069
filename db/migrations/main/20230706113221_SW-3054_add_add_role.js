exports.up = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."event_official_rate" ADD COLUMN IF NOT EXISTS "official_additional_role_id" INT DEFAULT NULL;
        COMMENT ON COLUMN "public"."event_official_rate"."official_additional_role_id"
            IS 'Official additional role ID';
        ALTER TABLE "public"."event_official_rate" DROP CONSTRAINT IF EXISTS "event_official_rate_unique";
        ALTER TABLE "public"."event_official_rate" ADD CONSTRAINT "event_official_rate_unique" UNIQUE (event_id, match_type, rank, official_additional_role_id);
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."event_official_rate" DROP CONSTRAINT IF EXISTS "event_official_rate_unique";
        ALTER TABLE "public"."event_official_rate" ADD CONSTRAINT "event_official_rate_unique" UNIQUE (event_id, match_type, rank);
        ALTER TABLE "public"."event_official_rate" DROP COLUMN "official_additional_role_id";
    `);
};

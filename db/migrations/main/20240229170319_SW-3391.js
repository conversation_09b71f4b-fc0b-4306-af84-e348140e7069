exports.up = function(knex) {
    return knex.schema.raw(`
        UPDATE master_athlete SET jersey = NULL WHERE jersey = 0 AND season = 2024;
        UPDATE master_athlete SET aau_jersey = NULL WHERE aau_jersey = 0 AND season = 2024;

        UPDATE roster_athlete 
            SET jersey = NULL
        FROM roster_athlete ra
            LEFT JOIN event e ON e.event_id = ra.event_id
        WHERE ra.jersey = 0 AND (e.season = 2023 OR e.season = 2024);
        UPDATE roster_athlete 
            SET aau_jersey = NULL
        FROM roster_athlete ra
            LEFT JOIN event e ON e.event_id = ra.event_id
        WHERE ra.aau_jersey = 0 AND (e.season = 2023 OR e.season = 2024);
    `);
};

exports.down = function(knex) {
    return knex.raw(``);
};


exports.up = function(knex) {
    return knex.schema.raw(`
        DELETE FROM custom_form_submitted_field_value WHERE value IS NULL;
    
        WITH fields AS (SELECT custom_form_field_id,
                               cff.custom_form_event_id
                        FROM custom_form_field cff
                                 JOIN custom_form_event cfe ON cfe.custom_form_event_id = cff.custom_form_event_id
                                 JOIN custom_form_field_type cfft
                                      ON cfft.custom_form_field_type_id = cff.custom_form_field_type_id
                        WHERE cff.is_required IS NULL
                          AND cfft.type <> 'paragraph'),
             data_to_insert AS (SELECT cfsfv.custom_form_event_id,
                                       f.custom_form_field_id,
                                       submitter_type,
                                       submitter_id,
                                       null AS value
                                FROM custom_form_submitted_field_value AS cfsfv
                                         JOIN fields f ON f.custom_form_field_id NOT IN
                                                          (SELECT empty_values.custom_form_field_id
                                                           FROM custom_form_submitted_field_value empty_values
                                                                    JOIN fields f ON f.custom_form_field_id = empty_values.custom_form_field_id
                                                           WHERE empty_values.value is not null
                                                             AND empty_values.submitter_id = cfsfv.submitter_id
                                                             AND empty_values.custom_form_event_id = f.custom_form_event_id) AND
                                                          f.custom_form_event_id = cfsfv.custom_form_event_id
                                GROUP BY submitter_type, submitter_id, f.custom_form_field_id, cfsfv.custom_form_event_id)
        INSERT
        INTO custom_form_submitted_field_value (custom_form_event_id, custom_form_field_id, submitter_type, submitter_id, value)
        SELECT *
        FROM data_to_insert;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM custom_form_submitted_field_value WHERE value IS NULL;
    `);
};

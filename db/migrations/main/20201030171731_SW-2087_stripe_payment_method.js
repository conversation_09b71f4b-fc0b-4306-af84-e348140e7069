
exports.up = function(knex) {
    return knex.schema.raw(`
    -- stripe_payment_method_type enum ---------------------------------------------------------------------------------
    CREATE TYPE "public"."stripe_payment_method_type" AS ENUM (
        'alipay', 'au_becs_debit', 'bacs_debit', 'bancontact', 'card', 'eps', 'fpx', 'giropay',
        'ideal', 'oxxo', 'p24', 'sepa_debit', 'sofort'
        );
    COMMENT ON TYPE "public"."stripe_payment_method_type" IS 'The type of the PaymentMethod.
        An additional hash is included on the PaymentMethod with a name matching this value.
        It contains additional information specific to the PaymentMethod type
        (https://stripe.com/docs/api/payment_methods/object#payment_method_object-type)';
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Table -----------------------------------------------------------------------------------------------------------
    CREATE TABLE "public"."stripe_payment_method"
    (
        "stripe_payment_id"  TEXT UNIQUE                            NOT NULL PRIMARY KEY,
        "stripe_customer_id" TEXT                                   NOT NULL,
        "created"            TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        "modified"           TIMESTAMP WITH TIME ZONE DEFAULT NULL,
        "type"               stripe_payment_method_type             NOT NULL,
        "card_last_4"        VARCHAR(4),
        "card_brand"         TEXT,
        "card_exp_month"     INT,
        "card_exp_year"      INT,
        "payment_object"     JSONB                                  NOT NULL
    );
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Indexes ---------------------------------------------------------------------------------------------------------
    CREATE INDEX "stripe_payment_method_stripe_customer_id_index"
        ON "public"."stripe_payment_method" ("stripe_customer_id");
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Comments --------------------------------------------------------------------------------------------------------
    COMMENT ON COLUMN "public"."stripe_payment_method"."stripe_payment_id" IS 'Stripe API Payment Method ID';
    COMMENT ON COLUMN "public"."stripe_payment_method"."stripe_customer_id" IS 'Stripe API Customer ID';
    COMMENT ON COLUMN "public"."stripe_payment_method"."type" IS 'The type of the Payment Method';
    COMMENT ON COLUMN "public"."stripe_payment_method"."card_last_4" IS 'Last 4 card characters';
    COMMENT ON COLUMN "public"."stripe_payment_method"."card_brand" IS 'Card Brand (Visa, MasterCard, etc.)';
    COMMENT ON COLUMN "public"."stripe_payment_method"."card_exp_month" IS 'Card Expire Month (two digits - MM)';
    COMMENT ON COLUMN "public"."stripe_payment_method"."card_exp_year" IS 'Card Expire Year (4 digits - YYYY)';
    COMMENT ON COLUMN "public"."stripe_payment_method"."payment_object" IS 'Stripe API Payment Method Object';
    -- -----------------------------------------------------------------------------------------------------------------
    
    
    -- Modified trigger ------------------------------------------------------------------------------------------------
    CREATE TRIGGER "update_stripe_payment_method_modified"
        BEFORE UPDATE
        ON "public"."stripe_payment_method"
        FOR EACH ROW
    EXECUTE PROCEDURE
        update_modified_column();
    -- -----------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."stripe_payment_method";
        DROP TYPE IF EXISTS "public"."stripe_payment_method_type";
    `)
};

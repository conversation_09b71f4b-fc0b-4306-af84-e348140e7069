
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE purchase_ticket ALTER COLUMN has_covid_test SET DEFAULT NULL;

        ALTER TABLE purchase_ticket
            ALTER COLUMN has_covid_test 
            TYPE TIMESTAMP WITHOUT TIME ZONE USING CASE WHEN has_covid_test IS TRUE THEN NOW() ELSE NULL END;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE purchase_ticket
            ALTER COLUMN has_covid_test 
            TYPE BOOLEAN USING CASE WHEN has_covid_test IS NOT NULL THEN TRUE ELSE FALSE END;
            
        ALTER TABLE purchase_ticket ALTER COLUMN has_covid_test SET DEFAULT FALSE;
    `)
};

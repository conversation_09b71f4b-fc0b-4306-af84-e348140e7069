exports.up = function(knex) {
    return knex.schema.raw(`
         CREATE INDEX IF NOT EXISTS event_email_ticket_coupons_gin_jsonb_path_ops_index 
            ON event_email using GIN (ticket_coupons jsonb_path_ops);
         CREATE INDEX IF NOT EXISTS ticket_coupon_event_ticket_id_index ON ticket_coupon (event_ticket_id);
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP INDEX IF EXISTS event_email_ticket_coupons_gin_jsonb_path_ops_index;
        DROP INDEX IF EXISTS ticket_coupon_event_ticket_id_index;
    `)
};

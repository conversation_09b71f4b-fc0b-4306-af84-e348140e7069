exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event"
            ADD COLUMN IF NOT EXISTS "teams_justifi_account_id" INT NULL,
            ADD COLUMN IF NOT EXISTS "teams_payment_provider" TEXT NULL DEFAULT 'stripe',
            ADD COLUMN IF NOT EXISTS "teams_justifi_statement_descriptor" TEXT NULL;
    `);

};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event"
            DROP COLUMN IF EXISTS "teams_justifi_account_id",
            DROP COLUMN IF EXISTS "teams_payment_provider",
            DROP COLUMN IF EXISTS "teams_justifi_statement_descriptor";
    `);
};

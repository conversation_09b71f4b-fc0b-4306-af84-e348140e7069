
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_athlete"
            ALTER COLUMN "aau_jersey" DROP DEFAULT,
            ALTER COLUMN "aau_jersey" TYPE INTEGER USING "aau_jersey"::INTEGER,
            ALTER COLUMN "aau_jersey" SET DEFAULT NULL;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."master_athlete"
            ALTER COLUMN "aau_jersey" DROP DEFAULT,
            ALTER COLUMN "aau_jersey" TYPE VARCHAR(20),
            ALTER COLUMN "aau_jersey" SET DEFAULT NULL;
    `);
};


exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE payment_hub."event"
        DROP COLUMN order_uuid,
        DROP COLUMN payment_uuid,
        ADD COLUMN object VARCHAR;

        ALTER TABLE payment_hub."payment"
        DROP COLUMN order_uuid,
        DROP COLUMN payment_uuid,
        ADD COLUMN payment_id UUID,
        ADD COLUMN payment_intent_id UUID;

        -- Rename 'payment_hub_order_uuid' to 'payment_hub_payment_id' in 'purchase' table
        ALTER TABLE "public"."purchase" RENAME COLUMN "payment_hub_order_uuid" TO "payment_hub_payment_intent_id";

        -- Rename 'payment_hub_order_uuid' to 'payment_hub_payment_id' in 'purchase_history' table
        ALTER TABLE "public"."purchase_history" RENAME COLUMN "payment_hub_order_uuid" TO "payment_hub_payment_id";

    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE payment_hub."event"
        ADD COLUMN order_uuid UUID,
        ADD COLUMN payment_uuid UUID,
        DROP COLUMN object;

        ALTER TABLE payment_hub."payment"
        ADD COLUMN order_uuid UUID,
        ADD COLUMN payment_uuid UUID,
        DROP COLUMN payment_id,
        DROP COLUMN payment_intent_id;

        -- Rename 'payment_hub_payment_id' back to 'payment_hub_order_uuid' in 'purchase' table
        ALTER TABLE "public"."purchase" RENAME COLUMN "payment_hub_payment_id" TO "payment_hub_order_uuid";

        -- Rename 'payment_hub_payment_id' back to 'payment_hub_order_uuid' in 'purchase_history' table
        ALTER TABLE "public"."purchase_history" RENAME COLUMN "payment_hub_payment_id" TO "payment_hub_order_uuid";
    `);
};

exports.up = function(knex) {
    return knex.raw(`
        --------------------FIX WALLET FUNCTION--------------------

        CREATE OR REPLACE FUNCTION ticket_wallet_created_or_updated_notify() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        DECLARE
            data text;
        BEGIN
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.*, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            IF (
                NEW.shared_by_purchaser <> OLD.shared_by_purchaser 
                OR NEW.holder_user_id <> OLD.holder_user_id 
            ) THEN
                PERFORM pg_notify('table_update', data);
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        --------------------FIX WALLET FUNCTION--------------------

        CREATE OR REPLACE FUNCTION ticket_wallet_created_or_updated_notify() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        DECLARE
            data text;
        B<PERSON><PERSON>
            SELECT row_to_json(payload) into data FROM (
                SELECT NEW.*, TG_OP AS pg_notification_type, TG_TABLE_NAME AS pg_notification_table_name
            ) AS payload;
            IF (
                NEW.fast_line_allowed <> OLD.fast_line_allowed 
                OR NEW.holder_user_id <> OLD.holder_user_id 
            ) THEN
                PERFORM pg_notify('table_update', data);
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    `)
};



exports.up = function (knex) {
    return knex.schema.raw(`
        ALTER TABLE email_template DROP COLUMN IF EXISTS grapes_json;
        
        ALTER TABLE email_template ADD COLUMN IF NOT EXISTS unlayer_json JSONB DEFAULT NULL;
        COMMENT ON COLUMN email_template.unlayer_json IS 'Unlayer editor json';
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        ALTER TABLE email_template DROP COLUMN IF EXISTS unlayer_json;
        
        ALTER TABLE email_template ADD COLUMN IF NOT EXISTS grapes_json JSONB DEFAULT NULL;
        COMMENT ON COLUMN email_template.grapes_json IS 'Grapes editor json';
    `);
};

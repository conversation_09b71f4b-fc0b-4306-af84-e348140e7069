
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "ncsa_sport" -----------------------------------------------------------------------------------
        CREATE TABLE "public"."ncsa_sport"
        (
            "ncsa_sport_id" INT UNIQUE,
            "created"       TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"      TIMESTAMP WITH TIME ZONE DEFAULT NULL,
            "sw_sport_id"   INTEGER                                NOT NULL,
            "name"          TEXT                                   NOT NULL,
            "gender"        gender                                 NOT NULL
        );
        
        COMMENT ON COLUMN "public"."ncsa_sport"."ncsa_sport_id" IS 'NCSA Internal Sport ID';
        COMMENT ON COLUMN "public"."ncsa_sport"."sw_sport_id" IS 'SW Internal Sport ID (sport table)';
        COMMENT ON COLUMN "public"."ncsa_sport"."name" IS 'NCSA Sport Name';
        COMMENT ON COLUMN "public"."ncsa_sport"."gender" IS 'Sport Type Gender';
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add indexes to "ncsa_sport" ---------------------------------------------------------------------------------
        CREATE INDEX "ncsa_sport_sw_sport_id_gender_index" ON "public"."ncsa_sport" ("sw_sport_id", "gender");
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add modified trigger to ncsa_sport table --------------------------------------------------------------------
        CREATE TRIGGER "update_ncsa_sport_modified"
            BEFORE UPDATE
            ON "public"."ncsa_sport"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add values to ncsa_sport table ------------------------------------------------------------------------------
        INSERT INTO "public"."ncsa_sport"
            (ncsa_sport_id, sw_sport_id, name, gender)
        VALUES (17696, 2, 'Women''s Volleyball', 'female'::gender),
               (17639, 4, 'Women''s Basketball', 'female'::gender),
               (17695, 2, 'Men''s Volleyball', 'male'::gender),
               (17638, 4, 'Men''s Basketball', 'male'::gender)
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
          DROP TABLE IF EXISTS "public"."ncsa_sport";
    `)
};

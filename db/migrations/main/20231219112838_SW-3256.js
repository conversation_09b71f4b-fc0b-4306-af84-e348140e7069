exports.up = function(knex) {
    return knex.schema.raw(`
        -- Remove "id_at_gateway" columns ---------------------------------------------------------------------
        ALTER TABLE tilled.payment_intent DROP COLUMN IF EXISTS id_at_gateway;
        ALTER TABLE tilled.customer DROP COLUMN IF EXISTS id_at_gateway;
        ALTER TABLE tilled.account DROP COLUMN IF EXISTS id_at_gateway;
        ALTER TABLE tilled.event DROP COLUMN IF EXISTS id_at_gateway;

        -- Rename "tilled_percentage" to "tilled_tickets_percentage" in "event" table --------------------------
        ALTER TABLE public.event RENAME COLUMN tilled_percentage TO tilled_tickets_percentage;

        -- Rename "tilled_tickets_fee" to "tilled_tickets_fixed" in "event" table --------------------------
        ALTER TABLE public.event RENAME COLUMN tilled_tickets_fee TO tilled_tickets_fixed;

        -- Add "tilled_tickets_fee_payer" column to "event" table ----------------------------------------------
        ALTER TABLE public.event ADD COLUMN tilled_tickets_fee_payer payer_option DEFAULT 'seller'::payer_option;

        -- Add "application_fee" column to "tilled.payment_intent" table ----------------------------------------
        ALTER TABLE tilled.payment_intent ADD COLUMN application_fee NUMERIC(8, 2) NOT NULL;

        -- Add "payment_provider" column to "purchase" table ---------------------------------------------------
        ALTER TABLE public.purchase ADD COLUMN IF NOT EXISTS payment_provider TEXT;

        -- Add "tilled_percentage" column to "purchase" table ---------------------------------------------------
        ALTER TABLE public.purchase ADD COLUMN tilled_percentage NUMERIC(8, 2);

        -- Add "tilled_fee" column to "purchase" table ---------------------------------------------------
        ALTER TABLE public.purchase ADD COLUMN tilled_fee NUMERIC(8, 2);

        -- Add "tickets_payment_provider" column to "event" table ------------------------------------------------
        ALTER TABLE public.event ADD COLUMN tickets_payment_provider TEXT;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Add "id_at_gateway" columns back --------------------------------------------------------------
        ALTER TABLE tilled.payment_intent ADD COLUMN id_at_gateway TEXT;
        ALTER TABLE tilled.customer ADD COLUMN id_at_gateway TEXT;
        ALTER TABLE tilled.account ADD COLUMN id_at_gateway TEXT;
        ALTER TABLE tilled.event ADD COLUMN id_at_gateway TEXT;

        -- Rename "tilled_tickets_percentage" back to "tilled_percentage" in "event" table ---------------
        ALTER TABLE public.event RENAME COLUMN tilled_tickets_percentage TO tilled_percentage;

        -- Rename "tilled_tickets_fixed" to "tilled_tickets_fee" in "event" table ------------------------
        ALTER TABLE public.event RENAME COLUMN tilled_tickets_fixed TO tilled_tickets_fee;

        -- Remove "tilled_tickets_fee_payer" column from "event" table ------------------------------------
        ALTER TABLE public.event DROP COLUMN IF EXISTS tilled_tickets_fee_payer;

        -- Remove "application_fee" column from "tilled.payment_intent" table -----------------------------
        ALTER TABLE tilled.payment_intent DROP COLUMN IF EXISTS application_fee;

        -- Remove "payment_provider" column from "purchase" table -----------------------------------------
        ALTER TABLE public.purchase DROP COLUMN IF EXISTS payment_provider;

        -- Remove "tilled_fee" column from "purchase" table -----------------------------------------
        ALTER TABLE public.purchase DROP COLUMN IF EXISTS tilled_fee;

        -- Remove "tilled_percentage" column from "purchase" table -----------------------------------------
        ALTER TABLE public.purchase DROP COLUMN IF EXISTS tilled_percentage;
        
        -- Remove "tickets_payment_provider" column from "event" table --------------------------------------------
        ALTER TABLE public.event DROP COLUMN IF EXISTS tickets_payment_provider;
    `);
};

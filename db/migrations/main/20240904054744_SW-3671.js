exports.up = function (knex) {
    return knex.schema.raw(`
        -- Create the email_editor_image table
        CREATE TABLE "public"."email_editor_image" (
            "id"  INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            "link" TEXT NOT NULL,
            "event_owner_id" INTEGER NOT NULL, 
            "created" TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            "updated" TIMESTAMP WITH TIME ZONE DEFAULT NULL
        );
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        -- Drop the email_editor_image table if it exists
        DROP TABLE IF EXISTS "public"."email_editor_image";
    `);
};

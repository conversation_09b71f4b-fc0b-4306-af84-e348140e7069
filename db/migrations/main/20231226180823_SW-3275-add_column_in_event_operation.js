
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE event_operation ADD COLUMN IF NOT EXISTS parent_event_operation text DEFAULT NULL;
        ALTER TABLE event_operation ADD CONSTRAINT event_operation_parent_event_operation_fkey
            FOREIGN KEY (parent_event_operation)
            REFERENCES event_operation(event_operation);
        INSERT INTO event_operation (event_operation, parent_event_operation, title) 
            VALUES ('ticket_settings_tab', 'tickets_tab', 'Settings'),
                   ('ticket_payments_list_tab', 'tickets_tab', 'Payment List'),
                   ('ticket_map_tab', 'tickets_tab', 'Map'),
                   ('ticket_statistics_tab', 'tickets_tab', 'Statistics'),
                   ('ticket_discounts_tab', 'tickets_tab', 'Discounts');
        INSERT INTO event_user_permission (event_operation_id, event_id, user_id, granter_user_id)
        SELECT eoi.event_operation_id, eup.event_id, eup.user_id, eup.granter_user_id
        FROM event_user_permission eup
                 CROSS JOIN (VALUES ('ticket_settings_tab'), ('ticket_payments_list_tab'), ('ticket_map_tab'), ('ticket_statistics_tab'), ('ticket_discounts_tab')) eoi(event_operation_id)
        WHERE eup.event_operation_id = 'tickets_tab' AND eup.deleted IS NULL;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE event_operation DROP CONSTRAINT event_operation_parent_event_operation_fkey;
        ALTER TABLE event_operation DROP COLUMN IF EXISTS parent_event_operation;
        DELETE FROM event_operation WHERE event_operation IN ('ticket_settings_tab', 'ticket_payments_list_tab', 'ticket_map_tab', 'ticket_statistics_tab', 'ticket_discounts_tab');
        DELETE FROM event_user_permission WHERE event_operation_id IN ('ticket_settings_tab', 'ticket_payments_list_tab', 'ticket_map_tab', 'ticket_statistics_tab', 'ticket_discounts_tab');
    `);
};

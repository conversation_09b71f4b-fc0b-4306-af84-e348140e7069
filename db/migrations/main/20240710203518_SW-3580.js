/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE custom_payment_dispute_fee_failed_ach_fee
            DROP COLUMN IF EXISTS "status";
            
        ALTER TABLE custom_payment_dispute_fee_failed_ach_fee
            DROP COLUMN IF EXISTS "date_paid";    
        
        DROP TYPE IF EXISTS custom_payment_dispute_fee_failed_ach_fee_status;
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        CREATE TYPE "public"."custom_payment_dispute_fee_failed_ach_fee_status" AS ENUM ('paid', 'pending', 'cancelled');

        ALTER TABLE custom_payment_dispute_fee_failed_ach_fee
            ADD COLUMN IF NOT EXISTS "status" custom_payment_dispute_fee_failed_ach_fee_status NOT NULL DEFAULT 'pending'::custom_payment_dispute_fee_failed_ach_fee_status;
    
        ALTER TABLE custom_payment_dispute_fee_failed_ach_fee
            ADD COLUMN IF NOT EXISTS "date_paid" TIMESTAMP WITHOUT TIME ZONE;
    `)
};


exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE TYPE "public"."member_types" AS ENUM('athlete', 'staff');
        COMMENT ON TYPE "public"."member_types" IS 'Member types.';
    
        CREATE TABLE IF NOT EXISTS "public"."unresolved_memberships" (
            "membership_id"                 INT GENERATED ALWAYS AS IDENTITY,
            "created"                       TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"                      TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "region"                        TEXT NOT NULL,
            "club_code"                     TEXT NOT NULL,
            "organization_code"             TEXT NOT NULL,
            "membership_definition_id"      TEXT NOT NULL,
            "membership_name"               TEXT,
            "membership_status"             TEXT,
            "membership_start_date"         TIMESTAMP DEFAULT NULL,
            "membership_end_date"           TIMESTAMP DEFAULT NULL,
            "correct_membership_type"       member_types,
            "first_name"                    TEXT,
            "last_name"                     TEXT,
            "birthdate"                     TIMESTAMP DEFAULT NULL,
            "age_group"                     TEXT,
            "error_message"                 TEXT,
            "approved"                      BOOLEAN DEFAULT FALSE NOT NULL,
            "membership_data"               JSONB DEFAULT '{}'::JSONB,
            PRIMARY KEY("membership_id")
        );
        
        CREATE UNIQUE INDEX IF NOT EXISTS "unresolved_memberships_club_code_region_organization_code_index" 
            ON "public"."unresolved_memberships" ("region", "club_code", "organization_code");

        COMMENT ON COLUMN "public"."unresolved_memberships"."correct_membership_type" IS 'A field to manually select a table for membership';
        COMMENT ON COLUMN "public"."unresolved_memberships"."error_message" IS 'The reason for inserting the membership into the table';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."unresolved_memberships";
        DROP TYPE IF EXISTS "public"."member_types";
    `);
};

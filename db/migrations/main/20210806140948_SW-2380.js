

exports.up = function(knex) {
    return knex.raw(`
        -- Update teams season ----------------------------------------------------------------
        WITH club_teams_to_update_season AS (
            SELECT DISTINCT mt.master_team_id
            FROM master_team mt
                     JOIN master_club mc ON mt.master_club_id = mc.master_club_id
            WHERE NOT EXISTS(
                    SELECT 1
                    FROM master_club_sanctioning mcs
                    WHERE mcs.master_club_id = mc.master_club_id
                      AND mcs.sport_sanctioning_id = 3
                )
              AND mt.season = 2021
            UNION ALL
            SELECT DISTINCT mt.master_team_id
            FROM master_team mt
                     JOIN roster_team rt ON rt.master_team_id = mt.master_team_id
                     JOIN "event" e ON rt.event_id = e.event_id
            WHERE e.date_start > '2021-09-01'
              AND rt.deleted IS NULL
        )
        UPDATE master_team mt
        SET season = 2022
        WHERE mt.master_team_id IN (SELECT cttus.master_team_id FROM club_teams_to_update_season cttus);


        -- Update upcoming events season -----------------------------------------------------
        UPDATE event e SET season = 2022 WHERE e.date_start > '2020-09-01' AND e.season = 2021;


        -- Update min event sequence ID value ------------------------------------------------
        SELECT SETVAL('event_event_id_seq'::regclass, 22000);


        -- Sset profile_completed_at = NULL for USAV clubs -----------------------------------
        UPDATE master_club mc
        SET profile_completed_at = NULL
        WHERE NULLIF(director_usav_code, '') IS NOT NULL
          AND EXISTS(SELECT 1
                     FROM master_club_sanctioning mcs
                     WHERE mcs.master_club_id = mc.master_club_id
                       AND mcs.sport_sanctioning_id = 3);
        
        
        -- Set profile_completed_at = NULL for USAV officials --------------------------------
        UPDATE official SET profile_completed_at = NULL WHERE NULLIF(usav_num, '') IS NOT NULL;
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        UPDATE event e SET season = 2021 WHERE e.date_start > '2021-09-01' AND e.season = 2022;
        UPDATE master_team mt SET season = 2021 WHERE mt.season = 2022;
        SELECT SETVAL('event_event_id_seq'::regclass, (SELECT MAX(event_id) + 1 FROM event));
    `)
};

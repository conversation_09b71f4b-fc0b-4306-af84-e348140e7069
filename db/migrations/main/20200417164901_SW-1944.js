
exports.up = function(knex) {
  return knex.schema.raw(
      `INSERT INTO public.email_template_group ("group", title, description, variables, usage_restrictions)
        VALUES ('exhibitors', 'Exhibitors',
                '<i>Email templates that can be sent to Exhibitors</i>', '[
            {
              "field": "event_name",
              "title": "Event Name",
              "pattern": "{event_name}"
            },
            {
              "field": "event_city",
              "title": "Event City",
              "pattern": "{event_city}"
            },
            {
              "field": "company_name",
              "title": "Company Name",
              "pattern": "{company_name}"
            },
            {
              "field": "event_email",
              "title": "Event Email",
              "pattern": "{event_email}"
            },
            {
              "field": "event_month",
              "title": "Event Month",
              "pattern": "{event_month}"
            }
          ]', '{
            "roles": [
              "god_mode"
            ]
          }');
        
        INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id,
                                           recipient_type, sender_type, title, event_id, bee_json, img_name,
                                           email_template_type, is_valid, email_template_group, published, deleted)
        VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        
        <head>
        <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width">
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!--<![endif]-->
        <title></title>
        <!--[if !mso]><!-->
        <!--<![endif]-->
        <style type="text/css">
        body {
        margin: 0;
        padding: 0;
        }
        
        table,
        td,
        tr {
        vertical-align: top;
        border-collapse: collapse;
        }
        
        * {
        line-height: inherit;
        }
        
        a[x-apple-data-detectors=true] {
        color: inherit !important;
        text-decoration: none !important;
        }
        </style>
        <style type="text/css" id="media-query">
        @media (max-width: 660px) {
        
        .block-grid,
        .col {
        min-width: 320px !important;
        max-width: 100% !important;
        display: block !important;
        }
        
        .block-grid {
        width: 100% !important;
        }
        
        .col {
        width: 100% !important;
        }
        
        .col>div {
        margin: 0 auto;
        }
        
        img.fullwidth,
        img.fullwidthOnMobile {
        max-width: 100% !important;
        }
        
        .no-stack .col {
        min-width: 0 !important;
        display: table-cell !important;
        }
        
        .no-stack.two-up .col {
        width: 50% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num8 {
        width: 66% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num3 {
        width: 25% !important;
        }
        
        .no-stack .col.num6 {
        width: 50% !important;
        }
        
        .no-stack .col.num9 {
        width: 75% !important;
        }
        
        .video-block {
        max-width: none !important;
        }
        
        .mobile_hide {
        min-height: 0px;
        max-height: 0px;
        max-width: 0px;
        display: none;
        overflow: hidden;
        font-size: 0px;
        }
        
        .desktop_hide {
        display: block !important;
        max-height: none !important;
        }
        }
        </style>
        </head>
        
        <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #F7F7F7;">
        <!--[if IE]><div class="ie-browser"><![endif]-->
        <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #F7F7F7; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#F7F7F7" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top;" valign="top">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#F7F7F7"><![endif]-->
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.8; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 22px;">
        <p style="font-size: 12px; line-height: 1.8; text-align: center; mso-line-height-alt: 22px; margin: 0;"><strong><span style="font-size: 18px;">Declined Application</span></strong></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px solid #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 1.2; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: left; mso-line-height-alt: 14px; margin: 0;">Thank you for your registering as a sponsor/exhibitor at the {event_name}. Unfortunately, at this time we are unable to accept your company application for {event_name}.<br><br>For more details email to: {event_email}<br> <br>Thanks,<br>{event_name} Staff</p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px dotted #BBBBBB; height: 0px; width: 100%;" align="center" role="presentation" height="0" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" height="0" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:0px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.2; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: center; mso-line-height-alt: 14px; margin: 0;"><span style="font-size: 12px;">Copyright © SportWrench Inc. All rights reserved</span></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <div class="img-container center  autowidth " align="center" style="padding-right: 0px;padding-left: 0px;">
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]--><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"> <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: none; width: 100%; max-width: 84px; display: block;" width="84"></a>
        <!--[if mso]></td></tr></table><![endif]-->
        </div>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (IE)]></div><![endif]-->
        </body>
        
        </html>', '{event_name} Exhibitor Registration Declined',
                'Declined Application Thank you for your registering as a sponsor/exhibitor at the {event_name}. Unfortunately, at this time we are unable to accept your company application for {event_name}. For more details email to: {event_email} Thanks, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ',
                0, 'exhibitor', 'event', 'Exhibitor Registration Declined', null, '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#0000FF",
                    "messageWidth": "640px",
                    "messageBackgroundColor": "#FFFFFF"
                  }
                },
                "webFonts": [],
                "container": {
                  "style": {
                    "background-color": "#F7F7F7"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 21px; font-family: Tahoma, Verdana, Segoe, sans-serif;\">\n<p style=\"font-size: 12px; line-height: 21px; text-align: center;\"><strong><span style=\"font-size: 18px; line-height: 32px;\">Declined Application</span></strong></p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px solid #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: left;\">Thank you for your registering as a sponsor/exhibitor at the {event_name}. Unfortunately, at this time we are unable to accept your company application for {event_name}.<br /><br />For more details email to: {event_email}<br /> <br />Thanks,<br />{event_name} Staff</p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "Tahoma, Verdana, Segoe, sans-serif",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "height": "0px",
                                "border-top": "1px dotted #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Logo",
                              "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                              "href": "https://sportwrench.com"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": 84
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "BF-basic-newsletter",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": "BF-basic-newsletter"
            }
          }', null, 'exhibitor.declined', true, 'exhibitors', true, null);
        INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id,
                                           recipient_type, sender_type, title, event_id, bee_json, img_name,
                                           email_template_type, is_valid, email_template_group, published, deleted)
        VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        
        <head>
        <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width">
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!--<![endif]-->
        <title></title>
        <!--[if !mso]><!-->
        <!--<![endif]-->
        <style type="text/css">
        body {
        margin: 0;
        padding: 0;
        }
        
        table,
        td,
        tr {
        vertical-align: top;
        border-collapse: collapse;
        }
        
        * {
        line-height: inherit;
        }
        
        a[x-apple-data-detectors=true] {
        color: inherit !important;
        text-decoration: none !important;
        }
        </style>
        <style type="text/css" id="media-query">
        @media (max-width: 660px) {
        
        .block-grid,
        .col {
        min-width: 320px !important;
        max-width: 100% !important;
        display: block !important;
        }
        
        .block-grid {
        width: 100% !important;
        }
        
        .col {
        width: 100% !important;
        }
        
        .col>div {
        margin: 0 auto;
        }
        
        img.fullwidth,
        img.fullwidthOnMobile {
        max-width: 100% !important;
        }
        
        .no-stack .col {
        min-width: 0 !important;
        display: table-cell !important;
        }
        
        .no-stack.two-up .col {
        width: 50% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num8 {
        width: 66% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num3 {
        width: 25% !important;
        }
        
        .no-stack .col.num6 {
        width: 50% !important;
        }
        
        .no-stack .col.num9 {
        width: 75% !important;
        }
        
        .video-block {
        max-width: none !important;
        }
        
        .mobile_hide {
        min-height: 0px;
        max-height: 0px;
        max-width: 0px;
        display: none;
        overflow: hidden;
        font-size: 0px;
        }
        
        .desktop_hide {
        display: block !important;
        max-height: none !important;
        }
        }
        </style>
        </head>
        
        <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #F7F7F7;">
        <!--[if IE]><div class="ie-browser"><![endif]-->
        <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #F7F7F7; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#F7F7F7" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top;" valign="top">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#F7F7F7"><![endif]-->
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.8; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 22px;">
        <p style="font-size: 12px; line-height: 1.8; text-align: center; mso-line-height-alt: 22px; margin: 0;"><strong><span style="font-size: 18px;">Approved Application</span></strong></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px solid #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 1.2; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: left; mso-line-height-alt: 14px; margin: 0;">We have received your sponsor/exhibitor application for the {event_name}. Your application status has been upgraded to approved. This email will serve as confirmation of your company attendance at the event as a sponsor/exhibitor.<br> <br>For more details email to: {event_email}<br> <br>We look forward to seeing you in {event_city} in {event_month}!<br> <br>Thanks,<br>{event_name} Staff</p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px dotted #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:0px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.2; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: center; mso-line-height-alt: 14px; margin: 0;"><span style="font-size: 12px;">Copyright © SportWrench Inc. All rights reserved</span></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <div class="img-container center  autowidth " align="center" style="padding-right: 0px;padding-left: 0px;">
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]--><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"> <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: none; width: 100%; max-width: 84px; display: block;" width="84"></a>
        <!--[if mso]></td></tr></table><![endif]-->
        </div>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (IE)]></div><![endif]-->
        </body>
        
        </html>', '{event_name} Exhibitor Registration Approved',
                'Approved Application We have received your sponsor/exhibitor application for the {event_name}. Your application status has been upgraded to approved. This email will serve as confirmation of your company attendance at the event as a sponsor/exhibitor. For more details email to: {event_email} We look forward to seeing you in {event_city} in {event_month}! Thanks, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ',
                0, 'exhibitor', 'event', 'Exhibitor Registration Approved', null, '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#0000FF",
                    "messageWidth": "640px",
                    "messageBackgroundColor": "#FFFFFF"
                  }
                },
                "webFonts": [],
                "container": {
                  "style": {
                    "background-color": "#F7F7F7"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 21px; font-family: Tahoma, Verdana, Segoe, sans-serif;\">\n<p style=\"font-size: 12px; line-height: 21px; text-align: center;\"><strong><span style=\"font-size: 18px; line-height: 32px;\">Approved Application</span></strong></p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px solid #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: left;\">We have received your sponsor/exhibitor application for the {event_name}. Your application status has been upgraded to approved. This email will serve as confirmation of your company attendance at the event as a sponsor/exhibitor.<br /> <br />For more details email to: {event_email}<br /> <br />We look forward to seeing you in {event_city} in {event_month}!<br /> <br />Thanks,<br />{event_name} Staff</p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "Tahoma, Verdana, Segoe, sans-serif",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Logo",
                              "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                              "href": "https://sportwrench.com"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": 84
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "hideContentOnMobile": false,
                      "rowColStackOnMobile": true,
                      "hideContentOnDesktop": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "BF-basic-newsletter",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": "BF-basic-newsletter"
            }
          }', null, 'exhibitor.approved', true, 'exhibitors', true, null);
        INSERT INTO public.email_template (email_html, email_subject, email_text, event_owner_id,
                                           recipient_type, sender_type, title, event_id, bee_json, img_name,
                                           email_template_type, is_valid, email_template_group, published, deleted)
        VALUES ('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        
        <head>
        <!--[if gte mso 9]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width">
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!--<![endif]-->
        <title></title>
        <!--[if !mso]><!-->
        <!--<![endif]-->
        <style type="text/css">
        body {
        margin: 0;
        padding: 0;
        }
        
        table,
        td,
        tr {
        vertical-align: top;
        border-collapse: collapse;
        }
        
        * {
        line-height: inherit;
        }
        
        a[x-apple-data-detectors=true] {
        color: inherit !important;
        text-decoration: none !important;
        }
        </style>
        <style type="text/css" id="media-query">
        @media (max-width: 660px) {
        
        .block-grid,
        .col {
        min-width: 320px !important;
        max-width: 100% !important;
        display: block !important;
        }
        
        .block-grid {
        width: 100% !important;
        }
        
        .col {
        width: 100% !important;
        }
        
        .col>div {
        margin: 0 auto;
        }
        
        img.fullwidth,
        img.fullwidthOnMobile {
        max-width: 100% !important;
        }
        
        .no-stack .col {
        min-width: 0 !important;
        display: table-cell !important;
        }
        
        .no-stack.two-up .col {
        width: 50% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num8 {
        width: 66% !important;
        }
        
        .no-stack .col.num4 {
        width: 33% !important;
        }
        
        .no-stack .col.num3 {
        width: 25% !important;
        }
        
        .no-stack .col.num6 {
        width: 50% !important;
        }
        
        .no-stack .col.num9 {
        width: 75% !important;
        }
        
        .video-block {
        max-width: none !important;
        }
        
        .mobile_hide {
        min-height: 0px;
        max-height: 0px;
        max-width: 0px;
        display: none;
        overflow: hidden;
        font-size: 0px;
        }
        
        .desktop_hide {
        display: block !important;
        max-height: none !important;
        }
        }
        </style>
        </head>
        
        <body class="clean-body" style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%; background-color: #F7F7F7;">
        <!--[if IE]><div class="ie-browser"><![endif]-->
        <table class="nl-container" style="table-layout: fixed; vertical-align: top; min-width: 320px; Margin: 0 auto; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #F7F7F7; width: 100%;" cellpadding="0" cellspacing="0" role="presentation" width="100%" bgcolor="#F7F7F7" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top;" valign="top">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#F7F7F7"><![endif]-->
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.8;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.8; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 22px;">
        <p style="font-size: 12px; line-height: 1.8; text-align: center; mso-line-height-alt: 22px; margin: 0;"><strong><span style="font-size: 18px;">Thank you for applying to {event_name}!</span></strong></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px solid #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 10px; padding-bottom: 10px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:10px;padding-right:10px;padding-bottom:10px;padding-left:10px;">
        <div style="font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 1.2; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: left; mso-line-height-alt: 14px; margin: 0;">Recently, you registered your company {company_name} as a sponsor/exhibitor for the event {event_name}. Your application is currently pending review. You will receive an update on the status of your application from one of the event representatives soon.<br>&nbsp;<br><br>Thank you for your interest in officiating at the {event_name}.<br><br>&nbsp;<br>Thanks,<br><br>{event_name} Staff</p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <table class="divider" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td class="divider_inner" style="word-break: break-word; vertical-align: top; min-width: 100%; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;" valign="top">
        <table class="divider_content" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-top: 1px dotted #BBBBBB; width: 100%;" align="center" role="presentation" valign="top">
        <tbody>
        <tr style="vertical-align: top;" valign="top">
        <td style="word-break: break-word; vertical-align: top; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;" valign="top"><span></span></td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 10px; padding-left: 10px; padding-top: 0px; padding-bottom: 0px; font-family: Tahoma, Verdana, sans-serif"><![endif]-->
        <div style="color:#555555;font-family:Tahoma, Verdana, Segoe, sans-serif;line-height:1.2;padding-top:0px;padding-right:10px;padding-bottom:0px;padding-left:10px;">
        <div style="font-size: 12px; line-height: 1.2; font-family: Tahoma, Verdana, Segoe, sans-serif; color: #555555; mso-line-height-alt: 14px;">
        <p style="font-size: 12px; line-height: 1.2; text-align: center; mso-line-height-alt: 14px; margin: 0;"><span style="font-size: 12px;">Copyright © SportWrench Inc. All rights reserved</span></p>
        </div>
        </div>
        <!--[if mso]></td></tr></table><![endif]-->
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <div style="background-color:transparent;">
        <div class="block-grid " style="Margin: 0 auto; min-width: 320px; max-width: 640px; overflow-wrap: break-word; word-wrap: break-word; word-break: break-word; background-color: #FFFFFF;">
        <div style="border-collapse: collapse;display: table;width: 100%;background-color:#FFFFFF;">
        <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:640px"><tr class="layout-full-width" style="background-color:#FFFFFF"><![endif]-->
        <!--[if (mso)|(IE)]><td align="center" width="640" style="background-color:#FFFFFF;width:640px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
        <div class="col num12" style="min-width: 320px; max-width: 640px; display: table-cell; vertical-align: top; width: 640px;">
        <div style="width:100% !important;">
        <!--[if (!mso)&(!IE)]><!-->
        <div style="border-top:0px solid transparent; border-left:0px solid transparent; border-bottom:0px solid transparent; border-right:0px solid transparent; padding-top:5px; padding-bottom:5px; padding-right: 0px; padding-left: 0px;">
        <!--<![endif]-->
        <div class="img-container center  autowidth " align="center" style="padding-right: 0px;padding-left: 0px;">
        <!--[if mso]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr style="line-height:0px"><td style="padding-right: 0px;padding-left: 0px;" align="center"><![endif]--><a href="https://sportwrench.com" target="_blank" style="outline:none" tabindex="-1"> <img class="center  autowidth " align="center" border="0" src="https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png" alt="Logo" title="Logo" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: none; width: 100%; max-width: 84px; display: block;" width="84"></a>
        <!--[if mso]></td></tr></table><![endif]-->
        </div>
        <!--[if (!mso)&(!IE)]><!-->
        </div>
        <!--<![endif]-->
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
        </div>
        </div>
        </div>
        <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
        </td>
        </tr>
        </tbody>
        </table>
        <!--[if (IE)]></div><![endif]-->
        </body>
        
        </html>', '{event_name} Exhibitor Registration',
                'Thank you for applying to {event_name}! Recently, you registered your company {company_name} as a sponsor/exhibitor for the event {event_name}. Your application is currently pending review. You will receive an update on the status of your application from one of the event representatives soon.   Thank you for your interest in officiating at the {event_name}.   Thanks, {event_name} Staff Copyright © SportWrench Inc. All rights reserved https://sportwrench.com ',
                0, 'exhibitor', 'event', 'Exhibitor Registration', null, '{
            "page": {
              "body": {
                "type": "mailup-bee-page-properties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "linkColor": "#0000FF",
                    "messageWidth": "640px",
                    "messageBackgroundColor": "#FFFFFF"
                  }
                },
                "webFonts": [],
                "container": {
                  "style": {
                    "background-color": "#F7F7F7"
                  }
                }
              },
              "rows": [
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 21px; font-family: Tahoma, Verdana, Segoe, sans-serif;\">\n<p style=\"font-size: 12px; line-height: 21px; text-align: center;\"><strong><span style=\"font-size: 18px; line-height: 32px;\">Thank you for applying to {event_name}!</span></strong></p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        },
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px solid #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 12px; line-height: 14px;\">\n<p style=\"font-size: 12px; line-height: 14px; text-align: left;\">Recently, you registered your company {company_name} as a sponsor/exhibitor for the event {event_name}. Your application is currently pending review. You will receive an update on the status of your application from one of the event representatives soon.<br />&nbsp;<br /><br />Thank you for your interest in officiating at the {event_name}.<br /><br />&nbsp;<br />Thanks,<br /><br />{event_name} Staff</p>\n</div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "Tahoma, Verdana, Segoe, sans-serif",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "locked": false,
                          "descriptor": {
                            "style": {
                              "padding-top": "5px",
                              "padding-left": "5px",
                              "padding-right": "5px",
                              "padding-bottom": "5px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px dotted #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center"
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "locked": false,
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 12px; line-height: 14px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"font-size: 14px; line-height: 16px; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; text-align: center;\"><span style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\">Copyright © ﻿SportWrench Inc. All rights reserved</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "locked": false,
                  "columns": [
                    {
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-image",
                          "locked": false,
                          "descriptor": {
                            "image": {
                              "alt": "Logo",
                              "src": "https://sw-email-media.s3.amazonaws.com/shared-images/sw-icon-textbottom-blue.png",
                              "href": "https://sportwrench.com"
                            },
                            "style": {
                              "width": "100%",
                              "padding-top": "0px",
                              "padding-left": "0px",
                              "padding-right": "0px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "class": "center  autowidth ",
                              "width": 84
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "BF-basic-newsletter",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "2.0.0"
              },
              "description": "BF-basic-newsletter"
            }
          }', null, 'exhibitor.applied', true, 'exhibitors', true, null);
        
        
        WITH template AS (SELECT email_template_id
                          FROM public.email_template
                          WHERE email_template.email_template_type = 'exhibitor.declined')
           , insert_type AS (
            INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                    default_email_template_id)
                VALUES ('exhibitor.declined', 'exhibitors', 'Exhibitor Declined', '<em>Exhibitor Declined</em>',
                        'Exhibitor Declined',
                        true, (SELECT email_template_id
                               FROM template))
        )
        INSERT
        INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('exhibitor.declined', 'exhibitors', (SELECT email_template_id
                                                     FROM template), 0);
        
        WITH template AS (SELECT email_template_id
                          FROM public.email_template
                          WHERE email_template.email_template_type = 'exhibitor.approved')
           , insert_type AS (
            INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                    default_email_template_id)
                VALUES ('exhibitor.approved', 'exhibitors', 'Exhibitor Approved', '<em>Exhibitor Approved</em>',
                        'Exhibitor Approved',
                        true, (SELECT email_template_id
                               FROM template))
        )
        INSERT
        INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('exhibitor.approved', 'exhibitors', (SELECT email_template_id
                                                     FROM template), 0);
        
        
        WITH template AS (SELECT email_template_id
                          FROM public.email_template
                          WHERE email_template.email_template_type = 'exhibitor.applied')
           , insert_type AS (
            INSERT INTO public.email_template_type (type, email_template_group, title, description, long_title, is_trigger,
                                                    default_email_template_id)
                VALUES ('exhibitor.applied', 'exhibitors', 'Exhibitor Applied', '<em>Exhibitor Applied</em>',
                        'Exhibitor Applied',
                        true, (SELECT email_template_id
                               FROM template))
        )
        INSERT
        INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
        VALUES ('exhibitor.applied', 'exhibitors', (SELECT email_template_id
                                                    FROM template), 0);
        `
  )
};

exports.down = function(knex) {
  return knex.schema.raw(`
    DELETE FROM email_template WHERE email_template_group = 'exhibitors';
    DELETE FROM email_template_type WHERE email_template_group = 'exhibitors';
    DELETE FROM email_template_group WHERE "group" = 'exhibitors';
    DELETE FROM event_email_trigger WHERE email_template_group = 'exhibitors';
  `)
};

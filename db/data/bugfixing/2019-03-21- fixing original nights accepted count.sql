BEGIN;

-- Recount "total_tentative", "total_accepted", "total_confirmed" and  "max_total_accepted" fields -----------------
WITH "not_local_clubs" AS (
  SELECT roster_club_id "clubs"
  FROM roster_club
  WHERE is_local IS NOT TRUE
    AND event_id in (SELECT e.event_id FROM event e WHERE e.date_start > (NOW() AT TIME ZONE e.timezone))
),
     "loyalty_teams" AS (
       SELECT array_agg(rt.roster_team_id) "teams"
       FROM roster_team rt
       WHERE (
         SELECT SUM(COALESCE(ths."ths_loyalty", 0)) > 0
         FROM "ths_booking" ths
         WHERE ths.roster_team_id = rt.roster_team_id
           AND ths."ths_hotel_status" IN ('Confirmed', 'Accepted', 'Tentative')) IS TRUE
         AND rt.is_local IS NOT TRUE
         AND rt.roster_club_id IN (SELECT clubs FROM not_local_clubs)
     )
UPDATE roster_team
SET total_tentative    =
      COALESCE((
                 SELECT SUM(
                            CASE
                              WHEN ths.ths_hotel_status = 'Tentative'
                                THEN ths.ths_tentative_nights
                              WHEN ths.ths_hotel_status = 'Accepted'
                                THEN ths.ths_tentative_nights
                              WHEN ths.ths_hotel_status = 'Confirmed'
                                THEN ths.ths_confirmed_nights
                              ELSE 0
                              END
                          )
                 FROM ths_booking ths
                 WHERE ths.roster_team_id = roster_team.roster_team_id
                   AND ths."ths_loyalty" = (CASE
                                              WHEN ths.roster_team_id = ANY
                                                   ((SELECT teams FROM loyalty_teams)::integer[])
                                                THEN 1
                                              ELSE 0
                   END)
               ), 0),
    total_accepted     =
      COALESCE((
                 SELECT SUM(
                            CASE
                              WHEN ths.ths_hotel_status = 'Accepted'
                                THEN ths.ths_tentative_nights
                              WHEN ths.ths_hotel_status = 'Confirmed'
                                THEN ths.ths_confirmed_nights
                              ELSE 0
                              END
                          )
                 FROM ths_booking ths
                 WHERE ths.roster_team_id = roster_team.roster_team_id
                   AND ths."ths_loyalty" = (CASE
                                              WHEN ths.roster_team_id = ANY
                                                   ((SELECT teams FROM loyalty_teams)::integer[])
                                                THEN 1
                                              ELSE 0
                   END)
               ), 0),
    total_confirmed    =
      COALESCE((
                 SELECT SUM(
                            CASE
                              WHEN ths.ths_hotel_status = 'Confirmed'
                                THEN ths.ths_confirmed_nights
                              ELSE 0
                              END
                          )
                 FROM ths_booking ths
                 WHERE ths.roster_team_id = roster_team.roster_team_id
                   AND ths."ths_loyalty" = (CASE
                                              WHEN ths.roster_team_id = ANY
                                                   ((SELECT teams FROM loyalty_teams)::integer[])
                                                THEN 1
                                              ELSE 0
                   END)
               ), 0),
    max_total_accepted = COALESCE((SELECT SUM(max_accepted_nights_per_order.max_value)
                                   FROM (
                                          SELECT MAX(
                                                     CASE
                                                       WHEN ths_h.ths_hotel_status = 'Accepted'
                                                         THEN ths_h.ths_tentative_nights
                                                       WHEN ths_h.ths_hotel_status = 'Confirmed'
                                                         THEN ths_h.ths_confirmed_nights
                                                       ELSE 0
                                                       END
                                                   ) max_value
                                          FROM ths_history ths_h
                                          WHERE ths_h.roster_team_id = roster_team.roster_team_id
                                            AND ths_h."ths_loyalty" = (CASE
                                                                         WHEN ths_h.roster_team_id = ANY
                                                                              ((SELECT teams FROM loyalty_teams)::integer[])
                                                                           THEN 1
                                                                         ELSE 0
                                            END)
                                          GROUP BY ths_id
                                        ) max_accepted_nights_per_order), 0)
WHERE roster_team.is_local IS NOT TRUE
  AND roster_team.roster_club_id IN (SELECT clubs FROM not_local_clubs);
-- ----------------------------------------------------------------------------------------------------------------------


-- Update roster_team status_housing ------------------------------------------------------------------------------------
UPDATE "roster_team" AS _rt
SET status_housing = (SELECT COALESCE((SELECT CASE
                                                WHEN (rt2.status_entry = 13
                                                  AND (rt2.total_tentative >= e.housing_nights_required)
                                                  AND (rt2.total_accepted < e.housing_nights_required)) THEN 33
                                                WHEN (rt2.status_entry = 12
                                                  AND
                                                      CASE
                                                        WHEN e.housing_rooming_list_due_date IS NOT NULL
                                                          THEN (CURRENT_DATE < e.housing_rooming_list_due_date
                                                          AND rt2.total_accepted < e.housing_nights_required)
                                                          OR rt2.total_confirmed < e.housing_nights_required
                                                        ELSE rt2.total_confirmed < e.housing_nights_required
                                                        END
                                                  )
                                                  THEN 34
                                                WHEN (rt2.ths_loyalty = 1
                                                  AND
                                                      (rt2.ths_total_loyalty_confirmed < rt2.max_total_accepted * 0.9))
                                                  THEN 35
                                                WHEN (rt2.total_accepted >= e.housing_nights_required) THEN 32
                                                ELSE 31 END
                                       FROM "roster_team" AS rt2
                                       WHERE (rt2.roster_team_id = _rt.roster_team_id)), 31) :: INTEGER)
FROM (SELECT e.event_id,
             e.housing_nights_required,
             e.housing_nights_threshold,
             e.housing_rooming_list_due_date
      FROM "event" AS e
      WHERE e.has_status_housing IS TRUE
        AND e.event_id IN (SELECT e.event_id FROM event e WHERE e.date_start > (NOW() AT TIME ZONE e.timezone))) AS e
WHERE (_rt.is_local IS NOT TRUE)
  AND (_rt.event_id = e.event_id)
  AND NOT EXISTS(SELECT 1 FROM roster_club WHERE is_local IS TRUE AND roster_club.roster_club_id = _rt.roster_club_id);
----------------------------------------------------------------------------------------------------------------------


-- Update roster clubs data ------------------------------------------------------------------------------------------
UPDATE roster_club
SET total_tentative    =
      (SELECT SUM(rt.total_tentative)
       FROM roster_team rt
       WHERE rt.roster_club_id = roster_club.roster_club_id
         AND rt.deleted IS NULL),
    total_accepted     =
      (SELECT SUM(rt.total_accepted)
       FROM roster_team rt
       WHERE rt.roster_club_id = roster_club.roster_club_id
         AND rt.deleted IS NULL),
    total_confirmed    =
      (SELECT SUM(rt.total_confirmed)
       FROM roster_team rt
       WHERE rt.roster_club_id = roster_club.roster_club_id
         AND rt.deleted IS NULL),
    max_total_accepted =
      (SELECT SUM(rt.max_total_accepted)
       FROM roster_team rt
       WHERE rt.roster_club_id = roster_club.roster_club_id
         AND rt.deleted IS NULL)
WHERE roster_club.event_id IN (SELECT e.event_id FROM event e WHERE e.date_start > (NOW() AT TIME ZONE e.timezone))
  AND roster_club.is_local IS NOT TRUE;
----------------------------------------------------------------------------------------------------------------------


COMMIT;

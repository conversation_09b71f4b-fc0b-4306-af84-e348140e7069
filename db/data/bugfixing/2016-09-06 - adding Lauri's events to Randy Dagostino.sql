-- inserting CoEO rows for <PERSON>
insert into event_user (user_id, event_owner_id, event_id, role_co_owner)
select 42 user_id, e.event_owner_id, e.event_id, true
from event e
  left JOIN event_user eu on eu.event_id = e.event_id and eu.user_id = 42
where eu.event_user_id is null
  and e.event_owner_id = 6;

--
insert into event_user (user_id, event_owner_id, event_id, role_co_owner)
select 42 user_id, e.event_owner_id, e.event_id, true
from event e
  left JOIN event_user eu on eu.event_id = e.event_id and eu.user_id = 42
where eu.event_user_id is null
  and e.event_id = 43


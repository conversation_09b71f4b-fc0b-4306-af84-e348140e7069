UPDATE roster_team _rt
SET team_name = mt.team_name
   FROM roster_team rt
   LEFT JOIN master_team mt ON mt.master_team_id = rt.master_team_id
   WHERE _rt.roster_team_id = rt.roster_team_id
     AND rt.deleted IS NULL
     AND (rt.team_name <> mt.team_name);


UPDATE roster_team _rt
SET organization_code = mt.organization_code, rank = mt.rank
   FROM roster_team rt
   LEFT JOIN master_team mt ON mt.master_team_id = rt.master_team_id
   WHERE _rt.roster_team_id = rt.roster_team_id
     AND rt.deleted IS NULL
     AND rt.status_entry <> 11
     AND (rt.organization_code <> mt.organization_code)
     AND (rt.rank <> mt.rank);



SELECT tm.team_roster_id roster_team_id, tm.team_master_id master_team_id,
tm.team_name, tm.organization_code, tm.club_roster_id roster_club_id, tm.age, tm.rank, tm.division division_name, 4410 event_id,
2 sport_id, tm.date_created created, tm.date_modified modified, tm.date_modified date_entered, FALSE deleted,

FROM team_roster tm

WHERE tm.tournament_id = 4410;

-- query to backup table in PG
SELECT * INTO _roster_team FROM roster_team;

DELETE FROM roster_team WHERE event_id = 4410;



-- refactoring

-- CREATE FIELD "roster_club_id" -------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "roster_club_id" INTEGER;
-- -------------------------------------------------------------;


-- CREATE FIELD "division_name" --------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "division_name" CHARACTER VARYING( 20 );COMMENT ON COLUMN "public"."roster_team"."division_name" IS 'Division name (temprorary field to calculate division_id on import from UA)';
-- -------------------------------------------------------------;

-- DROP FIELD "club_id" ----------------------------------------
ALTER TABLE "public"."roster_team" DROP COLUMN "club_id";
-- -------------------------------------------------------------;
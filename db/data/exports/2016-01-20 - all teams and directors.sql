select e.name, d.name, rt.team_name, rc.club_name, mc.director_first, mc.director_last, mc.director_email, mc.director_phone
from event e
  INNER JOIN roster_team rt on rt.event_id = e.event_id
  INNER JOIN roster_club rc on rc.roster_club_id = rt.roster_club_id
  INNER JOIN master_club mc on mc.master_club_id = rc.master_club_id
  INNER JOIN division d ON d.division_id = rt.division_id
WHERE e.date_start > now()
  and rt.status_entry = 12
ORDER BY e.date_start, e.name, d.name, rt.team_name

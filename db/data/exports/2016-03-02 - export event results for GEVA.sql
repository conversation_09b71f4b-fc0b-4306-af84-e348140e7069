
select e.name, to_char(m.secs_start, 'mm/dd/YYYY'),
  d.short_name,
  rt1.team_name first_team, rt1.organization_code first_code,
  rt2.team_name second_team, rt2.organization_code second_code,
  CASE WHEN (m.results::JSON->>'winner')::INT = 1 THEN 'First team won'
       WHEN (m.results::JSON->>'winner')::INT = 2 THEN 'Second team won'
      ELSE 'Unknown result' END outcome,
  substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7) scores
from "event" e
INNER JOIN division d ON d.event_id = e.event_id
  INNER JOIN matches m ON m.division_id = d.division_id
  INNER JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id AND rt1.deleted IS NULL AND rt1.status_entry = 12
  INNER JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id AND rt2.deleted IS NULL AND rt2.status_entry = 12
where e.event_id in (50)
ORDER BY date(m.secs_start), d.short_name, m.secs_start
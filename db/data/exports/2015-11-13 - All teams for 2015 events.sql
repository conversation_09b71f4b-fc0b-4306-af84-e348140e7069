SELECT date(e.date_start) event_start, e.name, rt.date_entered, rt.date_accepted, rt.date_housing, rt.date_paid, ts.status_value paid_status, rt.organization_code, rt.team_name, rc.club_name, d.name
FROM roster_team rt
  LEFT JOIN roster_club rc ON rc.roster_club_id = rt.roster_club_id
  LEFT JOIN division d ON d.division_id = rt.division_id
  LEFT JOIN team_status ts ON ts.team_status_id = rt.status_paid
  LEFT JOIN event e ON e.event_id = rt.event_id
WHERE rt.deleted IS NULL
  AND rt.status_entry = 12
ORDER BY date(e.date_start), e.name, rt.date_entered
;
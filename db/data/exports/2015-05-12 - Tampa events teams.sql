--
SELECT e.name, mc.city, mc.state, COUNT(rt.roster_team_id) cnt
FROM roster_team rt
INNER JOIN master_team mt ON rt.master_team_id = mt.master_team_id
INNER JOIN master_club mc ON mt.master_club_id = mc.master_club_id
INNER JOIN "event" e ON e.event_id = rt.event_id
WHERE rt.event_id IN (11,12,13)
  AND rt.status_entry = 12
  AND rt.deleted IS NULL
  GROUP BY e.name, e.date_start, mc.state, mc.city
ORDER BY e.date_start, mc.state, mc.city


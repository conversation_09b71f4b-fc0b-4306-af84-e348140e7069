-- This event was imported to SW from excel file.
-- We do not match teams and clubs to existing rows - just created duplicate rows.

-- Questions not answered yet:
--  - Do we need to add some field to mark master_club, roster_club, roster_team rows that they are virtual
--    (i.e. not linked to existing club account, etc)

-- 0. Created event 17047 with divisions

-- 1. Creating temp table
CREATE TABLE teams_import (
    team_code text NOT NULL,
    team_name text,
    division_name text,
    club_name text,
    club_state text
)

-- 2. Importing teams to teams_import in DataGrip

-- 3. some fixes in data
UPDATE teams_import
SET division_name = '17/18 Girls'
WHERE division_name IN ('17 Girls', '18 Girls');

-- 4. Creating master_club rows
INSERT INTO master_club (club_name, club_owner_id, code, region,
                         sport_id, has_male_teams, has_female_teams, has_coed_teams, zip, country, state, city, address)
    SELECT DISTINCT ti.club_name, 0, SUBSTR(ti.team_code, 10, 2), SUBSTR(ti.team_code, 4, 5),
      2, false, true, false, 17047, 'US', s.state, '17047', 'Virtual Club'
    FROM teams_import ti
    LEFT JOIN state s ON s.name = ti.club_state;

-- 5. Creating roster_club rows
INSERT INTO roster_club (master_club_id, event_id, club_name, zip, country, state, region, city, address, code)
    SELECT DISTINCT (select mc.master_club_id from master_club mc WHERE mc.club_owner_id = 0 AND mc.zip = '17047' AND mc.address = 'Virtual Club' AND mc.club_name = ti.club_name AND mc.state = s.state),
      17047, ti.club_name,
      NULL, 'US', s.state, SUBSTR(ti.team_code, 10, 2), '', '', SUBSTR(ti.team_code, 4, 5)
    FROM teams_import ti
    LEFT JOIN state s ON s.name = ti.club_state;

-- 6. Creating roster_team rows
INSERT INTO roster_team (event_id, club_owner_id, sport_id, master_team_id,
                         organization_code, team_name, age, rank, gender, division_id, division_name,
                         date_entered, date_completed, date_paid, date_accepted,
                         status_entry, status_paid, is_local,
                         roster_club_id)
SELECT 17047, 0, 2, null,
  ti.team_code, ti.team_name, SUBSTR(ti.team_code, 3, 1)::INTEGER age, SUBSTR(ti.team_code, 9, 1) rank, 'female'::team_gender gender,
  d.division_id, ti.division_name,
  now(), now(), now(), now(),
  12, 22, TRUE,
  (select rc.roster_club_id from roster_club rc WHERE rc.event_id = 17047 AND rc.club_name = ti.club_name AND rc.state = s.state)
  FROM teams_import ti
    LEFT JOIN state s ON s.name = ti.club_state
    LEFT JOIN division d ON d.event_id = 17047 AND d.name = ti.division_name




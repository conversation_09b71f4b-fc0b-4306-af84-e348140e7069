
-- Manually adding master club
INSERT INTO master_club (club_name, club_owner_id, code, region,
                         sport_id, has_male_teams, has_female_teams, has_coed_teams, zip, country, state, city, address)
    SELECT 'DCPG Pearls' club_name, 0, 'DCPGP' club_code, 'CH' region,
      2, false, true, false, 17032, 'US', 'CH', '17032', 'Virtual Club'

-- Manually adding roster club
INSERT INTO roster_club (master_club_id, event_id, club_name, zip, country, state, region, city, address, code)
    SELECT  (select mc.master_club_id from master_club mc WHERE mc.club_owner_id = 0 AND mc.zip = '17032' AND mc.address = 'Virtual Club' AND mc.club_name = 'DCPG Pearls'),
      17032, 'DCPG Pearls' club_name,
      NULL, 'US', 'CH' state, 'CH' region, '', '', 'DCPGP' code

-- Manually adding a team to event
INSERT INTO roster_team (event_id, club_owner_id, sport_id, master_team_id,
                         organization_code, team_name, age, rank, gender, division_id,
                         date_entered, date_completed, date_paid, date_accepted,
                         status_entry, status_paid, is_local,
                         roster_club_id)
SELECT 17032 event_id, 0 club_owner_id, 2 sport_id, null master_team_id,
  'FJ5DCPGP1CH' team_code, 'DCPG Pearls' team_name, 15 age, 1 rank, 'female'::team_gender gender,
  1109 division_id,
  now(), now(), now(), now(),
  12, 22, TRUE,
  (select rc.roster_club_id from roster_club rc WHERE rc.event_id = 17047 AND rc.club_name = 'DCPG Pearls') roster_club_id

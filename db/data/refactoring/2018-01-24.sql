BEGIN;

-- CREATE FIELD "master_staff_id" ---------------------------------
ALTER TABLE "public"."event_email" ADD COLUMN "master_staff_id" INTEGER DEFAULT NULL;
-- -------------------------------------------------------------


-- UPDATE club's email_template_group variables ---------------------------------
UPDATE email_template_group etg
SET variables = '[{"field":"event_name","title":"Event Name","pattern":"{event_name}"},{"field":"event_city","title":"Event City","pattern":"{event_city}"},{"field":"event_website","title":"Event Website","pattern":"{event_website}"},{"field":"event_email","title":"Event Email","pattern":"{event_email}"},{"field":"event_month","title":"Event Month","pattern":"{event_month}"},{"field":"club_name","title":"Club Name","pattern":"{club_name}"},{"field":"receiver_first","title":"Receiver First Name","pattern":"{receiver_first}"},{"field":"receiver_last","title":"Receiver Last Name","pattern":"{receiver_last}"},{"field":"receiver_name","title":"Receiver First and Last Name","pattern":"{receiver_name}"},{"field":"roster_teams","title":"Team Names (separated by comma)","pattern":"{teams}"},{"field":"teams_divs","title":"Team Names with divisions (separated by comma)","pattern":"{teams_divs}","custom_action":true},{"field":"social_icons","title":"Social Icons","pattern":"{social_icons}","custom_action":true},{"field":"facebook_icon","title":"Facebook Icon","pattern":"{facebook_icon}","custom_action":true},{"field":"twitter_icon","title":"Twitter Icon","pattern":"{twitter_icon}","custom_action":true},{"field":"instagram_icon","title":"Instagram Icon","pattern":"{instagram_icon}","custom_action":true},{"field":"snapchat_icon","title":"Snapchat Icon","pattern":"{snapchat_icon}","custom_action":true}]':: JSONB
WHERE etg.group = 'clubs'
---------------------------------------------------------------------------------

COMMIT;

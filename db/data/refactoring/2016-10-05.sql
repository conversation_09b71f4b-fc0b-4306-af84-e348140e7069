BEGIN;

-- CREATE FIELD "security_pin" ---------------------------------
ALTER TABLE "public"."official" ADD COLUMN "security_pin" Text;
COMMENT ON COLUMN "public"."official"."security_pin" IS 'Pin code for Results App access';
-- -------------------------------------------------------------

COMMIT;

-- SET sucirity pin from the latest event

UPDATE "official" o 
SET "security_pin" = "d"."security_pin"
FROM (
	SELECT o."official_id", eof."security_pin"
	FROM "official" o 
	INNER JOIN "event_official" eof 
		ON eof."official_id" = o."official_id"
		AND eof."security_pin" IS NOT NULL
		--AND eof."work_status" = 'approved'
		AND eof."event_id" = (
			SELECT e."event_id"
			FROM "event_official" _eof
			INNER JOIN "event" e 
				ON e.event_id = _eof.event_id
			WHERE _eof."official_id" = eof."official_id"
			ORDER BY e."date_start" DESC
			LIMIT 1
		)
) "d"
WHERE "d"."official_id" = "o"."official_id"
RETURNING o."official_id", o."security_pin"

-- SET security pin for those, who has no certain event_official rows
UPDATE "official" o 
SET "security_pin" = "d"."security_pin"
FROM (
    SELECT "official_id", ((random() * 99999 + 1000)::INTEGER)::TEXT "security_pin"
    FROM "official" 
    WHERE "security_pin" IS NULL
) "d"
WHERE o."official_id" = "d"."official_id"
RETURNING o."official_id", o."security_pin"

-- SET another pin for duplicates
UPDATE "official" o 
SET "security_pin" = "d"."security_pin"
FROM (
    SELECT "official_id", ((random() * 99999 + 1000)::INTEGER)::TEXT "security_pin"
    FROM "official" 
    WHERE "security_pin" IN (
    	SELECT "security_pin"
		FROM "official"
		GROUP BY "security_pin"
		HAVING COUNT(*) > 1
    )
) "d"
WHERE o."official_id" = "d"."official_id"
RETURNING o."official_id", o."security_pin"

-- Add unique constraint
BEGIN;

-- CREATE UNIQUE "unique_official_security_pin" ----------------
ALTER TABLE "public"."official" ADD CONSTRAINT "unique_official_security_pin" UNIQUE( "security_pin" );
-- -------------------------------------------------------------

COMMIT;


-- Query to get a list of events without any payment method set to TRUE
select e.event_id, e.date_start, e.long_name
from "event" e
where e.date_start > NOW()
  and e.is_test = FALSE
  and e.allow_check_payments = FALSE
  and e.allow_card_payments = FALSE
  and e.allow_ach_payments = FALSE;

-- Fixing event settings - making pay by check TRUE
UPDATE "event"
SET allow_check_payments = TRUE
WHERE date_start > NOW();

-- Making allow_check_payments set to TRUE by default
ALTER TABLE public.event ALTER COLUMN allow_check_payments SET DEFAULT TRUE;

BEGIN;

-- CREATE FIELD "date_reg_close" -------------------------------
ALTER TABLE "public"."division" ADD COLUMN "date_reg_close" Timestamp Without Time Zone;
-- -------------------------------------------------------------

COMMIT;

--- CAMPS ---

BEGIN;

CREATE SEQUENCE "public"."event_camp_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 9223372036854775807
START 0
CACHE 1;

COMMIT;

BEGIN;

-- CREATE TABLE "event_camp" -----------------------------------
CREATE TABLE "public"."event_camp" ( 
	"event_camp_id" Integer DEFAULT nextval('event_camp_id_seq'::regclass) NOT NULL,
	"created" Timestamp Without Time Zone DEFAULT now(),
	"modified" Timestamp Without Time Zone DEFAULT now(),
	"name" Text COLLATE "pg_catalog"."default" NOT NULL,
	CONSTRAINT "unique_event_camp_id" UNIQUE( "event_camp_id" ) );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

drop trigger if exists update_event_camp_modified on "public"."event_camp";

CREATE TRIGGER update_event_camp_modified
BEFORE UPDATE ON "public"."event_camp" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;


BEGIN;

-- CREATE FIELD "event_camp_id" --------------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "event_camp_id" Integer;COMMENT ON COLUMN "public"."event_ticket"."event_camp_id" IS 'For Camps sales; Every event ticket becomes a type of Event Camp;';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE FIELD "ticket_camps_registration" --------------------
ALTER TABLE "public"."event" ADD COLUMN "ticket_camps_registration" Boolean DEFAULT 'false';COMMENT ON COLUMN "public"."event"."ticket_camps_registration" IS 'If Event has Camps Sales (Tickets and Camps sales are not allowed at the same time)';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "event_id" -------------------------------------
ALTER TABLE "public"."event_camp" ADD COLUMN "event_id" Integer NOT NULL;
-- -------------------------------------------------------------

COMMIT;




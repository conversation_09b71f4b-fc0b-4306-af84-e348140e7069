CREATE TRIGGER "update_purchase_modified"
BEFORE UPDATE ON "public"."purchase" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column()

BEGIN;

-- CREATE FIELD "event_template_id" ----------------------------
ALTER TABLE "public"."event_email" ADD COLUMN "event_template_id" Integer;
-- -------------------------------------------------------------

-- CREATE INDEX "index_event_template_id" ----------------------
CREATE INDEX "index_event_template_id" ON "public"."event_email" USING btree( "event_template_id" );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "NAME" OF "FIELD "event_template_id" -----------------
ALTER TABLE "public"."event_email" RENAME COLUMN "event_template_id" TO "email_template_id";
-- -------------------------------------------------------------

-- DROP INDEX "index_event_template_id" ------------------------
DROP INDEX IF EXISTS "public"."index_event_template_id";
-- -------------------------------------------------------------

-- CREATE INDEX "index_event_template_id" ----------------------
CREATE INDEX "index_event_template_id" ON "public"."event_email" USING btree( "email_template_id" Asc NULLS Last );
-- -------------------------------------------------------------

COMMIT;


BEGIN;


-- SET "event"."official_payment_method" = {"on_site": true} for all events season 2016 and earlier ------------
UPDATE event "e"
SET "official_payment_method" = (
  JSONB_BUILD_OBJECT('on_site', true)
) :: JSON
WHERE e."season" <= 2016
    AND e.deleted IS NULL
    AND e.live_to_public IS TRUE
    AND e.has_officials = TRUE;
-- -------------------------------------------------------------------------------------------------------------


COMMIT;

-- Adding column deleted to event table
ALTER TABLE public.event ADD deleted TIMESTAMP DEFAULT NULL  NULL;
COMMENT ON COLUMN public.event.deleted IS 'NULL value means event is NOT deleted';


-- Adding triggers to update column "modified"

-- master_team
CREATE TRIGGER update_master_team_modified BEFORE UPDATE
ON master_team FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- roster_team
CREATE TRIGGER update_roster_team_modified BEFORE UPDATE
ON roster_team FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();


-- master_club
CREATE TRIGGER update_master_club_modified BEFORE UPDATE
ON master_club FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- roster_club
CREATE TRIGGER update_roster_club_modified BEFORE UPDATE
ON roster_club FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- purchase_booth
CREATE TRIGGER update_purchase_booth_modified BEFORE UPDATE
ON purchase_booth FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- purchase_team
CREATE TRIGGER update_purchase_team_modified BEFORE UPDATE
ON purchase_team FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- event_booth
CREATE TRIGGER update_event_booth_modified BEFORE UPDATE
ON event_booth FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- event_location
CREATE TRIGGER update_event_location_modified BEFORE UPDATE
ON event_location FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- event_official
CREATE TRIGGER update_event_official_modified BEFORE UPDATE
ON event_official FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- event_official_schedule
CREATE TRIGGER update_event_official_schedule_modified BEFORE UPDATE
ON event_official_schedule FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- club_owner
CREATE TRIGGER update_club_owner_modified BEFORE UPDATE
ON club_owner FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- official
CREATE TRIGGER update_official_modified BEFORE UPDATE
ON official FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- webpoint_adult
CREATE TRIGGER update_webpoint_adult_modified BEFORE UPDATE
ON webpoint_adult FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- webpoint_athlete
CREATE TRIGGER update_webpoint_athlete_modified BEFORE UPDATE
ON webpoint_athlete FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- webpoint_parse
CREATE TRIGGER update_webpoint_parse_modified BEFORE UPDATE
ON webpoint_parse FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- webpoint_queue
CREATE TRIGGER update_webpoint_queue_modified BEFORE UPDATE
ON webpoint_queue FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- ths_history
CREATE TRIGGER update_ths_history_modified BEFORE UPDATE
ON ths_history FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- ths_booking
CREATE TRIGGER update_ths_booking_modified BEFORE UPDATE
ON ths_booking FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- team_status
CREATE TRIGGER update_team_status_modified BEFORE UPDATE
ON team_status FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();

-- stripe_event
CREATE TRIGGER update_stripe_event_modified BEFORE UPDATE
ON stripe_event FOR EACH ROW EXECUTE PROCEDURE
update_modified_column();
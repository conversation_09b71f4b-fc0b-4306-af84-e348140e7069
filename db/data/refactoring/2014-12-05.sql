---
--- Adding fields for THS data
---
BEGIN;

-- CREATE FIELD "total_tentative" ------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "total_tentative" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."total_tentative" IS 'Total Tentative (or better) Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "total_accepted" -------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "total_accepted" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."total_accepted" IS 'Total Accepted (or better) Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "total_confirmed" ------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "total_confirmed" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."total_confirmed" IS 'Total Confirmed Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "max_total_accepted" ---------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "max_total_accepted" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."max_total_accepted" IS 'Max Total Accepted Nights';
-- -------------------------------------------------------------;

COMMIT;


---
--- Query to get housing values
---
-- SELECT rt.roster_team_id, rt.team_name,
--   COALESCE( (SELECT SUM( CASE
-- 			WHEN ths.ths_hotel_status = 'Tentative' THEN ths.ths_tentative_nights
-- 			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
-- 			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
-- 			ELSE 0
-- 			END)
--   FROM ths_booking ths
--   WHERE ths.roster_team_id = rt.roster_team_id
--     )
--   , 0) total_tentative,
--   COALESCE( (SELECT SUM( CASE
-- 			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
-- 			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
-- 			ELSE 0
-- 			END)
--   FROM ths_booking ths
--   WHERE ths.roster_team_id = rt.roster_team_id
--     )
--   , 0) total_accepted,
--  COALESCE( (SELECT SUM( CASE
-- 			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
-- 			ELSE 0
-- 			END)
--   FROM ths_booking ths
--   WHERE ths.roster_team_id = rt.roster_team_id
--     )
--   , 0) total_confirmed
-- FROM roster_team rt
-- WHERE rt.roster_team_id BETWEEN 1 AND 1000

---
--- Query to update housing values
---
UPDATE roster_team rt
SET
	total_tentative =
  COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Tentative' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0),
	total_accepted =
  COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0),
	total_confirmed =
 COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0)
---WHERE rt.roster_team_id IN (1)
;

UPDATE roster_team rt
SET max_total_accepted = total_accepted
WHERE  COALESCE(max_total_accepted,0) < total_accepted
;


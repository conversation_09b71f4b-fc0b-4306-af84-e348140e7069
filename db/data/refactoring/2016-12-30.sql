CREATE OR REPLACE FUNCTION public.esw_event_journal_sync_function()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
    DECLARE
        sql         TEXT;
        sql_params  TEXT;
        obj         JSON;
        where_obj   JSON;
        m           TEXT;
        iter        INTEGER;
        method      TEXT;
        tname       TEXT;
        table_name  TEXT;
        upd_keys    TEXT[];
    BEGIN
        method  := LOWER(NEW.method);
        table_name := LOWER(NEW.table_name);
        tname   := FORMAT('%s', LOWER('"public"."' || table_name || '"'));
        -- dump table name
        -- RAISE NOTICE '%', tname;

        IF NEW.skip_update = FALSE AND table_name IN (
            'results', 'division_standing', 'rounds', 'poolbrackets', 'matches', 'courts',
            'division', 'event'
        ) THEN
            
            IF (method = 'insert') THEN
                -- INSERT is not allowed for tables:
                IF (table_name NOT IN ('division', 'event')) THEN
                    obj         := NEW.data::JSON->'data';
                    sql_params  := ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(obj)), ',');
                    sql         := FORMAT('INSERT INTO %s ( %s ) (SELECT %s FROM JSON_POPULATE_RECORD(NULL::%s, $1))', tname, sql_params, sql_params, tname);
               
                    -- RAISE NOTICE '%', sql;
                    -- RAISE NOTICE '%', sql_params;
                    EXECUTE sql USING  obj;
                ELSE 
                    RAISE EXCEPTION 'Not allowed to insert into "%"', table_name;
                END IF;
        
            ELSEIF (method = 'update') THEN
        
                iter        := 0;
                obj         := NEW.data::JSON->'data';
                where_obj   := NEW.data::JSON->'where';
                sql         := FORMAT('UPDATE %s tn SET ', tname);

                IF (table_name = 'division') THEN
                    -- List of columns allowed to update in 'divisions'
                    upd_keys  := 
                        ARRAY(
                            SELECT * FROM JSON_OBJECT_KEYS(obj) "key"
                            WHERE "key" IN (
                                'swb_settings',
                                'officials_required_override',
                                'color',
                                'tie_breaker_type',
                                'sort_order',
                                'sort_priority',
                                'division_seeds',
                                'division_finishes'
                            )
                        );
                ELSEIF (table_name = 'event') THEN
                    -- List of columns allowed to update in 'event'
                    upd_keys  := 
                        ARRAY(
                            SELECT * FROM JSON_OBJECT_KEYS(obj) "key"
                            WHERE "key" IN (
                                'swb_settings',
                                'hide_seeds',
                                'officials_required',
                                'tie_breaker_type'
                            )
                        );
                ELSE
                    -- Otherwise - allowed to update all columns
                    upd_keys  := ARRAY(SELECT * FROM JSON_OBJECT_KEYS(obj));
                END IF;

                IF (ARRAY_LENGTH(upd_keys, 1) IS NULL)
                THEN 
                    RAISE EXCEPTION 'No allowed keys to update!';
                END IF;
                
                FOREACH m IN ARRAY upd_keys
                LOOP
                    IF (iter > 0) THEN
                        sql := FORMAT('%s, "%s" = sq."%s" ', sql, m, m);
                    ELSE
                        sql := FORMAT('%s "%s" = sq."%s" ', sql, m, m);
                    END IF;
                        iter := iter + 1;
                END LOOP;   

                sql := FORMAT(
                    '%s FROM ( SELECT %s FROM JSON_POPULATE_RECORD(NULL::%s, $1)) AS sq WHERE CAST(%s AS TEXT) = $2', 
                    sql,
                    ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(obj)), ', '), 
                    tname,
                    ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(where_obj)), ',')
                );
                
                EXECUTE sql USING obj, where_obj->>ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(where_obj)), ',');
        
            ELSEIF (method = 'delete') THEN
                -- DELETE is not allowed for tables:
                IF (table_name NOT IN ('division', 'event')) THEN
                    where_obj   := NEW.data::json->'where';
                    sql         := FORMAT('DELETE FROM %s WHERE CAST(%s AS TEXT) = $1', tname, ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(where_obj)), ','));
                
                    EXECUTE sql USING where_obj->>ARRAY_TO_STRING(ARRAY(SELECT * FROM JSON_OBJECT_KEYS(where_obj)), ',');
                ELSE 
                    RAISE EXCEPTION 'Not allowed to delete from "%"', table_name;
                END IF;
            
            END IF;
        
        END IF;

        RETURN NEW;
    
        EXCEPTION
            WHEN unique_violation THEN
                RAISE NOTICE 'EXCEPTION in esw_event_journal_sync_function() - Row already exists';
                RETURN NEW;

    END
$function$

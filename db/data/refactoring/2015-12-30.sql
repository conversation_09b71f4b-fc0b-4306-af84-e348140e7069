BEGIN;

CREATE TRIGGER update_purchase_ticket_modified
BEFORE UPDATE ON "public"."purchase_ticket" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;


BEGIN;

-- CREATE FIELD "foreground_color" -----------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "foreground_color" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "background_color" -----------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "background_color" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

COMMIT;

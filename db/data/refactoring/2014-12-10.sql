---
--- Adding hotel data columns to roster_club table
---

BEGIN;

-- CREATE FIELD "total_tentative" ------------------------------
ALTER TABLE "public"."roster_club" ADD COLUMN "total_tentative" INTEGER;
COMMENT ON COLUMN "public"."roster_club"."total_tentative" IS 'Club Total Tentative (or better) Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "total_accepted" -------------------------------
ALTER TABLE "public"."roster_club" ADD COLUMN "total_accepted" INTEGER;
COMMENT ON COLUMN "public"."roster_club"."total_accepted" IS 'Club Total Accepted (or better) Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "total_confirmed" ------------------------------
ALTER TABLE "public"."roster_club" ADD COLUMN "total_confirmed" INTEGER;
COMMENT ON COLUMN "public"."roster_club"."total_confirmed" IS 'Club Total Confirmed Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "max_total_accepted" ---------------------------
ALTER TABLE "public"."roster_club" ADD COLUMN "max_total_accepted" INTEGER;
COMMENT ON COLUMN "public"."roster_club"."max_total_accepted" IS 'Club Max Total Accepted Nights';
-- -------------------------------------------------------------;

COMMIT;


--- Update roster_club rows
UPDATE roster_club
SET
total_tentative =
(SELECT SUM(rt.total_tentative)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
total_accepted =
(SELECT SUM(rt.total_accepted)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
total_confirmed =
(SELECT SUM(rt.total_confirmed)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
max_total_accepted =
(SELECT SUM(rt.max_total_accepted)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL)

WHERE deleted IS NULL;

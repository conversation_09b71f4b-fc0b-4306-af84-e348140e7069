/**
 * SW-210 Task
 * See the Sport<PERSON>rench QMS to find comments on these changes
 */

BEGIN;

-- CREATE FIELD "linked_purchase_id" ---------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "linked_purchase_id" Integer;
-- -------------------------------------------------------------

-- CREATE FIELD "is_payment" -----------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "is_payment" Boolean DEFAULT true;
-- -------------------------------------------------------------

-- CREATE FIELD "is_ticket" ------------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "is_ticket" Boolean DEFAULT false;
-- -------------------------------------------------------------

COMMIT;

/**
 * Check values in the new columns. 
 *
 * Rows considered to have payment information only ("is_payment" = TRUE) must not have 
 * a value in the "linked_purchase_id" column
 */

ALTER TABLE "purchase" 
    ADD CONSTRAINT ck_is_payment_or_is_ticket CHECK (
        ("linked_purchase_id" IS NULL AND "is_payment" IS TRUE) OR "is_ticket" IS TRUE);


/*

UPDATE "event"
SET "tickets_settings" = 
    COALESCE("tickets_settings", '{}'::JSONB) || '{"require_recipient_name_for_each_ticket": true}'
WHERE "event_id" = <?>
    AND "allow_ticket_sales" IS TRUE

*/

/*

UPDATE "purchase"
SET "is_payment" = TRUE,
    "is_ticket" = TRUE 
WHERE "payment_for" = 'tickets'

 */


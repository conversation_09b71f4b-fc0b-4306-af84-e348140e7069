/*
    Table to save all requests to the SWT API
 */

BEGIN;

CREATE SEQUENCE "public"."swt_api_history_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 2147483647
START 0
CACHE 1;

-- CREATE TABLE "swt_api_history" ------------------------------
CREATE TABLE "public"."swt_api_history" ( 
    "swt_api_history_id" Integer DEFAULT NEXTVAL('swt_api_history_id_seq'::REGCLASS) NOT NULL,
    "created" Timestamp Without Time Zone DEFAULT NOW(),
    "modified" Timestamp Without Time Zone DEFAULT NOW(),
    "request_url" Text NOT NULL,
    "request_body" JSONB,
    "request_query" JSONB,
    "verb" Text NOT NULL,
    "ip" Text NOT NULL,
    "user_agent" Text,
    "action_type" Text NOT NULL,
    "scanner_name" Text,
    "scanner_location" Text,
    "event_id" Integer,
    "ticket_barcode" Text,
    "response_body" JSONB,
    "response_status_code" Integer,
    "success" <PERSON><PERSON><PERSON>,
    "exec_time" Integer,
    CONSTRAINT "unique_swt_api_history_swt_api_history_id" UNIQUE( "swt_api_history_id" ) );
 ;
-- -------------------------------------------------------------

-- CREATE INDEX "swt_api_history_ticket_barcode" ---------------
CREATE INDEX "swt_api_history_ticket_barcode" ON "public"."swt_api_history" USING btree( "ticket_barcode" Asc NULLS Last );
-- -------------------------------------------------------------

-- CREATE INDEX "swt_api_history_scanner_name" -----------------
CREATE INDEX "swt_api_history_scanner_name" ON "public"."swt_api_history" USING btree( "scanner_name" Asc NULLS Last );
-- -------------------------------------------------------------

-- CREATE INDEX "swt_api_history_scanner_location" -------------
CREATE INDEX "swt_api_history_scanner_location" ON "public"."swt_api_history" USING btree( "scanner_location" Asc NULLS Last );
-- -------------------------------------------------------------

-- CREATE INDEX "swt_api_history_action_type" ------------------
CREATE INDEX "swt_api_history_action_type" ON "public"."swt_api_history" USING btree( "action_type" Asc NULLS Last );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- Add "email_template_group" check
DROP VIEW IF EXISTS "public"."v_aem_trigger_templates";
CREATE OR REPLACE VIEW "public"."v_aem_trigger_templates"
    AS (
        SELECT

            ett."type" "type_id",
            ett."title" "type_title",

            et."title",
            et."email_template_id" "id",
            et."is_valid",

            (et."email_template_id" = ett."default_email_template_id") "is_default",

            et."published",

            (et."event_id" IS NULL AND et."event_owner_id" IS NOT NULL) "all_events",

            (COALESCE(usg."qty", 0) > 0) "is_in_use",
            COALESCE(usg."qty", 0) "usage_qty",

            ett."is_trigger",

            /* new fields */
            et."img_name",
            et."event_id",
            et."event_owner_id",
            et."email_template_group" "group",
            etg."title" "group_title"

        FROM "email_template_type" ett
        INNER JOIN "email_template" et
            ON et."email_template_type"     = ett."type"
            AND et."email_template_group" = ett."email_template_group"
            AND et."deleted" IS NULL
        LEFT JOIN "email_template_group" etg
            ON ett."email_template_group" = etg."group"
        LEFT JOIN "v_aem_template_usage" usg
            ON usg."email_template_id"       = et."email_template_id"
            AND usg."email_template_type"   = et."email_template_type"
            AND usg."email_template_group"  = et."email_template_group"
        WHERE ett."is_trigger" IS TRUE
            AND ett."type" <> 'content'
    );

COMMIT;

--Add an ability to define a date that will be used to calculate age of camp participants

-- adding event aga_date ptoperty with default value
UPDATE event
SET tickets_settings = jsonb_set(
        COALESCE(tickets_settings, '{}'::JSONB), 
        '{age_date}', 
        CONCAT('"', '09/01/', event.season, '"') :: JSONB, 
        true)
WHERE event.ticket_camps_registration = TRUE -- camps ONLY
      AND  EXISTS(SELECT 1 FROM event_ticket WHERE event_ticket.event_id = event.event_id); -- event with tickets ONLY



ALTER TABLE event_camp ADD COLUMN age_date TIMESTAMP WITHOUT TIME ZONE;

COMMENT ON COLUMN event_camp.age_date IS 'Allow to calculate participants age; Used for camp filtering';


DROP VIEW IF EXISTS v_swt_participant_age;

CREATE OR REPLACE VIEW v_swt_participant_age
AS
SELECT pt.purchase_id,
        CASE
            WHEN ec.name IS NULL THEN 'Ticket Type: ' || et.label  
            ELSE 'Camp: ' || ec.name
        END as name,
        COALESCE(ec.age_date, TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY')) as age_date,
        TO_DATE(tickets_additional->>'birthdate','MM/DD/YYYY') as player_bithdate,
        DATE_PART('year',AGE(
                        COALESCE(ec.age_date, TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY')),
                        CASE COALESCE(p.tickets_additional->>'birthdate','')
                                WHEN '' THEN COALESCE(ec.age_date, TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY'))
                                ELSE TO_DATE(p.tickets_additional->>'birthdate','MM/DD/YYYY')
                        END
                 )) :: int as age
FROM purchase_ticket pt JOIN purchase p ON pt.purchase_id = p.purchase_id 
        JOIN event e on p.event_id=e.event_id
        JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id
        LEFT JOIN event_camp ec on ec.event_camp_id = et.event_camp_id
UNION
SELECT pt.purchase_id,
        'Event: ' || e.long_name as name,
        TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY') as age_date,
        TO_DATE(tickets_additional->>'birthdate','MM/DD/YYYY') as player_bithdate,
        DATE_PART('year',AGE(
                        TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY'),
                        CASE COALESCE(p.tickets_additional->>'birthdate','')
                                WHEN '' THEN TO_DATE(e.tickets_settings->>'age_date','MM/DD/YYYY')
                                ELSE TO_DATE(p.tickets_additional->>'birthdate','MM/DD/YYYY')
                        END
                 )) :: int as age
FROM purchase_ticket pt JOIN purchase p ON pt.purchase_id = p.purchase_id 
        JOIN event e on p.event_id=e.event_id
        JOIN event_ticket et ON et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id; 



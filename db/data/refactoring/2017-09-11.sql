-- aem : adding data for head.referee

UPDATE email_template_group
SET variables = 
        variables ||
        jsonb_build_object('field',     'event_name',
                          'title',      'Event Name',
                          'pattern',    '{event_name}') ||
        jsonb_build_object('field',     'event_city',
                          'title',      'Event City',
                          'pattern',    '{event_city}') ||
        jsonb_build_object('field',     'event_website',
                          'title',      'Event Website',
                          'pattern',    '{event_website}') ||
        jsonb_build_object('field',     'event_email',
                          'title',      'Event Email',
                          'pattern',    '{event_email}') ||
        jsonb_build_object('field',     'event_month',
                          'title',      'Event Month',
                          'pattern',    '{event_month}') ||
        jsonb_build_object('field',     'official_first',
                          'title',      'Official First Name',
                          'pattern',    '{official_first}') ||
        jsonb_build_object('field',     'official_last',
                          'title',      'Official Last Name',
                          'pattern',    '{official_last}') ||
        jsonb_build_object('field',     'official_name',
                          'title',      'Official First and Last Name',
                          'pattern',    '{official_name}') ||
        jsonb_build_object('field',     'official_email',
                          'title',      'Official Email',
                          'pattern',    '{official_email}')                                
WHERE "group" = 'head.referee';


INSERT INTO email_template_type ("type", email_template_group, title, description, long_title, is_trigger, default_email_template_id)
VALUES ('official.withdrew', 'head.referee', 'Official Withdrew','<em>Official Withdrew</em>', 'Official Withdrew', TRUE, NULL);


DROP VIEW IF EXISTS v_head_referee_notification_receiver_data;

CREATE VIEW v_head_referee_notification_receiver_data
AS
(
    SELECT  eo.event_id,
            u.first, 
            u.last, 
            u.email, 
            u.phone_mob
    FROM event_official eo JOIN official o ON eo.official_id = o.official_id
        JOIN public.user u ON u.user_id = o.user_id   
    WHERE head_official = TRUE AND eo.is_email_notifications_receiver = TRUE
)
UNION
(
    SELECT e.event_id, u.first, u.last, COALESCE(e.email, u.email) as email, u.phone_mob
    FROM event e JOIN event_owner eo on e.event_owner_id = eo.event_owner_id
        JOIN public.user u ON u.user_id = eo.user_id    
);
/*
	NOTES: we have a constraint, that does not allow to have multiple "roster_athlete"
	rows for any athlete on event. 
	But, if athlete enters the event "as staff", we can have multiple rows for him/her. 
	To allow such logic we have to get around the constraint's restriction: 
		1. Change the type of "as_staff" field from BOOLEAN to INTEGER
		2. Add "as_staff" field to the constraint
		3. Create a sequence for "as_staff" field
		4. For the default entrance "as_staff" field will be always set 0 value, otherwise -
		 the next value of the sequence -> this way we have our constraint working for the 
			"default" entrance and we can enter athlete "as staff" as many time as we want
*/

BEGIN;

CREATE SEQUENCE "public"."roster_athlete_as_staff_sequence"
INCREMENT 1
MINVALUE 1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

COMMIT;

/* === */

BEGIN;

-- CHANGE "DEFAULT VALUE" OF "FIELD "as_staff" -----------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "as_staff" DROP DEFAULT;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "TYPE" OF "FIELD "as_staff" --------------------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "as_staff" TYPE INTEGER USING "as_staff"::INTEGER;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "DEFAULT VALUE" OF "FIELD "as_staff" -----------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "as_staff" SET DEFAULT 0;
-- -------------------------------------------------------------

COMMIT;

/* === */

BEGIN;

-- CHANGE "FIELDS" OF "UNIQUE "unique_athlete_on_event" --------
ALTER TABLE "public"."roster_athlete" DROP CONSTRAINT IF EXISTS "unique_athlete_on_event";
ALTER TABLE "public"."roster_athlete" ADD CONSTRAINT "unique_athlete_on_event" UNIQUE( "master_athlete_id", "event_id", "as_staff" );
-- -------------------------------------------------------------

COMMIT;






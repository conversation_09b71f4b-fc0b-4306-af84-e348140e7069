begin;

CREATE TRIGGER update_roster_athlete_modified
BEFORE UPDATE ON "public"."roster_athlete" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_roster_staff_modified
BEFORE UPDATE ON "public"."roster_staff" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

commit;

BEGIN;

-- CREATE FIELD "organization_code" ----------------------------
ALTER TABLE "public"."roster_athlete" ADD COLUMN "organization_code" Character Varying( 30 ) COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "birthdate" ------------------------------------
ALTER TABLE "public"."roster_athlete" ADD COLUMN "birthdate" Date;
-- -------------------------------------------------------------

-- CREATE FIELD "phonem" ---------------------------------------
ALTER TABLE "public"."roster_athlete" ADD COLUMN "phonem" Character Varying( 30 ) COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

COMMIT;

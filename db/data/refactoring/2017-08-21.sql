/**
 * NOTE: "v_aem_admin_templates" depends on this view, so need to DROP it first, before dropping 
 * this one
 */

DROP VIEW IF EXISTS "public"."v_aem_manual_mailing_templates";
CREATE OR REPLACE VIEW "public"."v_aem_manual_mailing_templates"
    AS (
        SELECT 

            ett."type" "type_id",
            ett."title" "type_title",

            et."title", 
            et."email_template_id" "id",
            et."is_valid",

            (et."email_template_id" = ett."default_email_template_id") "is_default",

            et."published",

            (et."event_id" IS NULL AND et."event_owner_id" IS NOT NULL) "all_events",
                            
            FALSE "is_in_use",
            NULL "usage_qty",

            ett."is_trigger",

            /* new fields */
            et."img_name",
            et."event_id",
            et."event_owner_id",
            et."email_template_group" "group",
            etg."title" "group_title"

        FROM "email_template_type" ett 
        INNER JOIN "email_template" et 
            ON et."email_template_type"     = ett."type"
            AND et."deleted" IS NULL
        INNER JOIN "email_template_group" etg 
            ON ett."email_template_group" = etg."group"
            AND et."email_template_group" = etg."group"
        WHERE ett."is_trigger" IS NOT TRUE 
            AND ett."type" = 'content'
    );

BEGIN;

-- <PERSON><PERSON>GE "COMMENT" OF "FIELD "teams_entry_sw_fee" -------------
COMMENT ON COLUMN "public"."event"."teams_entry_sw_fee" IS 'see tickets_sw_fee field for details';
-- -------------------------------------------------------------

-- CHANGE "TYPE" OF "FIELD "teams_entry_sw_fee" ----------------
ALTER TABLE "public"."event" ALTER COLUMN "teams_entry_sw_fee" TYPE Numeric;
-- -------------------------------------------------------------

-- CREATE FIELD "teams_sw_extra_fee" ---------------------------
ALTER TABLE "public"."event" ADD COLUMN "teams_sw_extra_fee" Numeric;COMMENT ON COLUMN "public"."event"."teams_sw_extra_fee" IS 'see tickets_sw_extra_fee field description for details';
-- -------------------------------------------------------------

-- CREATE FIELD "teams_sw_balance" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "teams_sw_balance" Numeric DEFAULT 0;COMMENT ON COLUMN "public"."event"."teams_sw_balance" IS 'see tickets_sw_balance field description for details';
-- -------------------------------------------------------------

-- CREATE FIELD "teams_sw_target_balance" ----------------------
ALTER TABLE "public"."event" ADD COLUMN "teams_sw_target_balance" Numeric;COMMENT ON COLUMN "public"."event"."teams_sw_target_balance" IS 'see tickets_sw_target_balance field for details';
-- -------------------------------------------------------------

-- CREATE FIELD "sw_fee" ---------------------------------------
ALTER TABLE "public"."purchase_team" ADD COLUMN "sw_fee" Numeric DEFAULT 0;COMMENT ON COLUMN "public"."purchase_team"."sw_fee" IS 'SportWrench team registration fee';
-- -------------------------------------------------------------

COMMIT;

BEGIN;


---- Create "update_wristbands_count" function --------------------
CREATE OR REPLACE FUNCTION update_wristbands_count()
  RETURNS TRIGGER AS
$update_wristbands_count$
DECLARE
  sql_all     TEXT;
  sql_staff   TEXT;
  tname       TEXT;
  sql_query   TEXT;
BEGIN
  -- Query updates roster_team wristbands_count_athletes and wristbands_count_staff fields
  sql_all := 'UPDATE roster_team "rt"
              SET wristbands_count_staff  = (
                SELECT count
                FROM v_wristbands_for_staff_count v
                WHERE v.roster_team_id = rt.roster_team_id
              ),
                wristbands_count_athletes = (
                  SELECT count
                  FROM v_wristbands_for_athletes_count v
                  WHERE v.roster_team_id = rt.roster_team_id
                )
              WHERE rt.roster_team_id = $1
                    AND rt."deleted" IS NULL
                    AND EXISTS(
                        SELECT 1 FROM event e
                        WHERE e.event_id = rt.event_id
                            AND e."date_end" > NOW() AT TIME ZONE e."timezone"
                            AND e."deleted" IS NULL
                    )';

  -- Query updates only roster_team wristbands_count_staff fields
  sql_staff := 'UPDATE roster_team "rt"
                SET wristbands_count_staff = (
                  SELECT count FROM v_wristbands_for_staff_count v WHERE v.roster_team_id = rt.roster_team_id
                  )
                WHERE rt.roster_team_id = $1
                        AND rt."deleted" IS NULL
                        AND EXISTS(
                            SELECT 1 FROM event e
                            WHERE e.event_id = rt.event_id
                                AND e."date_end" > NOW() AT TIME ZONE e."timezone"
                                AND e."deleted" IS NULL
                        )';


  tname := TG_TABLE_NAME;

  IF tname = 'roster_athlete'
  THEN
    sql_query := sql_all;
  ELSIF tname = 'roster_staff_role'
    THEN
      sql_query := sql_staff;
  END IF;

  IF (TG_OP = 'DELETE')
  THEN
    IF OLD.roster_team_id IS NOT NULL
    THEN
      EXECUTE sql_query
      USING OLD.roster_team_id;
    END IF;

  ELSIF (TG_OP = 'UPDATE')
    THEN
      IF NEW.roster_team_id IS NOT NULL
      THEN
        EXECUTE sql_query
        USING NEW.roster_team_id;
      END IF;

      IF OLD.roster_team_id IS NOT NULL
         AND NEW.roster_team_id <> OLD.roster_team_id OR NEW.roster_team_id IS NULL
      THEN
        EXECUTE sql_query
        USING OLD.roster_team_id;
      END IF;

  ELSIF (TG_OP = 'INSERT')
    THEN
      EXECUTE sql_query
      USING NEW.roster_team_id;
  END IF;

  RETURN NEW;
END;
$update_wristbands_count$
LANGUAGE plpgsql;
---------------------------------------------------------------


---- Create "update_staff_wristbands_count" trigger -----------
CREATE TRIGGER update_staff_wristbands_count
AFTER INSERT OR UPDATE OR DELETE ON roster_staff_role
FOR EACH ROW EXECUTE PROCEDURE update_wristbands_count();
---------------------------------------------------------------


---- Create "update_athlete_wristbands_count" trigger ---------
CREATE TRIGGER update_athlete_wristbands_count
AFTER INSERT OR UPDATE OR DELETE ON roster_athlete
FOR EACH ROW EXECUTE PROCEDURE update_wristbands_count();
---------------------------------------------------------------


---- Create "update_wristbands_count_master" function ---------
CREATE OR REPLACE FUNCTION update_wristbands_count_master()
  RETURNS TRIGGER AS
$update_wristbands_count_master$
DECLARE
  sql_staff TEXT;
BEGIN
  sql_staff := 'WITH "teams" AS (
                  SELECT
                    rt.roster_team_id
                  FROM roster_team rt
                    LEFT JOIN master_staff_role msr
                      ON rt.master_team_id = msr.master_team_id
                    LEFT JOIN event e
                      ON e.event_id = rt.event_id
                  WHERE msr.master_staff_id = $1
                        AND e."date_end" > NOW() AT TIME ZONE e."timezone"
                        AND e."deleted" IS NULL
                        AND rt."deleted" IS NULL
                        AND rt."locked" IS NOT TRUE
                )
                UPDATE roster_team "rt"
                SET wristbands_count_staff = (
                  SELECT count
                  FROM v_wristbands_for_staff_count v
                  WHERE v.roster_team_id = rt.roster_team_id
                )
                WHERE rt.roster_team_id IN(SELECT * FROM teams)';

  IF NEW.primary IS NOT NULL AND NEW.primary <> OLD.primary
  THEN
    EXECUTE sql_staff
    USING OLD.master_staff_id;
  END IF;

  RETURN NEW;
END;
$update_wristbands_count_master$
LANGUAGE plpgsql;
-------------------------------------------------------------------------


---- Create "update_master_staff_role_wristbands_count" trigger ---------
CREATE TRIGGER update_master_staff_role_wristbands_count
AFTER UPDATE ON master_staff_role
FOR EACH ROW EXECUTE PROCEDURE update_wristbands_count_master();
-------------------------------------------------------------------------


COMMIT;

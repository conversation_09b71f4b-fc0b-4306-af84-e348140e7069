BEGIN;

---
--- Adding User ID of creator
---

-- CREATE FIELD "user_id" --------------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "user_id" INTEGER;
COMMENT ON COLUMN "public"."purchase"."user_id" IS 'User ID of row creator';
-- -------------------------------------------------------------

---
--- Booth label for Exhibior items
---

-- CREATE FIELD "booth_label" ----------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "booth_label" CHARACTER VARYING( 10 );
COMMENT ON COLUMN "public"."purchase"."booth_label" IS 'Booth number/label';
-- -------------------------------------------------------------;

---
--- Additional field to cancel purchase rows (invoices)
---

-- CREATE FIELD "canceled_date" --------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "canceled_date" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."purchase"."canceled_date" IS 'Has timestamp if purchase was canceled';
-- -------------------------------------------------------------;

COMMIT;

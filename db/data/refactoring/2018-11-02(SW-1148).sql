
/**
 * "common_item" table 
 * Stores items of different types that can be used for different purposes in the project. 
 * For now, we store staff/official clothes types in there.
 */
BEGIN;

CREATE TABLE "public"."common_item" ( 
    "common_item_id" Text NOT NULL,
    "title" Text NOT NULL,
    "item_type" Text NOT NULL,
    "details" JSONB DEFAULT '{}'::JSON<PERSON>,
    "created" Timestamp Without Time Zone DEFAULT NOW(),
    "modified" Timestamp Without Time Zone DEFAULT NOW(),
    PRIMARY KEY ("item_type", "common_item_id")
);


COMMENT ON COLUMN "public"."common_item"."item_type" IS 'As the table can store items serving for different purposes, this column shows a type of an item that is stored, e.g. ''clothes''';


COMMENT ON COLUMN "public"."common_item"."details" IS 'Extra info about an item';


CREATE INDEX "common_item_type_index" ON "public"."common_item" USING btree( "item_type" Asc NULLS Last );



CREATE TRIGGER update_common_item_modified
BEFORE UPDATE ON "public"."common_item" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();


INSERT INTO "public"."common_item" ("item_type", "common_item_id", "title", "details")
VALUES 
('event_clothes'      , 'shirt'                 , 'Shirt'                   , '{"gender":"any","order":1,"size_type":"international"}'),
('event_clothes'      , 'shoes'                 , 'Shoes'                   , '{"gender":"any","order":2,"size_type":"shoes_inches"}'),
('event_clothes'      , 'jacket'                , 'Jacket'                  , '{"gender":"any","order":3,"size_type":"international"}'),
('event_clothes'      , 'short_sleeve_tshirt'   , 'Short Sleeve T-Shirt'    , '{"gender":"any","order":4,"size_type":"international"}'),
('event_clothes'      , 'long_sleeve_tshirt'    , 'Long Sleeve T-Shirt'     , '{"gender":"any","order":5,"size_type":"international"}'),
('event_clothes'      , 'polo_shirt'            , 'Polo Shirt'              , '{"gender":"any","order":6,"size_type":"international"}'),
('event_clothes'      , 'hoodie'                , 'Hoodie'                  , '{"gender":"any","order":7,"size_type":"international"}'),
('event_clothes'      , 'hat'                   , 'Hat'                     , '{"gender":"any","order":8,"size_type":"international"}'),
('event_clothes'      , 'shorts'                , 'Shorts'                  , '{"gender":"any","order":9,"size_type":"international"}');

COMMIT;



/**
 * "event_clothes" table stores types of clothes that are required by an event
 */
BEGIN;

CREATE TABLE "public"."event_clothes" (
    "event_clothes_id" Serial NOT NULL,
    "common_item_id" Text NOT NULL,
    "event_id" Integer NOT NULL,
    "gender" Text NOT NULL,
    "member_type" Text NOT NULL,
    "deleted" Timestamp Without Time Zone,
    "created" Timestamp Without Time Zone DEFAULT NOW(),
    "modified" Timestamp Without Time Zone DEFAULT NOW(),
    PRIMARY KEY ("event_clothes_id"),
    CONSTRAINT "event_clothes_unique" UNIQUE("event_id", "gender", "common_item_id", "member_type")
);


COMMENT ON COLUMN "public"."event_clothes"."gender" IS 'Can be ''male'' or ''female''';


COMMENT ON COLUMN "public"."event_clothes"."member_type" IS 'can be ''staff'' or ''official''';


CREATE INDEX "index_member_type" ON "public"."event_clothes" USING btree( "member_type" );
CREATE INDEX "index_gender" ON "public"."event_clothes" USING btree( "gender" );

CREATE TRIGGER update_event_clothes_modified
BEFORE UPDATE ON "public"."event_clothes" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();


COMMIT;



/**
 * "official_clothes_size" table stores clothing sizes of staff/official
 */
BEGIN;


CREATE TABLE "public"."official_clothes_size" (
    "official_clothes_size_id" Serial NOT NULL,
    "official_id" Integer NOT NULL,
    "common_item_id" Text NOT NULL,
    "size" Text NOT NULL,
    "created" Timestamp Without Time Zone DEFAULT NOW(),
    "modified" Timestamp Without Time Zone DEFAULT NOW(),
    PRIMARY KEY("official_clothes_size_id")
);

CREATE INDEX "index_common_item_id" ON "public"."official_clothes_size" USING btree( "common_item_id" );


CREATE TRIGGER update_official_clothes_size_modified
BEFORE UPDATE ON "public"."official_clothes_size" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();



COMMIT;


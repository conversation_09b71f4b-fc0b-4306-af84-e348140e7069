BEGIN;

-- CREATE table for custom payments ----------------------------------------------------------------------------
CREATE TABLE public.custom_payment_item
(
    custom_payment_item_id SERIAL PRIMARY KEY NOT NULL,
    created TIMESTAMP DEFAULT now() NOT NULL,
    modified TIMESTAMP,
    title TEXT NOT NULL,
    description TEXT,
    price NUMERIC(10,2),
    visible BOOLEAN DEFAULT TRUE NOT NULL,
    payment_page TEXT DEFAULT 'coalition',
    sort_order INT
);
COMMENT ON COLUMN public.custom_payment_item.title IS 'Short item title (for selects, etc)';
COMMENT ON COLUMN public.custom_payment_item.description IS 'Item description';
COMMENT ON COLUMN public.custom_payment_item.price IS 'Item price. If NULL - purchaser can define custom price.';
COMMENT ON COLUMN public.custom_payment_item.visible IS 'Item visibility flag';
COMMENT ON COLUMN public.custom_payment_item.payment_page IS 'Field to group items by payment page, like ''coalition'', etc.';
COMMENT ON COLUMN public.custom_payment_item.sort_order IS 'Sorting order for items';
COMMENT ON TABLE public.custom_payment_item IS 'Items for simple custom payment page to stripe account';
-----------------------------------------------------------------------------------------------------------------

COMMIT;

/**
 * Ad<PERSON> can see:
 * · default trigger templates, 
 * · not assigned (to any EO/EVENT) trigger templates, 
 * · not assigned (to any EO/EVENT) manual mailing templates
 * · basic layouts
 */

DROP VIEW IF EXISTS "public"."v_aem_admin_templates";

CREATE OR REPLACE VIEW "public"."v_aem_admin_templates" 
    AS (
        SELECT * FROM "v_aem_trigger_templates" "t" 
        WHERE (
            "t"."is_default" IS TRUE 
            OR ("t"."event_id" IS NULL AND "t"."event_owner_id" IS NULL)
        )

        UNION ALL 

        SELECT * FROM "v_aem_manual_mailing_templates" "m" 
        WHERE "m"."event_id" IS NULL 
            AND "m"."event_owner_id" IS NULL

        UNION ALL 

        SELECT * FROM "v_aem_basic_layouts"
    );



/**
 * add sort order to event_camp
 */

ALTER TABLE event_camp ADD COLUMN sort_order NUMERIC;



WITH event_camp_ordered AS (
           SELECT event_camp_id,
                  ROW_NUMBER() OVER(
                                PARTITION BY event_id
                                ORDER BY event_camp_id) as "sort_order"
           FROM event_camp)
UPDATE event_camp AS ec
SET sort_order = eco.sort_order
FROM event_camp_ordered AS eco
WHERE ec.event_camp_id = eco.event_camp_id;

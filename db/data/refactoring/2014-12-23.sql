BEGIN;


-- CREATE TABLE "webpoint_queue" -------------------------------
CREATE TABLE "public"."webpoint_queue" ( 
    "webpoint_queue_id" Serial NOT NULL, 
    "created" Timestamp With Time Zone DEFAULT now() NOT NULL, 
    "modified" Timestamp With Time Zone DEFAULT now() NOT NULL, 
    "master_club_id" Integer NOT NULL, 
    "requested" Timestamp Without Time Zone, 
    "response_body" Text, 
    "responded" Timestamp Without Time Zone
, CONSTRAINT "unique_webpoint_queue_id" UNIQUE( "webpoint_queue_id" ) );
CREATE INDEX "index_master_club_id1" ON "public"."webpoint_queue" USING btree( "master_club_id" );


CREATE INDEX "index_created3" ON "public"."webpoint_queue" USING btree( "created" );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE FIELD "webpoint_username" ----------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "webpoint_username" Character Varying( 100 );
-- -------------------------------------------------------------

-- CREATE FIELD "webpoint_password" ----------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "webpoint_password" Character Varying( 100 );
-- -------------------------------------------------------------;

COMMIT;

-- NEW TABLE "EVENT_TEAM_CHECKIN" --

BEGIN;

CREATE SEQUENCE "public"."event_team_check_in_id_seq"
INCREMENT 1
MINVALUE 1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

COMMIT;

BEGIN;

-- CREATE TABLE "event_team_checkin" ---------------------------
CREATE TABLE "public"."event_team_checkin" ( 
	"event_team_checkin_id" Integer DEFAULT nextval('event_team_check_in_id_seq'::regclass) NOT NULL,
	"event_id" Integer NOT NULL,
	"roster_team_id" Integer NOT NULL,
	"master_staff_id" Integer NOT NULL,
	"history" JSON,
	CONSTRAINT "unique_event_team_checkin_id" UNIQUE( "event_team_checkin_id" ) );
 
CREATE INDEX "index_event_id7" ON "public"."event_team_checkin" USING btree( "event_id" );


CREATE INDEX "index_roster_team_id5" ON "public"."event_team_checkin" USING btree( "roster_team_id" );


CREATE INDEX "index_master_staff_id" ON "public"."event_team_checkin" USING btree( "master_staff_id" );

COMMENT ON TABLE  "public"."event_team_checkin" IS 'The table for online event teams check in.';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "created" --------------------------------------
ALTER TABLE "public"."event_team_checkin" ADD COLUMN "created" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

-- CREATE FIELD "modified" -------------------------------------
ALTER TABLE "public"."event_team_checkin" ADD COLUMN "modified" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

CREATE TRIGGER update_online_checkin_modified
BEFORE UPDATE ON "public"."event_team_checkin" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;

--- MASTER_STAFF ---

BEGIN;

-- CREATE FIELD "checkin_barcode" ------------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "checkin_barcode" Text COLLATE "pg_catalog"."default";COMMENT ON COLUMN "public"."master_staff"."checkin_barcode" IS 'A barcode for online event teams checkin procedure';
-- -------------------------------------------------------------

COMMIT;

--- ROSTER_TEAM ---

BEGIN;

-- CREATE FIELD "online_checkin_date" --------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "online_checkin_date" Timestamp Without Time Zone;COMMENT ON COLUMN "public"."roster_team"."online_checkin_date" IS 'The datetime when the team was checked for online checkin procedure ';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- DROP FIELD "history" ----------------------------------------
ALTER TABLE "public"."event_team_checkin" DROP COLUMN "history";
-- -------------------------------------------------------------

COMMIT;

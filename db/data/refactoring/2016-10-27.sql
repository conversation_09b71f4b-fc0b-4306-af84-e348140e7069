BEGIN;

-- <PERSON><PERSON><PERSON> "NULLABLE" OF "FIELD "recipient_type" ----------------
ALTER TABLE "public"."email_template" ALTER COLUMN "recipient_type" DROP NOT NULL;
-- -------------------------------------------------------------

COMMIT;

-- Creating new WebPoint columns

-- In master_staff table
ALTER TABLE public.master_staff ADD ref_cert_name TEXT NULL;
COMMENT ON COLUMN public.master_staff.ref_cert_name IS 'Level of *indoor officials* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.master_staff ADD ref_end_date TEXT NULL;
COMMENT ON COLUMN public.master_staff.ref_end_date IS 'End date through which the certification listed is valid';
ALTER TABLE public.master_staff ADD score_cert_name TEXT NULL;
COMMENT ON COLUMN public.master_staff.score_cert_name IS 'Level of *scoring* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.master_staff ADD score_end_date TEXT NULL;
COMMENT ON COLUMN public.master_staff.score_end_date IS 'End date through which the certification listed is valid';
ALTER TABLE public.master_staff ADD bch_ref_cert_name TEXT NULL;
COMMENT ON COLUMN public.master_staff.bch_ref_cert_name IS 'Level of *beach officials* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.master_staff ADD bch_ref_end_date TEXT NULL;
COMMENT ON COLUMN public.master_staff.bch_ref_end_date IS 'End date through which the certification listed is valid';

-- In webpoint_adult table
ALTER TABLE public.webpoint_adult ADD ref_cert_name TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.ref_cert_name IS 'Level of *indoor officials* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.webpoint_adult ADD ref_end_date TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.ref_end_date IS 'End date through which the certification listed is valid';
ALTER TABLE public.webpoint_adult ADD score_cert_name TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.score_cert_name IS 'Level of *scoring* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.webpoint_adult ADD score_end_date TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.score_end_date IS 'End date through which the certification listed is valid';
ALTER TABLE public.webpoint_adult ADD bch_ref_cert_name TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.bch_ref_cert_name IS 'Level of *beach officials* certification of the official (National, Jr. National, regional, etc)';
ALTER TABLE public.webpoint_adult ADD bch_ref_end_date TEXT NULL;
COMMENT ON COLUMN public.webpoint_adult.bch_ref_end_date IS 'End date through which the certification listed is valid';

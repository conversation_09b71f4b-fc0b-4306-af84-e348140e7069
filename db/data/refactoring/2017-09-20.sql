BEGIN;

-- CREATE FIELD "stripe_payment_id" ----------------------------
ALTER TABLE "public"."stripe_charge" ADD COLUMN "stripe_payment_id" Text;

COMMENT ON COLUMN "public"."stripe_charge"."stripe_payment_id" IS 'The ID of the payment that the destination account received for the transfer. (for charges via Stripe Connect only). 
Has a format py_***';
-- -------------------------------------------------------------

COMMIT;


/* === */

UPDATE "stripe_charge"
SET "stripe_payment_id" = NULLIF("balance_transaction"->'sourced_transfers'->'data'->'0'->>'destination_payment', '')
WHERE "stripe_payment_id" IS NULL

/**
 * NOTE: we might add a contraint: all the rows that have 'connect' in the "type" column must have filled "stripe_payment_id"
 */

BEGIN;


-- CREATE TABLE "event_ticket" ---------------------------------
CREATE TABLE "public"."event_ticket" ( 
    "event_id" Integer NOT NULL, 
    "label" Character Varying( 100 ) NOT NULL, 
    "initial_price" Numeric NOT NULL, 
    "initial_date" Timestamp Without Time Zone NOT NULL, 
    "prices" JSON, 
    "event_ticket_id" Integer NOT NULL UNIQUE
 );
CREATE INDEX "index_event_ticket_id" ON "public"."event_ticket" USING btree( "event_ticket_id" );
-- -------------------------------------------------------------;

COMMIT;


CREATE SEQUENCE "public"."event_ticket_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 9223372036854775807
START 0
CACHE 1
;


BEGIN;


-- CHANGE "DEFAULT VALUE" OF "FIELD "event_ticket_id" ----------
ALTER TABLE "public"."event_ticket" ALTER COLUMN "event_ticket_id" SET DEFAULT nextval('event_ticket_id_seq'::regclass);
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE FIELD "created" --------------------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "created" Timestamp Without Time Zone DEFAULT now() NOT NULL;
-- -------------------------------------------------------------

-- CREATE FIELD "modified" -------------------------------------
ALTER TABLE "public"."event_ticket" ADD COLUMN "modified" Timestamp Without Time Zone DEFAULT now() NOT NULL;
-- -------------------------------------------------------------;

COMMIT;

---
--- Adding audit to most important tables
--- https://wiki.postgresql.org/wiki/Audit_trigger_91plus
---

SELECT audit.audit_table('"roster_team"');
SELECT audit.audit_table('"master_team"');
SELECT audit.audit_table('"roster_club"');
SELECT audit.audit_table('"master_club"');
SELECT audit.audit_table('"master_club_sanctioning"');
SELECT audit.audit_table('"master_club_sport_variation"');
SELECT audit.audit_table('"user"'); --- I had to rename table to "user_" then attach audit to it and then rename back to "user"
SELECT audit.audit_table('"event"');
SELECT audit.audit_table('"event_booth"');
SELECT audit.audit_table('"event_location"');
SELECT audit.audit_table('"purchase"');
SELECT audit.audit_table('"purchase_team"');
SELECT audit.audit_table('"purchase_booth"');
SELECT audit.audit_table('"ths_booking"');
SELECT audit.audit_table('"ths_history"');
SELECT audit.audit_table('"sponsor"');

BEGIN;

-- CREATE FIELD "created" --------------------------------------
ALTER TABLE "public"."ticket_discount" ADD COLUMN "created" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

-- CREATE FIELD "modified" -------------------------------------
ALTER TABLE "public"."ticket_discount" ADD COLUMN "modified" Timestamp Without Time Zone DEFAULT now();
-- -------------------------------------------------------------

COMMIT;


BEGIN;

CREATE TRIGGER update_ticket_discount_modified
BEFORE UPDATE ON "public"."ticket_discount" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;


BEGIN;

-- CHANGE "FIELDS" OF "UNIQUE "unique_discount_per_user" -------
ALTER TABLE "public"."ticket_discount" DROP CONSTRAINT IF EXISTS "unique_discount_per_user";
ALTER TABLE "public"."ticket_discount" ADD CONSTRAINT "unique_discount_per_user" UNIQUE( "email", "event_id", "event_ticket_id", "last" );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE TYPE "payer_option" ----------------------------------
CREATE TYPE "public"."payer_option" AS Enum( 'seller', 'buyer' );

COMMENT ON TYPE  "public"."payer_option" IS 'Determine who is a payer (of fee, service fee, etc.):
"seller" - E<PERSON> (the one who sells some items on SW), "buyer" - the person who buys smth on SW (e.g. tickets purhaser or teams entry payer)';
-- -------------------------------------------------------------

-- CREATE FIELD "stripe_tickets_percent" -----------------------
ALTER TABLE "public"."event" ADD COLUMN "stripe_tickets_percent" Numeric DEFAULT '0';COMMENT ON COLUMN "public"."event"."stripe_tickets_percent" IS 'The percent of Stripe Transaction fee (amount of charge +  amount of charge * this field value)';
-- -------------------------------------------------------------

-- CREATE FIELD "stripe_tickets_transaction" -------------------
ALTER TABLE "public"."event" ADD COLUMN "stripe_tickets_transaction" Numeric DEFAULT '0';COMMENT ON COLUMN "public"."event"."stripe_tickets_transaction" IS 'Stripe transaction fee: charge amount + this field value';
-- -------------------------------------------------------------

-- CREATE FIELD "stripe_tickets_fee_payer" ---------------------
ALTER TABLE "public"."event" ADD COLUMN "stripe_tickets_fee_payer" "public"."payer_option";COMMENT ON COLUMN "public"."event"."stripe_tickets_fee_payer" IS 'Who pays Stripe fee';
-- -------------------------------------------------------------

-- CREATE FIELD "tickets_sw_fee_payer" -------------------------
ALTER TABLE "public"."event" ADD COLUMN "tickets_sw_fee_payer" "public"."payer_option";COMMENT ON COLUMN "public"."event"."tickets_sw_fee_payer" IS 'Who pays SW fee';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CHANGE "NAME" OF "FIELD "stripe_tickets_transaction" --------
ALTER TABLE "public"."event" RENAME COLUMN "stripe_tickets_transaction" TO "stripe_tickets_fixed";
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "DEFAULT VALUE" OF "FIELD "stripe_tickets_fee_payer" -
ALTER TABLE "public"."event" ALTER COLUMN "stripe_tickets_fee_payer" SET DEFAULT 'seller';
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "tickets_sw_fee_payer" -----
ALTER TABLE "public"."event" ALTER COLUMN "tickets_sw_fee_payer" SET DEFAULT 'seller';
-- -------------------------------------------------------------

COMMIT;


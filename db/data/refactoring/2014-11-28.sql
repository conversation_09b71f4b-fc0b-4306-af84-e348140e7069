---
--- Adding new fields to sponsor table
---

BEGIN;

-- CREATE FIELD "website_url" ----------------------------------
ALTER TABLE "public"."sponsor" ADD COLUMN "website_url" Character Varying( 500 );
COMMENT ON COLUMN "public"."sponsor"."website_url" IS 'Website';
-- -------------------------------------------------------------

-- CREATE FIELD "sponsor_title" --------------------------------
ALTER TABLE "public"."sponsor" ADD COLUMN "sponsor_title" Character Varying( 500 );
COMMENT ON COLUMN "public"."sponsor"."sponsor_title" IS 'Title';
-- -------------------------------------------------------------

-- CREATE FIELD "samples_food" ---------------------------------
ALTER TABLE "public"."sponsor" ADD COLUMN "samples_food" Boolean;
COMMENT ON COLUMN "public"."sponsor"."samples_food" IS 'Company Samples Food';
-- -------------------------------------------------------------

-- CREATE FIELD "samples_beverages" ----------------------------
ALTER TABLE "public"."sponsor" ADD COLUMN "samples_beverages" Boolean;
COMMENT ON COLUMN "public"."sponsor"."samples_beverages" IS 'Company Samples Beverages';
-- -------------------------------------------------------------

COMMIT;



---
--- Adding fields to purchase_booth
----

BEGIN;

-- CREATE FIELD "booth_label" ----------------------------------
ALTER TABLE "public"."purchase_booth" ADD COLUMN "booth_label" CHARACTER VARYING( 10 );
COMMENT ON COLUMN "public"."purchase_booth"."booth_label" IS 'Booth Number/Label';
-- -------------------------------------------------------------

-- CREATE FIELD "notes" ----------------------------------------
ALTER TABLE "public"."purchase_booth" ADD COLUMN "notes" CHARACTER VARYING( 2044 );
COMMENT ON COLUMN "public"."purchase_booth"."notes" IS 'Notes';
-- -------------------------------------------------------------;

COMMIT;

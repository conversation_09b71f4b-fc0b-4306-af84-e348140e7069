BEGIN;

/**
 * Drop dependent view
*/
DROP VIEW IF EXISTS "public"."v_aem_admin_templates";


/**
 * Fix "usage_qty" - casting NULL to BIGINT
 */
DROP VIEW IF EXISTS "public"."v_aem_manual_mailing_templates";
CREATE OR REPLACE VIEW "public"."v_aem_manual_mailing_templates"
    AS (
        SELECT 

            ett."type" "type_id",
            ett."title" "type_title",

            et."title", 
            et."email_template_id" "id",
            et."is_valid",

            (et."email_template_id" = ett."default_email_template_id") "is_default",

            et."published",

            (et."event_id" IS NULL AND et."event_owner_id" IS NOT NULL) "all_events",
                            
            FALSE "is_in_use",
            NULL::BIGINT "usage_qty",

            ett."is_trigger",

            /* new fields */
            et."img_name",
            et."event_id",
            et."event_owner_id",
            et."email_template_group" "group",
            etg."title" "group_title"

        FROM "email_template_type" ett 
        INNER JOIN "email_template" et 
            ON et."email_template_type"     = ett."type"
            AND et."deleted" IS NULL
        INNER JOIN "email_template_group" etg 
            ON ett."email_template_group" = etg."group"
            AND et."email_template_group" = etg."group"
        WHERE ett."is_trigger" IS NOT TRUE 
            AND ett."type" = 'content'
    );


/**
 Fix:
    "usage_qty" - casting NULL to BIGINT
    "type_id" - casting NULL to TEXT
    "type_title" - casting NULL to TEXT
    "group_title" - cassting NULL to TEXT
*/
DROP VIEW IF EXISTS "public"."v_aem_basic_layouts";
CREATE OR REPLACE VIEW "public"."v_aem_basic_layouts"
    AS (
        SELECT 

            null::TEXT "type_id",
            null::TEXT "type_title",

            et."title", 
            et."email_template_id" "id",
            et."is_valid",

            FALSE "is_default",

            et."published",

            FALSE "all_events",

            TRUE "is_in_use",
            NULL::BIGINT "usage_qty",

            FALSE "is_trigger",

            /* new fields */
            et."img_name",
            et."event_id",
            et."event_owner_id",
            et."email_template_group" "group",
            NULL::TEXT "group_title"

        FROM "email_template" et 
        WHERE et."email_template_type" = 'layout'
            AND et."deleted" IS NULL
    );

CREATE OR REPLACE VIEW "public"."v_aem_admin_templates" 
    AS (
        SELECT * FROM "v_aem_trigger_templates" "t" 
        WHERE (
            "t"."is_default" IS TRUE 
            OR ("t"."event_id" IS NULL AND "t"."event_owner_id" IS NULL)
        )

        UNION ALL 

        SELECT * FROM "v_aem_manual_mailing_templates" "m" 
        WHERE "m"."event_id" IS NULL 
            AND "m"."event_owner_id" IS NULL

        UNION ALL 

        SELECT * FROM "v_aem_basic_layouts"
    );



COMMIT;
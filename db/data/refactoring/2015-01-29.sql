

-- CHANGE "PRIMARY KEY" OF "TABLE "webpoint_parse" -------------
ALTER TABLE "public"."webpoint_parse" ADD PRIMARY KEY( "webpoint_parse_id" );
-- -------------------------------------------------------------

-- CHANGE "PRIMARY KEY" OF "TABLE "webpoint_athlete" -----------
ALTER TABLE "public"."webpoint_athlete" ADD PRIMARY KEY( "webpoint_athlete_id" );
-- -------------------------------------------------------------;

-- CHANGE "PRIMARY KEY" OF "TABLE "webpoint_adult" -------------
ALTER TABLE "public"."webpoint_adult" ADD PRIMARY KEY( "webpoint_adult_id" );
-- -------------------------------------------------------------;


--- Cleaning up clubs with wrong staff data from webpoint: http://take.ms/IPNmf
DELETE FROM master_staff
WHERE master_club_id IN
(
SELECT DISTINCT master_club_id
FROM master_staff ms
WHERE ms.organization_code IN
(
SELECT organization_code
FROM master_staff
WHERE organization_code IS NOT NULL
GROUP BY organization_code
HAVING count(master_staff_id) > 1
)
AND master_team_id IS NULL
) AND master_team_id IS NULL



--- TODO: Rename column master_team_id in master_staff table - to avoid it's usage

--- TODO: Update height values in master_athlete
--- TODO: Drop height_int column in master_athlete


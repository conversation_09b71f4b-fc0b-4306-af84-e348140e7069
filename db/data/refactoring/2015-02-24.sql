--- Adding flag score_entry_allowed to control score entry feature

BEGIN;

-- CREATE FIELD "score_entry_allowed" --------------------------
ALTER TABLE "public"."event" ADD COLUMN "score_entry_allowed" BOOLEAN;
-- -------------------------------------------------------------;

COMMIT;

UPDATE "event" SET score_entry_allowed = FALSE;


--- Adding demo event row with event id = 1
INSERT INTO "event"(
    "event_id",
    "created",
    "modified",
    "name",
    "long_name",
    "date_start",
    "date_end",
    "event_owner_id",
    "sport_id",
    "has_status_housing",
    "has_status_roster",
    "status",
    "sport_sanctioning_id",
    "country",
    "city",
    "zip",
    "state",
    "address",
    "event_class_id",
    "date_reg_open",
    "late_reg_date",
    "date_reg_close",
    "enter_req_roster",
    "accept_req_roster",
    "roster_deadline",
    "sport_variation_id",
    "host",
    "location",
    "website",
    "email",
    "has_male_teams",
    "has_female_teams",
    "has_coed_teams",
    "region",
    "late_reg_penalty",
    "reg_fee",
    "stripe_statement",
    "payment_name",
    "payment_address",
    "payment_city",
    "payment_state",
    "payment_zip",
    "payment_country",
    "mincount_enter",
    "mincount_accept",
    "has_late_reg",
    "housing_company_id",
    "custom_housing_company",
    "hosting_org_name",
    "hosting_org_address",
    "hosting_org_city",
    "hosting_org_state",
    "hosting_org_zip",
    "hosting_org_phone",
    "event_notes",
    "published",
    "stripe_publishable_key",
    "stripe_secret_key",
    "stripe_acc_email",
    "stripe_date_modified",
    "stripe_acc_name",
    "stripe_acc_statement",
    "stripe_acc_id",
    "credit_surcharge",
    "housing_nights_required",
    "housing_local_teams_distance",
    "housing_nights_threshold",
    "sales_manager_id",
    "show_teams_entered",
    "notify_frequency",
    "notify_emails",
    "rules_website",
    "has_officials",
    "officials_meeting_datetime",
    "officials_meeting_venue",
    "officials_additional_info",
    "official_applied_email_template_id",
    "official_accepted_email_template_id",
    "official_waitlisted_email_template_id",
    "official_declined_email_template_id",
    "timezone",
    "has_rosters",
    "remote_sync_allowed",
    "schedule_published",
    "score_entry_allowed"
) VALUES (
    1,
    '2015-02-24 10:52:41.978799',
    '2015-02-24 10:52:41.978799',
    'Demo',
    'Demo Event',
    '2015-01-01 00:00:00',
    '2025-01-01 00:00:00',
    0,
    2,
    NULL,
    NULL,
    NULL,
    3,
    'US',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    1,
    NULL,
    NULL,
    NULL,
    NULL,
    '1',
    '1',
    '1',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '0',
    0,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    '0',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    'hide',
    'never',
    NULL,
    NULL,
    '0',
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    0,
    '0',
    '0',
    NULL,
    NULL
 );

BEGIN;


-- CREATE TYPE "roster_validation_source" ------------------------
CREATE TYPE "public"."roster_validation_source" AS ENUM( 'system', 'eo' );
-- ---------------------------------------------------------------


-- CREATE FIELD "roster_validated_by" ---------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "roster_validated_by" roster_validation_source DEFAULT NULL;
-- --------------------------------------------------------------------


-- UPDATE "roster_team"."roster_validated_by" field for validated rows --------
UPDATE roster_team rt
SET roster_validated_by = 'system'::roster_validation_source
WHERE rt.roster_validated_at IS NOT NULL;
-------------------------------------------------------------------------------


COMMIT;

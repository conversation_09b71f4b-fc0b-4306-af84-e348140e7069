BEGIN;

-- CREATE FIELD "img_name" -------------------------------------
ALTER TABLE "public"."email_template" ADD COLUMN "img_name" Text;COMMENT ON COLUMN "public"."email_template"."img_name" IS 'Image name for layout preview';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "NULLABLE" OF "FIELD "email_html" --------------------
ALTER TABLE "public"."email_template" ALTER COLUMN "email_html" DROP NOT NULL;
-- -------------------------------------------------------------

-- CHANGE "NULLABLE" OF "FIELD "event_owner_id" ----------------
ALTER TABLE "public"."email_template" ALTER COLUMN "event_owner_id" DROP NOT NULL;
-- -------------------------------------------------------------

-- CHANGE "NULLABLE" OF "FIELD "sender_type" -------------------
ALTER TABLE "public"."email_template" ALTER COLUMN "sender_type" DROP NOT NULL;
-- -------------------------------------------------------------


COMMIT;


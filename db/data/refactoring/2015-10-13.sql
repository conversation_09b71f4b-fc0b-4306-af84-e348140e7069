BEGIN;

-- DROP INDEX "index_roster_athlete_last" ----------------------
DROP INDEX IF EXISTS "public"."index_roster_athlete_last";
-- -------------------------------------------------------------

-- DROP FIELD "first" ------------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "first";
-- -------------------------------------------------------------

-- DROP FIELD "last" -------------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "last";
-- -------------------------------------------------------------

-- DROP FIELD "locked" -----------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "locked";
-- -------------------------------------------------------------

-- DROP FIELD "organization_code" ------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "organization_code";
-- -------------------------------------------------------------

-- DROP FIELD "birthdate" --------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "birthdate";
-- -------------------------------------------------------------

-- DROP FIELD "phonem" -----------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "phonem";
-- -------------------------------------------------------------

-- DROP FIELD "age" --------------------------------------------
ALTER TABLE "public"."roster_athlete" DROP COLUMN "age";
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE FIELD "deleted" --------------------------------------
ALTER TABLE "public"."roster_staff_role" ADD COLUMN "deleted" Timestamp Without Time Zone;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "roster_athletes_count" ------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "roster_athletes_count" Integer DEFAULT '0';
-- -------------------------------------------------------------

-- CREATE FIELD "roster_staff_count" ---------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "roster_staff_count" Integer DEFAULT '0';
-- -------------------------------------------------------------

COMMIT;

BEGIN;


-- CREATE "public"."sms_message" TABLE ------------------------------------------------

CREATE TABLE public.sms_message
(
  sms_message_id    SERIAL PRIMARY KEY NOT NULL,
  created           TIMESTAMP DEFAULT NOW(),
  modified          TIMESTAMP DEFAULT NULL,
  sms_from          TEXT               NOT NULL,
  sms_to            TEXT               NOT NULL,
  sms_text          TEXT,
  sms_service_id    TEXT,
  response          JSONB              NOT NULL,
  date_sent         TIMESTAMP,
  date_queued       TIMESTAMP,
  date_delivered    TIMESTAMP,
  date_failed       TIMESTAMP,
  date_undelivered  TIMESTAMP,
  date_unsubscribed TIMESTAMP,
  purchase_id       INT
);
CREATE UNIQUE INDEX sms_message_sms_message_id_uindex
  ON public.sms_message (sms_message_id);
COMMENT ON TABLE public.sms_message IS 'Table for storing sms data';

---------------------------------------------------------------------------------



-- CREATE "public"."sms_event" TABLE --------------------------------------------

CREATE TABLE public.sms_event
(
  sms_event_id   SERIAL PRIMARY KEY NOT NULL,
  modified       TIMESTAMP,
  created        TIMESTAMP DEFAULT now(),
  phone_to       TEXT               NOT NULL,
  phone_from     TEXT               NOT NULL,
  event          TEXT               NOT NULL,
  event_body     JSONB              NOT NULL,
  text           TEXT,
  sms_service_id TEXT               NOT NULL
);
CREATE UNIQUE INDEX sms_event_sms_event_id_uindex
  ON public.sms_event (sms_event_id);
COMMENT ON COLUMN public.sms_event.event IS 'The status of the message. Possible statuses include: "queued", "sent", "failed", "delivered", "undelivered" or "rejected"';
COMMENT ON COLUMN public.sms_event.event_body IS 'All the data retrieved for the current event';

----------------------------------------------------------------------------------



-- SET TRIGGER "update_modified_column()" on "public"."sms_events" ----------------

CREATE TRIGGER sms_event_modified
BEFORE UPDATE ON "public"."sms_event"
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-----------------------------------------------------------------------------------


-- SET TRIGGER "update_modified_column()" on "public"."sms_message" -----------------------

CREATE TRIGGER sms_message_modified
BEFORE UPDATE ON "public"."sms_message"
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

-----------------------------------------------------------------------------------


COMMIT;

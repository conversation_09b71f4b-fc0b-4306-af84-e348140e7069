BEGIN;

-- CREATE FIELD "transfer_type" --------------------------------
ALTER TABLE "public"."stripe_transfer" ADD COLUMN "transfer_type" Text COLLATE "pg_catalog"."default";COMMENT ON COLUMN "public"."stripe_transfer"."transfer_type" IS 'The type of transfer';
-- -------------------------------------------------------------

-- CREATE FIELD "check_num" ------------------------------------
ALTER TABLE "public"."stripe_transfer" ADD COLUMN "check_num" Text COLLATE "pg_catalog"."default";COMMENT ON COLUMN "public"."stripe_transfer"."check_num" IS 'Check number for transfers of type "check"';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CHANGE "NULLABLE" OF "FIELD "stripe_account_id" -------------
ALTER TABLE "public"."stripe_transfer" ALTER COLUMN "stripe_account_id" DROP NOT NULL;
-- -------------------------------------------------------------

-- CHANGE "NULLABLE" OF "FIELD "stripe_transfer_id" ------------
ALTER TABLE "public"."stripe_transfer" ALTER COLUMN "stripe_transfer_id" DROP NOT NULL;
-- -------------------------------------------------------------

COMMIT;

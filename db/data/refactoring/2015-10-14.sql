BEGIN;

-- CREATE SEQUENCE "event_user_id_seq" -------------------------
CREATE SEQUENCE "public"."event_user_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 9223372036854775807
START 1
CACHE 1
;
COMMENT ON SEQUENCE "public"."event_user_id_seq" IS 'Sequence for event_user table';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- CREATE TABLE "event_user" -----------------------------------
CREATE TABLE "public"."event_user" ( 
    "event_user_id" Integer DEFAULT nextval('event_user_id_seq'::regclass) NOT NULL,
    "user_id" Integer NOT NULL,
    "event_owner_id" Integer NOT NULL,
    "event_id" Integer NOT NULL,
    "role_co_owner" Boolean DEFAULT 'false' NOT NULL );
 
CREATE INDEX "index_event_user_id" ON "public"."event_user" USING btree( "event_user_id" );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "has_shared_events" ----------------------------
ALTER TABLE "public"."user" ADD COLUMN "has_shared_events" Boolean DEFAULT 'false' NOT NULL;
-- -------------------------------------------------------------

COMMIT;


---
--- Changing table name to event_note.
--- Adding indexes
---

BEGIN;

-- CHANGE "NAME" OF "TABLE "event_notes" -----------------------
ALTER TABLE "public"."event_notes" RENAME TO "event_note";
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "created" ------------------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "event_notes_id" --------------------
ALTER TABLE "public"."event_note" RENAME COLUMN "event_notes_id" TO "event_note_id";
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "roster_club_id" -----------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "roster_team_id" -----------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CREATE INDEX "index_created1" -------------------------------
CREATE INDEX "index_created1" ON "public"."event_note" USING btree( "created" );
-- -------------------------------------------------------------

-- CREATE INDEX "index_roster_club_id2" ------------------------
CREATE INDEX "index_roster_club_id2" ON "public"."event_note" USING btree( "roster_club_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "index_roster_team_id3" ------------------------
CREATE INDEX "index_roster_team_id3" ON "public"."event_note" USING btree( "roster_team_id" );
-- -------------------------------------------------------------

-- CHANGE "FIELDS" OF "UNIQUE "unique_event_notes_id" ----------
ALTER TABLE "public"."event_note" DROP CONSTRAINT IF EXISTS "unique_event_notes_id";
ALTER TABLE "public"."event_note" ADD CONSTRAINT "unique_event_notes_id" UNIQUE( "event_note_id" );
-- -------------------------------------------------------------;

COMMIT;



---
--- Renaming table event_notifications to event_change
--- Adding indexes
---

BEGIN;

-- CHANGE "COMMENT" OF "TABLE "event_notifications" ------------
COMMENT ON TABLE  "public"."event_notifications" IS 'Event Changes and Notifications table';
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "TABLE "event_notifications" ---------------
ALTER TABLE "public"."event_notifications" RENAME TO "event_change";
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "created" ------------------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "event_id" -----------------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "event_notifications_id" ------------
ALTER TABLE "public"."event_change" RENAME COLUMN "event_notifications_id" TO "event_change_id";
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "roster_club_id" -----------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CHANGE "INDEXED" OF "FIELD "roster_team_id" -----------------
-- Will be changed by indexes;
-- -------------------------------------------------------------

-- CREATE INDEX "index_created2" -------------------------------
CREATE INDEX "index_created2" ON "public"."event_change" USING btree( "created" );
-- -------------------------------------------------------------

-- CREATE INDEX "index_roster_team_id4" ------------------------
CREATE INDEX "index_roster_team_id4" ON "public"."event_change" USING btree( "roster_team_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "index_roster_club_id3" ------------------------
CREATE INDEX "index_roster_club_id3" ON "public"."event_change" USING btree( "roster_club_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "index_event_id4" ------------------------------
CREATE INDEX "index_event_id4" ON "public"."event_change" USING btree( "event_id" );
-- -------------------------------------------------------------

-- CHANGE "FIELDS" OF "UNIQUE "unique_event_notifications_id" --
ALTER TABLE "public"."event_change" DROP CONSTRAINT IF EXISTS "unique_event_notifications_id";
ALTER TABLE "public"."event_change" ADD CONSTRAINT "unique_event_notifications_id" UNIQUE( "event_change_id" );
-- -------------------------------------------------------------;

COMMIT;

BEGIN;

-- Create sequence for "email_sending_history"."email_sending_history_id" ---------
CREATE SEQUENCE "public"."email_sending_history_id_sequence"
  INCREMENT 1
  MINVALUE 0
  MAXVALUE 9223372036854775807
  START 0
  CACHE 1;
------------------------------------------------------------------------------------


-- Create "email_sending_history" table --------------------------------------------
CREATE TABLE public.email_sending_history
(
  email_sending_history_id INTEGER PRIMARY KEY DEFAULT nextval(
      'email_sending_history_id_sequence' :: regclass) NOT NULL,
  email_address            TEXT                        NOT NULL,
  email_sending_id         TEXT                        NOT NULL,
  created                  TIMES<PERSON>MP WITHOUT TIME ZONE DEFAULT NOW(),
  modified                 TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW()
);
------------------------------------------------------------------------------------

-- Create indexes for "system_job" table--------------------------------------------
CREATE INDEX email_sending_history_email_sending_id_index
  ON public.email_sending_history (email_sending_id);
CREATE INDEX email_sending_history_email_address_index
  ON public.email_sending_history (email_address);
------------------------------------------------------------------------------------


-- Add "update_modified_column" function to "email_sending_history"."modified"------
CREATE TRIGGER update_email_sending_history_modified
  BEFORE UPDATE
  ON email_sending_history
  FOR EACH ROW EXECUTE PROCEDURE
  update_modified_column();
------------------------------------------------------------------------------------


-- Create field type "system_job_type" for "system_job" table ----------------------
CREATE TYPE "public"."system_job_type" AS ENUM ('email_sending');
------------------------------------------------------------------------------------


-- Create field type "system_job_status" for "system_job" table --------------------
CREATE TYPE "public"."system_job_status" AS ENUM ('scheduled', 'started', 'sending_emails', 'recipients_list_generation', 'done');
------------------------------------------------------------------------------------


-- Create sequence for "system_job"."system_job_id" --------------------------------
CREATE SEQUENCE "public"."system_job_id_sequence"
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 9223372036854775807
  START 1
  CACHE 1;
------------------------------------------------------------------------------------


-- Create "system_job" table ---------------------------------------------------------------------------
CREATE TABLE public.system_job
(
  system_job_id     INTEGER PRIMARY KEY DEFAULT nextval('system_job_id_sequence' :: regclass) NOT NULL,
  job_type          system_job_type                                                           NOT NULL,
  status            system_job_status           DEFAULT 'scheduled' :: system_job_status      NOT NULL,
  event_id          INTEGER,
  email_sending_id  TEXT                        DEFAULT NULL,
  recipient_type    TEXT                        DEFAULT NULL,
  email_template_id INTEGER                     DEFAULT NULL,
  user_id           INTEGER                                                                   NOT NULL,
  started_at        TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
  finished_at       TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
  created           TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  modified          TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  UNIQUE ("email_sending_id", "event_id", "recipient_type")
);
--------------------------------------------------------------------------------------------------------


-- Add "modified" trigger "system_job"."modified"----------------------------------
CREATE TRIGGER update_system_job_modified
  BEFORE UPDATE
  ON system_job
  FOR EACH ROW EXECUTE PROCEDURE
  update_modified_column();
------------------------------------------------------------------------------------


-- Create indexes for "system_job" table--------------------------------------------
CREATE INDEX system_job_job_type_index
  ON public.system_job (job_type);
CREATE INDEX system_job_status_index
  ON public.system_job (status);
CREATE INDEX system_job_recipient_type_index
  ON public.system_job (recipient_type);
CREATE INDEX system_job_email_sending_id_index
  ON public.system_job (email_sending_id);
------------------------------------------------------------------------------------


-- Create partial indexes for "system_job" table-----------------------------------------------------------
CREATE UNIQUE INDEX system_job_event_id_recipient_type_email_template_id_index
  ON public.system_job (event_id, recipient_type, email_template_id)
  WHERE public.system_job.status <> 'done' :: system_job_status;

CREATE UNIQUE INDEX system_job_email_sending_id_job_type_index
  ON public.system_job (email_sending_id, job_type)
  WHERE public.system_job.job_type = 'email_sending' :: system_job_type;
------------------------------------------------------------------------------------------------------------


COMMIT;

---
--- Creating table for role Sponsor (or Exhibitor)
---

BEGIN;


-- CREATE TABLE "sponsor" --------------------------------------
CREATE TABLE "public"."sponsor" (
	"sponsor_id" Serial NOT NULL UNIQUE, 
	"created" TIMES<PERSON>MP WITHOUT TIME ZONE DEFAULT now() NOT NULL, 
  "modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"user_id" INTEGER, 
	"company_name" CHARACTER VARYING( 2044 ) NOT NULL UNIQUE, 
	"first" CHARACTER VARYING( 100 ) NOT NULL,
	"last" CHARACTER VARYING( 100 ) NOT NULL,
	"email" CHARACTER VARYING( 100 ) NOT NULL,
	"mobile_phone" CHARACTER VARYING( 20 ) NOT NULL, 
	"office_phone" CHARACTER VARYING( 20 ), 
	"street" CHARACTER VARYING( 200 ), 
	"city" CHARACTER VARYING( 100 ),
	"state" CHARACTER VARYING( 2 ), 
	"zip" CHARACTER VARYING( 10 ), 
	"company_description" CHARACTER VARYING( 4096 ), 
	"badge_names" CHARACTER VARYING( 2044 ), 
	"is_exhibitor" BOOLEAN DEFAULT 'true', 
	"is_sponsor" BOOLEAN DEFAULT 'false', 
	"is_non_profit" BOOLEAN DEFAULT 'false', 
	"is_other" BOOLEAN,
 PRIMARY KEY ( "sponsor_id" )
, CONSTRAINT "unique_company_name" UNIQUE( "company_name" ) );
COMMENT ON TABLE  "public"."sponsor" IS 'Table with Sponsor''s data';
-- Set comments for fields
COMMENT ON COLUMN "public"."sponsor"."user_id" IS 'User ID';
COMMENT ON COLUMN "public"."sponsor"."company_name" IS 'Exhibitor''s Company Name';
COMMENT ON COLUMN "public"."sponsor"."first" IS 'First Name';
COMMENT ON COLUMN "public"."sponsor"."last" IS 'Last Name';
COMMENT ON COLUMN "public"."sponsor"."email" IS 'Email';
COMMENT ON COLUMN "public"."sponsor"."mobile_phone" IS 'Mobile Phone';
COMMENT ON COLUMN "public"."sponsor"."office_phone" IS 'Office Phone';
COMMENT ON COLUMN "public"."sponsor"."company_description" IS 'Description of Company, what items will be sold';
COMMENT ON COLUMN "public"."sponsor"."badge_names" IS 'First & Last Names for Badges';
COMMENT ON COLUMN "public"."sponsor"."is_exhibitor" IS 'Exhibitor (Booth Space)';
COMMENT ON COLUMN "public"."sponsor"."is_sponsor" IS 'Sponsor';
COMMENT ON COLUMN "public"."sponsor"."is_non_profit" IS 'Non-Profit';
COMMENT ON COLUMN "public"."sponsor"."is_other" IS 'Other';
-- -------------------------------------------------------------;

COMMIT;


---
--- Changes in user table - adding role for Sales Manager, renaming Vendor to Sponsor
---

BEGIN;


-- CHANGE "NAME" OF "FIELD "role_vendor" -----------------------
ALTER TABLE "public"."user" RENAME COLUMN "role_vendor" TO "role_sponsor";
-- -------------------------------------------------------------

-- CREATE FIELD "role_sales_manager" ---------------------------
ALTER TABLE "public"."user" ADD COLUMN "role_sales_manager" BOOLEAN DEFAULT 'false';
-- -------------------------------------------------------------

-- DROP FIELD "stripe_customer_id" -----------------------------
ALTER TABLE "public"."user" DROP COLUMN "stripe_customer_id";
-- -------------------------------------------------------------;

COMMIT;


---
--- Creating new table for role Sales Manager
---

BEGIN;


-- CREATE TABLE "sales_manager" --------------------------------
CREATE TABLE "public"."sales_manager" (
	"sales_manager_id" Serial NOT NULL UNIQUE,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"user_id" INTEGER NOT NULL UNIQUE,
	"activated" BOOLEAN DEFAULT 'false' NOT NULL
, CONSTRAINT "unique_sales_manager_id" UNIQUE( "sales_manager_id" ),
CONSTRAINT "unique_sales_manager_user_id" UNIQUE( "user_id" ) );
COMMENT ON TABLE  "public"."sales_manager" IS 'Table with Sales Manager''s data';
-- Set comments for fields
COMMENT ON COLUMN "public"."sales_manager"."user_id" IS 'User ID';
COMMENT ON COLUMN "public"."sales_manager"."activated" IS 'Is Sales Manager activated';
-- -------------------------------------------------------------;

COMMIT;


---
--- Renaming column cancelled to canceled
---

BEGIN;


-- CHANGE "COMMENT" OF "FIELD "cancelled" ----------------------
COMMENT ON COLUMN "public"."purchase_team"."cancelled" IS 'Timestamp if purchase team was canceled. NULL if not.';
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "cancelled" -------------------------
ALTER TABLE "public"."purchase_team" RENAME COLUMN "cancelled" TO "canceled";
-- -------------------------------------------------------------;

COMMIT;


---
--- THS API related fields
---

BEGIN;


-- CHANGE "COMMENT" OF "FIELD "confirmed_nights" ---------------
COMMENT ON COLUMN "public"."roster_team"."confirmed_nights" IS 'THS API - Confirmed Nights';
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "confirmed_nights" ------------------
ALTER TABLE "public"."roster_team" RENAME COLUMN "confirmed_nights" TO "ths_confirmed_nights";
-- -------------------------------------------------------------

-- CHANGE "COMMENT" OF "FIELD "tentative_nights" ---------------
COMMENT ON COLUMN "public"."roster_team"."tentative_nights" IS 'THS API - Tentative Nights';
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "tentative_nights" ------------------
ALTER TABLE "public"."roster_team" RENAME COLUMN "tentative_nights" TO "ths_tentative_nights";
-- -------------------------------------------------------------

-- CREATE FIELD "ths_id" ---------------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_id" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."ths_id" IS 'THS API - THS row ID';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_hotel_name" -------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_hotel_name" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."roster_team"."ths_hotel_name" IS 'THS API - Hotel Name';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_hotel_status" -----------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_hotel_status" CHARACTER VARYING( 20 );
COMMENT ON COLUMN "public"."roster_team"."ths_hotel_status" IS 'THS API - Hotel Status';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_loyalty" ----------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_loyalty" INTEGER;
COMMENT ON COLUMN "public"."roster_team"."ths_loyalty" IS 'THS API - Loyalty';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_contract_issued" --------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_contract_issued" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."roster_team"."ths_contract_issued" IS 'THS API - Contract Issued datetime';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_when_accepted" ----------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_when_accepted" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."roster_team"."ths_when_accepted" IS 'THS API - Contract Accepted datetime';
-- -------------------------------------------------------------

-- CREATE FIELD "ths_when_canceled" ----------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_when_canceled" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."roster_team"."ths_when_canceled" IS 'THS API - Contract Canceled datetime';
-- -------------------------------------------------------------;


-- CREATE FIELD "ths_modified" ---------------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "ths_modified" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."roster_team"."ths_modified" IS 'THS API - Modified datetime';
-- -------------------------------------------------------------;


COMMIT;


---
--- Adding new housing fields
---
BEGIN;

-- CREATE FIELD "housing_nights_required" ----------------------
ALTER TABLE "public"."event" ADD COLUMN "housing_nights_required" INTEGER;
COMMENT ON COLUMN "public"."event"."housing_nights_required"
IS 'Minimum number of hotel room nights required per team';
-- -------------------------------------------------------------

-- CREATE FIELD "housing_local_teams_distance" -----------------
ALTER TABLE "public"."event" ADD COLUMN "housing_local_teams_distance" INTEGER;
-- -------------------------------------------------------------

-- CREATE FIELD "housing_nights_threshold" --------------------
ALTER TABLE "public"."event" ADD COLUMN "housing_nights_threshold" INTEGER;
COMMENT ON COLUMN "public"."event"."housing_nights_threshold"
IS 'Caution threshold for teams nearing required minimum room nights. (if minimum room nights is set to 15 and threshold is 2, warning would appear at 15-17 room nights)';
-- -------------------------------------------------------------;

COMMIT;

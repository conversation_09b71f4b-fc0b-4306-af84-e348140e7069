<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Receipt</title>
    <link rel="stylesheet" href="<%= sails.config.urls.main_app.baseUrl %>/styles/main.css"/>
</head>
<body>
    <div class="container">        
        <h1>Receipt #<%= payment.purchase_id %></h1>
    <div class="row">
        <div class="col-sm-12">
            Created: <%= payment.created %><br/>
            <%if (payment.date_paid) { %>
            Paid: <%= payment.date_paid %><br/>
            <% } %>    
            Payer Email: <%= payment.payer_email %>      
            <p class="lead">Event: <%= payment.event_name %></p>
            Date Start: <%= payment.event_start %> <br/>
            Date End: <%= payment.event_end %> <br/>
            Email: <a href="mailto:<%= payment.event_email %>"><%= payment.event_email %></a><br/>
            City: <%= payment.event_city %> <br/>
            State: <%= payment.event_state %>
        </div>
    </div>        
    <hr/>
    <div class="row">
        <div class="col-sm-12">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Team Name</th>
                        <th>Division</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <% for(var i = 0; i < payment.teams.length; ++i) {%>
                        <tr>
                            <td><%= (i + 1) %></td>              
                            <td><%= payment.teams[i].team_name %></td>
                            <td><%= payment.teams[i].division_name %></td>
                            <td>$<%= payment.teams[i].amount %></td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 text-right">
            <p class="lead">Total: $<%= payment.amount %></p>
        </div>
    </div>
    </div>
    <script>
        window.print();
    </script>
</body>
</html>

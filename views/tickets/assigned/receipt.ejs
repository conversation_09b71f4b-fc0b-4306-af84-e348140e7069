<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title><%= event_name %></title>
    <link rel="stylesheet" href="<%= sails.config.urls.main_app.baseUrl %>/../styles/main.css"/>
</head>
<body>
    <div class="receipt_wrapper">
        <div class="receipt_header">
            <div class="receipt_header_ticket">
                <div class="receipt_header_ticket-image">
                    <% if(ticket_type === 'weekend') { %>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                            <g fill="#9B9B9B" fill-rule="nonzero" transform="rotate(-90 15 15)">
                                <circle cx="21.098" cy="24.077" r="1.233"/>
                                <circle cx="18.221" cy="9.283" r="1.233"/>
                                <path d="M28.572 18.287h1.156V11.36h-2.947a2.313 2.313 0 0 1-2.312-2.308c0-.864.478-1.65 1.247-2.05l.984-.511L23.757.24.167 11.356l.002.005h-.03v6.927h1.156a2.313 2.313 0 0 1 2.312 2.309 2.313 2.313 0 0 1-2.312 2.308H.14v6.927h29.59v-6.927h-1.157a2.313 2.313 0 0 1-2.311-2.308 2.313 2.313 0 0 1 2.311-2.31zM15.868 6.51a1.153 1.153 0 1 0 2.088-.984l4.694-2.211 1.071 2.273a4.6 4.6 0 0 0-1.563 3.463c0 .84.227 1.629.622 2.308H5.574l10.294-4.849zm11.549 18.556v2.455h-5.202a1.155 1.155 0 0 0-2.311 0H2.45v-2.455a4.627 4.627 0 0 0 3.467-4.471 4.627 4.627 0 0 0-3.467-4.472v-2.455h17.453a1.155 1.155 0 0 0 2.311 0h5.202v2.455a4.627 4.627 0 0 0-3.468 4.472 4.627 4.627 0 0 0 3.468 4.471z"/>
                                <circle cx="21.098" cy="17.502" r="1.233"/>
                                <circle cx="21.098" cy="20.79" r="1.233"/>
                            </g>
                        </svg>
                    <% } else { %>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="31" viewBox="0 0 20 31">
                            <g fill="#9B9B9B" fill-rule="nonzero">
                                <circle cx="13.279" cy="9.073" r="1.233" transform="rotate(-90 13.279 9.073)"/>
                                <path d="M7.488 1.6V.442H.562v2.948L.557 30.005l.005-.002v.03h6.926v-1.157a2.313 2.313 0 0 1 2.31-2.311 2.313 2.313 0 0 1 2.308 2.311v1.156h6.926V.443h-6.926V1.6a2.313 2.313 0 0 1-2.309 2.312 2.313 2.313 0 0 1-2.309-2.312zM.562 7.391v0zm13.707-4.637h2.455v5.201a1.155 1.155 0 0 0 0 2.312V27.72h-2.455a4.627 4.627 0 0 0-4.472-3.468 4.627 4.627 0 0 0-4.471 3.468H2.87V10.268a1.155 1.155 0 0 0 0-2.312V2.755h2.455a4.627 4.627 0 0 0 4.471 3.467 4.627 4.627 0 0 0 4.472-3.467z"/>
                                <circle cx="6.704" cy="9.073" r="1.233" transform="rotate(-90 6.704 9.073)"/>
                                <circle cx="9.991" cy="9.073" r="1.233" transform="rotate(-90 9.991 9.073)"/>
                            </g>
                        </svg>
                    <% } %>
                </div>
                <div class="receipt_header_ticket-number">1</div>
                <div class="receipt_header_ticket-label">
                    <% if(ticket_type === 'weekend') { %>
                        All Tournament
                    <% } else { %>
                        Daily
                    <% } %>
                </div>
            </div>
            <div class="receipt_header_print">
                <div class="receipt_header_text hidden-print">
                    <% if (ticket_type === 'daily') { %>
                        Daily pass good for entire day on available date listed below
                    <% }%>
                </div>
                <div class="receipt_header_print_button hidden-print">
                    <button onclick="window.print()">Print Ticket</button>
                </div>
            </div>
        </div>
        <div class="receipt_divide-line"></div>
        <div class="receipt_event_info">
            <div class="receipt_event_info-logo">
                <img src="<%= event_logo %>" alt="Event Logo">
            </div>
            <div class="receipt_event_info-text">
                <span class="receipt_event_info-text_event-name"><%= event_name %></span><br>
                <span class="receipt_event_info-text_event-data"><%= event_dates_info %></span>
            </div>
        </div>
        <div class="receipt_divide-line"></div>
        <div class="receipt_present-qr-code">
            Present this QR code on a mobile device along with a <span class="receipt_blue-bold">Government photo ID</span> to gain entry. 
            Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. 
        </div>
        <div class="receipt_participant">
            <div class="receipt_participant_information">
                <div class="receipt_participant_information-title">
                    <div class="receipt_participant_information-label">
                        Participant Information
                    </div>
                    <div class="receipt_participant_information-line"></div>
                </div>
                <div class="receipt_participant_information-body">
                    <% if (ticket_type === 'daily') { %>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Available Dates:</div>
                        <div class="receipt_participant_information-body_item-value">
                            <% for(const date of valid_dates || ['All Days']) { %>
                                <div><%= date %></div>
                            <% } %>
                        </div>
                    </div>
                    <% } %>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Pass barcode:</div>
                        <div class="receipt_participant_information-body_item-value"><%= formatted_ticket_barcode %></div>
                    </div>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Email:</div>
                        <div class="receipt_participant_information-body_item-value"><%= email || '-' %></div>
                    </div>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Phone:</div>
                        <div class="receipt_participant_information-body_item-value"><%= formatted_phone || '-' %></div>
                    </div>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Zip Code:</div>
                        <div class="receipt_participant_information-body_item-value"><%= zip || '-' %></div>
                    </div>
                    <div class="receipt_participant_information-body_item">
                        <div class="receipt_participant_information-body_item-label">Purchased:</div>
                        <div class="receipt_participant_information-body_item-value"><%= type === 'check' && check_num !== null ? received_date : date_paid %></div>
                    </div>
                    <div class="receipt_participant_information-body_item receipt_price">
                        <div class="receipt_participant_information-body_item-label">
                            <div>Price:</div>
                            <% if(ticket_type === 'weekend') { %>
                                <div class="receipt_price_all-tournament">All Tournament</div>
                            <% } %>
                        </div>
                        <div class="receipt_participant_information-body_item-value text-bold"><%= discounted_amount ? tickets[0].ticket_price : amount %></div>
                    </div>
                    <% if (discounted_amount) { %>
                        <div class="receipt_participant_information-body_item">
                            <div class="receipt_participant_information-body_item-label">Discount:</div>
                            <div class="receipt_participant_information-body_item-value text-bold"><%= discounted_amount %></div>
                        </div>
                    <% } %>
                    <% if (discounted_amount) { %>
                        <div class="receipt_participant_information-body_item">
                            <div class="receipt_participant_information-body_item-label">Total Price:</div>
                            <div class="receipt_participant_information-body_item-value text-bold"><%= amount %></div>
                        </div>
                    <% } %>
                </div>
            </div>
            <div class="receipt_participant_qrcode">
                <div class="receipt_participant_qrcode-name"><%= first %> <%= last %></div>
                <div class="receipt_participant_qrcode-image">
                    <% if(border_colour) { %>
                        <img style="border: 15px #a5a4a4 solid !important;outline: 10px solid <%= border_colour %>" src="<%= url %>" alt="qr code"/>
                    <% } else{ %>
                        <img src="<%= url %>" alt="qr code"/>
                    <% } %>
                </div>
                <% if (ticket_share_deep_link) { %>
                    <div class="hidden-print receipt_swt_button" onclick="openDeepLink('<%= ticket_share_deep_link %>')">
                        <img src="<%= sw_tickets_app_icon %>" alt=""/>
                    </div>
                <% } %>
                <% if (isAppleDevice && !is_canceled_payment) { %>
                    <div class="receipt_participant_qrcode-wallet hidden-print">
                        <a class="receipt_participant_qrcode-wallet_button" href="<%= appleWalletURL %>" download>
                            <img src="<%= apple_wallet_icon %>" alt=""/>
                        </a>
                    </div>
                <% } %>
            </div>
            <div class="receipt_present-qr-code_mobile">
                Present this QR code on a mobile device along with a <span class="receipt_blue-bold">Government photo ID</span> to gain entry. 
                Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter. 
            </div>
        </div>
        <% if (assigned_tickets && assigned_tickets.length) { %>
            <div class="receipt_ticket-holders hidden-print">
                <div class="receipt_ticket-holders_title">
                    <div class="receipt_ticket-holders_title-label">
                        All ticket holders on this purchase:
                    </div>
                    <div class="receipt_ticket-holders_title-line"></div>
                </div>
                <div class="receipt_ticket-holders_notes">
                    (Click on a name to access each ticket if you do not receive the individual QR code ticket email)
                </div>
                <div class="receipt_ticket-holders_items-list">
                    <% for(var i = 0; i < assigned_tickets.length; ++i) { %>
                        <a href="<%=assigned_tickets[i].url%>" target="_blank" class="btn receipt_ticket-holders_item">
                            <span><%= assigned_tickets[i].first %> <%= assigned_tickets[i].last %></span>
                            <span class="to-right receipt_ticket-holders_items-list_ticket-type">
                                <% if(assigned_tickets[i].ticket_type === 'weekend') { %>
                                    Weekend
                                <% } else { %>
                                    Daily
                                <% } %>
                            </span>
                        </a>
                    <%}%>
                </div>
            </div>
        <% } %>
        <% if (tickets_receipt_descr) { %>
            <div class="receipt_important-information">
                <div class="receipt_important-information_title">
                    <div class="receipt_important-information_title-label">
                        Important Information
                    </div>
                    <div class="receipt_important-information_title-line"></div>
                </div>
                <div class="receipt_important-information_body">
                    <%- tickets_receipt_descr %>
                </div>
            </div>
        <% } %>
        <div>
            <div style="padding-top:10px;padding-bottom:10px;">
                Download the SportWrench Express app for quick and easy entry to the event. You can pre-verify your ID in the app, then produce an Express ticket at the event to keep your ID in your pocket and enter the venue effortlessly.
            </div>
            <div style="padding-top:10px;padding-bottom:10px;display:flex;justify-content: center;">
                <a class="receipt_mobile_store_button" style="margin-right: 20px;" href="<%= appStoreSwAppUrl %>" >
                    <img src="<%= app_store_download_icon %>" alt=""/>
                </a>
                <a class="receipt_mobile_store_button" href="<%= playMarketSwAppUrl %>" >
                    <img src="<%= play_market_download_icon %>" alt=""/>
                </a>
            </div>
        </div>
        
        <% if (type !== 'free') { %>
            <div style="padding-top:10px;padding-bottom:10px;">
                You can find your purchased tickets in the SportWrench Tickets Holder account. You can also modify or
                refund unscanned tickets: <a href="<%=account_activation_link%>" target="_blank">Generate Account
                Password feature</a>. To log in, please enter the email you used when purchasing the tickets then use
                the Generate Account Password feature to create a password for your account.
            </div>
        <% } %>
        <% if (social_links && social_links.length) { %>
            <div class="receipt_socials hidden-print">
                <div class="receipt_socials_divide-line"></div>
                <div class="receipt_socials_items-list">
                    <% for(var i = 0; i < social_links.length; ++i) { %>
                        <% if ((social_links[i].name === 'Snapchat' || social_links[i].title === 'Snapchat') && social_links[i].value) { %>
                            <% social_links[i].value = `https://www.snapchat.com/add/${social_links[i].value}` %>
                            <span class="receipt_socials_item">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;" target="_blank">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="29" viewBox="0 0 29 29">
                                        <path fill="#337AB7" fill-rule="nonzero" d="M14.5 0C6.493 0 0 6.493 0 14.5S6.493 29 14.5 29 29 22.507 29 14.5 22.507 0 14.5 0zm8.684 19.182c-.104.243-.574.589-2.216.842-.135.021-.187.196-.267.563-.029.133-.058.264-.099.402-.035.12-.11.176-.235.176h-.02a2.06 2.06 0 0 1-.368-.047 4.92 4.92 0 0 0-.986-.104c-.232 0-.472.02-.712.06-.493.082-.912.377-1.355.69-.643.456-1.308.925-2.343.925-.045 0-.09-.001-.134-.003a1.025 1.025 0 0 1-.087.003c-1.035 0-1.7-.47-2.342-.924-.444-.313-.863-.61-1.356-.691-.24-.04-.48-.06-.712-.06-.417 0-.746.064-.986.111a2.159 2.159 0 0 1-.368.053c-.1 0-.208-.021-.255-.182-.041-.14-.07-.275-.1-.406-.073-.335-.125-.542-.266-.564-1.642-.253-2.112-.6-2.216-.845a.316.316 0 0 1-.026-.105.185.185 0 0 1 .155-.193c2.524-.415 3.656-2.995 3.703-3.105a.097.097 0 0 1 .004-.008c.154-.313.184-.585.09-.808-.173-.408-.738-.587-1.112-.706-.092-.03-.178-.056-.247-.084-.746-.295-.808-.597-.779-.751.05-.264.402-.447.686-.447a.48.48 0 0 1 .204.041c.336.157.638.237.9.237.36 0 .518-.152.537-.172l-.032-.533c-.075-1.193-.169-2.676.209-3.523 1.132-2.536 3.53-2.734 4.24-2.734l.31-.003h.042c.71 0 3.115.197 4.247 2.736.378.847.284 2.33.209 3.523l-.003.052-.03.482c.019.019.164.158.49.17.25-.01.535-.089.849-.235a.634.634 0 0 1 .263-.052c.106 0 .213.02.303.057l.005.003c.254.09.42.267.423.453.004.175-.13.438-.784.696-.068.027-.155.055-.247.084-.375.118-.94.298-1.112.706-.095.223-.065.494.09.807l.004.01c.047.109 1.178 2.688 3.703 *************.16.099.155.193a.31.31 0 0 1-.026.106z"/>
                                    </svg>
                                </a>
                            </span>
                        <% } %>
    
                        <% if ((social_links[i].name === 'Twitter' || social_links[i].title === 'Twitter') && social_links[i].value) { %>
                            <span class="receipt_socials_item">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;" target="_blank">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                        <path fill="#337AB7" fill-rule="nonzero" d="M15.29.79C7.223.79.683 7.33.683 15.394.684 23.46 7.224 30 15.29 30c8.066 0 14.606-6.54 14.606-14.605 0-8.065-6.54-14.606-14.606-14.606zm6.668 11.387c.007.144.01.288.01.433 0 4.43-3.372 9.538-9.538 9.538a9.489 9.489 0 0 1-5.139-1.505c.262.***********.046 1.57 0 3.016-.536 4.163-1.435a3.356 3.356 0 0 1-3.131-2.328 3.336 3.336 0 0 0 1.514-.058 3.353 3.353 0 0 1-2.69-3.286v-.043a3.33 3.33 0 0 0 1.519.42 3.35 3.35 0 0 1-1.038-4.476 9.518 9.518 0 0 0 6.91 3.503 3.353 3.353 0 0 1 5.713-3.057c.763-.151 1.48-.43 2.128-.814a3.365 3.365 0 0 1-1.474 1.854 6.686 6.686 0 0 0 1.925-.528 6.813 6.813 0 0 1-1.672 1.736z"/>
                                    </svg>
                                </a>
                            </span>
                        <% } %>
    
                        <% if ((social_links[i].name === 'Facebook' || social_links[i].title === 'Facebook') && social_links[i].value) { %>
                            <span class="receipt_socials_item">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;" target="_blank">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                        <path fill="#337AB7" fill-rule="nonzero" d="M29.21 14.783C29.21 6.62 22.67 0 14.606 0 6.54 0 0 6.62 0 14.783s6.54 14.782 14.605 14.782c.086 0 .171-.002.257-.003V18.054h-3.138v-3.701h3.138v-2.724c0-3.159 1.905-4.878 4.69-4.878 1.332 0 2.478.1 2.812.146v3.301h-1.92c-1.514 0-1.807.729-1.807 1.798v2.357h3.621l-.472 3.701h-3.15v10.94c6.106-1.771 10.575-7.464 10.575-14.211z"/>
                                    </svg>
                                </a>
                            </span>
                        <% } %>
    
                        <% if ((social_links[i].name === 'Instagram' || social_links[i].title === 'Instagram') && social_links[i].value) { %>
                            <span class="receipt_socials_item">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;" target="_blank">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" viewBox="0 0 31 30">
                                        <g fill="#337AB7" fill-rule="nonzero">
                                            <path d="M18.346 14.605a2.793 2.793 0 1 1-5.587 0 2.793 2.793 0 0 1 5.587 0z"/>
                                            <path d="M22 9.747a2.594 2.594 0 0 0-.627-.962 2.592 2.592 0 0 0-.962-.627c-.295-.114-.739-.25-1.555-.288-.883-.04-1.148-.049-3.383-.049-2.236 0-2.5.009-3.383.05-.816.036-1.26.173-1.555.287a2.59 2.59 0 0 0-.962.627c-.278.27-.492.599-.627.962-.114.295-.25.739-.288 1.555-.04.883-.049 1.148-.049 3.383 0 2.236.009 2.5.05 3.383.036.817.173 1.26.287 1.555.135.364.348.692.626.962.27.279.6.493.963.627.295.114.739.25 1.555.288.883.04 1.147.049 3.383.049 2.236 0 2.5-.009 3.383-.049.816-.037 1.26-.174 1.555-.288a2.772 2.772 0 0 0 1.588-1.59c.115-.294.251-.737.289-1.554.04-.883.048-1.147.048-3.383 0-2.235-.008-2.5-.048-3.383-.037-.816-.174-1.26-.289-1.555zm-6.527 9.237a4.3 4.3 0 1 1 0-8.598 4.3 4.3 0 0 1 0 8.598zm4.469-7.763a1.005 1.005 0 1 1 0-2.01 1.005 1.005 0 0 1 0 2.01z"/>
                                            <path d="M15.553 0C7.488 0 .947 6.54.947 14.605c0 8.065 6.54 14.606 14.606 14.606 8.065 0 14.605-6.54 14.605-14.606C30.158 6.54 23.618 0 15.553 0zm8.336 18.063c-.04.893-.183 1.502-.39 2.036a4.288 4.288 0 0 1-2.453 2.453c-.533.207-1.143.349-2.035.39-.895.04-1.18.05-3.458.05-2.278 0-2.563-.01-3.458-.05-.893-.041-1.502-.183-2.036-.39a4.109 4.109 0 0 1-1.485-.968 4.11 4.11 0 0 1-.967-1.485c-.208-.534-.35-1.143-.39-2.036-.041-.894-.051-1.18-.051-3.458 0-2.277.01-2.563.05-3.457.041-.893.183-1.503.39-2.036.21-.56.541-1.067.968-1.486a4.108 4.108 0 0 1 1.485-.967c.534-.207 1.143-.349 2.036-.39.894-.04 1.18-.05 3.458-.05 2.277 0 2.563.01 3.457.05.893.041 1.503.183 ***********.21 1.067.54 1.486.967.426.42.756.926.967 1.486.207.533.35 1.143.39 2.036.04.894.05 1.18.05 3.457 0 2.278-.01 2.564-.05 3.458z"/>
                                        </g>
                                    </svg>
                                </a>
                            </span>
                        <% } %>
                    <% } %>
                </div>
                <div class="receipt_socials_divide-line"></div>
            </div>
        <% } %>
    </div>
    </body>
<script>
    function mobileAndTabletCheck() {
        let check = false;
        (function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
        return check;
    };

    function openUsingIframe(url) {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = url;
        document.body.appendChild(iframe);
    }

    function openDeepLink(deepLink) {
        if(mobileAndTabletCheck()) {
            window.location = deepLink;
        }else {
            openUsingIframe(deepLink);
        }

        setTimeout(function() {
            alert('If the app did not open, please download it from App Store/Google Play');
        }, 1000);
    }
</script>
</html>

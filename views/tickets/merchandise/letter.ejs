<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= event_name %></title>
    <style style="box-sizing: border-box;">
        .merchandise-table {
            width: 40%;
            margin: 0 auto;
            padding: 0;

            .merchandise-list {
                background-color: #F9FAFB;
                border-radius: 4px;
                padding: 15px;
            }

            @media (max-width: 900px) {
                width: 50% !important;
            }

            @media (max-width: 720px) {
                width: 60% !important;
            }

            @media (max-width: 540px) {
                width: 70% !important;
            }

            @media (max-width: 460px) {
                width: 80% !important;
            }
        }
    </style>
</head>
<body style="font-family: helvetica, arial; font-size: 16px">
    <table class="merchandise-table" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td style="text-align: center;">
                <p style="font-weight: bold; font-size: 18px;"><b><%= event_name %></b></p>
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                <p>
                    If you don't see a QR Image, click here: <br>
                    <a href="<%= receipt_url %>">
                        Open Receipt
                    </a>
                </p>
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                <img src="<%= qr_url %>" alt="QR Code">
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                <p style="font-weight: bold; font-size: 18px;"><b>Your Order:</b></p>
            </td>
        </tr>
        <tr>
            <td>
                <div class="merchandise-list">
                    <% for(let i = 0, l = receipt.length; i < l; ++i) { %>
                    <div style="padding: 6px 0;">
                        <b>
                            <%= receipt[i].quantity %> x <span style="text-transform: capitalize;"><%= receipt[i].label %></span>
                        </b>
                    </div>
                    <% } %>
                </div>
            </td>
        </tr>
        <tr>
            <td style="padding: 15px;">
                <p style="margin: 6px 0;">Purchase Barcode: <b><%= barcode %></b></p>
                <p style="margin: 6px 0;">Purchaser: <b><%= first %> <%= last %></b></p>
                <p style="margin: 6px 0;">Total Price: <b>$<%= total %></b></p>
            </td>
        </tr>
        <tr>
            <td>
                <p style="text-align: center; margin: 15px;">
                    <span>
                        Thank you for your purchase! If you can see the above QR code on your mobile device, it can be scanned at the event without needing to print it.
                    </span><br>
                    <a href="<%= receipt_url %>">
                        Open Receipt
                    </a>
                </p>
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                <p style="margin-top: 25px;">
                    <img width="40px" src="<%= app_domain  + '/images/passbook_icons/icon.png'%>" alt="SportWrench Logo">
                    <span>SportWrench Inc. <%= current_season %></span>
                </p>
            </td>
        </tr>
    </table>
</body>
</html>

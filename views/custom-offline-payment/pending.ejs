<% const renderTicketsBalanceData = (balance_details) => { %>
    <div>
  <span style="margin-bottom: 6px; display: block">
    Cash Tickets Count: <%= balance_details.cash_count %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Check Tickets Count: <%= balance_details.check_count %>
  </span>
    </div>
<% } %>

<% const renderBoothsBalanceData = (balance_details) => { %>
    <div>
  <span style="margin-bottom: 6px; display: block">
    Approved and Paid Exhibitors: <%= balance_details.approved_and_paid %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Approved not Paid Exhibitors: <%= balance_details.approved_not_paid %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Paid not Approved Exhibitors: <%= balance_details.paid_not_approved %>
  </span>
    </div>
<% } %>

<% const renderTeamsBalanceData = (balance_details) => { %>
    <div>
  <span style="margin-bottom: 6px; display: block">
    Accepted Or Paid Teams: <%= balance_details.accepted_or_paid %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Paid Only Teams: <%= balance_details.paid_only %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Accepted Only Teams: <%= balance_details.accepted_only %>
  </span>
        <span style="margin-bottom: 6px; display: block">
    Accepted And Paid Teams: <%= balance_details.accepted_and_paid %>
  </span>
    </div>
<% } %>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
      <title><%= event_name %></title>
  </head>
  <body style="width: 640px; font-family: helvetica, arial; font-size: 16px">
    <p style="font-weight: bold; font-size: 18px;"><b><%= event_name %></b></p>
    <p>
      Payment pending! <%= pending_message %>
    </p>
    <p>
      <span style="margin-bottom: 6px; display: block"><b>Payment details:</b></span>
      <span style="margin-bottom: 6px; display: block">Amount: $<%= totals.amount %></span>
      <span style="margin-bottom: 6px; display: block">Merchant Fee: $<%= totals.merchant_fee %></span>
      <span style="margin-bottom: 6px; display: block">Total: $<%= totals.total %></span>
    </p>
    <p>
      <span style="margin-bottom: 6px; display: block"><b>Accounting details:</b></span>
      <span style="margin-bottom: 6px; display: block">SW Fee on Event: $<%= balance.sw_fee %></span>
      <span style="margin-bottom: 6px; display: block">
        Uncollected SW Fee before Payment: $<%= balance.current_balance_sum %>
      </span>
      <span style="margin-bottom: 6px; display: block">
        Uncollected SW Fee after Payment: $<%= balance.new_balance_sum %>
      </span>
      <% if(payment_type_for === 'teams') { %>
        <%= renderTeamsBalanceData(balance.balance_details); %>
      <% } %>
      <% if(payment_type_for === 'tickets') { %>
        <%= renderTicketsBalanceData(balance.balance_details); %>
      <% } %>
      <% if(payment_type_for === 'booths') { %>
        <%= renderBoothsBalanceData(balance.balance_details); %>
      <% } %>
    </p>
  </body>
</html>

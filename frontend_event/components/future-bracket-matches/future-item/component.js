class Component {
    constructor($state) {
        this.$state = $state;
    }

    isEmpty(ob) {
        return _.isEmpty(ob);
    }

    getInfo(ob) {
        return `${ob.display_name} ${ob.week_day} ${ob.start_time_string} ${ob.court}`;
    }

    showInfo(ob, title) {
        // tournament complete
        if (Object.keys(ob).length === 1 && ob.display_name) {
            return ob.display_name;
        }

        return `${title} ${this.getInfo(ob)}`;
    }

    goToMatch({ match_id }) {
        if (match_id) {
            this.$state.go('events.event.matches.match', {
                match: match_id
            });
        }
    }

}

window.FutureItem = Component;

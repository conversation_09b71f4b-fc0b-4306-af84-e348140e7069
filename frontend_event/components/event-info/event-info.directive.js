angular.module('SportWrench').directive('eventInfo', function ($state, $window, HOME_PAGE_URL, UtilsService) {
    return {
        restrict: 'E',
        scope: {
            info: '='
        },
        templateUrl: 'components/event-info/event-info.html',
        link: function (scope) {
            if(scope.info && typeof scope.info.website == 'string' 
                && !(/^((https?:\/\/)|\/\/)/.test(scope.info.website)) ) {
                scope.info.website= 'http://'+ scope.info.website;
            }

            scope.hasSocialLinks = function () {
                return !(this.info && _.isEmpty(this.info.social_links));
            }

            scope.openDoublesRegForm = function () {
                if(!(scope.info && scope.info.event_id)) return;
                $window.location.replace(HOME_PAGE_URL + '/#/assign/' + scope.info.event_id + '/doubles/type');
            }

            scope.isDoubles = function () {
                return this.info && (this.info.registration_method === 'doubles')
            }

            scope.hasTeams = function () {
                return this.info && (this.info.allow_teams_registration)
            }

            scope.showSWTDate = function () {
                return this.info && (this.info.tickets_published && !this.info.tickets_code)
            }

            scope.showSWTLink = function () {
                return this.info && (this.info.tickets_code && this.info.tickets_published)
            }

            scope.openSWT = function () {
                let link;

                if(this.info.allow_point_of_sales) {
                    link = UtilsService.getTicketsSalesHubLink(this.info.point_of_sales_id);
                } else {
                    link = UtilsService.getTicketsDirectLink(this.info.tickets_code);
                }

                $window.location.replace(link);
            }

            scope.locationsExists = function () {
                let info = this.info;
                return info && info.locations && info.locations.length;
            }

            scope.getLocationTitle = function (locationNumber) {
                return this.info && this.info.locations.length > 1
                    ? `Event Location #${locationNumber}:`
                    : 'Event Location:';
            }
        }
    }
})

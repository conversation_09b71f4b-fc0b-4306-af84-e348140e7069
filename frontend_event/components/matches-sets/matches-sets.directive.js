angular.module('SportWrench').directive('matchesSets', ['$filter', function ($filter) {
	return {
		restrict: 'EA',
		scope: true,
		link: function (scope, iElement, iAttrs) {
			var pools = angular.copy(scope.team.results);
			pools = $filter('filter')(pools, iAttrs.pool);

			scope.gamesWon = 0;
			scope.gamesLost = 0;
			scope.setsWon = 0;
			scope.setsLost = 0;

			var pb_stats = [];

			if (pools[0].pb_stats) {
				for(var key in pools[0].pb_stats) {
				    pb_stats.push(pools[0].pb_stats[key]);
				}
			}

			pools[0].matches.forEach(function(match) {
				if ((scope.team.roster_team_id == match.results.team1.roster_team_id && 
				match.results.winner == '1') || 
				(scope.team.roster_team_id == match.results.team2.roster_team_id && 
				match.results.winner == '2')) {
					scope.gamesWon++;
				} else {
					scope.gamesLost++;
				}

				if (match.results && match.results.set1) {
					var sets1 = match.results.set1.split('-');
					if ((Number(sets1[0]) > Number(sets1[1]) &&
						(scope.team.roster_team_id == match.results.team1.roster_team_id && 
						match.results.winner == '1')) ||
						(Number(sets1[0]) < Number(sets1[1]) &&
						(scope.team.roster_team_id == match.results.team2.roster_team_id && 
						match.results.winner == '2'))) {
						scope.setsWon++;
					} else {
						scope.setsLost++;
					}
				}

				if (match.results && match.results.set2) {
					var sets2 = match.results.set2.split('-');
					if ((Number(sets2[0]) > Number(sets2[1]) &&
						(scope.team.roster_team_id == match.results.team1.roster_team_id && 
						match.results.winner == '1')) ||
						(Number(sets2[0]) < Number(sets2[1]) &&
						(scope.team.roster_team_id == match.results.team2.roster_team_id && 
						match.results.winner == '2'))) {
						scope.setsWon++;
					} else {
						scope.setsLost++;
					}
				}

				if (match.results && match.results.set3) {
					var sets3 = match.results.set3.split('-');
					if ((Number(sets3[0]) > Number(sets3[1]) &&
						(scope.team.roster_team_id == match.results.team1.roster_team_id && 
						match.results.winner == '1')) ||
						(Number(sets3[0]) < Number(sets3[1]) &&
						(scope.team.roster_team_id == match.results.team2.roster_team_id && 
						match.results.winner == '2'))) {
						scope.setsWon++;
					} else {
						scope.setsLost++;
					}
				}

			});
		},
		template: '{{gamesWon}}-{{gamesLost}} ({{setsWon}}-{{setsLost}})'
	};
}]);
<span ng-if="!team.temp_id && !team.id && team.source_type != 5">{{ (team.pool_id ? team.pool_name : false ) || team.source_name || "unknown team"}} <br/> </span>
<span ng-if="!team.temp_id && !team.id && team.source_type == 5"><a ng-if="team.pool_id" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: team.pool_id})">{{team.pool_name}}</a> <br/> </span>

<a ng-if="team.temp_id" ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: team.temp_id})">{{team.temp_name}}</a>
<a ng-if="!team.temp_id && team.id" ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: team.id})">{{team.name}}</a>

<prev-qual-badge accepted-bid="team.show_previously_accepted_bid"></prev-qual-badge>

<div class="spacer-sm-t spacer-sm-l" ng-if="!isRank"><b>** This pool is still in process **</b></div>
<div class="spacer-sm-t spacer-sm-l" ng-if="isRank"><b>This pool is finished</b></div>

<br/>

<span ng-show="pool.standings[0].pb_stats" class="clearfix list-group-item">
  <table class="table table-hover future-table-desktop">
    <thead>
      <tr>
        <td ng-if="isRank" class="col-xs-1">
          <span>&nbsp;</span>
        </td>
        <td class="col-xs-3">Team Name</td>
        <td class="col-xs-2 text-center">Match W-L</td>
        <td class="col-xs-2 text-center" ng-hide="pool.hide_winner">Set W-L</td>
        <td class="col-xs-2 text-center">Point Ratio</td>
        <td class="col-xs-1 text-center"></td>
      </tr>
    </thead>
    <tbody>
      <tr ng-repeat="(key, standing) in pool.standings[0].pb_stats | toArray | orderBy:orderColumn" ng-click="ifAvailableGoTo(standing, events.event.divisions.division.divisionteams.divisionteam.schedule)">
        <td class="col-xs-1" ng-if="standing.rank">
          <b>
            <span>{{standing.rank | suffix}}</span>
          </b>
        </td>
        <td class="col-xs-3 standings-team-name">{{standing.name}}</td>
        <td class="col-xs-2 text-center">{{standing.matches_won}}-{{standing.matches_lost}}</td>
        <td class="col-xs-2 text-center" ng-hide="pool.hide_winner">
          {{standing.sets_won}}-{{standing.sets_lost}} 
          <span ng-if="standing.sets_pct < 10">({{standing.sets_pct | number:2}}%)</span>
          <span ng-if="standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%)</span>
        </td>
        <td class="col-xs-2 text-center">{{standing.points_lost ? num(standing.points_ratio) : "" }}</td>
        <td class="col-xs-1 text-center"><span class="list-arrow glyphicon glyphicon-chevron-right"></span></td>
      </tr>
    </tbody>
  </table>

  <table class="table future-table-mobile">
    <thead>
      <!-- <tr>
        <td class=""><span ng-if="isRank">Rank</span> Team Name</td>
      </tr> -->
      <tr>
        <td class="col-xs-12"><span class="standings-scores-data">Matches, Sets (%), Point Ratio</span></td>
      </tr>
    </thead>
  </table>
</span>

<a ng-show="pool.standings[0].pb_stats" class="list-group-item future-mobile-item" ng-repeat="(key, standing) in pool.standings[0].pb_stats | orderObjectBy:'rank':true | orderBy:orderColumn:reverseSort" ui-sref="events.event.divisions.division.divisionteams.divisionteam.schedule({team: standing.team_id})">
  <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
  <div class="clearfix">
      <b>
          <span ng-if="standing.rank">{{standing.rank | suffix}}</span>
      </b> {{ standing.name }}
  </div>
  <div class="clearfix standings-scores">
    <div ng-hide="standing.matches_lost == '0' && standing.matches_won == '0'">
      <table class="table table-condensed future-table-mobile">
        <tbody>
          <tr>
            <td class="col-xs-12">
              <span class="standings-scores-data">
                {{standing.matches_won}}-{{standing.matches_lost}},
                {{standing.sets_won}}-{{standing.sets_lost}}
                <span ng-if="standing.sets_pct < 10">({{standing.sets_pct | number:2}}%),</span>
                <span ng-if="standing.sets_pct >= 10">({{standing.sets_pct | number:0}}%),</span>
                {{num(standing.points_ratio) | number:2}}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <span ng-show="standing.matches_lost == '0' && standing.matches_won == '0'" class="center-block text-center">No Scores Entered Yet</span>
  </div>
</a>

<br/>

<!-- <strong class="list-group-item" ng-show="pool.results">Results</strong> -->
<h4 ng-show="!pool.results.length">No results</h4>
<table class="table table-hover pool-detail-results">
  <tr ui-sref="events.event.matches.match({match: results.match_id})" ng-repeat="results in pool.results">
    <td class="col-md-8">
      <span class="clearfix" style="padding: 5px 0;">
        <span class="list-arrow link-arrow glyphicon glyphicon-chevron-right"></span>
        <i ng-class="{'fa fa-frown-o': results.results.winner == 2, 'fa fa-smile-o': results.results.winner == 1}"></i>
        <b>{{results.team1_name}}</b> <span ng-if="results.results.winner == 1"><i>Wins</i></span> <span ng-if="results.results.winner == 2"><i>Loses</i></span>
      </span>
      <span class="clearfix" style="padding: 5px 0;">
        <i ng-class="{'fa fa-frown-o': results.results.winner == 1, 'fa fa-smile-o': results.results.winner == 2}"></i>
        <b>{{results.team2_name}}</b> <span ng-if="results.results.winner == 2"><i>Wins</i></span> <span ng-if="results.results.winner == 1"><i>Loses</i></span>
      </span>
    </td>
    <td class="col-md-4 scores">
      <span class="clearfix" style="padding: 5px 0;"><span class="list-arrow glyphicon glyphicon-chevron-right"></span>{{results.results.team1.scores}}</span>
      <span class="clearfix" style="padding: 5px 0;">{{results.results.team2.scores}}</span>
    </td>
  </tr>
</table>
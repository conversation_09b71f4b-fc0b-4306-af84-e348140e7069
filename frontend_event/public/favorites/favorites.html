<div class="modal-header">
    <h3 class="modal-title">
      Favorites
      <button class="btn btn-primary favorite-button" ng-if="!isFavorite" ng-click="addCurrent()">
        <span class="desktop-layout">Add Current to Favorites</span>
        <span class="mobile-layout">Add Current</span>
      </button>
      <button class="btn btn-primary favorite-button" ng-if="isFavorite" ng-click="removeCurrent()">
        <span class="desktop-layout">Remove Current from Favorites</span>
        <span class="mobile-layout">Remove Current</span>
      </button>
    </h3>
</div>
<div class="modal-body">
  <div class="list-group">
    <span ng-show="!$storage.favorites.length || !$storage.favorites[0].title">Add a page to your favorites. Click the Star any time to jump to your favorites.</span>
    
    <div ng-repeat="ev in uniqFavorites">
      <h3 ng-if="ev.eventName">{{ev.eventName}}</h3>
      <a ng-if="ev.event" href="#{{fav.url}}" class="list-group-item" ng-repeat="fav in $storage.favorites | filter: hasTitle() | filter: {event: ev.event}">
        <span class="pull-right">
          <span class="remove-fav pull-right" ng-click="remove(fav)">
            <i class="fa fa-times"></i>
          </span>
          <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        </span>
        <span class="text-ellipsis">{{fav.title}}</span>
      </a>
    </div>

  </div>
</div>
<div class="modal-footer">
    <button class="btn btn-warning" ng-click="cancel()">Close</button>
</div>
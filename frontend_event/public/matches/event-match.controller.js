angular.module('SportWrench')

.controller('Public.Events.MatchController', MatchController);

function MatchController($scope, $rootScope, $stateParams, eswService, currentMatch, $filter)
{
	$scope.title = currentMatch.division_short_name + ' > ' + currentMatch.display_name;
	$rootScope.pageTitle = $scope.title;

  	$scope.head = currentMatch.display_name;

    $scope.match = currentMatch;

   	$scope.match.start = new Date(parseInt($scope.match.date_start));

   	if ($scope.match.results) {
		try {
	   		$scope.match.results = angular.fromJson($scope.match.results);

	   		if ($scope.match.results.team1 && 
	   			$scope.match.results.team1.scores && 
	   			$scope.match.results.team1.scores.indexOf(',undefined-undefined') != -1) {
	   			$scope.match.results.team1.scores = $scope.match.results.team1.scores.replace(',undefined-undefined', '');
	   		}

	   		if ($scope.match.results.team2 && 
	   			$scope.match.results.team2.scores && 
	   			$scope.match.results.team2.scores.indexOf(',undefined-undefined') != -1) {
	   			$scope.match.results.team2.scores = $scope.match.results.team2.scores.replace(',undefined-undefined', '');
	   		}
		} catch (e) {
			console.log('JSON Error: $scope.match.results = ', $scope.match.results);
		}
	}

   	if($scope.match.source) {
		try {
   			$scope.match.source = angular.fromJson($scope.match.source);
		} catch (e) {
			console.log('JSON Error: $scope.match.source = ', $scope.match.source);
		}
   	}   
}

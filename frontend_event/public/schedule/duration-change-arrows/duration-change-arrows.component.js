angular.module('SportWrench').component('durationChangeArrows', {
    templateUrl: 'public/schedule/duration-change-arrows/duration-change-arrows.html',
    bindings: {
        hideLeftArrow       : '<',
        hideRightArrow      : '<',
        disableLeftArrow    : '<',
        disableRightArrow   : '<',
        changeDuration      : '&'
    },
    transclude: true,
    controller: DurationChangeArrowsController,
});

function DurationChangeArrowsController () {}

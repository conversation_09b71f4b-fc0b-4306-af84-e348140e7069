angular.module('SportWrench').component('manualClubs', {
    templateUrl : 'public/clubs/manual/manual-clubs.html',
    controller : ManualClubsController
});

ManualClubsController.$inject = ['ManualClubsService', '$stateParams'];

function ManualClubsController(ManualClubsService, $stateParams) {
    this.clubs = [];

    this.$onInit = function () {
        ManualClubsService.getClubs($stateParams.event)
            .then(({clubs}) => this.clubs = clubs);
    }
}

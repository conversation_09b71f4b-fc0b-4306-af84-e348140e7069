<h3 class="esw_title">
  <div class="form-group div-short">
    <select class="form-control divisions-desktop spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.name for div in divisions">
    </select>
    <select class="form-control divisions-mobile spacer-mobile-sm-t" ng-model="currentDiv" ng-change="changeDivision()" ng-options="div.id as div.short_name for div in divisions">
    </select>
  </div>

  <span class="page-title">Teams <i>({{teamsCount}})</i></span>
</h3>

<div class="list-group">
    <spinner active="teams == undefined"></spinner>
    <div ng-show="!teams.length">0 teams</div>
    <a 
      ui-sref="events.event.divisions.division.divisionteams.divisionteam({team: team.roster_team_id})" 
      class="list-group-item" 
      ng-repeat="team in teams | orderBy: 'team_name'">
        <span>{{team.team_name}}</span>
        <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
        <span class="list-text-right team-code-adaptive">{{team.organization_code}} {{team.state}}</span>
    </a>
</div>

<br/>

<!-- <div ng-show="team.upcoming == undefined && team.upcoming">
  <img class="loading-image" src="/images/loading-bubbles.svg" width="64" height="64" alt="">
</div> -->

<span ng-show="!team.upcoming.length">No upcoming matches</span>

<div ng-repeat="rnd in team.upcoming | unique: 'round'" class="list-group">
  <div ng-repeat="mt in team.upcoming | unique: 'pool_name' | filter:rnd.round" class="list-group">
    <a class="list-group-item list-group-item-info" ng-if="mt.is_pool" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({pool: mt.pool_bracket_id})">
      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
      <span class="match-info-icon fa fa-list-ol"></span>
      <span class="underlined">{{ mt.round_name }} {{ mt.pb_name }} - {{ mt.matches_played_exp }}</span>
    </a>
    <a class="list-group-item list-group-item-info" ng-if="!mt.is_pool" ui-sref="events.event.divisions.division.divisionbrackets.bracket({bracket: mt.pool_bracket_id})">
      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
      <span class="match-info-icon fa fa-sitemap fa-rotate-90"></span>
      <span class="underlined">{{ mt.round_name }} {{ mt.pb_name }} Bracket</span>
    </a>
    <a class="list-group-item list-group-item-warning" ui-sref="events.event.divisions.division.pools.pooldetails.schedule({
      event: $stateParams.event,
      division: $stateParams.division,
      pool: mt.pool_bracket_id
    })">
      <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
      <span class="match-info-icon fa fa-binoculars"></span>
      See Future Matches
    </a>
    <a ng-repeat="match in team.upcoming | filter:mt.pool_name" class="list-group-item" ui-sref="events.event.matches.match({match: match.match_id})">
      <div title="{{ match.date_start_formatted }}">
        <div class="clearfix">
          <div class="match-info-list">
            <i class="match-info-icon" ng-class="{'fa fa-flag': match.match_type == 'ref', 'fa fa-play-circle-o': match.match_type != 'ref'}"></i>
            <span class="list-arrow glyphicon glyphicon-chevron-right"></span>
            <span>{{ match.start | UTCdate: 'ddd h:mma' }} {{ match.court_name }}<span ng-show="match.match_type == 'ref'"> - Officiate</span></span>
            <br/>
            <span class="match-opponent"
                  ng-show="match.match_type != 'ref' && match.opponent_team_name">
                <b>{{ match.opponent_team_name }}</b>
                <span ng-if="match.opponent_organization_code">({{ match.opponent_organization_code }})</span>
            </span>
            <br/>
            <span ng-show="match.footnote_play && match.match_type != 'ref'"><i class="match-info-icon footnote-icon fa fa-flag"></i> <i>{{ match.footnote_play }}</i></span>
          </div>          
        </div>
      </div>
    </a>
  </div>
</div>

<future-bracket-matches
    ng-if="showFutureBracketMatches()"
    finishes="finishes"
>

</future-bracket-matches>

angular.module('SportWrench')

.controller('Public.Events.FlowChartController', flowChartController);

function flowChartController($http, $scope, $state, $rootScope, $stateParams, $location, eswService, eventsList, currentEvent, currentDivision, currentStandings, HOME_PAGE_URL)
{
	$rootScope.pageTitle    = "Flow Chart";
	$scope.currentDiv       = currentDivision.division_id;
	$scope.divisions        = eswService.getCurrentDivisions();

	$scope.changeDivision = function() {
		$state.go('events.event.divisions.division.flowchart', {division: $scope.currentDiv});
	};

    const flowchart_path    = `${HOME_PAGE_URL}${currentDivision.flowchart_path}`;

	$scope.source = currentDivision.flowchart_path
        ? flowchart_path
        : `data/events/${currentEvent.id}/flow-charts/${currentDivision.short_name}.png`;
}

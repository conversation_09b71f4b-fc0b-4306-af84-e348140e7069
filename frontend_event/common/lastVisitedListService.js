/**
* Stires last 5 visited events in localstorage. 
* The stored list is used on the SW main page at "Your Recent Events" Accordion
* eswlv for ESW Last Visited
*/
angular.module('SportWrench').factory('lastVisitedListService', function () {
    var cookiesDomain = '',
        __getDomain = function () {
            if(cookiesDomain) return cookiesDomain;
            var host = location.host,
                splitted = host.split('.');
            if(splitted.length === 1) { 
                cookiesDomain = host;
                return host;
            }
            splitted.shift();
            cookiesDomain = splitted.join('.')
            return cookiesDomain;
        },
        __getCookie = function (name) {
          var matches = document.cookie.match(new RegExp(
            "(?:^|; )" + name.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g, '\\$1') + "=([^;]*)"
          ));
          return matches ? decodeURIComponent(matches[1]) : undefined;
        },
        COOKIE_NAME = 'eswlv',
        SPLIT_SYMBOL = '|'
    return {
        addLastVisited: function (event) {
            if(!navigator.cookieEnabled || !event) return; 
            var cookieValue = __getCookie(COOKIE_NAME),
                eventsList = cookieValue?cookieValue.split(SPLIT_SYMBOL):[];    
            for(var i = 0, l = eventsList.length; i < l; ++i) {
                if(parseInt(eventsList[i], 10) === parseInt(event.event_id, 10)) return;
            }
            eventsList.push(event.event_id);
            if(eventsList.length > 5) eventsList.shift();
            document.cookie = COOKIE_NAME + '=' + eventsList.join(SPLIT_SYMBOL) + '; domain=' + __getDomain();
        }
    }
})

angular.module('SportWrench')

.config(function($stateProvider, $urlRouterProvider) {

    $urlRouterProvider.otherwise('/');

    $stateProvider.state('pb_for_match', {
        url: '/pb_for_match/:uuid',
        views: {
            'rootView': {
                templateUrl: 'public/matches/pb_for_match.html',
                controller: 'Public.Events.PbForMatch'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events', {
        url: '/',
        views: {
            'rootView': {
                templateUrl: 'public/events/events.html',
                controller: 'Public.Events.EventsController'
            }
        },
        data: {
            displayName: false
        },
        resolve: {
            eventsList: function (eswService, $q) {
                var defer = $q.defer();
                eswService.getEvents(function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event', {
        url: 'events/:event',
        views: {
            'rootView@': {
                templateUrl: 'public/events/event.html',
                controller: 'Public.Events.EventController'
            }
        },
        data: {
            displayName: '{{currentEvent.name}}',
            breadcrumbProxy: '{{openFavorites}}'
        },
        resolve: {
            currentEvent: currentEvent
        }
    });

    $stateProvider.state('events.event.db100final', {
        url: '/db100final',
        views: {
            'rootView@': {
                templateUrl: 'db100final.html',
                controller: function($scope, $http) {
                    $http.get('data/db100final.json').success(function(data) {
                        $scope.items = data;
                    });
                }
            }
        },
        data: {
            displayName: 'DB100'
        }
    });

    $stateProvider.state('events.event.prevqual', {
        url: '/prevqual',
        views: {
            'rootView@': {
                templateUrl: 'prevqual.html',
                controller: function($scope, $http, currentEvent) {
                    $http.get('/api/esw/' + currentEvent.id + '/prevqual').then(function(response) {
                        $scope.items = response && response.data && response.data.teams;
                    });
                }
            }
        },
        data: {
            displayName: 'BSQ'
        }
    });

    $stateProvider.state('events.event.schedule', {
        url: '/schedule',
        views: {
            'rootView@': {
                templateUrl: 'public/schedule/schedule.html',
                controller: 'ScheduleController'
            }
        },
        data: {
            displayName: false
        },
        resolve: {
            currentSchedule: getCurrentSchedule
        }
    });

    $stateProvider.state('events.event.schedule-officials', {
        url: '/schedule-officials',
        views: {
            'rootView@': {
                templateUrl: 'public/schedule/schedule-officials.html',
                controller: 'ScheduleOfficialsController'
            }
        },
        data: {
            displayName: false
        },
        resolve: {
            currentSchedule: getCurrentSchedule
        }
    });

    $stateProvider.state('events.event.courts', {
        url: '/courts',
        views: {
            'rootView@': {
                templateUrl: 'public/courts/event-courts.html',
                controller: 'Public.Events.CourtsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.courts.courtdetails', {
        url: '/:court',
        views: {
            'rootView@': {
                templateUrl: 'public/courts/event-court-details.html',
                controller: 'Public.Events.CourtDetailsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.matches', {
        url: '/matches',
        views: {
            'rootView@': {
                templateUrl: 'public/matches/event-matches.html',
                controller: function($scope) {

                }
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.matches.match', {
        url: '/:match',
        views: {
            'rootView@': {
                templateUrl: 'public/matches/event-match.html',
                controller: 'Public.Events.MatchController'
            }
        },
        data: {
            displayName: '{{currentMatch.division_short_name}}'
        },
        resolve: {
            currentMatch: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getMatch($stateParams.event, $stateParams.match, function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.divisions', {
        url: '/divisions',
        views: {
            'rootView@': {
                templateUrl: 'public/divisions/event-divisions.html',
                controller: 'Public.Events.DivisionsController'
            }
        },
        data: {
            displayName: false
        },
        resolve: {
            divisionsList: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getEventDivisions($stateParams.event, function(response) {
                    if(!Array.isArray(response.data)) {
                        defer.resolve({});
                    } else {
                        var divisions = response.data.map(function(div) {
                            return {
                                id: div.division_id,
                                name: div.name,
                                short_name: div.short_name
                            };
                        });

                        eswService.setCurrentDivisions(divisions);

                        defer.resolve(response.data);
                    }
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.divisions.division', {
        url: '/:division',
        views: {
            'rootView@': {
                templateUrl: 'public/divisions/event-division-menu.html',
                controller: 'Public.Events.DivisionTeamsController'
            }
        },
        data: {
            displayName: '{{currentDivision.short_name}}'
        },
        resolve: {
            currentDivision: function ($stateParams, eswService, $q, divisionsList) {
                var defer = $q.defer();
                divisionsList.forEach(function(division) {
                    if ($stateParams.division == division.division_id) {
                        defer.resolve(division);
                    }
                });
                return defer.promise;
            },
            divisionTeams: function($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getEventDivisionTeams($stateParams.event, $stateParams.division, function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            },
            currentPools: function($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getPools($stateParams.event, $stateParams.division, function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            },
            currentStandings: function($stateParams, eswService, $q, $location) {
                const withCacheCleaning = $location.search().withCacheCleaning;

                var defer = $q.defer();
                eswService.getEventDivisionStandings($stateParams.event, $stateParams.division, withCacheCleaning, function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.divisions.division.flowchart', {
        url: '/flowchart',
        views: {
            'rootView@': {
                templateUrl: 'public/divisions/flow-chart.html',
                controller: 'Public.Events.FlowChartController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.qualified', {
        url: '/qualified',
        views: {
            'rootView@': {
                templateUrl: 'public/divisions/qualified.html',
                controller: 'Public.Events.QualifiedController'
            }
        },
        data: {
            displayName: false
        },
        resolve: {
            jsonData: function ($q, $http, currentEvent) {
                var defer = $q.defer();
                $http.get('data/events/' + currentEvent.id + '/prevqual.json').success(function(data) {
                    defer.resolve(data);
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams', {
        url: '/teams',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-teams.html',
                controller: 'Public.Events.DivisionTeamsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam', {
        url: '/:team',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-team.html',
                controller: 'Public.Events.TeamController'
            }
        },
        data: {
            displayName: '{{currentTeam.team_name}}'
        },
        resolve: {
            currentTeam: getCurrentTeam
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam.schedule', {
        url: '/schedule',
        templateUrl: 'public/teams/event-team-shedule.html',
        controller: 'Public.Events.TeamSheduleController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam.future', {
        url: '/future',
        templateUrl: 'public/teams/event-team-future.html',
        controller: 'Public.Events.TeamFutureController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam.results', {
        url: '/results',
        templateUrl: 'public/teams/event-team-results.html',
        controller: 'Public.Events.TeamResultsController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionteams.divisionteam.roster', {
        url: '/roster',
        templateUrl: 'public/teams/event-team-roster.html',
        controller: 'Public.Events.TeamRosterController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.pools', {
        url: '/pools',
        views: {
            'rootView@': {
                templateUrl: 'public/pools/event-pools.html',
                controller: 'Public.Events.PoolsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.pools.pooldetails', {
        url: '/:pool',
        views: {
            'rootView@': {
                templateUrl: 'public/pools/event-pool-details.html',
                controller: 'Public.Events.PoolDetailsController'
            }
        },
        data: {
            displayName: '{{currentPool.display_name}}'
        },
        resolve: {
            currentPool: getCurrentPool
        }
    });

    $stateProvider.state('events.event.divisions.division.pools.pooldetails.schedule', {
        url: '/schedule',
        templateUrl: 'public/pools/event-pool-details-shedule.html',
        controller: 'Public.Events.PoolDetailsSheduleController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.pools.pooldetails.results', {
        url: '/results',
        templateUrl: 'public/pools/event-pool-details-results.html',
        controller: 'Public.Events.PoolDetailsResultsController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.pools.pooldetails.standings', {
        url: '/standings',
        templateUrl: 'public/pools/event-pool-details-standings.html',
        controller: 'Public.Events.PoolDetailsStandingsController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.pools.pooldetails.future', {
        url: '/future',
        templateUrl: 'public/pools/event-pool-details-future.html',
        controller: 'Public.Events.PoolDetailsFutureController',
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionstandings', {
        url: '/standings',
        views: {
            'rootView@': {
                templateUrl: 'public/divisions/event-standings.html',
                controller: 'Public.Events.DivisionStandingsController'
            }
        },
        data: {
            displayName: 'Standings'
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionbrackets', {
        url: '/brackets',
        views: {
            'rootView@': {
                templateUrl: 'public/brackets/brackets.html',
                controller: 'Public.Events.DivisionBracketsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.divisions.division.divisionbrackets.bracket', {
        url: '/:bracket',
        views: {
            'rootView@': {
                templateUrl: 'public/brackets/bracket.html',
                controller: 'Public.Events.DivisionBracketController'
            }
        },
        data: {
            displayName: '{{currentBracket.display_name}}'
        },
        resolve: {
            currentBracket: function ($state, $stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getPoolDetail($stateParams.event, $stateParams.bracket, function(response) {
                    if (response.data && response.data.is_pool == 1) {
                        var params = angular.extend({
                                'pool': $stateParams.bracket
                            }, $stateParams);
                        $state.go('events.event.divisions.division.pools.pooldetails', params);
                        defer.reject();
                    } else {
                        defer.resolve(response.data);
                    }
                });
                return defer.promise;
            },
            bracketMatches: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getBracketMatches($stateParams.event, $stateParams.bracket, function(response) {
                    defer.resolve(response.data);
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.clubs', {
        url: '/clubs',
        views: {
            'rootView@': {
                templateUrl : 'public/clubs/event-clubs.html',
                controller  : 'Public.Events.ClubsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.clubs.clubteams', {
        url: '/:club',
        views: {
            'rootView@': {
                templateUrl: 'public/clubs/event-club-teams.html',
                controller: 'Public.Events.ClubTeamsController'
            }
        },
        data: {
            displayName: '{{currentClub.club_name}}'
        },
        resolve: {
            currentClub: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getEventClubs($stateParams.event, function(response) {
                    for (var i = response.data.length - 1; i >= 0; i--) {
                        if ($stateParams.club == response.data[i].roster_club_id) {
                            defer.resolve(response.data[i]);
                            break;
                        }
                    };
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.clubs.clubteams.clubteam', {
        url: '/:team',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-team.html',
                controller: 'Public.Events.TeamController'
            }
        },
        data: {
            displayName: '{{currentTeam.team_name}}'
        },
        resolve: {
            currentTeam: getCurrentTeam
        }
    });

    $stateProvider.state('events.event.manualclubs', {
        url: '/manual-clubs',
        views: {
            'rootView@': {
                template : '<manual-clubs></manual-clubs>',
            }
        }
    });

    $stateProvider.state('events.event.manualclubs.clubteams', {
        url: '/:club',
        views: {
            'rootView@': {
                template: '<manual-club-teams></manual-club-teams>',
            }
        },
        params: {
            club: null
        }
    });

    $stateProvider.state('events.event.athletes', {
        url: '/athletes',
        views: {
            'rootView@': {
                templateUrl: 'public/athletes/event-athletes.html',
                controller: 'Public.Events.AthletesController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.athletes.athleteteams', {
        url: '/:athlete',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-teams.html',
                controller: 'Public.Events.AthleteTeamsController'
            }
        },
        data: {
            displayName: '{{currentAthlete.name}}'
        },
        resolve: {
            currentAthlete: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getEventClubTeams($stateParams.event, $stateParams.athlete, function(response) {
                    console.log(response)
                    for (var i = response.data.length - 1; i >= 0; i--) {
                        if ($stateParams.athlete == response.data[i].id) {
                            defer.resolve(response.data[i]);
                            break;
                        }
                    };
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.staff', {
        url: '/staff',
        views: {
            'rootView@': {
                templateUrl: 'public/staff/event-staff.html',
                controller: 'Public.Events.StaffController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.staff.staffteams', {
        url: '/:staff',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-teams.html',
                controller: 'Public.Events.StaffTeamsController'
            }
        },
        data: {
            displayName: '{{currentStaff.name}}'
        },
        resolve: {
            currentStaff: function ($stateParams, eswService, $q) {
                var defer = $q.defer();
                eswService.getEventStaffTeams($stateParams.event, $stateParams.staff, function(response) {
                    console.log(response)
                    for (var i = response.data.length - 1; i >= 0; i--) {
                        if ($stateParams.staff == response.data[i].id) {
                            defer.resolve(response.data[i]);
                            break;
                        }
                    };
                });
                return defer.promise;
            }
        }
    });

    $stateProvider.state('events.event.teams', {
        url: '/teams',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-teams.html',
                controller: 'Public.Events.TeamsController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('events.event.teams.team', {
        url: '/:team',
        views: {
            'rootView@': {
                templateUrl: 'public/teams/event-team.html',
                controller: 'Public.Events.TeamController'
            }
        },
        data: {
            displayName: '{{currentTeam.team_name}}'
        },
        resolve: {
            currentTeam: getCurrentTeam
        }
    });

    $stateProvider.state('brackets-test', {
        url: '/brackets-test/:bracketTpl',
        views: {
            'rootView@': {
                templateUrl: 'public/brackets-test/brackets-test.html',
                controller: 'BracketsTestController'
            }
        },
        data: {
            displayName: false
        }
    });

    $stateProvider.state('testerror', {
        url: '/make_error',
        views: {
            'rootView': {
                template: '<p class="text-danger text-center">An error occured</p>',
                controller: function () {}
            }
        },
        resolve: {
            makeError: function () {
                throw new Error('This is a test error from "testerror" state');
            }
        }
    });

    $stateProvider.state('invalid-event', {
        url: '/invalid-event-id',
        views: {
            'rootView': {
                templateUrl: 'public/errors/invalid-event-id.html',
                controller: function () {}
            }
        }
    })

    function currentEvent($stateParams, eswService, $q, $timeout, $rootScope) {
        var defer = $q.defer();
        var timeout = $timeout(function () {
            defer.resolve({});
        }, 15 * 1000);
        eswService.getEvent($stateParams.event, function(response) {
            $timeout.cancel(timeout);
            $rootScope.currentEvent = response.data;
            defer.resolve(response.data);
        });
        return defer.promise;
    }

    function getCurrentTeam($stateParams, eswService, $q) {
        var defer = $q.defer();
        eswService.getEventTeam($stateParams.event, $stateParams.team, function(response) {
            defer.resolve(response.data);
        });
        return defer.promise;
    }

    function getCurrentPool($state, $stateParams, eswService, $q) {
        var defer = $q.defer();
        eswService.getPoolDetail($stateParams.event, $stateParams.pool, function(response) {
            if (response.data && response.data.is_pool == 0) {
                var params = angular.extend({
                        'bracket': $stateParams.pool
                    }, $stateParams);
                $state.go('events.event.divisions.division.divisionbrackets.bracket', params);
                defer.reject();
            } else {
                defer.resolve(response.data);
            }
        });
        return defer.promise;
    }

    function getCurrentSchedule ($rootScope, $stateParams, eswService, $q, currentEvent, $localStorage) {
        let start_hour = 0,
            hours_count = 3;

        let localStorageCourtFilters = $localStorage.courtFilters && $localStorage.courtFilters[$stateParams.event];

        if(!_.isEmpty(localStorageCourtFilters)) {
            $rootScope.courtFilters = localStorageCourtFilters;
        }

        const EVENT_DAY_FORMAT = 'YYYY-MM-DD';
        const EVENT_DAYS_FORMAT = 'MM/DD/YYYY';

        if ($rootScope.courtFilters.event_id === $stateParams.event) {
            if ($rootScope.courtFilters.start_hour) {
                start_hour = $rootScope.courtFilters.start_hour.time;
            }
            hours_count = $rootScope.courtFilters.hours_count;
        }

        let event_day;

        if (start_hour && $rootScope.courtFilters.event_day) {
            event_day = moment.utc($rootScope.courtFilters.event_day).format(EVENT_DAY_FORMAT);
        } else {
            let date_start = moment.utc(currentEvent.date_start, EVENT_DAYS_FORMAT),
                date_end = moment.utc(currentEvent.date_end, EVENT_DAYS_FORMAT),
                days_count = date_end.diff(date_start, 'days');

            let days_array = [];

            for (let i = 0, count = days_count; i <= count; i++) {
                days_array.push(moment(date_start).add(i, 'd'));
            }

            event_day = date_end.format(EVENT_DAY_FORMAT);

            if (moment(event_day, EVENT_DAY_FORMAT).isAfter(new Date())) {
                event_day = date_start.format(EVENT_DAY_FORMAT);
            }

            var current_date = moment(new Date()).utc().format(EVENT_DAY_FORMAT);

            if (days_array && days_array.length) {
                days_array.forEach(function(date) {
                    if (date.format(EVENT_DAY_FORMAT) === current_date) {
                        event_day = moment.utc(date).format(EVENT_DAY_FORMAT);
                    }
                });
            }
        }

        if(!moment(event_day, EVENT_DAY_FORMAT).isValid()) {
            event_day = moment.utc().format(EVENT_DAY_FORMAT);
        }

        console.log(event_day, start_hour)

        return eswService.getCourtSchedule($stateParams.event, event_day, start_hour, hours_count);
    }
});

- hosts: all
  vars:
      deploy_folder: /home/<USER>/sw-main
      image_name: sw-main
      container_name: sw-main

  tasks:
  - name: Copy docker files to the server
    copy: src={{ item.src }} dest={{ item.dest }}
    with_items:
    - { src: '../.dockerignore', dest: "{{ deploy_folder }}" }
    - { src: '../Dockerfile', dest: "{{ deploy_folder }}" }
    - { src: '../docker-compose.yml', dest: "{{ deploy_folder }}" }
    register: dockerfile_copy

  - name: Rebuild docker image
    shell: export uid=$(id -u) gid=$(id -g) LOG_PG_CS={{ LOG_PG_CS }} LOG_APP_ID={{ LOG_APP_ID }} HOST_PORT={{ HOST_PORT }} NODE_ENV={{ NODE_ENV }} WORK_DIR={{ deploy_folder }} SW_DB={{ SW_DB }} EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }} REDIS_URL={{ REDIS_URL }} && docker-compose -f docker-compose.yml down && docker-compose -f docker-compose.yml up -d --build
    args:
        chdir: "{{ deploy_folder }}"
    when: dockerfile_copy.changed

  - name: Check if container is running
    command: docker container inspect "{{ container_name }}"
    register: container_info
    ignore_errors: True

  - name: Run pm2 reload
    shell: docker exec --env "NODE_ENV={{ NODE_ENV }}" --env "SW_DB={{ SW_DB }}" --env "EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }}" --env "REDIS_URL={{ REDIS_URL }}" "{{ container_name }}" pm2 reload ecosystem-{% if NODE_ENV=="production" %}prod{% else %}dev{% endif %}.json
    when: container_info is succeeded

  - name: Start docker container
    environment:
        REDIS_URL: REDIS_URL
        SW_DB: $SW_DB_DEV
    shell: uid=$(id -u) gid=$(id -g) LOG_PG_CS={{ LOG_PG_CS }} LOG_APP_ID={{ LOG_APP_ID }} HOST_PORT={{ HOST_PORT }} NODE_ENV={{ NODE_ENV }} WORK_DIR={{ deploy_folder }} SW_DB={{ SW_DB }} REDIS_URL={{ REDIS_URL }} EMAIL_REDIS_URL={{ EMAIL_REDIS_URL }} docker-compose -f docker-compose.yml up -d
    args:
        chdir: "{{ deploy_folder }}"
    when: container_info is failed

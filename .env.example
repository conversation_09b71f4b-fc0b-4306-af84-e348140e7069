UID=501
GID=1000
NODE_ENV=development
TEST_NODE_ENV=development
REDIS_URL=redis://localhost:6379
SW_DB=postgresql://postgres:postgres@localhost:5432/sw_dev
TEST_DB_CONNECTION=postgresql://postgres:postgres@localhost:5432/sw_dev_test
WORK_DIR=/home/<USER>/app
LOG_PG_CS=*******************************************/pm2_logger
EMAIL_REDIS_URL=redis://localhost:6379
LOG_APP_ID=1
HOST_PORT=3050
URLS='{"home_page":{"hostname":"dev.swstage.com","baseUrl":"https://dev.swstage.com"},"main_app":{"hostname":"my.dev.swstage.com","baseUrl":"https://my.dev.swstage.com"},"esw":{"hostname":"events.swstage.com","baseUrl":"https://events-dev.swstage.com"},"swt":{"hostname":"tickets-dev.swstage.com","baseUrl":"https://tickets-dev.swstage.com"},"scores":{"hostname":"scores.swstage.com","baseUrl":"https://scores.swstage.com"}}'
SW_LOGGER_FILE_PATH=./

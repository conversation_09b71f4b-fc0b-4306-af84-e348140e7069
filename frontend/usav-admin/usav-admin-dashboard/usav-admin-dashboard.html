<div class="row" style="margin-bottom: 15px">
    <div class="col-sm-2">
        <sw-searchbox
            css="search_teams white-ro"
            reload="$ctrl.filterSearch()"
            input-model="$ctrl.search.club"
            placeholder="Filter Clubs and Teams ..."
            is-readonly="$ctrl.dashboardTable.settings().$loading"
            reload-time="1000"
        ></sw-searchbox>
    </div>
    <div class="col-sm-2">
        <sw-searchbox
            css="search_teams white-ro"
            reload="$ctrl.filterSearch()"
            input-model="$ctrl.search.members"
            placeholder="Filter Individuals ..."
            is-readonly="$ctrl.dashboardTable.settings().$loading"
            reload-time="1000"
        ></sw-searchbox>
    </div>
    <div class="col-sm-2">
        <div class="form-group">
            <select ng-model="$ctrl.filters.role" class="form-control">
                <option value="" selected>Athletes and Staff</option>
                <option value="athlete">Athletes Only</option>
                <option value="staff">Staff Only</option>
            </select>
        </div>
    </div>
    <div class="col-sm-2">
        <div class="form-group">
            <select ng-model="$ctrl.filters.safesport" class="form-control">
                <option value="" selected>Any SS Status</option>
                <option value="true">SS Verified</option>
                <option value="false">SS Not Verified</option>
            </select>
        </div>
    </div>
    <div class="col-sm-1 pull-right">
        <button
            class="ctrl-btn btn btn-default text-danger"
            ng-if="$ctrl.isFiltersApplied()"
            uib-tooltip="Reset All Filters"
            ng-click="$ctrl.clearFilters()"
        >
            <i class="fa fa-times fa-lg" title="Clear All filters"></i>
            <span>Reset</span>
        </button>
    </div>
</div>
<div class="row">
    <div class="col-lg-12" loading-container="$ctrl.dashboardTable.settings().$loading">
        <table class="table ng-table sw-adaptive-grid table-condensed event-all-teams-table table-hover"
               ng-table="$ctrl.dashboardTable"
               sticky-header>
            <thead>
            <tr>
                <th ng-class="$ctrl.columnClass('club_name')" ng-click="$ctrl.sort('club_name')">
                    <div class="sort-indicator">Club Name</div>
                </th>
                <th ng-class="$ctrl.columnClass('team_usav')" ng-click="$ctrl.sort('team_usav')">
                    <div class="sort-indicator">Team USAV</div>
                </th>
                <th class="text-center">
                    <div>Role/Uniform</div>
                </th>
                <th ng-class="$ctrl.columnClass('first')" ng-click="$ctrl.sort('first')">
                    <div class="sort-indicator">First</div>
                </th>
                <th ng-class="$ctrl.columnClass('last')" ng-click="$ctrl.sort('last')">
                    <div class="sort-indicator">Last</div>
                </th>
                <th class="text-center">
                    <div>SS</div>
                </th>
                <th class="text-center">
                    <div>BKG</div>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr class="bg-info text-center" ng-if="$ctrl.dashboardTable.settings().$loading">
                <td colspan="7">Loading ...</td>
            </tr>
            <tr ng-repeat="m in $data track by $index">
                <td><a href="" ng-bind="m.club_name" ng-click="$ctrl.openClub(m)"></a></td>
                <td ng-bind="m.team_usav"></td>
                <td ng-bind="m.role_uniform"></td>
                <td><a href="" ng-bind="m.first" ng-click="$ctrl.openMemberInfoModal(m.id, m.role)"></a></td>
                <td><a href="" ng-bind="m.last" ng-click="$ctrl.openMemberInfoModal(m.id, m.role)"></a></td>
                <td class="col-xs-1">
                    <usav-dashboard-checkbox member="m" type="ss"></usav-dashboard-checkbox>
                </td>
                <td class="col-xs-1">
                    <usav-dashboard-checkbox member="m" type="bkg"></usav-dashboard-checkbox>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
<pagination
    total="$ctrl.utils.totalRows"
    limit="$ctrl.filters.limit"
    page="$ctrl.filters.page"
    change-page="$ctrl.changePage(page)"
    current="$ctrl.members.length"
    loading="$ctrl.dashboardTable.settings().$loading"
></pagination>

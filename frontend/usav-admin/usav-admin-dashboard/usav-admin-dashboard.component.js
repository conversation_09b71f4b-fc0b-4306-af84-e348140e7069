angular.module('SportWrench').component('usavAdminDashboard', {
    templateUrl 	: 'usav-admin/usav-admin-dashboard/usav-admin-dashboard.html',
    controller 		: UsavAdminDashboardController
});

UsavAdminDashboardController.$inject = ['UsavAdminService', '$stateParams', 'ngTableParams'];

function UsavAdminDashboardController(UsavAdminService, $stateParams, ngTableParams) {
    let self = this;

    this.members        = [];
    this.search         = {};
    this.role           = null;
    this.defaultSort    = false;
    this.filters        = {
        page    : 1,
        limit   : 20
    };

    this.utils = { totalRows: 0 };

    let getData = function ($q, params) {
        let _params = {};
        let filter  = params.filter();
        let orderBy = params.orderBy();

        if (filter) {
            _params = _.clone(filter);
        }

        if(orderBy && orderBy.length && !self.defaultSort) {
            _params.order  = orderBy[0].substr(1);
            _params.revert = orderBy[0].charAt(0) === '-';
        }

        return UsavAdminService.getDashboardData($stateParams.event, _params).then(function(members) {
            self.members            = members;
            self.defaultSort        = false;
            self.utils.totalRows    = members[0] && members[0].total_rows || 0;

            return members;
        })
    };

    this.dashboardTable = new ngTableParams({
        page        : 1,
        count       : 20,
        sorting     : {},
        filter      : self.filters
    }, {
        total       : 0,
        counts      : [],
        filterDelay : 0,
        getData     : getData
    });

    this.columnClass = function (column) {
        return {
            'text-center sortable'  : true,
            'sort-asc'              : self.dashboardTable.isSortBy(column, 'asc'),
            'sort-desc'             : self.dashboardTable.isSortBy(column, 'desc')
        };
    };

    this.sort = function (column) {
        self.filters.page = 1;
        self.dashboardTable.sorting(column, self.dashboardTable.isSortBy(column, 'asc') ? 'desc'
            : self.dashboardTable.isSortBy(column, 'desc') ? self.setSortDefault() : 'asc');
    };

    this.setSortDefault = function () {
        self.defaultSort = true;
        return 'text-center sortable';
    };

    this.filterSearch = function () {
        if(self.dashboardTable.settings().$loading) return;

        self.filters.search   = self.search;
        self.filters.page     = 1;
    };

    this.clearFilters = function () {
        self.search                 = {};
        self.filters.search         = {};
        self.filters.safesport      = "";
        self.filters.role           = "";
        self.filters.page           = 1;
    };

    this.isFiltersApplied = function () {
        let omitFilterProps = ['limit', 'page', 'revert'];
        let filters = Object.assign({}, self.filters);

        if(filters.search) {
            filters.membersSearch   = filters.search.members;
            filters.clubSearch      = filters.search.club;
        }

        let keys = Object.keys(filters);

        for(let i = 0, f, k; i < keys.length; ++i) {
            k = keys[i];

            if(omitFilterProps.indexOf(k) === -1) {

                f = filters[k];

                if(f && f.length) {
                    return true;
                }

            }
        }

        return false;
    };

    this.openMemberInfoModal = function(id, role) {
        UsavAdminService.openMemberInfoModal(id, role);
    };

    this.changePage = function (page) {
        self.filters.page = page;
    }

    this.openClub = function (member) {
        UsavAdminService.openClubModal(member.roster_club_id, member.club_name);
    }
}

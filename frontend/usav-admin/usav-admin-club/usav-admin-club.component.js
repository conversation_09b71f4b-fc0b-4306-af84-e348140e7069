angular.module('SportWrench').component('usavAdminClub', {
    templateUrl 	: 'usav-admin/usav-admin-club/usav-admin-club.html',
    bindings        : {
        clubId  : '<'
    },
    controller 		: UsavAdminClubController
});

UsavAdminClubController.$inject = ['UsavAdminService', '$stateParams'];

function UsavAdminClubController(UsavAdminService, $stateParams) {
    let self = this;

    this.club       = [];
    this.teams      = [];
    this.loading    = false;

    this.$onInit = function () {
        loadClubData();
    };

    function loadClubData() {
        self.loading = true;

        UsavAdminService.getClubData(self.clubId, $stateParams.event)
            .then(clubData => {
                if(clubData) {
                    self.club   = clubData.club;
                    self.teams  = clubData.teams;
                }

                self.loading = false;
            })
    }

}

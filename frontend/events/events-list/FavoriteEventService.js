angular.module('SportWrench').service('FavoriteEventService', ['$http', FavoriteEventService]);

function FavoriteEventService ($http) {
    this.$http = $http;
}

FavoriteEventService.prototype.addToFavorites = function (eventID) {
    return this.$http.post(`/api/event/${eventID}/favorite`);
};

FavoriteEventService.prototype.removeFromFavorites = function (eventID) {
    return this.$http.delete(`/api/event/${eventID}/favorite`);
};

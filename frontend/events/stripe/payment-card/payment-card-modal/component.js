
class Controller {
    constructor () {
        // Empty constructor - no dependencies needed for this component
    }

    $onInit () {
        this.tabs = { active: this.activeTab || 0 };
    }

    onCardSave() {
        this.tabs.active = 0;
    }
}

// Add $inject annotation for minification safety
Controller.$inject = [];

angular.module('SportWrench').component('paymentCardModal', {
    templateUrl: 'events/stripe/payment-card/payment-card-modal/template.html',
    bindings: {
        activeTab: '<'
    },
    controller: Controller
});

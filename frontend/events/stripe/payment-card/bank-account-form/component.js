class Controller {
    constructor(StripeElementsService, PaymentCardService, toastr, $scope) {
        this._StripeElementsService = StripeElementsService;
        this._PaymentCardService = PaymentCardService;
        this._toastr = toastr;
        this.$scope = $scope;
    }

    $onInit() {
        this.isLoading = true;
        this.accounts = [];
        this.saveButtonText = 'Save Bank Account(s)';
        this.duplicateErrorMessage = 'Payment Card Duplicate';
        this.bankAccountConsent = false;

        this.getStripeSettings();
    }

    initStripe(bankAccountSession) {
        try {
            this._StripeElementsService.initStripeElements(
                bankAccountSession.client_secret
            );
        } catch (err) {
            throw err;
        }
    }

    showChooseBankAccount() {
        return this.accounts.length === 0;
    }

    async initACHForm() {
        const bankAccountSession =
            await this._PaymentCardService.createBankAccountSession();

        const { error, financialConnectionsSession } =
            await this._StripeElementsService.collectBankAccountToken(
                bankAccountSession.client_secret
            );

        if (error) {
            this.showBankAccountError(error);
            return;
        }

        this.setAccounts(financialConnectionsSession.accounts);
    }

    setAccounts(accounts) {
        this.accounts = accounts;
        this.$scope.$digest();
    }

    async confirmSave() {
        this.isLoading = true;

        await Promise.all(
            this.accounts.map((account) =>
                this._PaymentCardService
                    .saveBankAccount(account.id)
                    .then(() => this.showBankAccountSuccess(account))
                    .catch((err) => this.showBankAccountError(err))
            )
        ).finally(() => {
            this.isLoading = false;
            this.bankAccountConsent = false;
        });

        this.setAccounts([]);
    }

    showBankAccountSuccess(account) {
        this._toastr.success(
            `Bank account ${account.display_name} ${account.last4} saved.`
        );
    }

    showBankAccountError(error) {
        const message = error.message || (error.data && error.data.message);

        if(this.isDuplicateErrorMessage(message)) {
            this._toastr.error('You are trying to add a bank account that has already been connected to the platform')
            return;
        }

        this._toastr.error(message);
    }

    isDuplicateErrorMessage(message) {
        return message === this.duplicateErrorMessage;
    }
    
    getStripeSettings() {
        this.isLoading = true;

        return this._PaymentCardService
            .paymentCardCreationSettings()
            .then((data) => {
                let settings = data && data.settings;

                if (settings && settings.stripe_client) {
                    try {
                        this._StripeElementsService.initStripeElements(
                            settings.stripe_client
                        );
                    } catch (err) {
                        console.error(err);
                    }
                }
            })
            .finally(() => (this.isLoading = false));
    }
}

Controller.$inject = [
    'StripeElementsService',
    'PaymentCardService',
    'toastr',
    '$scope',
];

angular.module('SportWrench').component('bankAccountForm', {
    templateUrl: 'events/stripe/payment-card/bank-account-form/template.html',
    bindings: {
        onBankAccountSave: '&',
    },
    controller: Controller,
});

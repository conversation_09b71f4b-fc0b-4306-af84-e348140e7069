<div>
    <spinner active="$ctrl.isLoading"></spinner>
    <button class="btn" type="button" ng-click="$ctrl.initACHForm()" ng-if="$ctrl.showChooseBankAccount()" ng-disabled="$ctrl.isLoading">
        Choose Bank Accounts
    </button>
    <div ng-if="!$ctrl.showChooseBankAccount()">
        <ul class="stripe-card-list">
            <li class="stripe-card" ng-class="{'stripe-card-selected': $ctrl.cardSelected(c)}" ng-repeat="bankAccount in $ctrl.accounts">
                <div>{{bankAccount.display_name}}</div>
                <div>****{{bankAccount.last4}}</div>
            </li>
        </ul>

        <div>
            <p>By clicking [{{$ctrl.saveButtonText}}], you authorize SW to debit the bank account(s) specified above for any amount owed for charges arising from your use of SW's services and/or purchase of products from SW, pursuant to SW's website terms and conditions, until this authorization is revoked. You may amend or cancel this authorization at any time by providing notice to SW with 30 (thirty) days' notice.</p>
            <p>If you use SW's services or purchase additional products periodically pursuant to SW's terms, you authorize SW to debit your bank account(s) periodically. Payments that fall outside the regular debits authorized above will only be debited after your authorization is obtained.</p>
            <input type="checkbox" id="acknowledge" name="acknowledge" ng-model="$ctrl.bankAccountConsent">
            <label for="acknowledge">I have read and agree to the authorization terms.</label>
        </div>
        <button class="btn btn-primary" type="button" ng-click="$ctrl.confirmSave()" ng-disabled="$ctrl.isLoading || !$ctrl.bankAccountConsent">
            {{$ctrl.saveButtonText}}
        </button>
    </div>
</div>

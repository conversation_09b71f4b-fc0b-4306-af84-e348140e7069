<spinner active="$ctrl.isLoading"></spinner>
<div class="row" ng-if="!$ctrl.isLoading && $ctrl.isLoaded">
    <div class="col-xs-12">
        <uib-alert type="info" class="text-center" ng-if="!$ctrl.payments.length">
            No incomplete payments found.
        </uib-alert>
    </div>
    <div class="col-xs-12">
        <ul class="list-group payment-cards-list">
            <li class="list-group-item pointer" ng-repeat="payment in $ctrl.payments track by $index">
                <div class="row">
                    <span class="col-xs-2" ng-bind="payment.event_name"></span>
                    <span class="col-xs-2" ng-bind="payment.amount | currency"></span>
                    <span class="col-xs-3" ng-bind="payment.description"></span>
                    <span class="col-xs-2">**** {{payment.card_last_4}}</span>
                    <span class="col-xs-2">
                        <button class="btn btn-primary btn-sm pull-right"
                                ng-click="$ctrl.confirm(payment)"
                                ng-disabled="payment.paymentInProcess"
                        >Confirm</button>
                    </span>
                    <span class="col-xs-1" uib-tooltip="Cancel Payment">
                        <button class="btn btn-danger btn-sm pull-right"
                                sw-confirm="Do you really want to cancel this payment?"
                                sw-confirm-do="$ctrl.remove"
                                sw-confirm-args="payment"
                                sw-confirm-hide-no
                                ng-disabled="payment.paymentInProcess"
                        ><i class="fa fa-times fa-lg"></i></button>
                    </span>
                </div>
            </li>
        </ul>
    </div>
</div>

angular.module('SportWrench')

.controller('eventsController', EventsController);

function EventsController ($scope, eventsService, userService, $q) {
    $scope.active = {
        my   : true,
        other: false
    };

    $scope.isNoEvents = {
        own     : false,
        other   : false
    };

    $scope.loaded = false;

    $scope.isNotEO = function () {
        return !userService.hasEORole() && !userService.hasGodRole();
    };

    $scope.toggleFavorites = function (hide) {
        $scope.ownSeasons.user_has_favorite_events = !hide;
    };

    $scope.hideOwnTab = function () {
        if($scope.isNotEO()) {
            return !$scope.ownSeasons.user_has_favorite_events;
        }

        return false;
    };

    loadSeasons();

    function loadSeasons () {
        $q.all([
            eventsService.getSeasons('other'),
            eventsService.getSeasons('own')
        ]).then(results => {
            $scope.loaded = true;

            $scope.otherSeasons = results[0];
            $scope.ownSeasons   = results[1];

            $scope.hideOtherTab = !$scope.otherSeasons.length;
        })
    }
}

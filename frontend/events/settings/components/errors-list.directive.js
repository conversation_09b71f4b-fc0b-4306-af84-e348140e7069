angular.module('SportWrench').directive('errorsList', function ($filter, $sce) {
    return {
        restrict: 'E',
        scope: {
            errors: '=',
            size: '@'
        },
        templateUrl: 'events/settings/components/errors-list.html',
        replace: true,
        link: function (scope) {
            var limitTo = $filter('limitTo');
            scope.rowsLimit = 2;
            scope.notShowedCount = 0;

            var __recountUnshowed = function () {
                scope.notShowedCount = (scope.errorsList && scope.rowsLimit)?(scope.errors.length - scope.rowsLimit):0;
            }

            scope.$watch('[errors, rowsLimit]', function (val) {
                if(!val) return;
                scope.errorsList = (scope.rowsLimit)?limitTo(scope.errors, scope.rowsLimit):scope.errors;
                __recountUnshowed();
            }, true);

            scope.errorText = function (error) {
                var errorText = (typeof error === 'object')
                                    ?error.text
                                    :error
                return $sce.trustAsHtml(errorText)
            }

            scope.showAll = function () {
                scope.rowsLimit = 0;
                __recountUnshowed();
            }
        }
    }
})

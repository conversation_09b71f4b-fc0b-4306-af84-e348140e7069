angular.module('SportWrench').directive('officialsForm',
    ['_', 'moment', 'UtilsService', 'STAFF_MEMBER_TYPE', 'OFFICIAL_MEMBER_TYPE', 'AVAILABLE_OFFICIALS_SANCTIONINGS',
        function (_, moment, UtilsService, STAFF_MEMBER_TYPE, OFFICIAL_MEMBER_TYPE, AVAILABLE_OFFICIALS_SANCTIONINGS) {
        return {
        restrict: 'E',
        scope: {
            tournament: '=',
            memberType: '@',
            roleId: '@',
            clothesTypes: '<',
        },
        templateUrl: 'events/settings/general/forms/officials-form.html',
        require: '^generalSettings',
        replace: true,
        link: function (scope, elem, attrs, ctrl) {
            scope.utils = {
                formSubmitted: false,
                genderTitles: {
                    male: 'Male',
                    female: 'Female',
                },
            };
            scope.AVAILABLE_OFFICIALS_SANCTIONINGS = AVAILABLE_OFFICIALS_SANCTIONINGS;

            scope.formName          = scope.memberType + 'Form';
            scope.capitalizedType   = UtilsService.capitalizeFirstLetter(scope.memberType);
            
            scope.isOfficialMemberType = function () {
                return scope.memberType === OFFICIAL_MEMBER_TYPE;
            }
            
            scope.isPropInvalid = function (prop) {
                let form = scope[scope.formName];
                return scope.utils.formSubmitted && form[prop] && form[prop].$invalid;
            };

            scope.isPropWithError = function (prop) {
                let form = scope[scope.formName];
                return form.$error[prop];
            };

            scope.enable_hotel_for_officials = scope.tournament.officials_hotel_comp ? true : false;

            var enumerateDaysBetweenDates = function(startDate, endDate) {
                var currDate    = startDate.clone().startOf('day');
                var lastDate    = endDate.clone().startOf('day');
                var dates       = [startDate.clone().format("MMDDYYYY")];

                while(currDate.add(1, 'days').diff(lastDate) < 0) {
                    dates.push(currDate.clone().format("MMDDYYYY"));
                }

                return dates;
            };
            scope.allHotelDays = enumerateDaysBetweenDates(
                moment(scope.tournament.officials_hotel_date_start),
                moment(scope.tournament.officials_hotel_date_end)
            );

            scope.generateHotelDates = function () {
                if (!scope.tournament.officials_hotel_comp.type || scope.tournament.officials_hotel_comp.type ==='all_self'
                    || scope.tournament.officials_hotel_comp.type === 'certain_comp') {
                    checkAllCheckBoxes(false);
                }
                if  (scope.tournament.officials_hotel_comp.type ==='all_comp') {
                    checkAllCheckBoxes(true);
                }
            };
            
            var checkAllCheckBoxes = function (flag) {
                scope.tournament.officials_hotel_comp.dates = {};
                _.each(scope.hotelDays, function (day) {
                    scope.tournament.officials_hotel_comp.dates[day.date] = flag;
                });
            };

            var generateCertainDates = function () {

                if (!scope.tournament.officials_hotel_date_start || !scope.tournament.officials_hotel_date_end
                    || !scope.tournament.date_start || !scope.tournament.date_end) {
                    return;
                }

                scope.allHotelDays = enumerateDaysBetweenDates(
                    moment(scope.tournament.officials_hotel_date_start),
                    moment(scope.tournament.officials_hotel_date_end)
                );

                scope.hotelDays = _.map(scope.allHotelDays, function (day) {
                    return {date: day, text: 'Night of ' + moment(day, 'MMDDYYYY').format('ddd, MMM DD', 'en')};
                });

            };

            var __validateHotelOpen = function () {
                scope.officialsHotelStartErr = "";

                if (!scope.tournament.date_start) {
                    scope.officialsHotelStartErr = "You need to set event start date";
                }
                if (scope.tournament.officials_hotel_date_end && scope.tournament.officials_hotel_date_start
                    && scope.tournament.officials_hotel_date_start > scope.tournament.officials_hotel_date_end) {
                    scope.officialsHotelStartErr = "Check In Date can't be later than close date";
                }
                if (scope.tournament.date_end && scope.tournament.officials_hotel_date_start
                    && scope.tournament.officials_hotel_date_start > scope.tournament.date_end) {
                    scope.officialsHotelStartErr = "Check In Date can't be later then event end date";
                }
            };

            var __validateHotelClose = function () {
                scope.officialsHotelEndErr = "";

                if (!scope.tournament.date_start) {
                    scope.officialsHotelEndErr = "You need to set event start date";
                }
                if (scope.tournament.date_start && scope.tournament.officials_hotel_date_end
                    && scope.tournament.officials_hotel_date_end < scope.tournament.date_start) {
                    scope.officialsHotelEndErr = "Check Out Date can't be earlier then event start date";
                }
            };

            var __validateRegOpen = function () {
                scope.officialsRegStartErr = "";

                if (!scope.tournament.date_start) {
                    scope.officialsRegStartErr = "You need to set event start date";
                }
                if (scope.tournament.date_official_reg_close && scope.tournament.date_official_reg_open
                    && scope.tournament.date_official_reg_open > scope.tournament.date_official_reg_close) {
                    scope.officialsRegStartErr = "Open date can't be later than close date";
                }
            };

            var __validateRegClose = function () {
                scope.closeDateWarningMessage   = '';
                scope.officialsRegEndErr        = '';

                const closeDateWarningRules = [
                    scope.tournament.date_official_reg_close >= scope.tournament.date_start,
                    scope.tournament.date_official_reg_close <= scope.tournament.date_end
                ];

                if (!scope.tournament.date_start) {
                    scope.officialsRegEndErr = "You need to set event start date";
                }

                if (scope.tournament.date_official_reg_close > scope.tournament.date_end) {
                    scope.officialsRegEndErr = "Close date can't be later then event end date";
                }

                if (closeDateWarningRules.every(rule => rule)) {
                    scope.closeDateWarningMessage = `
                        Warning: This date is later than event start date.
                        ${scope.capitalizedType} will be able to apply during the event`;
                }
            };

            scope.$watch('clothesTypes.data', function () {
                if(scope.clothesTypes.loading) {
                    return;
                }
                if(!('clothes_requirements' in scope.tournament)) {
                    scope.tournament.clothes_requirements = {};
                }
                if(!(scope.roleId in scope.tournament.clothes_requirements)) {
                    scope.tournament.clothes_requirements[scope.roleId] = Object.keys(scope.utils.genderTitles).reduce(
                        (r, gender) => {
                            r[gender] = Object.keys(scope.clothesTypes.data).filter(
                                common_item_id => [gender, 'any'].includes(scope.clothesTypes.data[common_item_id].details.gender)
                            ).map(common_item_id => (
                                {
                                    common_item_id,
                                    required: false,
                                }
                            ));
                            return r;
                        }, {}
                    );
                }
            });

            scope.$watch('tournament.date_official_reg_open', function () {
                __validateRegOpen();
                __validateRegClose();
            });

            scope.$watch('tournament.date_official_reg_close', function () {
                __validateRegOpen();
                __validateRegClose();
            });

            scope.$watch('tournament.officials_hotel_date_start', function () {
                generateCertainDates();
                __validateHotelOpen();
                __validateHotelClose();
            });

            scope.$watch('tournament.officials_hotel_date_end', function () {
                generateCertainDates();
                __validateHotelOpen();
                __validateHotelClose();
            });

            scope.$on('EventSettingsForm.DateStartChanged', function () {
                generateCertainDates();
                __validateRegOpen();
                __validateRegClose();
                __validateHotelOpen();
                __validateHotelClose();

            });
            scope.$on('EventSettingsForm.DateEndChanged', function () {
                generateCertainDates();
                __validateRegOpen();
                __validateRegClose();
                __validateHotelOpen();
                __validateHotelClose();
            });

            scope.$on('EventSettingsForm.Submitted', function () {
                scope.utils.formSubmitted = true;

                var errors = [], form = scope[scope.formName];


                if (form.reg_open.$invalid
                    || scope.tournament.date_official_reg_open > scope.tournament.date_official_reg_close) {
                    errors.push(`Invalid ${scope.capitalizedType} Registration Dates`);
                }
                if (form.officials_reg_close.$invalid || scope.tournament.date_official_reg_close > scope.tournament.date_end) {
                    errors.push(`Invalid ${scope.capitalizedType} Registration Close Date`);
                }
                if (form.official_payment_method.$invalid) {
                    errors.push(`Choose At Least One ${scope.capitalizedType} Payment Option`);
                }

                if (scope.isOfficialMemberType() && form.available_officials_sanctionings.$invalid) {
                    errors.push(`Choose At Least One Officials sanctioning`);
                }

                if (scope.tournament.enable_hotel_for_officials) {

                    if(__checkHotelCompType()) {
                        form.comped_type.$invalid = true;
                        errors.push(`Choose ${scope.capitalizedType} Hotel Comp Type`)
                    }
                    if (__checkHotelCompDays()) {
                        errors.push('Check At Least One Night For Hotel Comp')
                    }
                    if (__checkHotelCloseDate()) {
                        errors.push(`Invalid ${scope.capitalizedType} Hotel First Available Check In Date`)
                    }
                    if (__checkHotelOpenDate()) {
                        errors.push(`Invalid ${scope.capitalizedType} Hotel Last Available Check Out Date`)
                    }

                }

                if(scope.memberType === STAFF_MEMBER_TYPE) {
                    ctrl.setStaffFormErrors(errors);
                } else {
                    ctrl.setOfficialsFormErrors(errors);
                }
            });

            scope.isOptRequired = function () {
                var opts = scope.tournament.official_payment_method;

                var picked = _.reduce(Object.keys(opts), function (counter, item) {
                    if (opts[item]) {
                        ++counter;
                    }

                    return counter;
                }, 0);

                return (picked === 0);
            };

            scope.isOptionRequired = function (fieldName) {
                const options = scope.tournament[fieldName];

                return (options.length === 0);
            };

            scope.findAvailableOfficialsSanctioning = function(sanctioningName) {
                return scope.tournament.available_officials_sanctionings.indexOf(sanctioningName);
            }

            scope.changeAvailableOfficialsSanctioning = function(sanctioningName) {
                const sanctioningIndex = scope.findAvailableOfficialsSanctioning(sanctioningName);

                if (sanctioningIndex === -1) {
                    scope.tournament.available_officials_sanctionings.push(sanctioningName);
                } else {
                    scope.tournament.available_officials_sanctionings.splice(sanctioningIndex, 1);
                }
            }

            scope.clearHotelType = function () {
                if (scope.tournament.officials_hotel_comp && scope.tournament.officials_hotel_comp.type) {
                    scope.tournament.officials_hotel_comp.type = '';
                    checkAllCheckBoxes(false);
                }
            }

            scope.clearUnusedDates = function () {
                if (scope.tournament.officials_hotel_comp && scope.tournament.officials_hotel_comp.dates) {

                    _.each(Object.keys(scope.tournament.officials_hotel_comp.dates), function (date) {
                        if (scope.allHotelDays.indexOf(date) === -1) {
                            delete scope.tournament.officials_hotel_comp.dates[date];
                        }
                    });
                }
            }

            var __checkHotelOpenDate = function () {
                return !scope.tournament.officials_hotel_date_start ||
                    scope.tournament.date_end && scope.tournament.officials_hotel_date_start
                    && scope.tournament.officials_hotel_date_start > scope.tournament.date_end ||
                    scope.tournament.officials_hotel_date_end && scope.tournament.officials_hotel_date_start
                    && scope.tournament.officials_hotel_date_start > scope.tournament.officials_hotel_date_end;
            }

            var __checkHotelCloseDate = function () {
                return !scope.tournament.officials_hotel_date_end ||
                    scope.tournament.date_start && scope.tournament.officials_hotel_date_end
                    && scope.tournament.officials_hotel_date_end < scope.tournament.date_start
            }

            var __checkHotelCompDays = function () {
                if (scope.tournament.officials_hotel_comp && scope.tournament.officials_hotel_comp.dates) {
                    var hotelDays = _.find(scope.tournament.officials_hotel_comp.dates, function (day) {
                        return day === true;
                    });

                    return scope.tournament.officials_hotel_comp
                        && scope.tournament.officials_hotel_comp.type === 'certain_comp' && !hotelDays;
                }
            }

            var __checkHotelCompType = function () {
                return scope.tournament.officials_hotel_date_end && scope.tournament.officials_hotel_date_start &&
                    !scope.officialsHotelStartErr && !scope.officialsHotelEndErr &&
                    (!scope.tournament.officials_hotel_comp || !scope.tournament.officials_hotel_comp.type);
            }

            var formName = scope.memberType;

            ctrl.registerForm(formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(formName);
            });


        }
    };
}]);

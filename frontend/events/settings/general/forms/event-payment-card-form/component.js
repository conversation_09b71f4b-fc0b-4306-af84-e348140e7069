

class Controller {
    constructor(PaymentCardService, $scope) {
        this._PaymentCardService = PaymentCardService;
        this.$scope = $scope;
    }

    $onInit () {
        this.paymentCards = [];
        this.isLoading = false;
        this.hasErrors = false;

        this.loadUserPaymentMethods();

        this.$scope.$on('EventSettingsForm.Submitted', () => {
            let errors = [];

            const selectedCard = this.paymentCards.find(card => card.stripe_payment_method_id === this.tournament.payment_method_id);

            if (!selectedCard.offline_is_active) {
                errors.push('The selected payment method is inactive. Please select an active payment method to save your changes.');
                this.hasErrors = true;
            }

            if (!this.tournament.payment_method_id) {
                errors.push('Choose Payment Card');

                this.hasErrors = true;
            }

            if(!errors.length) {
                this.hasErrors = false;
            }

            this.setHousingFormErrors({ errors });
        })
    }

    cardLabel (card) {
        return this._PaymentCardService.getCardLabel(card);
    }

    getCardBrandClass (cardBrand) {
        return this._PaymentCardService.getCardBrandClass(cardBrand);
    }

    loadUserPaymentMethods () {
        this.isLoading = true;

        this._PaymentCardService.getPaymentCards(this.tournament.event_id)
            .then(data => {
                if(!data || !data.paymentCards) {
                    this.paymentCards = [];
                }

                this.paymentCards = data.paymentCards.filter(card => card.offline_is_active || card.stripe_payment_method_id === this.tournament.payment_method_id);

                if(!this.tournament.payment_method_id) {
                    this._setDefaultCard(this.paymentCards);
                }
            })
            .finally(() => this.isLoading = false);
    }

    _setDefaultCard (cards) {
        let defaultCard = cards.filter(card => card.is_default)[0];

        if(!_.isEmpty(defaultCard)) {
            this.tournament.payment_method_id = defaultCard.stripe_payment_method_id;
        }
    }
}

Controller.$inject = ['PaymentCardService', '$scope'];

angular.module('SportWrench').component('eventPaymentCardForm', {
    templateUrl: 'events/settings/general/forms/event-payment-card-form/template.html',
    bindings: {
        tournament: '=',
        setHousingFormErrors: '&'
    },
    controller: Controller
});

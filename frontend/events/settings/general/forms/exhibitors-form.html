<form name="{{formName}}" class="form-horizontal">
    <div ng-class="{ 'form-group': true}">
        <div class="col-sm-5 col-sm-offset-2">
            <label>
                <input
                    type="checkbox"
                    ng-model="tournament.enable_exhibitors_reg"
                    name="live_reg">
                Activate Exhibitors Registration
            </label>
        </div>
    </div>
    <div class="register-dates col-sm-10 col-sm-offset-2">
        <div class="form-group">
            <label class="col-sm-4">Exhibitors Registration Dates</label>
        </div>
        <div ng-class="{ 'form-group validation-required': true, 'has-error': isPropInvalid('reg_open') || exhibitorsRegStartErr }">
            <label class="col-sm-2 control-label">Open Date</label>
            <div class="col-sm-5">
                <date-time-form-control
                    date="tournament.date_exhibitors_reg_open"
                    format="MM/dd/yyyy HH:mm"
                    name="reg_open"
                    field-required="true"
                    max-date="tournament.date_start"
                ></date-time-form-control>
            </div>
            <div class="has-error-div col-sm-5" ng-bind="exhibitorsRegStartErr"></div>
        </div>
        <div ng-class="{ 'form-group validation-required': true, 'has-error': isPropInvalid('reg_close') || exhibitorsRegEndErr}">
            <label class="col-sm-2 control-label">Close Date</label>
            <div class="col-sm-5">
                <date-time-form-control
                    ng-model="tournament.date_exhibitors_reg_close"
                    date="tournament.date_exhibitors_reg_close"
                    format="MM/dd/yyyy HH:mm"
                    field-required="true"
                    min-date="tournament.date_exhibitors_reg_open"
                    max-date="tournament.date_end"
                    deftime="23:59:59"
                    name="reg_close"
                    time-validator
                ></date-time-form-control>
            </div>
            <div ng-if="closeDateWarningMessage" class="col-sm-5 has-warning-div">
                {{ closeDateWarningMessage }}
            </div>
            <div ng-if="!exhibitorsRegEndErr && validators.exhibitors_reg_close" class="col-sm-5 has-warning-div">
                <label ng-bind="validators.exhibitors_reg_close"></label>
            </div>
            <div class="has-error-div col-sm-5" ng-bind="exhibitorsRegEndErr"></div>
        </div>
    </div>
    <stripe-account-select
        account-id="tournament.exhibitors_stripe_account_id"
        event-id="tournament.event_id"
        disabled="tournament.block_exhibitors_keys_edit"
        has-error="utils.formSubmitted && ExhibitorsForm.stripe_account.$invalid"
        account-name="tournament.exhibitors_stripe_account_name"
        on-change="onStripeAccountChange"
    ></stripe-account-select>
    <statement-descriptor-input
        is-required="true"
        ng-model="tournament.exhibitors_stripe_statement"
        has-error="utils.formSubmitted && ExhibitorsForm.stripe_statement.$invalid"
    ></statement-descriptor-input>
    <div class="form-group">
        <label class="control-label col-sm-3">Sales Manager</label>
        <div class="col-sm-7">
            <spinner active="salesManagers === null"></spinner>
            <select
                class="form-control"
                ng-if="salesManagers !== null"
                ng-model="tournament.sales_manager_id"
                ng-options="sm.sales_manager_id as salesManagerLabel(sm) for sm in salesManagers"
            >
                <option value="">Sales Person is not assigned (EO manages exhibitors)</option>
            </select>
        </div>
    </div>
</form>

<div ng-class="{ 'form-group validation-required': $ctrl.isRequired, 'has-error': $ctrl.hasError }">
    <label class="control-label col-sm-3">Statement Descriptor</label>
    <div class="col-sm-7">
        <input
            type="text"
            class="form-control"
            ng-model="$ctrl.ngModel"
            name="{{ $ctrl.inputName}}"
            ng-maxlength="$ctrl.maxLength"
            ng-required="$ctrl.isRequired"
            statement-descriptor-validator>
        <p class="help-block">What you want to appear on your customers credit card statement</p>
        <p class="help-block">
            {{$ctrl.maxLength}} characters maximum. Single quote or double-quote symbols (&#60;&#44; &#62;&#44; &#39;&#44; &#34;&#44; &lowast;) are NOT allowed. Numbers only are not allowed.<br/>
            <i class="fa fa-exclamation-triangle"></i> NOTES: most banks will truncate this information and/or display it inconsistently. Some may not display any descriptor information at all, also these descriptors won’t show up in pending American Express charges.
            Descriptor examples: FarNorthQualVB, BigSouthTckt.
        </p>
    </div>
</div>

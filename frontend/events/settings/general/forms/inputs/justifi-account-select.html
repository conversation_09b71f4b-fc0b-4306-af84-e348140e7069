<div ng-class="{ 'form-group validation-required': true, 'has-error': $ctrl.hasError }">
    <label class="control-label col-sm-3">Justifi Account</label>
    <div class="col-sm-7" ng-if="!$ctrl.disabled">
        <spinner active="$ctrl.isLoading()"></spinner>
        <select
            name="justifi_account"
            class="form-control"
            ng-model="$ctrl.accountId"
            ng-change="$ctrl._onChange()"
            ng-options="acc.account_id as $ctrl.accLabel(acc) disable when acc.is_disabled for acc in $ctrl.utils.justifiAccounts"
            ng-if="!$ctrl.isLoading()"
            required
        >
            <option value="" ng-if="!$ctrl.accountId">Choose a Justifi Account ...</option>
        </select>
    </div>
    <div class="col-sm-7 center-form-text" ng-if="$ctrl.disabled">
        {{$ctrl.accountName || 'N/A'}}
    </div>
</div>

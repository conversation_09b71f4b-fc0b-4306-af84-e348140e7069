angular.module('SportWrench').component('justifiAccountSelect', {
    templateUrl: 'events/settings/general/forms/inputs/justifi-account-select.html',
    bindings: {
        accountId: '=',
        eventId: '<',
        hasError: '<',
        disabled: '<',
        accountName: '<',
        onChange: '<',
    },
    controller: [
        '_', 'moment', 'UtilsService', 'EventOwnerService',
        function (_, moment, UtilsService, EventOwnerService) {
            this.utils = {
                accsLoading: false,
                justifiAccounts: null,
            };

            this._onChange = function (acc) {
                if(!acc) {
                    acc = _.find(this.utils.justifiAccounts, {account_id: this.accountId});
                }
                if(this.onChange) {
                    this.onChange(acc);
                }
            };

            this.accLabel = function (acc) {
                return (acc.is_test?'TEST: ':'') + acc.title + ' (' + acc.email + ')';
            };

            this.isLoading = function() {
                return this.utils.accsLoading || this.justifiAccounts === null;
            };

            this.loadAccs = function () {
                if (this.utils.justifiAccounts !== null || this.utils.accsLoading) {
                    return;
                }

                this.utils.accsLoading = true;
                EventOwnerService.getEOJustifiSubAccounts(this.eventId, 'event')
                    .then((accounts) => {
                        this.utils.justifiAccounts = UtilsService.filterHiddenAccounts(accounts, this.accountId);

                        if(_.isEmpty(_.find(accounts, {account_id: this.accountId}))) {
                            this.utils.justifiAccounts.push({
                                "title": "Account not available for current user",
                                "email": "N/A",
                                "account_id": this.accountId,
                                "is_test": true,
                                "connected": true,
                                "is_disabled": true,
                            })
                        }

                        if (accounts.length === 1 && !this.accountId) {
                            this.accountId = accounts[0].account_id;
                            this._onChange(accounts[0]);
                        }
                        else if(this.accountId) {
                            this._onChange(_.find(accounts, {account_id: this.accountId}));
                        }
                    })
                    .finally(() => {
                        this.utils.accsLoading = false;
                    });
            };

            this.loadAccs();
        },
    ],
});


angular.module('SportWrench').component('justifiStatementDescriptorInput', {
    templateUrl: 'events/settings/general/forms/inputs/justifi-statement-descriptor-input.html',
    bindings: {
        ngModel: '=',
        hasError: '<',
        isRequired: '<',
        maxLength: '<',
        inputName: '@',
    },
    controller: [
        function () {
            this.$onInit = function () {
                const JUSTIFI_STATEMENT_MAX_LENGTH = 22;

                if (!this.maxLength) {
                    this.maxLength = JUSTIFI_STATEMENT_MAX_LENGTH;
                }
            };
        },
    ],
});


angular.module('SportWrench').directive('checkInForm', checkInForm);

checkInForm.$inject = ['CHECKIN_MODES', 'ONLINE_CHECKIN_MESSAGES'];

function checkInForm (CHECKIN_MODES, ONLINE_CHECKIN_MESSAGES) {
	return {
		restrict: 'E',
		scope: {
			tournament: '='
		},
		templateUrl: 'events/settings/general/forms/checkin-form.html',
		replace: true,
		require: '^generalSettings',
		link: function (scope, attrs, elem, ctrl) {
			scope.utils = {};
            scope.isPrimaryStaffBarcodesMode
                = (scope.tournament.online_team_checkin_mode === CHECKIN_MODES.PRIMARY_STAFF_BARCODES);

			scope.$on('EventSettingsForm.DateEndChanged', function () {           
                var dateEnd = scope.tournament.date_end;
                if(scope.tournament.online_team_checkin_end > dateEnd) {
                	scope.tournament.online_team_checkin_end = new Date(dateEnd)
                }
            });

			scope.$on('EventSettingsForm.Submitted', function () {
				scope.utils.formSubmitted = true;
                var errors 	= [], 
                	form 	= scope.teamsCheckInDetailsForm;

                if(form.online_team_checkin_end.$invalid) {
                	errors.push('Invalid Online Check-in End Date Value');
                }

                if(form.online_team_checkin_start.$invalid) {
                    errors.push('Invalid Online Check-in Start Date Value');
                }

                if(scope.tournament.online_team_checkin_end < scope.tournament.online_team_checkin_start) {
                    errors.push('End date earlier start date');
                }

                if(form.online_team_checkin_mode.$invalid) {
                    errors.push('Choose a Check In Mode');
                }

                if(form.border_colour && form.border_colour.$invalid) {
                    errors.push('Choose a QR code border color');
                }

                ctrl.setCheckinFormErrors(errors);

                scope.updateEmptyOnlineTeamCheckinInfo();
			});

			scope.onlineCheckinModeChanged = function () {
                scope.isPrimaryStaffBarcodesMode
                    = (scope.tournament.online_team_checkin_mode === CHECKIN_MODES.PRIMARY_STAFF_BARCODES);
                scope.tournament.validation_rules.require_primary_staff_email_and_phone
                    = scope.isPrimaryStaffBarcodesMode;

                scope.updateEmptyOnlineTeamCheckinInfo();
            };

            scope.updateEmptyOnlineTeamCheckinInfo = function () {
                if(scope.isPrimaryStaffBarcodesMode) {
                    scope.tournament.online_team_checkin_info
                        = scope.tournament.online_team_checkin_info || ONLINE_CHECKIN_MESSAGES.PRIMARY_STAFF_BARCODES;
                }
            }

            scope.updateEmptyOnlineTeamCheckinInfo();

			var formName = 'checkin';

			ctrl.registerForm(formName);

			scope.$on('$destroy', function () {
				ctrl.unRegisterForm(formName);
			})
		}
	}
}

angular.module('SportWrench').component('assignTemplate', {
    templateUrl : 'events/settings/assign-templates/assign-template.html',
    require     : { parentCtrl: '^eventSettings'},
    controller  : AssignTemplateController,
});

AssignTemplateController.$inject =
    ['$stateParams', '$scope', 'EventSettingsService', 'toastr', 'UtilsService', '_', 'AEMEventFactory', 'AEMService'];

function AssignTemplateController($stateParams, $scope, EventSettingsService,
                                  toastr, UtilsService, _, AEMEventFactory, AEMService) {
    let vm = this;

    let AEMEventService = new AEMEventFactory($stateParams.event);

    this.triggers  = {};
    this.templates = {};

    $scope.$watch(function () {
        return vm.parentCtrl.isAssignTemplateActive();
    }, function (activate) {
        if(activate === true) {
            init();
        }
    });

    this.setTemplate = function (group, typeID, templateID) {
        vm.triggers[group][typeID].email_template_id = Number(templateID);
        vm.triggers[group][typeID].event_id = Number($stateParams.event);
    };

    this.setTitle = function (templateID) {
        return vm.templates[templateID];
    };

    this.capitalizeFirstLetter = function (word) {
        return UtilsService.capitalizeFirstLetter(word);
    };

    this.save = function () {
        vm.isSaving = true;

        const triggers = getFilteredTriggers(vm.triggers);

        return EventSettingsService.assignTmplToTrigger($stateParams.event, triggers)
            .then(() => {
                init();
                toastr.success('Saved');
            });
    };

    this.isTriggersEmpty = function () {
        return _.isEmpty(vm.triggers);
    };

    this.previewTmpl = function (item) {
        let link = AEMEventService.getPreviewLink(item.email_template_id);
        AEMService.openPreviewModal(item.email_template_id, link, item.title);
    };

    function init() {
        if(!vm.isSaving) {
            vm.isLoading = true;
        }

        return EventSettingsService.loadTemplateTypes($stateParams.event).then( data => {

            vm.triggers  = {};
            vm.templates = {};

            if(!_.isEmpty(data)) {
                vm.triggers  = data.triggers;
                vm.templates = data.templates;
            }

            vm.isSaving  = false;
            vm.isLoading = false;
        });
    }

    function getFilteredTriggers(ob) {
        const _ob = angular.copy(ob);

        Object.keys(_ob).map(group => {
            Object.keys(_ob[group]).map(type => {
                if (_ob[group][type].event_id === null) {
                    delete _ob[group][type];
                }
            })
        });

        return _ob;
    }
}

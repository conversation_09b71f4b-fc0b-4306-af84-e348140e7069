angular.module('SportWrench').component('justifiAccountForm', {
    templateUrl: 'events/justifi/account-form.html',
    bindings: {
        onSubmit: '&?',
    },
    controller: [
        'toastr',
        'JustifiService',
		'$scope',
		'$sce',
        function (toastr, JustifiService, $scope, $sce) {
            $scope.loading = true;
            $scope.businessId = null;
			$scope.justifiOnboardingUrl = null;

            this.$onInit = () => {
                JustifiService.generateOnboardingWebToken()
                    .then(({ businessId }) => {
                        $scope.businessId = businessId;
						$scope.justifiOnboardingUrl =  $sce.trustAsResourceUrl(
							`https://onboarding.justifi.ai/v1/provisioning/${businessId}`,
						);

						$scope.loading = false;
                    })
                    .catch((err) => {
                        toastr.error(
                            err.message || 'Service is unavailable, please try again later',
                        );
                        $scope.loading = false;
                    });

                this._iframeMessageHandler = async (event) => {
                    const { eventType, detail } =
                        event.data || {};

                    if (eventType === 'submitSuccess') {
                        await this.onSubmit({ businessId: $scope.businessId });
                        toastr.success('Onboarding has been completed');
                    }

                    if (eventType === 'submitFailure') {
                        toastr.error(detail.message || 'Failed to complete onboarding');
                    }
                };

                window.addEventListener('message', this._iframeMessageHandler);
            };

            this.$onDestroy = () => {
                window.removeEventListener('message', this._iframeMessageHandler);
            };
        },
    ],
});

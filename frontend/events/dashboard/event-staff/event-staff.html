<form class="form form-inline row-space">
    <div class="form-group">
        <searchbox
            search="$ctrl.filters.search"
            status-changed="$ctrl.onFilterChange(status, filter)"
        ></searchbox>
    </div>
    <div class="form-group">
        <work-status
            work-status="$ctrl.filters.work_status"
            status-changed="$ctrl.onFilterChange(status, filter)"
        ></work-status>
    </div>
    <div class="form-group">
        <hotel-filter
            hotel="$ctrl.filters.hotel"
            status-changed="$ctrl.onFilterChange(status, filter)"
        ></hotel-filter>
    </div>
    <div class="form-group member-action">
        <member-actions actions="$ctrl.actions"></member-actions>
    </div>
</form>
<div ng-if="$ctrl.filteredStaffers.length">
    <member-group-operations
        total-count="$ctrl.getLength()"
        selected-count="$ctrl.selection.total_checked"
        group-work-status="$ctrl.group_work_status"
        show-withdrawn="$ctrl.showWithdrawn"
        on-save="$ctrl.changeWorkStatus()"
        on-export="$ctrl.exportExcel()"
        on-send-email="$ctrl.openEmailDialog()"
    >
    </member-group-operations>
</div>
<table class="table table-condensed" sticky-header>
    <thead class="pointer">
    <tr>
        <th>
            <input type="checkbox" ng-model="$ctrl.selection.all_selected" ng-change="$ctrl.selectAll()">
        </th>
        <th ng-click="$ctrl.changeOrder('first')">
            First
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('first')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('last')">
            Last
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('last')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('email')">
            Email
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('email')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('phone')">
            Mobile Phone
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('phone')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('has_restrictions')">
            Conflicts
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('has_restrictions')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('need_hotel_room')">
            Hotel
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('need_hotel_room')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('staff_arrival_datetime')">
            Arrival Day/Time
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('staff_arrival_datetime')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('staff_payment_method')">
            Payment
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('staff_payment_method')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('staff_work_status')">
            Work Status
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('staff_work_status')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('usav')" ng-if="$ctrl.eventHasUSAVSanctioning()">
            USAV
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('usav')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('aau')" ng-if="$ctrl.eventHasAAUSanctioning()">
            AAU
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('aau')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('background_screening')" ng-if="$ctrl.eventHasUSAVSanctioning()">
            BKG
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('background_screening')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('aau_bg_screening')" ng-if="$ctrl.eventHasAAUSanctioning()">
            BKG
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('aau_bg_screening')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('safesport_status')" ng-if="$ctrl.eventHasUSAVSanctioning()">
            SS
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('safesport_status')}}"></reverse-arrow>
        </th>
        <th ng-click="$ctrl.changeOrder('aau_safesport_status')" ng-if="$ctrl.eventHasAAUSanctioning()">
            SS
            <reverse-arrow reverse="$ctrl.order.reverse" show="{{$ctrl.showReverse('aau_safesport_status')}}"></reverse-arrow>
        </th>
    </tr>
    </thead>
    <tbody ng-click="$ctrl.openInfoModal($event)">
    <tr
        ng-repeat="staff in $ctrl.filteredStaffers track by $index"
        ng-class="{'pointer': true, 'alert-disabled': $ctrl.isWithdrawnMember(staff)}"
        of-id="{{staff.official_id}}"
    >
        <td ng-click="$event.stopPropagation();">
            <input type="checkbox" ng-model="staff.checked" ng-change="$ctrl.toggleStaffSelection(staff)">
        </td>
        <td ng-bind="staff.first"></td>
        <td ng-bind="staff.last"></td>
        <td ng-bind="staff.email"></td>
        <td ng-bind="staff.phone_mob | tel"></td>
        <td ng-click="$ctrl.openRestrictionsUpdateModal($event, staff)">
            <i ng-if="staff.has_restrictions"
               uib-tooltip="{{staff.additional_restrictions}}"
               class="fa fa-info-circle"
               aria-hidden="true">
            </i>
            <i ng-if="!staff.has_restrictions"
               class="fa fa-pencil-square-o"
               uib-tooltip="Edit"
               aria-hidden="true">
            </i>
        </td>
        <td>
            <input type="checkbox" ng-checked="staff.need_hotel_room" ng-disabled="true">
        </td>
        <td ng-bind="$ctrl.formatDate(staff.staff_arrival_datetime) | date: 'MMM dd, h:mm a'"></td>
        <td ng-bind="$ctrl.getPaymentOptionLabel(staff.staff_payment_option)"></td>
        <td ng-bind="staff.staff_work_status"></td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasUSAVSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.MBR_FIELD"
            ></table-field-error>
        </td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasAAUSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.AAU_FIELD"
            ></table-field-error>
        </td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasUSAVSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.BG_FIELD"
            ></table-field-error>
        </td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasAAUSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.AAU_BG_FIELD"
            ></table-field-error>
        </td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasUSAVSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.SAFESPORT_FIELD"
            ></table-field-error>
        </td>
        <td ng-click="$ctrl.openSanctioningCheckUpdateModal($event, staff)" ng-if="$ctrl.eventHasAAUSanctioning()">
            <table-field-error profile-info="staff"
                               role="$ctrl.STAFF_MEMBER_TYPE"
                               field="$ctrl.AAU_SAFESPORT_FIELD"
            ></table-field-error>
        </td>
    </tr>
    </tbody>
</table>

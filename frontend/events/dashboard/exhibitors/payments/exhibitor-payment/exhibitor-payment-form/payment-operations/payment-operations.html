<form class="form form-inline">
    <div class="row form-group" ng-if="$ctrl.isCheck">
        <div class="col-sm-3 text-right">
            <span class="text-bold">Received:</span>
        </div>
        <div class="col-sm-7 payment-operations_wrapper">
            <div class="input-group">
                <input
                    type="checkbox"
                    ng-model="$ctrl.enterCheck"
                    ng-change="$ctrl.setReceivedDate()"
                    ng-disabled="$ctrl.disableReceivedCheckbox()"
                >
            </div>
            <div class="input-group date date-reg-end">
                <input
                    ng-model="$ctrl.datePaid"
                    class="form-control input-sm padding-sm"
                    placeholder="mm/dd/yyyy"
                    ng-disabled="$ctrl.disableReceivedFields()"
                    date-formatter="date"
                >
                <span class="input-group-addon padding-sm">
                    <span class="glyphicon glyphicon-calendar"></span>
                </span>
            </div>
            <div class="input-group">
                <span class="input-group-addon padding-sm">
                    &nbsp;#&nbsp;
                </span>
                <input
                    ng-model="$ctrl.checkNumber"
                    class="form-control input-sm padding-sm"
                    maxlength="50"
                    ng-disabled="$ctrl.disableReceivedFields()"
                    placeholder="Check number"
                >
            </div>
            <div ng-if="!$ctrl.disableReceivedFields()">
                <button class="btn btn-primary" ng-if="$ctrl.showSaveButton()" ng-click="$ctrl.makeReceive(payment)">Save</button>
                <button class="btn btn-danger" ng-if="$ctrl.showVoidButton()" ng-click="$ctrl.makeVoid(payment)">Void</button>
            </div>
        </div>
    </div>
    <div class="row form-group" ng-if="$ctrl.showRefundButton()">
        <div class="col-sm-3 text-right">
            <button class="btn btn-warning" ng-click="$ctrl.makeRefund()">Refund</button>
        </div>
    </div>
    <div class="row form-group" ng-if="$ctrl.showInvoiceLink()">
        <div class="col-sm-3 text-right">
            <p><a href="/sales/invoice/{{$ctrl.purchaseId}}" target="_blank">Invoice #{{$ctrl.purchaseId}}</a></p>
        </div>
    </div>
</form>

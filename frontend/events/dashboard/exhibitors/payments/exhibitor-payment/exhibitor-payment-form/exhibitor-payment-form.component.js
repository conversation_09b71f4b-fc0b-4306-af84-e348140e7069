class ExhibitorPaymentFormComponent {
    constructor(ExhibitorsService, ExhibitorsPaymentsService, ExhibitorReceiptsService,
                StripeElementsService, UtilsService, ConfirmationService, INTERNAL_ERROR_MSG, PAYMENT_STATUS,
                PAYMENT_TYPE, OTHER_BOOTH, DEFAULT_OTHER_BOOTH, EVENT_DATES_FORMAT, APPLICATION_STATUS,
                $interval, toastr, _) {
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.ExhibitorReceiptsService = ExhibitorReceiptsService;
        this.StripeElementsService = StripeElementsService;
        this.UtilsService = UtilsService;
        this.ConfirmationService = ConfirmationService;
        this.$interval = $interval;
        this.toastr = toastr;
        this._ = _;

        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.PAYMENT_STATUS = PAYMENT_STATUS;
        this.PAYMENT_TYPE = PAYMENT_TYPE;
        this.OTHER_BOOTH = OTHER_BOOTH;
        this.DEFAULT_OTHER_BOOTH = DEFAULT_OTHER_BOOTH;
        this.EVENT_DATES_FORMAT = EVENT_DATES_FORMAT;
        this.APPLICATION_STATUS = APPLICATION_STATUS;

        this.initState();

        this.PAYMENT_FIELDS = [
            'sponsor_id',
            'event_id',
            'total',
            'amount',
            'booth',
            'token',
        ];

        this.BOOTH_FIELDS = ['fee', 'quantity', 'event_booth_id', 'title'];
    }

    get isCreateMode() {
        return !this.isUpdateMode;
    }

    initState() {
        this.loading = {
            inProcess: true,
            error: ''
        };

        this.payment = {};
        this.show_card_menu = false;
        this.in_progress = false;
        this.progress_bar_value = 0;
        this.showPaymentButtons = true;
        this.showPaymentChangeButton = false;
        this.paymentTypeChangeModeEnabled = false;

        this.exhibitors = [];
        this.selectedBooth = null;
        this.selectedBooths = [];
        this.otherBooths = [];
    }

    async $onInit() {
        if (this.isUpdateMode) {
            await this.getPaymentData();
        } else {
            await Promise.all([
                this.getExhibitorInvoiceInitData(),
                this.getExhibitors()
            ]);
        }
    }

    async getPaymentData() {
        try {
            const {data: exhibitorPaymentData = {}} = await this.ExhibitorsPaymentsService.getExhibitorInvoiceInfo(
                this.eventId,
                this.exhibitorId,
                this.eventExhibitorInvoiceId
            );

            this.selectedBooths = exhibitorPaymentData.chosen_event_booths || [];
            this.initializeExhibitorPaymentData(exhibitorPaymentData);
            this.initExhibitorPaymentData = structuredClone(this.exhibitorPaymentData);
            this.initTotal = this.getBoothsTotal();

            this.showPaymentChangeButton = this.isCheckPayment();

            this.StripeElementsService.initStripeElements(this.exhibitorPaymentData.publishable_key);
        } catch (error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    async getExhibitorInvoiceInitData() {
        try {
            const {data: exhibitorPaymentData = {}} = await this.ExhibitorsPaymentsService.getExhibitorInvoiceInitData(
                this.eventId
            );

            this.initializeExhibitorPaymentData(exhibitorPaymentData);
            this.exhibitorPaymentData.comment = '';
        } catch (error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    async getExhibitors() {
        try {
            this.exhibitors = await this.ExhibitorsService.getExhibitors(this.eventId);
        } catch (error) {
            this.loading.error = error && error.validation ? error.validation : this.INTERNAL_ERROR_MSG;
        } finally {
            this.loading.inProcess = false;
        }
    }

    initializeExhibitorPaymentData(exhibitorPaymentData) {
        this.exhibitorPaymentData = Object.assign({}, exhibitorPaymentData);
        this.exhibitorPaymentData.selectedBooths = this.selectedBooths;
        this.exhibitorPaymentData.otherBooths = this.otherBooths;
    }

    getBoothsTotal() {
        this.total = this.ExhibitorsService.getBoothsTotal(this.exhibitorPaymentData);

        return this.total;
    }

    isEventDatesEmpty() {
        const {event_dates = {}} = this.exhibitorPaymentData || {};

        return !this.ExhibitorsService.isEventDatesSelected(event_dates);
    }

    hasOtherBoothsWithoutFee() {
        const {otherBooths = []} = this.exhibitorPaymentData || {};

        return this.ExhibitorsService.hasOtherBoothsWithoutFee(otherBooths);
    }

    hasOtherBoothsWithoutDescription() {
        const {otherBooths = []} = this.exhibitorPaymentData || {};

        return this.ExhibitorsService.hasOtherBoothsWithoutDescription(otherBooths);
    }

    isBoothsEmpty() {
        return this.getBoothsTotal() <= 0;
    }

    addBooth() {
        if (!this.selectedBooth) {
            return;
        }

        if (this.selectedBooth.id === this.OTHER_BOOTH.id) {
            this.addOtherBooth();
        } else {
            this.addEventBooth();
        }
    }

    addOtherBooth() {
        this.otherBooths.push(Object.assign({}, this.DEFAULT_OTHER_BOOTH));
    }

    addEventBooth() {
        this.selectedBooths.push(JSON.parse(angular.toJson(this.selectedBooth)));
    }

    removeBooth(index) {
        this.selectedBooths.splice(index, 1);
    }

    showCardMenu() {
        return this.show_card_menu && this.getBoothsTotal();
    }

    isApplicationApproved() {
        return this.exhibitorPaymentData.application_status === this.APPLICATION_STATUS.APPROVED;
    }

    showPaymentComponent() {
        const rules = [
            this.isUpdateMode,
            ![this.PAYMENT_STATUS.PAID, this.PAYMENT_STATUS.CANCELED].includes(this.exhibitorPaymentData.purchase_status),
            this.getBoothsTotal() > 0,
            this.isExhibitorPaymentDataSaved(),
            this.isApplicationApproved()
        ]

        return rules.every(rule => rule);
    }

    isExhibitorPaymentDataSaved() {
        if (!this.exhibitorPaymentData || !this.initExhibitorPaymentData) {
            return false;
        }

        const rules = [
            this._.isEqual(
                this.exhibitorPaymentData.selectedBooths,
                this.initExhibitorPaymentData.selectedBooths
            ),
            this._.isEqual(
                this.exhibitorPaymentData.otherBooths,
                this.initExhibitorPaymentData.otherBooths
            ),
            this._.isEqual(
                this.exhibitorPaymentData.event_dates,
                this.initExhibitorPaymentData.event_dates
            ),
            this.exhibitorPaymentData.comment === this.initExhibitorPaymentData.comment
        ];

        return rules.every(rule => rule);
    }

    async payByCard(token) {
        if (this.in_progress) {
            return;
        }

        if (token) {
            this.payment.token = token;
        }

        this.in_progress = true;

        const progressBarInterval = this.createProgressBarInterval();

        try {
            this.ExhibitorsService.validateExhibitorPaymentData(this.exhibitorPaymentData);

            await this.processPaymentByCard();

            this.progress_bar_value = 100;
            this.onModalClose({ withReload: true });
            this.toastr.success('Successful Payment');
            this.show_card_menu = false;
        } catch (error) {
            if (progressBarInterval) {
                this.$interval.cancel(progressBarInterval);
            }

            const {
                validation: errorValidation = null,
                data: {
                    validation: dataValidation = null
                } = {}
            } = error || {};

            const errorMsg = errorValidation || dataValidation || this.INTERNAL_ERROR_MSG;
            this.toastr.error(errorMsg);
        } finally {
            this.in_progress = false;
        }
    }

    createProgressBarInterval() {
        this.progress_bar_value = 20;
        const progressBarInterval = this.$interval(() => {
            if (this.progress_bar_value >= 100) {
                this.$interval.cancel(progressBarInterval);
            } else if (this.progress_bar_value < 48) {
                this.progress_bar_value += 4;
            } else {
                this.progress_bar_value += 1;
            }
        }, 250);
        return progressBarInterval;
    }

    processPaymentByCard() {
        const paymentType = this.PAYMENT_TYPE.CARD;
        const paymentMethod = this.generatePaymentMethod(paymentType);

        if (!paymentMethod) {
            throw {validation: 'Payment cannot be processed. Please, try again later.'};
        }

        return paymentMethod(paymentType, this.payment, this.eventId);
    }

    generatePaymentMethod(paymentType) {
        if (this.paymentTypeChangeModeEnabled) {
            return this.generateChangePaymentTypeMethod();
        }

        if (this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorPaymentData)) {
            return this.generatePendingPaymentMethod(paymentType);
        }

        return null;
    }

    generateChangePaymentTypeMethod() {
        this.payment.purchase_id = this.exhibitorPaymentData.purchase_id;

        return this.ExhibitorsService.changePaymentType.bind(this.ExhibitorsService);
    }

    generatePendingPaymentMethod(paymentType) {
        this.payment = this.formatPayment(this.payment);
        this.payment.purchase_id = this.exhibitorPaymentData.purchase_id;
        this.payment.booth = this.formatBooths(this.payment.booth);
        this.payment.type = paymentType;

        return this.ExhibitorReceiptsService.payReceipt.bind(this.ExhibitorReceiptsService);
    }

    formatPayment(payment) {
        return _.pick(payment, this.PAYMENT_FIELDS);
    }

    formatBooths(booths) {
        return booths.map(booth => this.formatBooth(booth));
    }

    formatBooth(booth) {
        return _.pick(booth, this.BOOTH_FIELDS);
    }

    showCard() {
        this.show_card_menu = true;
        this.showPaymentButtons = false;
        this.payment = this.generatePayment();
    }

    showPaymentChange() {
        this.show_card_menu = true;
        this.showPaymentChangeButton = false;
        this.showPaymentButtons = false;
        this.paymentTypeChangeModeEnabled = true;
        this.payment = this.generatePayment();
    }

    generatePayment() {
        const boothsTotal = this.getBoothsTotal();

        return {
            amount: boothsTotal,
            total: boothsTotal,
            event_id: this.eventId,
            sponsor_id: this.exhibitorId,
            pk: this.exhibitorPaymentData && this.exhibitorPaymentData.publishable_key,
            booth: this.ExhibitorsService.generateBoothsForRequest(this.exhibitorPaymentData)
        };
    }

    isPaymentEditable() {
        return this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorPaymentData);
    }

    isCheckPayment() {
        return this.exhibitorPaymentData.purchase_type === this.PAYMENT_TYPE.CHECK
            && this.exhibitorPaymentData.purchase_status === this.PAYMENT_STATUS.PENDING
    }

     onExhibitorChange() {
        this.exhibitorPaymentData.exhibitor_id = this.exhibitorId;
        this.checkExhibitorApplicationStatus();
    }

    checkExhibitorApplicationStatus() {
        if (!this.exhibitorId) return;

        const selectedExhibitor = this.exhibitors.find(exhibitor => exhibitor.sponsor_id === this.exhibitorId);
        if (selectedExhibitor) {
            this.exhibitorPaymentData.application_status = selectedExhibitor.registration_status;
        }
    }

    async deleteExhibitorPayment() {
        if(!this.ExhibitorsPaymentsService.isPendingPayment(this.exhibitorPaymentData) || this.loading.inProcess) {
            return;
        }

        this.loading.inProcess = true;

        try {
            const paymentDeleted = await this.processDelete();

            if(paymentDeleted) {
                this.toastr.success('Exhibitor payment successfully deleted');
                this.onModalClose({ withReload: true });
            }
        } finally {
            this.loading.inProcess = false;
        }
    }

    async processDelete() {
        const shouldDelete = await this.confirmInvoiceDelete();

        if (!shouldDelete) {
            return false;
        }

        return this.ExhibitorsPaymentsService.deleteEventExhibitorInvoice(this.eventId, this.exhibitorId, this.eventExhibitorInvoiceId);
    }

    async confirmInvoiceDelete() {
        const answer = await this.ConfirmationService.ask(
            'Are you sure you want to remove this invoice?',
            { disableNoBtn: true }
        );

        return answer === this.ConfirmationService.YES_RESP;
    }
}

angular.module('SportWrench').component('exhibitorPaymentForm', {
    templateUrl: 'events/dashboard/exhibitors/payments/exhibitor-payment/exhibitor-payment-form/exhibitor-payment-form.html',
    bindings: {
        eventId: '<',
        exhibitorId: '<',
        eventExhibitorInvoiceId: '<',
        exhibitorPaymentData: '=',
        initExhibitorPaymentData: '=',
        total: '=',
        initTotal: '=',
        onModalClose: '&',
        isUpdateMode: '<',
        isFormSubmitted: '<',
    },
    controller: [
        'ExhibitorsService',
        'ExhibitorsPaymentsService',
        'ExhibitorReceiptsService',
        'StripeElementsService',
        'UtilsService',
        'ConfirmationService',
        'INTERNAL_ERROR_MSG',
        'PAYMENT_STATUS',
        'PAYMENT_TYPE',
        'OTHER_BOOTH',
        'DEFAULT_OTHER_BOOTH',
        'EVENT_DATES_FORMAT',
        'APPLICATION_STATUS',
        '$interval',
        'toastr',
        '_',
        ExhibitorPaymentFormComponent
    ]
});

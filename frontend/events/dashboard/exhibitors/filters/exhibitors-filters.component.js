class ExhibitorsFiltersComponent {
    constructor($scope, $filter) {
        this.$scope = $scope;
        this.$filter = $filter('filter');

        this.filters = {
            search: '',
            paymentStatus: '',
        }
    }

    $onInit() {
        this.$scope.$watch('$ctrl.filters', (newVal, oldVal) => {
            if (_.isEqual(newVal, oldVal)) {
                return;
            }

            const _exhibitors = this.filterList(this.exhibitors);

            this.onFilter({ filteredExhibitors: _exhibitors });
        }, true)
    }

    filterList(exhibitors) {
        return this.$filter(exhibitors, (exh) => {
            return this.searchFilter(exh, this.filters.search) && this.paymentStatusFilter(exh, this.filters.paymentStatus);
        })
    }

    searchFilter(exhibitor, search) {
        search = search.toLowerCase();

        return (exhibitor.company_name || '').toLowerCase().indexOf(search) !== -1
            || (exhibitor.booths.title || '').toLocaleLowerCase().indexOf(search) !== -1;
    }

    paymentStatusFilter(exhibitor, status) {
        if (!status) {
            return true;
        }

        return exhibitor.payment_status === status;
    }
}

angular.module('SportWrench').component('exhibitorsFilters', {
    templateUrl: 'events/dashboard/exhibitors/filters/exhibitors-filters.html',
    bindings: {
        onFilter: '&',
        exhibitors: '<',
    },
    controller: ['$scope', '$filter', ExhibitorsFiltersComponent]
});

class ExhibitorsPaymentStatusFilterComponent {
    constructor(PAYMENT_STATUS) {
        this.PAYMENT_STATUS = PAYMENT_STATUS;
    }

    capitalizeFirstLetter(status) {
        return status[0].toUpperCase() + status.slice(1);
    }
}

angular.module('SportWrench').component('exhibitorsPaymentStatusFilter', {
    templateUrl: 'events/dashboard/exhibitors/filters/payment-status/exhibitors-payment-status-filter.html',
    bindings: {
        paymentStatus: '=',
    },
    controller: ['PAYMENT_STATUS', ExhibitorsPaymentStatusFilterComponent]
});

class CreateExhibitorComponent {
    constructor(ExhibitorsService, $stateParams, ConfirmationService, $state) {
        this.service = ExhibitorsService;
        this.eventID = $stateParams.event;
        this.confirm = ConfirmationService;
        this.$state = $state;
        this.$stateParams = $stateParams;

        this.EXHIBITOR_IN_LIST_ERROR_TYPE = 'exhibitor_in_list';
        this.EXHIBITOR_EXISTS_ERROR_TYPE = 'exhibitor_exists';
        this.EXHIBITOR_ADDED_ERROR_TYPE = 'exhibitor_added';

        this.createExhibitorData = {};
        this.createExhibitorForm = null;

        this.saveInProcess = false;
    }

    onSave() {
        if (this.saveInProcess) {
            return;
        }

        this.createExhibitorForm.$setSubmitted();

        if (this.createExhibitorForm.$invalid) {
            return;
        } 

        this.saveInProcess = true;

        this.service.createExhibitor(this.eventID, this.createExhibitorData)
            .then(this.onSaveSucceeded.bind(this))
            .catch(this.onSaveFailed.bind(this))
            .finally(this.onSaveFinally.bind(this));
    }

    onSaveSucceeded() {
        this.onClose({ withReload: true });
    }

    onSaveFailed({ data: error }) {
        if (error && error.type === this.EXHIBITOR_ADDED_ERROR_TYPE) {
            this.exhibitorAddedAsk(error);
        } else if (error && error.type === this.EXHIBITOR_IN_LIST_ERROR_TYPE) {
            this.exhibitorInListAsk(error);
        } else if (error && error.type === this.EXHIBITOR_EXISTS_ERROR_TYPE) {
            this.exhibitorExistsAsk(error);
        }
    }

    exhibitorInListAsk(error) {
        this.confirm.ask(
            error.message,
            {
                disableNoBtn: true,
            }
        ).then(answer => {
            if (answer === this.confirm.YES_RESP) {
                this.onClose();

                this.service.openExhibitorInfoModal({
                    eventID: this.eventID,
                    companyName: error.company_name,
                    exhibitorID: error.exhibitor_id,
                    isApplyMode: true,
                    onSave: this.service.createExhibitorRegistrationInfo.bind(this.service),
                }).then(isSaved => {
                    if (isSaved) {
                        this.$state.go(this.$stateParams.exhibitorsState, {event: this.eventID});
                    }
                })
            } else {
                this.onClose();
                this.onSearch({ search: this.createExhibitorData.email });
            }
        });
    }

    exhibitorExistsAsk(error) {
        this.confirm.ask(
            error.message,
            {
                title: 'Info',
                disableNoBtn: true,
                disableYesBtn: true,
            }
        ).then(answer => {
            if (answer === this.confirm.CANCEL_RESP) {
                this.onClose();
            }
        });
    }

    exhibitorAddedAsk(error) {
        this.confirm.ask(
            error.message,
            {
                title: 'Info',
                disableNoBtn: true,
                disableYesBtn: true,
            }
        ).then(answer => {
            if (answer === this.confirm.CANCEL_RESP) {
                this.onClose();
            }
        });
    }

    onSaveFinally() {
        this.saveInProcess = false;
    }
}

angular.module('SportWrench').component('createExhibitor', {
    templateUrl: 'events/dashboard/exhibitors/user-exhibitors/create-exhibitor/create-exhiibitor.html',
    bindings: {
        onClose: '&',
        onSearch: '&',
    },
    controller: [
        'ExhibitorsService',
        '$stateParams',
        'ConfirmationService',
        '$state',
        CreateExhibitorComponent
    ]
})

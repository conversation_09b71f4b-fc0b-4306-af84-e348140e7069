<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error">
        <div class="col-sm-12">
            <form class="form form-inline row-space">
                <div ng-if="$ctrl.exhibitors.length" class="form-group exhibitors-list_filters_search">
                    <exhibitors-search-filter search="$ctrl.search" on-change="$ctrl.onSearch(search)"></exhibitors-search-filter>
                </div>
                <button ng-click="$ctrl.onCreate()" class="btn btn-primary exhibitors-list_add-exhibitor-btn">Create Exhibitor</button>
            </form>
        </div>
        <div class="col-sm-12">
            <uib-alert type="warning text-center" ng-if="!$ctrl.exhibitors.length">You have not created any exhibitors yet</uib-alert>
            <user-exhibitors-list ng-if="$ctrl.exhibitors.length" exhibitors="$ctrl.filteredExhibitors"></user-exhibitors-list>
        </div>
    </div>
</div>
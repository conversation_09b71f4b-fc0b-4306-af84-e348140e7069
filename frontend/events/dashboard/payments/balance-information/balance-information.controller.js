angular.module('SportWrench').component('balanceInformationModal', {
    templateUrl: 'events/dashboard/payments/balance-information/balance-information.html',
    bindings: {
        payment: '<',
        onClose: '&',
    },
    controller: [Component]
});

function Component () {
    this.$onInit = function() {
        this.before = this.payment.teams_balance_info.before;
        this.after = this.payment.teams_balance_info.after;
    }
}

angular.module('SportWrench').factory('PaymentsDynamicColsFactory', [ 'userService', function (userService) {
    return {
        getColumns: function () {
            return [
                {
                    title           : 'Created',
                    name            : 'created',
                    sortable        : 'created',
                    visible         : true
                }, {
                    title           : 'Paid',
                    name            : 'date_paid',
                    sortable        : 'date_paid',
                    visible         : true
                }, {
                    title           : 'Available',
                    name            : 'stripe_balance_available',
                    sortable        : 'stripe_balance_available',
                    visible         : true
                },{
                    title           : 'Invoice',
                    name            : 'purchase_id',
                    sortable        : 'purchase_id',
                    visible         : true
                }, {
                    title           : 'Type',
                    name            : 'type',
                    sortable        : 'type',
                    visible         : true
                }, {
                    title           : 'Status',
                    name            : 'status',
                    sortable        : 'status',
                    selectors       : 'col-paymt-status',
                    visible         : true
                }, {
                    title           : 'Amount',
                    name            : 'amount',
                    sortable        : 'amount',
                    selectors       : 'text-right',
                    visible         : true
                }, {
                    title           : 'Teams',
                    name            : 'teams_count',
                    selectors       : 'hiding-column-min',
                    sortable        : 'teams_count',
                    visible         : true
                }, {
                    title           : 'S<PERSON> Fee',
                    name            : 'collected_sw_fee',
                    sortable        : 'collected_sw_fee',
                    visible         : userService.hasGodRole()
                }, {
                    title           : 'Additional Fee',
                    name            : 'additional_fee_amount',
                    sortable        : 'additional_fee_amount',
                    visible         : userService.hasGodRole()
                }, {
                    title           : 'Club name',
                    name            : 'club_name',
                    sortable        : 'club_name',
                    visible         : true
                }, {
                    title           : 'Divisions',
                    name            : 'divisions',
                    selectors       : 'hiding-column-min',
                    visible         : true
                }
            ];
        }
    };
}]);

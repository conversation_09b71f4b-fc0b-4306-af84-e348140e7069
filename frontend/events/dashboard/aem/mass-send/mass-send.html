<div class="row mass-send-wrapper">
    <div ng-repeat="ev in $ctrl._events">
        <p>{{ev.long_name}}</p>
    </div>

    <div class="col-sm-12 col-xs-12 col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">New Campaign</h3>
            </div>
            <div class="panel-body">
                <div class="row mass-send_dropdown-wrapper">
                    <div class="col-xs-12 col-sm-10 col-md-6">
                        <events-dropdown
                            label="Events"
                            events="$ctrl.events"
                        >
                        </events-dropdown>
                    </div>
                    <div class="btn-group col-xs-12 col-sm-4 col-md-4" uib-dropdown>
                        <button type="button" class="btn btn-default mass-send_groups-btn" uib-dropdown-toggle>
                            Groups
                            <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu" ng-click="$event.stopPropagation()">
                            <li class="row rowm0" ng-repeat="group in $ctrl.groups">
                                <div class="col-sm-12" ng-if="$ctrl.showGroup(group)">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" ng-model="group.selected">
                                            {{group.title}}
                                        </label>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="row mass-send_text-field-wrapper">
                    <div class="mass-send_reply-to-wrapper col-xs-12 col-sm-10 col-md-8">
                        <label class="col-sm-2 control-label">Reply to</label>
                        <div class="col-sm-8">
                            <input
                                type="email"
                                class="col-sm-8 form-control"
                                ng-model="$ctrl.replyTo"
                                name="replyTo"
                            >
                        </div>
                    </div>

                    <div class="btn-group col-xs-12 col-sm-4 col-md-2">
                        <div class="mass-send_save-btn-wrapper">
                            <button class="btn btn-primary" ng-click="$ctrl.save()">Send</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-xs-12 col-md-12">
        <campaigns-info template-id="$ctrl.templateId"></campaigns-info>
    </div>
</div>

<div class="aem-variables-picker" uib-dropdown on-toggle="$ctrl.loadVariables()" is-open="$ctrl.isDrowDownOpened">
   <button type="button" class="btn btn-default" uib-dropdown-toggle>
        <span class="visible-lg-inline">Add variable to Subject</span>
        <span class="hidden-lg">Variables</span>
        <span class="caret"></span>
    </button>

    <ul class="dropdown-menu pull-right" uib-dropdown-menu role="menu">

    	<spinner active="$ctrl.loading"></spinner>

        <p ng-if="$ctrl.showMsg()" class="text-danger text-center font-bold">No variables available</p>


        <p class="text-center font-bold" ng-if="$ctrl.showList()">Available Variables:</p>
        <li role="menuitem" ng-if="$ctrl.showList()" ng-repeat="tVar in $ctrl.templateSubjectVariables">
        	<a href="" ng-click="$ctrl.onItemClick(tVar.pattern)">
        		<var class="label label-info" ng-bind="tVar.pattern"></var>
        		<span ng-bind="tVar.title"></span>
        	</a>
        </li>

    </ul>
</div>

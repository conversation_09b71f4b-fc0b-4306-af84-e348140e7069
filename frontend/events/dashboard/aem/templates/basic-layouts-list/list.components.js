angular.module('SportWrench').component('aemLayoutsList', {
	templateUrl : 'events/dashboard/aem/templates/basic-layouts-list/list.html',
	bindings 	: {
		items 			 	: '<',
		onItemSelect 	 	: '&onSelect',
		highlightNotPicked 	: '<showNotPicked',
		disabled 			: '<'
	},
	controller 	: [AEMLayoutsListController]
})

function AEMLayoutsListController () {

	this.pickedItemID = 0;
	
	this.getItemClass = function (index) {
		var itemsLength = this.items.length;

		var cssObj = {
			'col-xs-offset-4 col-xs-4' 	: itemsLength === 1,
			'col-xs-offset-2 col-xs-3' 	: itemsLength === 2,
			'col-xs-4' 					: itemsLength === 3,
			'col-xs-3' 					: itemsLength === 4,
			'col-xs-offset-1' 			: (itemsLength === 5) && (index === 0),
			'col-xs-2' 					: itemsLength > 4
		}

		return cssObj;
	}

	this.pickItem = function (id) {
		if (this.disabled) {
			return;
		}
		
		this.pickedItemID = id;

		this.onItemSelect({ id: id });
	}

	this.linkClass = function (id) {
		return {
			'thumbnail'  : true,
			'picked' 	 : (this.pickedItemID === id),
			'not-picked' : this.highlightNotPicked && (this.pickedItemID !== id)
		}
	}

	this.titleClass = function () {
		return {
			'text-center': true,
			'text-danger': this.highlightNotPicked
		}
	}

}
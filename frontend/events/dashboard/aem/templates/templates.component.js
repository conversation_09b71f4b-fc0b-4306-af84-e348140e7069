angular.module('SportWrench').component('aemTemplates', {
	templateUrl 	: 'events/dashboard/aem/templates/templates.html',
	bindings 		: {
		service: '<'
	},
    transclude: {
        'backBtn': '?backBtn'
    },
	controller 		: ['AEMService', 'AEMModalService', '_', '$q', AEMTemplatesController]
})

function loadInitData (UserAEMService, _, $q) {
	return $q.all([
		UserAEMService.getInitData(),
	]).then(function (results) {
		var initData 	= results[0].data || {};

		this.templatesListByGroup = initData.template_groups;

		this.groupsList = _.map(this.templatesListByGroup, function (g) {
			return { title: g.title, id: g.id, types: g.types };
		})
	}.bind(this));
}

function AEMTemplatesController (AEMService, AEMModalService, _, $q) {
	var UserAEMService = this.service;

	var reactEmailEditorData 	= {};

	this.loadData = loadInitData.bind(this, UserAEMService, _, $q);

	let openModal = function (service, editorConfig, groupsList, template) {
		AEMModalService.showEditorModal(service, editorConfig, groupsList, template)
		.then(function (isChanged) {
			if (isChanged === true) {
				this.loadData();
			}
		}.bind(this))
	}.bind(this, this.service);

	this.templatesListByGroup = [];
	this.groupsList = [];
	this.userRole = this.service.getRole();

	this.loadData().then(function (editorConfig) {
		reactEmailEditorData = editorConfig;
	});

	this.editTmpl = function (item) {
		UserAEMService.getTemplate(item.id).then(resp => {
			let template = resp.data.template;

			openModal(reactEmailEditorData, this.groupsList, template);
		});
	}

	this.createTmpl = function () {
	    // get groups which has types.
	    const groupsList = this.groupsList.filter(item => item.types && item.types.length);

		openModal(reactEmailEditorData, groupsList, null);
	}

	this.previewTmpl = function (item) {
		var link = UserAEMService.getPreviewLink(item.id);
		AEMService.openPreviewModal(item.id, link, item.title, false, item.group);
	}

	this.duplicateTmpl = function (item) {
		return UserAEMService.duplicateTemplate(item.id)
		.then(resp => {
            let template = resp.data.template;

			this.editTmpl(template);

			this.loadData();
		});
	}

	this.assignTmpl = function (item) {
		/* There is not such function for admin */
		return UserAEMService.assignTemplateToTrigger(item.id)
		.then(() => {
			this.loadData();
		})
	}

	this.removeTmpl = function (item) {
		return UserAEMService.removeTemplate(item.id).then(angular.noop);
	}

    this.togglePublish = function (item) {
        return UserAEMService.togglePublish(item.id, item.published).then(angular.noop);
    }
}

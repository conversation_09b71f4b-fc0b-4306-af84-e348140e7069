<div class="template-updating modal-body">
    <div class="row rowm0 top-menu">
        <div class="col-xs-9">
			<aem-template-details-from 
				form-type="updating" 
				form="$ctrl.tmplForm" 
				tmpl-group="$ctrl.tmplGroup.id"
				on-change="$ctrl.tmplDataChanged(data)"
				tmpl-title="$ctrl.tmplTitle"
				disabled="$ctrl.isUpdating"
				tmpl-subject="$ctrl.tmplSubject"
				type-variables="$ctrl.typeVariables"
				tmpl-type="$ctrl.tmplType.id"
                on-form-linked="$ctrl.computeSizeOfTheModal()">
				<type>
					<h4><span class="label label-info" ng-bind="$ctrl.getTypeLabel()"></span></h4>
				</type>
			</aem-template-details-from>
        </div>
        <div class="col-xs-3">
            <button type="button" ng-disabled="$ctrl.isUpdating" class="close" ng-click="$ctrl.onModalClose()">
	            <span aria-hidden="true">&times;</span>
	        </button>
        </div>
    </div>
    <react-email-editor 
		ng-if="$ctrl.showEditor"
		tmpl-id="$ctrl.templateId" 
		bee-tmpl="$ctrl.beeTmpl"
		tmpl="$ctrl.editorTmpl" 
		tmpl-html="$ctrl.editorTmplHtml" 
		on-save="$ctrl.onTmplSaving(json, html)"
		on-send="$ctrl.onTmplSending(html, reactEmailEditorInstance)"
		merge-tags="$ctrl.mergeTags"
		on-loaded="$ctrl.onEditorLoaded()"
        editor-window-height="$ctrl.editorHeight"
        on-auto-save="$ctrl.autoSave(json)">
	</react-email-editor>
</div>

angular.module('SportWrench').component('aemTemplateUpdating', {
	templateUrl 	: 'events/dashboard/aem/template-updating/updating.html',
	bindings 		: {
		onModalClose 	: '&onClose',
		updateTemplate 	: '&',
		sendTemplate 	: '&',
		templateId 	: '<tmplId',
		beeTmpl			: '<beeTmpl',
		editorTmpl 		: '<tmpl',
		editorTmplHtml 	: '<tmplHtml',
		tmplType 		: '<',
		tmplGroup  		: '<', // !!!
		// maybe move these to one object
		tmplTitle 		: '<',
		tmplSubject 	: '<',
		onEditorLoaded 	: '&',
        saveToStorage   : '&'
	},
	controller 		: [
	    'toastr', '$timeout', 'AEMService', 'UtilsService', '$element', '$uibModal', '$localStorage',
        'MAX_EMAIL_SUBJECT_LENGTH',
        AEMTemplateUpdatingController
    ]
});

function prepareDataAndRunEditor (UtilsService, variables) {
	this.typeVariables  	= variables;
	this.mergeTags  		= UtilsService.prepareMergeTags(this.typeVariables);
	this.showEditor  		= true;
}

function AEMTemplateUpdatingController (
    toastr, $timeout, AEMService, UtilsService, $element, $uibModal, $localStorage, MAX_EMAIL_SUBJECT_LENGTH
) {
	var formData = {};
	var __prepareAndRun__ = prepareDataAndRunEditor.bind(this, UtilsService);

	this.tmplForm = {};

	this.typeVariables = [];

	this.isUpdating = false;
	this.showEditor = false;

	this.editorHeight = 0;

	this.tmplDataChanged = function (data) {
		formData = data;
	}

	this._onTmplSending = false;
	this._dismissSendingModal = null;
	this.receiver = {};

	this.MAX_EMAIL_SUBJECT_LENGTH = MAX_EMAIL_SUBJECT_LENGTH;

	this.onTmplSaving = function (json, html) {
		$timeout(function () {
			this.tmplForm.$setSubmitted();

			if (this.tmplForm.$invalid) {
				if (this.tmplForm.title && this.tmplForm.title.$invalid) {
					toastr.warning('Title required!');
				} else if (this.tmplForm.subject.$invalid) {
				    if(this.tmplForm.subject.$error.maxlength) {
                        toastr.warning(`Subject max length ${MAX_EMAIL_SUBJECT_LENGTH} chars`);
                    } else {
                        toastr.warning('Subject required');
                    }
				} else {
					toastr.warning('Invalid form data');
				}
				return;
			}

			const TEMPLATE_LAYOUT_TYPE = 'layout'

			if(this.tmplType.id === TEMPLATE_LAYOUT_TYPE){
				toastr.warning(`Cannot edit a layout template`);
				return;
			}

            let error = AEMService.validateTemplateVariables(formData.email_subject, this.typeVariables);

            if(error) {
                toastr.warning(error);
                return;
            }

			if (this.isUpdating) {
				return;
			}

			this.isUpdating = true;

            this.updateTemplate({
				data: angular.extend(formData, { json: json, html: html, _onTmplSending: this._onTmplSending })
			}).then(() => {
                if (this._onTmplSending) {
                    const subject = formData.email_subject;

                    this.sendTemplate({ html, subject, receiver: this.receiver })
                        .then(() => {
                            this._onTmplSending = false;
                            this._dismissSendingModal();
                        })
                        .catch(() => this._dismissSendingModal());
                }
            }).finally(function () {
                this.isUpdating = false;
			}.bind(this));
		}.bind(this))
	}

	this.autoSave = function (json) {
        this.saveToStorage({json});
    };

	this.onTmplSending = function (html, reactEmailEditorInstance) {
	    const self = this;

        this.tmplForm.$setSubmitted();

 		if (this.tmplForm.subject.$invalid) {
            if(this.tmplForm.subject.$error.maxlength) {
                toastr.warning(`Subject max length ${MAX_EMAIL_SUBJECT_LENGTH} chars`);
            } else {
                toastr.warning('Subject required');
            }
 		    return;
        }

        return $uibModal.open({
			templateUrl : 'events/dashboard/aem/modal/test-receiver-modal.html',
			backdrop    : 'static',
			controller: ['$scope', function ($scope) {
			    $scope.data = {
			        receiver: {
                        email: $localStorage.user.email,
                        first: $localStorage.user.first,
                        last: $localStorage.user.last
                    },
			        showWarning: false,
                };

				$scope.send = function () {
					if ($scope.sendTestForm.$valid) {
					    self._onTmplSending = true;
					    self._dismissSendingModal = $scope.$dismiss;

						reactEmailEditorInstance.saveTemplate();
					} else {
						$scope.data.showWarning = true;
					}
				};

				$scope.$watch('data.receiver', (newVal) => {
                    self.receiver = newVal;
                }, true);
            }]
		}).result; 
	}

	this.getTypeLabel = function () {
		let groupTitle  = this.tmplGroup.title || '';
        let typeTitle   = this.tmplType && this.tmplType.title;

        if (groupTitle) {
            return groupTitle + (typeTitle ? (' / ' + typeTitle) : '');
        } else {
            return typeTitle;
        }
	}

    this.computeSizeOfTheModal = function () {
        let modalElem       = $element.find('.modal-body')[0];
        /**
         * https://developer.mozilla.org/ru/docs/Web/API/Window/getComputedStyle
         * We need to get a computed padding value
         */
        let modalStyle      = window.getComputedStyle(modalElem);
        /**
         * https://developer.mozilla.org/ru/docs/Web/API/Element/clientHeight
         * clientHeight includes padding but excludes border and margin. 
         * 
         * Our goal here is to compute height of a parent-block of the Bee Editor. 
         *
         * As "clientHeight" property contains paddings, we have to substract them from the result 
         * to obtain height value, that can be used for Bee Editor placement
         */
        let modalHeight     = (
            modalElem.clientHeight - 
            parseFloat(modalStyle.paddingTop) - 
            parseFloat(modalStyle.paddingBottom)
        );
        let topMenuHeight   = $element.find('.top-menu')[0].offsetHeight;
        
        this.editorHeight = Number((modalHeight - topMenuHeight).toFixed(10));
    }

	if (this.tmplGroup && this.tmplGroup.id) {
		AEMService.getTemplateGroupVariables(this.tmplGroup.id)
		.then(__prepareAndRun__);
	} else {
		/* pass in response with no variables */
		__prepareAndRun__([]);
	}
}

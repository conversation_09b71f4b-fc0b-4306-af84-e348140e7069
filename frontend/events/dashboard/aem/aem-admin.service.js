angular.module('SportWrench').service('AEMAdminService', ['$http', '$q', AEMAdminService]);

function AEMAdminService ($http, $q) {
	this._$http  			= $http;
	this._$q 				= $q;
	this.__baseURL__ 		= '/api/aem-admin/'
}

AEMAdminService.prototype.getInitData = function () {
	return this._$http.get(this.__baseURL__ + 'init');
};

AEMAdminService.prototype.getPreviewLink = function (templateID) {
	return this.__baseURL__ + 'tmpl/' + templateID + '/preview'
};

AEMAdminService.prototype.duplicateTemplate = function (templateID) {
	return this._$http.post(this.__baseURL__ + 'tmpl/' + templateID + '/duplicate');
};

AEMAdminService.prototype.getTemplate = function (templateID) {
	return this._$http.get(this.__baseURL__ + 'tmpl/' + templateID);
};

AEMAdminService.prototype.saveTemplate = function (templateID, data) {
	return this._$http.put(this.__baseURL__ + 'tmpl/' + templateID, data);
};

AEMAdminService.prototype.createTemplate = function (data) {
	return this._$http.post(this.__baseURL__ + 'tmpl', data);
}

AEMAdminService.prototype.removeTemplate = function (templateID) {
	return this._$http.delete(this.__baseURL__ + 'tmpl/' + templateID);
}

AEMAdminService.prototype.togglePublish = function (templateID, isPublished) {
   return this._$http.put(this.__baseURL__ + 'tmpl/' + templateID + '/publish', {
        is_published: isPublished
   });
}

AEMAdminService.prototype.getGroupTypes = function (groupID) {
    return this._$http.get(`${this.__baseURL__}${groupID}/types`).then(res => res.data.types);
}

AEMAdminService.prototype.testSend = function (templateID, html, subject, receiver) {
	return this._$http.post(this.__baseURL__ + 'tmpl/' + templateID + '/send', {
		html 		: html,
		subject 	: subject,
		receiver    : receiver
	}).then(result => result.data);
}

AEMAdminService.prototype.getRole = function () {
	return 'admin';
}

AEMAdminService.prototype.getAvailableTmpls = function (group) {
    return this._$http.get(this.__baseURL__ + 'sending/' + group + '/available-tmpls')
        .then(function (resp) {
            return resp.data.templates;
        })
}


AEMAdminService.prototype.sendLetter = function (group, data) {
    return this._$http.post(this.__baseURL__ + group + '/send-email', data)
        .then(function (res) {
            return res.data
        });
}

angular.module('SportWrench')
.service('AEMModalService', ['$uibModal', AEMModalService]);

const __TMPL_MODAL_CREATION__   = 'initial_creation';
const __TMPL_MODAL_EDITING__    = 'template_editing';

const SAVED_CHANGES_RESTORE_MSG = 'You appear to have a backup of this template that is more recent than the saved. ' +
                                    'Do you want to restore the backup?';

function AEMModalService ($uibModal) {
    this._$uibModal = $uibModal;
}

AEMModalService.prototype._getModalController = function (ActionsService, reactEmailEditorData, groupsList, template) {

    return [
        '$scope', 
        'AEMService', 
        'toastr',
        '$localStorage',
        '$window',
        'ConfirmationService',
        function ReactEmailEditorModalController ($scope, AEMService, toastr, $localStorage, $window, ConfirmationService) {
            let modalMode, 
                templateID;

            let isModified          = false, 
                isReactEmailEditorLoaded   = false,
                storage             = $localStorage;

            $scope.templateGroups       = groupsList;
            $scope.userRole             = ActionsService.getRole();
            $scope.basicLayouts         = [];


            /* === Initialization === */
            setTimeout(function initializeController () {
                if (template == null) {

                    AEMService.getBasicLayouts().then(function (resp) {
                        $scope.basicLayouts = resp.data.templates;
                    });

                    modalMode = __TMPL_MODAL_CREATION__;
                } else {
                    $scope.moveToSecondStep({
                        id      : template.id,
                        beeJson : template.bee_json,
                        json    : template.unlayer_json,
                        html    : template.email_html,
                        type    : { id: template.type   , title: template.type_title },
                        title   : template.title,
                        group   : { id: template.group  , title: template.group_title },
                        subject : template.subject
                    });
                }
            });
            /* === */


            $scope.moveToSecondStep = function (params) {
                let {
                    id, type, title, group, subject, beeJson, json, html
                } = params;

                //Check if autosaved data is empty
                if(storage.autosaved_templates && storage.autosaved_templates[id]) {
                    ConfirmationService.ask(
                        SAVED_CHANGES_RESTORE_MSG,
                        { disableCancelBtn: true, backdrop: 'static' }
                    ).then(function (resp) {
                        if(resp === ConfirmationService.YES_RESP) {
                            try {
                                let parsedJson = JSON.parse(storage.autosaved_templates[id]);
                                json = parsedJson;
                            } catch (e) {
                                // Catch error if json is not valid and delete data in storage
                                console.error(e);
                                delete storage.autosaved_templates[id];
                            }
                        }

                        _saveTemplateVars();
                    });
                } else {
                    _saveTemplateVars();
                }

                function _saveTemplateVars () {
                    $scope.templateId            = id;
                    $scope.beeJSON                = beeJson
                    $scope.templateJSON           = json;
                    $scope.templateHTML           = html;
                    $scope.currentTemplateType    = type;
                    $scope.currentTemplateTitle   = title;
                    $scope.currentTemplateGroup   = group;
                    $scope.currentTemplateSubject = subject;

                    modalMode   = __TMPL_MODAL_EDITING__;
                    templateID  = id;
                }
            }

            $scope.showFirstStep = function () {
                return modalMode === __TMPL_MODAL_CREATION__;
            };

            $scope.showSecondStep = function () {
                return modalMode === __TMPL_MODAL_EDITING__;
            };

            $scope.createTemplate = function (data) {
                let group = data.group;
                data.group = group.id;

                return ActionsService.createTemplate(data)
                .then(function (resp) {
                    let tmpl = resp.data.template;

                    $scope.moveToSecondStep({
                        id      : tmpl.id,
                        json    : tmpl.unlayer_json,
                        html    : tmpl.email_html,
                        type    : tmpl.type,
                        title   : data.title,
                        group   : group,
                        subject : null
                    });

                    isModified = true;
                });
            };

            $scope.updateTemplate = function (data) {
                const _onTmplSending = data._onTmplSending;

                return ActionsService.saveTemplate(templateID, _.omit(data, ['_onTmplSending']))
                .then(function () {
                    if (!_onTmplSending) {
                        clearTemplateStorageData(templateID);
                        toastr.success('Saved!');
                        isModified = true;
                        $scope.closeModal();
                    }
                })
            };

            $scope.saveTemplateToLocalStorage = function (json) {
                if(!storage.autosaved_templates) {
                    storage.autosaved_templates = {};
                }

                storage.autosaved_templates[templateID] = json;
            };

            $scope.sendTemplate = function (html, subject, receiver) {
                return ActionsService.testSend(templateID, html, subject, receiver)
                .then(function ({email, isTestDataUsed}) {
                    if(isTestDataUsed) {
                        toastr.warning('No real data for the event. Used test data.');
                    }
                    toastr.success('Sent to ' + email);
                })
            }

            $scope.loadGroupTypes = function (groupID) {
                return ActionsService.getGroupTypes(groupID);
            }

            $scope.onEditorLoaded = function () {
                isReactEmailEditorLoaded = true;
            }

            $scope.closeModal = function () {
                if ($scope.showSecondStep() && !isReactEmailEditorLoaded) {
                    return;
                }

                $scope.$close(isModified);
            }

            function clearTemplateStorageData(templateID) {
                if(storage.autosaved_templates && storage.autosaved_templates[templateID]) {
                    delete storage.autosaved_templates[templateID];
                }
            }
        }   
    ];
}

AEMModalService.prototype.showEditorModal = function (ActionsService, reactEmailEditorData, groupsList, template) {
    return this._$uibModal.open({
        templateUrl : 'events/dashboard/aem/modal/react-email-editor-modal.html',
        size        : 'xxl',
        windowClass : 'react-email-editor-modal',
        backdrop    : 'static',
        keyboard    : false,
        controller  : this._getModalController(ActionsService, reactEmailEditorData, groupsList, template)
    }).result;
};

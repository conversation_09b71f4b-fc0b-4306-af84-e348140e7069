angular.module('SportWrench').service('ContactListsService', ContactListsService);

ContactListsService.$inject = ['$http', '$uibModal'];

function ContactListsService ($http, $uibModal) {
    this._$http         = $http;
    this._$uibModal     = $uibModal;
}

Object.defineProperty(ContactListsService.prototype, 'CREATE_MODE', {
    value 			: 'create',
    writable 		: false,
    configurable 	: false
});

Object.defineProperty(ContactListsService.prototype, 'UPDATE_MODE', {
    value 			: 'update',
    writable 		: false,
    configurable 	: false
});

ContactListsService.prototype.getLists = function (eventID) {
    return this._$http.get(`/api/event/${eventID}/aem/custom-recipients`)
        .then(result => result.data);
};

ContactListsService.prototype.getList = function (eventID, listID) {
    return this._$http.get(`/api/event/${eventID}/aem/custom-recipients/${listID}`)
        .then(result => result.data);
};

ContactListsService.prototype.updateList = function (eventID, listID, formData) {
    return this._$http.post(`/api/event/${eventID}/aem/custom-recipients/${listID}`, formData, {
        withCredentials: true,
        headers: { 'Content-Type': undefined },
        transformRequest: angular.identity
    }).then(result => result.data);
};

ContactListsService.prototype.createList = function (eventID, formData) {
    return this._$http.post(`/api/event/${eventID}/aem/custom-recipients`, formData, {
        withCredentials: true,
        headers: { 'Content-Type': undefined },
        transformRequest: angular.identity
    }).then(result => result.data);
};

ContactListsService.prototype.deleteList = function (eventID, listID) {
    return this._$http.delete(`/api/event/${eventID}/aem/custom-recipients/${listID}`)
};

ContactListsService.prototype.updateTitle = function (eventID, listID, title) {
    return this._$http.put(`/api/event/${eventID}/aem/custom-recipients/${listID}/title`, { title });
};

ContactListsService.prototype.addRecipient = function (eventID, listID, contact) {
    return this._$http.post(`/api/event/${eventID}/aem/custom-recipients/${listID}/recipient`, { contact })
        .then(response => response.data && response.data.contact);
};

ContactListsService.prototype.updateRecipient = function (eventID, listID, contactID, contact) {
    return this._$http.put(
        `/api/event/${eventID}/aem/custom-recipients/${listID}/recipient/${contactID}`
        , { contact }
        ).then(response => response.data);
};

ContactListsService.prototype.deleteRecipient = function (eventID, listID, contactID) {
    return this._$http.delete(`/api/event/${eventID}/aem/custom-recipients/${listID}/recipient/${contactID}`)
        .then(response => response.data);
};

ContactListsService.prototype.openListModal = function (eventID, mode /* create/update */, list) {
    let self = this;

    return this._$uibModal.open({
        template    : `<modal-wrapper>
                        <new-contact-list 
                            save-list="saveList(form)" 
                            submitted="formIsSubmitted"
                            drop-submit="dropSubmitFlag()"
                            mode="mode"
                            list="list"
                            ></new-contact-list>
                        <external-button
                            class="btn btn-success pull-right"
                            ng-bind="'Save'"
                            ng-click="submitForm()">
                        </external-button>
                       </modal-wrapper>`,
        controller  : ['$scope', '$uibModalInstance', 'toastr', 'UtilsService',
            function (scope, $uibModalInstance, toastr, UtilsService
        ) {
            scope.modalTitle     = `<h4>${UtilsService.capitalizeFirstLetter(mode)} Contact List</h4>`;

            scope.modalShowClose = true;
            scope.mode           = mode;

            if(list) {
                scope.list = {
                    title   : list.title,
                    list_id : list.id
                };
            }

            scope.submitForm     = function () {
                scope.formIsSubmitted = true;
            };

            scope.saveList = function (form) {
                let formData = new FormData();
                let method;

                formData.append('title', form.title);

                if(mode === self.UPDATE_MODE) {
                    formData.append('import_option', form.import_option);

                    method =  self.updateList.bind(self, eventID, list.id);
                } else {
                    formData.append('visibility_scope', form.visibility_scope);

                    method =  self.createList.bind(self, eventID);
                }

                //NOTE: file should be in the end of form data
                formData.append('file' , form.fileToUpload);

                method(formData).then(({result}) => {
                        toastr.success(`Recognized: ${result.parsed} rows. Added: ${result.added} rows`);

                        $uibModalInstance.close(result.list);
                    }).catch(() => scope.formIsSubmitted = false);
            };

            scope.dropSubmitFlag = function () {
                scope.formIsSubmitted = false;
            }
        }]
    }).result;
};

ContactListsService.prototype.openContactModal = function (contact = null) {
    return this._$uibModal.open({
        template    : `<custom-contact-modal contact="contact" close="close(data)"></custom-contact-modal>`,
        controller  : ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
            scope.contact   = contact;

            scope.close = function (result) {
                $uibModalInstance.close(result);
            }
        }]
    }).result;
};

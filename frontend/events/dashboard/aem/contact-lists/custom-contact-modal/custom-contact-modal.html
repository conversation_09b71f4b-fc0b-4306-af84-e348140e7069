<div class="modal-header text-center">
    <button type="button" class="close" ng-click="$ctrl.close()"><span aria-hidden="true">&times;</span></button>
    <h4 ng-bind="$ctrl.modalTitle"></h4>
</div>
<div class="modal-body">
    <form name="$ctrl.contactForm">
        <div class="form-group" ng-class="{'has-error': $ctrl.hasError('email')}">
            <label class="control-label" for="email">Email</label>
            <input type="email"
                   name="email"
                   class="form-control"
                   email-validator
                   id="email"
                   ng-model="$ctrl.contact.email">
        </div>

        <div class="form-group">
            <label class="control-label" for="first">First</label>
            <input type="text"
                   name="first"
                   class="form-control"
                   id="first"
                   ng-model="$ctrl.contact.first"
                   required>
        </div>

        <div class="form-group">
            <label class="control-label" for="first">Last</label>
            <input type="text"
                   name="last"
                   class="form-control"
                   id="last"
                   ng-model="$ctrl.contact.last">
        </div>
    </form>
</div>
<div class="modal-footer">

    <button type="button"
            class="btn btn-danger pull-left"
            sw-confirm="Do you want to remove contact?"
            sw-confirm-do="$ctrl.delete"
            sw-confirm-args="$ctrl.contact"
            sw-confirm-hide-no
            ng-if="$ctrl.isUpdateMode"
    >Delete</button>

    <button type="submit"
            class="btn btn-info pull-right"
            ng-click="$ctrl.save()"
    >Save</button>

    <button type="button"
            class="btn btn-default pull-right"
            ng-click="$ctrl.close()"
    >Close</button>
</div>

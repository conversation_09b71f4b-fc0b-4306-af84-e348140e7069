angular.module('SportWrench').factory('AEMEventFactory', ['$http', '$q', AEMEventFactory]);

function AEMEventFactory ($http, $q) {
	return AEMEventService.bind(null, $http, $q);
}

function AEMEventService ($http, $q, eventID) {
	this._$http  			= $http;
	this._$q 				= $q;
	this._eventID 			= eventID;
	this.__baseURL__  		= '/api/event/' + this._eventID + '/aem/';
}

/* === AEM-specific routes === */
AEMEventService.prototype.getInitData = function () {
	return this._$http.get(this.__baseURL__ + 'init');
};

AEMEventService.prototype.getTemplate = function (templateID) {
	return this._$http.get(this.__baseURL__ + 'tmpl/' + templateID);
};

AEMEventService.prototype.getPreviewLink = function (templateID) {
	return this.__baseURL__ + 'preview/' + templateID
};

AEMEventService.prototype.duplicateTemplate = function (templateID, eventIds) {
	return this._$http.post(this.__baseURL__ + 'tmpl/' + templateID + '/duplicate', { eventIds });
};

AEMEventService.prototype.assignTemplateToTrigger = function (templateID) {
	return this._$http.post(this.__baseURL__ + 'tmpl/' + templateID + '/assign');
};

AEMEventService.prototype.saveTemplate = function (templateID, data) {
	return this._$http.put(this.__baseURL__ + 'tmpl/' + templateID, data);
};

AEMEventService.prototype.createTemplate = function (data) {
	return this._$http.post(this.__baseURL__ + 'tmpl', data);
};

AEMEventService.prototype.removeTemplate = function (templateID) {
	return this._$http.delete(this.__baseURL__ + 'tmpl/' + templateID + '/remove');
};

AEMEventService.prototype.testSend = function (templateID, html, subject, receiver) {
	return this._$http.post(this.__baseURL__ + 'tmpl/' + templateID + '/send', {
		html 		: html,
		subject 	: subject,
		receiver    : receiver
	}).then(result => result.data);
};

AEMEventService.prototype.getRole = function () {
	return 'eo';
}

/* === Quick messages === */
AEMEventService.prototype.getAvailableTmpls = function (group) {
	return this._$http.get(this.__baseURL__ + 'sending/' + group + '/available-tmpls')
	.then(function (resp) {
		return resp.data.templates;
	})
}


AEMEventService.prototype.sendLetter = function (group, data) {
	return this._$http.post(this.__baseURL__ + group + '/send-email', data)
	.then(function (res) {
		return res.data
	});
}

angular.module('SportWrench').controller('Event.InfoController', InfoController);

function InfoController ($scope, eventsService, $log, $stateParams, eventDashboardService, geoService, SANCTIONING_BODY) {
	var prefix = '/api/v2/event/';

	$scope.data = {
		intraReport 	    : prefix + $stateParams.event + '/teams/srva-report',
		gevaReport 		    : prefix + $stateParams.event + '/teams/geva-report',
		finalReport 	    : prefix + $stateParams.event + '/teams/usav-final-report',
        finishersReport     : prefix + $stateParams.event + '/teams/finishes-report',
        headToHeadReport    : prefix + $stateParams.event + '/teams/head-to-head-report',
        regionFinishesReport: prefix + $stateParams.event + '/teams/region-finishes-report',
        usavNationalReport  : prefix + $stateParams.event + '/teams/usav-national-report',
		regions 		    : []
	};

    eventsService.getEventInfo($stateParams.event, function (resp) {
        $scope.$parent.event_statistics = resp.data.info;
    });

    $scope.total = function () {
        return (
        	parseFloat(this.$parent.event_statistics.paid_credit, 10)  + 
            parseFloat(this.$parent.event_statistics.pending_amount, 10)  + 
            parseFloat(this.$parent.event_statistics.paid_check, 10) 
        );
    };

    if(eventDashboardService.getEvent().allow_teams_registration) {
    	geoService.getRegions(function (resp) {
    		$scope.data.regions = resp.data;
    	});
    }

    $scope.getTeamsRegistrationStatus = function() {
        const { teams_fee, published } = $scope.event;

        if (teams_fee !== null) {
            return published ? 'Teams Registration Active' : 'Teams Registration Disabled';
        } else {
            return 'Teams Registration Not Approved';
        }
    }

    $scope.isRegionFinishesExportAvailable = function () {
        return ['GE', 'SC'].includes($scope.event.region);
    }

    $scope.isUSAVEvent = function () {
        return SANCTIONING_BODY.USAV === $scope.event.sport_sanctioning_id;
    }
}

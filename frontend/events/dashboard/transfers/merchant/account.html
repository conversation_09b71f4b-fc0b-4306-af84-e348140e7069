<p class="lead">
    <span>{{$ctrl.headerTitle}}</span>
    <span ng-if="$ctrl.for">for {{$ctrl.for}}</span>
    <small class="text-grey">({{::$ctrl.getModeLabel()}} Mode)</small>:
</p>
<div class="row">
    <div class="col-xs-4"><b>Account ID</b></div>
    <div class="col-sm-4">{{$ctrl.account.id}}</div>
</div>
<div class="row">
    <div class="col-xs-4"><b>Business</b></div>
    <div class="col-sm-4">{{$ctrl.account.business_name}}</div>
</div>
<div class="row">
    <div class="col-xs-4"><b>Statement Descriptor</b></div>
    <div class="col-sm-4">{{$ctrl.account.statement_descriptor}}</div>
</div>
<div class="row">
    <div class="col-xs-4"><b>Email</b></div>
    <div class="col-sm-4">{{$ctrl.getEmail(account.support_email)}}</div>
</div>
<div class="row">
    <div class="col-xs-4"><b>Phone</b></div>
    <div class="col-sm-4">{{$ctrl.getPhone(account.support_phone)}}</div>
</div>
<div class="row" ng-if="!$ctrl.isTilledPaymentProvider">
    <div class="col-sm-6">
        <merchant-balance-stats balance="$ctrl.balance"></merchant-balance-stats>
    </div>
</div>
<div class="row" ng-if="$ctrl.current">
    <div class="col-sm-6">
        <merchant-balance-stats for="Current Event" balance="$ctrl.current"></merchant-balance-stats>
    </div>
</div>

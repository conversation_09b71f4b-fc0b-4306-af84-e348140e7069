
class Controller {
    constructor ($scope, $stateParams, clubInvoiceService, toastr) {
        this.$scope = $scope;
        this.eventId = $stateParams.event;
        this.clubInvoiceService = clubInvoiceService;
        this.toastr = toastr;
    }

    $onInit () {
        this.$scope.modalTitle = `<h4>Create Invoice</h4>`
        this.form = {};

        this.clubInvoiceService.getClubsList(this.eventId)
            .then((clubs) => {
                this.clubs = clubs;
            });
    }

    async save () {
        if (this.clubInvoiceForm.$invalid) {
            this.clubInvoiceForm.$setSubmitted();
            return;
        }

        try {
            await this.clubInvoiceService.createClubInvoice(this.eventId, this.form);

            this.toastr.success('Club Invoice Created');
            this.close();
        } catch (err) {
            console.error(err);
        }
    }

    close () {
        this.form = {};
        this.onClose();
    }

    fieldHasError (fieldName) {
        return this.clubInvoiceForm.$submitted &&
            this.clubInvoiceForm[fieldName] &&
            this.clubInvoiceForm[fieldName].$invalid
    }
}

Controller.$inject = ['$scope', '$stateParams', 'clubInvoiceService', 'toastr'];

angular.module('SportWrench').component('clubInvoiceEditor', {
    templateUrl: 'events/dashboard/club-invoices/club-invoice-editor/club-invoice-editor.template.html',
    bindings: {
        onClose: '&',
    },
    controller: Controller
});

angular.module('SportWrench').controller('EventPaymentsController', EventPaymentsController);
function EventPaymentsController(
    $scope, purchaseService, $stateParams, eventPaymentsService, PaymentsFiltersFactory, $state, $timeout, $uibModal,
    APP_ROUTES, ngTableParams, PaymentsDynamicColsFactory, $filter, userService, ClubPaymentsService, TEAM_STATUS,
    PAYMENT_TYPE, PAYMENT_STATUS) {
    $scope.payments = [];

    $scope.defaultSort = false;
    $scope.search = '';

    $scope.filters = {
        limit   : 100,
        page    : 1
    };

    $scope.allPymentsCount = 0;

    let getData = function ($defer, params) {

        let orderBy     = params.orderBy();
        let urlQuery    = {};
        let filter      = params.filter();

        if (filter) {
            urlQuery = _.clone(filter);
        }

        if(orderBy && orderBy.length && !$scope.defaultSort) {
            urlQuery.order      = orderBy[0].substr(1);
            urlQuery.direction  = (orderBy[0].charAt(0) === '-')? 'asc':'desc';
        }

        eventPaymentsService.eventPayments($stateParams.event, urlQuery)
            .then(function (resp) {
                if (resp.data.payments[0] && resp.data.payments[0].count) {
                    $scope.payments = resp.data.payments;
                    $scope.allPymentsCount = resp.data.payments[0].count;
                    $scope.defaultSort = false;
                    $defer.resolve(resp.data.payments);

                    $timeout(function () {
                        isLoadingFinished = true;
                    }, 100);

                    if(!_.isEmpty($stateParams.openPaymentModal)) {
                        let payment = $scope.payments.filter(p =>
                            p.purchase_id === $stateParams.openPaymentModal.purchase_id
                        )[0];

                        if(payment) {
                            $scope.showTeamsModal(payment, $stateParams.openPaymentModal.purchase_id);
                        }
                    }

                } else {
                    $scope.allPymentsCount = 0;
                    $scope.payments = [];
                    $defer.resolve([]);
                }

            }, function () {
                params.total(0);
                $scope.payments = [];
                $defer.resolve([]);
            });
    };

    $scope.status_statuses          = PaymentsFiltersFactory.getStatuses();
    $scope.type_statuses            = PaymentsFiltersFactory.getTypes();
    $scope.availability_statuses    = PaymentsFiltersFactory.getAvailability();
    $scope.dynamic_columns          = PaymentsDynamicColsFactory.getColumns();

    $scope.tableParams = new ngTableParams({
        page        : 1,
        filter      : $scope.filters,
        count       : 1,
        sorting     : { 'created': 'asc' }
    }, {
        filterDelay : 0,
        getData     : getData
    });

    var paymentsTable = $scope.tableParams;

    var clearHandlers = [];

    $scope.addClearFilterHandler = function (handler) {
        clearHandlers.push(handler);
    };

    $scope.clearFilters = function () {
        clearHandlers.forEach(function (handler) {
            if(_.isFunction(handler)) {
                handler();
            }
        });

        $scope.filters.search           = undefined;
        $scope.filters.statuses         = undefined;
        $scope.filters.availability     = undefined;
        $scope.filters.type             = undefined;
        $scope.filters.page             = 1;
    };

    $scope.columnClass = function (c) {
        let stylesClasses = {};

        if (typeof c !== 'string') {
            stylesClasses = {
                'text-center sortable'  : !!c.sortable,
                'sort-asc'              : paymentsTable.isSortBy(c.sortable, 'asc'),
                'sort-desc'             : paymentsTable.isSortBy(c.sortable, 'desc')
            };
            if(c.selectors) {
                stylesClasses[c.selectors] = true;
            }
        } else {
            angular.forEach($scope.dynamic_columns, function (col) {
                if (col.name === c && col.selectors) {
                    if(col.selectors instanceof Array) {
                        for(let clss of col.selectors) {
                            stylesClasses[clss] = true;
                        }
                    } else {
                        stylesClasses[col.selectors] = true;
                    }
                }
            })
        }

        return stylesClasses;
    };

    $scope.filterStatus = function (status, selection) {
        $scope.filters[status] = selection;
        $scope.filters.page    = 1;
    };

    $scope.filterSearch     = function () {
        if(paymentsTable.settings().$loading) return;

        $scope.filters.search   = $scope.search;
        $scope.filters.page     = 1;
    };

    $scope.filterAvailability   = $scope.filterStatus.bind(null, 'availability');
    $scope.filterStatuses       = $scope.filterStatus.bind(null, 'statuses');
    $scope.filterType           = $scope.filterStatus.bind(null, 'type');

    $scope.sort = function (column) {
        if (column) {
            $scope.filters.page = 1;
            paymentsTable.sorting(column, paymentsTable.isSortBy(column, 'asc') ? 'desc'
                : paymentsTable.isSortBy(column, 'desc') ? setSortDefault() : 'asc');
        }
    };

    let isLoadingFinished = true;

    $scope.loadMore = function () {
        let isLoading   = paymentsTable.settings().$loading,
            maxItems    = $scope.filters.limit * $scope.filters.page;

        if (!isLoadingFinished || isLoading || $scope.payments.length < maxItems) {
            return;
        }
        isLoadingFinished = false;

        ++$scope.filters.page;
    };

    function setSortDefault() {
        $scope.defaultSort = true;
        return 'text-center sortable';
    }

    let statusOrder = function (item) {
        if(item.canceled_date) return -1;
        else if(item.status === TEAM_STATUS.PAYMENT.PENDING)
            return 0;
        else if(item.status === TEAM_STATUS.PAYMENT.PAID)
            return 1;
        return 2;
    }

    $scope.orderData = function(colName) {
        if(colName === 'status') {
            $scope.order_col = statusOrder;
        } else
            $scope.order_col = colName;
        $scope.reverse = !$scope.reverse;
    };


    $scope.openDivision = function (id) {
        $state.go(APP_ROUTES.EO.TEAMS, {
            mode: 'division',
            value: id
        })
    }

    $scope.openReceipt = function (id) {
        return purchaseService.openReceipt(id);
    }

    $scope.showTeamsModal = function(p, openPayment) {
        $scope.payment = p;
        $scope.openPayment = openPayment;

        return $uibModal.open({
            templateUrl     : APP_ROUTES.EO.FOLDER + 'payments/teams-payment-modal.html',
            scope           : $scope,
            controller      : 'EventInfoModalController'
        })
    }

    $scope.getFee = function(payment_type, fee) {
        return payment_type === 'card' ? $filter('currency')(fee) : '-';
    }

    $scope.getStatusFieldValue = function(p) {
        if(p.has_not_won_dispute) {
            return `Dispute ${p.dispute_status}`;
        } else {
            return $scope.isCanceledPurchase(p) ? 'Canceled' : p.status;
        }
    }

    $scope.userHasGodRole = function() {
        return userService.hasGodRole();
    }

    $scope.openBalanceInformationModal = function(p) {
        ClubPaymentsService.openBalanceInfoModal(p);
    }

    $scope.isCanceledPurchase = function(p) {
        return p.canceled_date || p.status === PAYMENT_STATUS.CANCELED;
    }

    $scope.getStatusFieldTooltipText = function(p) {
        return isPaidCheck(p) ? `Check #: ${p.check_num}` : '';
    }

    $scope.getStatusFieldStyle = function(p) {
        return isPaidCheck(p) ? {'text-decoration': 'underline'} : {};
    }

    function isPaidCheck(payment) {
        let rules = [
            payment.type === PAYMENT_TYPE.CHECK,
            payment.status === TEAM_STATUS.PAYMENT.PAID,
            !payment.canceled_date
        ]

        return rules.every(rule => rule);
    }
}

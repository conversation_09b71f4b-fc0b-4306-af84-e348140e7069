angular.module('SportWrench').component('qualifierEventOptions', {
    templateUrl 	: 'events/dashboard/team-info/qualifier-event-options/qualifier-event-options.html',
    bindings 		: {
        team 	            : '=',
        getStandingPageLink : '&',
        show                : '<'
    },
    controller 		: ['QUALIFIED_DIVISIONS', QualifierEventOptionsController]
});

function QualifierEventOptionsController(QUALIFIED_DIVISIONS) {
    this.$onInit = function() {
        this.divisions = QUALIFIED_DIVISIONS;
    }
}

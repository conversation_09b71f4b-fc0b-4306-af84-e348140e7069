angular.module('SportWrench')

.controller('Events.TeamInfo.TabHousingController',[
'$scope', 'rosterTeamService', '$stateParams', function ($scope, rosterTeamService, $stateParams) {
    var teamId = $scope.$parent.modalParams.team_id;
    $scope.booking          = [];
    $scope.loading          = { data_loaded: false };

    rosterTeamService.event_owner.get_booking(teamId, $stateParams.event, function (data) {
        $scope.booking = data;
    });

    $scope.isUsingThs = function () {
        return Number($scope.$parent.event.housing_company_id) === 1;
    };

    $scope.$parent.$watch('tabs.team', function(v) {
        if (!v) return;
        $scope.team = v;

        $scope.housingTeamInfo = {
            roster_team_id: v.roster_team_id,
            roster_club_id: v.roster_club_id
        };

        $scope.loading.data_loaded = true;
    });

    $scope.clubReservationsEmpty = function () {
        let {
            club_max_total_accepted,
            club_total_tentative,
            club_total_accepted,
            club_total_confirmed
        } = $scope.team;

        return _.isNull(club_max_total_accepted) &&
            _.isNull(club_total_tentative) &&
            _.isNull(club_total_accepted) &&
            _.isNull(club_total_confirmed);
    }

    $scope.isBookingEmpty = function () {
        return !$scope.booking || !$scope.booking.length;
    }

}]);

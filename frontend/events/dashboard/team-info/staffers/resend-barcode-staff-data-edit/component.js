angular.module('SportWrench').component('resendBarcodeStaffDataEdit', {
    templateUrl: 'events/dashboard/team-info/staffers/resend-barcode-staff-data-edit/template.html',
    bindings: {
        email: '<',
        phone: '<',
        staffId: '<',
        onSend: '&',
        onClose: '&'
    },
    controller: ['EMAIL_PATTERN', '_', 'toastr', Component],
});

Component.$inject = [];

function Component(EMAIL_PATTERN, _, toastr) {
    this.editStaffDataForm = 'editStaffDataForm';
    this.isSubmitted = false;

    this.EMAIL_REGEX = EMAIL_PATTERN;

    /**
     * Clears fields with undefined, null, '' values
     *
     * @param {object} ob
     * @return {object}
     */
    this.filterRequestData = (ob) => {
        return _.pick(ob, (ob) => ob);
    };

    this.fieldHasError = (fieldName) => {
        return this.isSubmitted &&  this.editStaffDataForm[fieldName].$invalid
    };

    this.send = () => {
        this.isSubmitted = true;

        if (this.editStaffDataForm.$invalid || this.sendingInProcess) {
            return;
        }

        const data = this.filterRequestData({
            phone: this.phone,
            email: this.email,
        });

        if (_.isEmpty(data)) {
            toastr.warning('One of the fields must be filled');
            return;
        }

        this.sendingInProcess = true;

        this.onSend({ id: this.staffId, data })
            .then(this.onClose)
    }
}

<div class="btn-group" uib-dropdown >
    <button class="btn btn-info btn-info" uib-dropdown-toggle>
        Add <span class="caret"></span>
    </button>
    <ul class="dropdown-menu" role="menu" ng-if="type === 'Player'">
        <li><a href="" ng-click="action()">As Player</a></li>
        <li><a href="" ng-click="action({ asStaff: true })">As Staff</a></li>
    </ul>
    <ul class="dropdown-menu" role="menu" ng-if="type !== 'Player'">
        <li><a href="" ng-click="action({ role: 4 })">Head Coach</a></li>
        <li><a href="" ng-click="action({ role: 5 })">Asst Coach</a></li>
        <li><a href="" ng-click="action({ role: 6 })">Team Representative</a></li>
        <li><a href="" ng-click="action({ role: 7 })">Manager</a></li>
        <li><a href="" ng-click="action({ role: 11 })">Recruiting Coordinator</a></li>
        <li><a href="" ng-click="action({ role: 15 })">Chaperone</a></li>
    </ul>
</div>

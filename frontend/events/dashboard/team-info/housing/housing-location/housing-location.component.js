angular.module('SportWrench').component('housingLocation', {
    templateUrl 	: 'events/dashboard/team-info/housing/housing-location/housing-location.html',
    bindings 		: {
        team 	    : '='
    },
    controller 		: [
        '$stateParams', 'geoService', 'rosterTeamService', '$q' ,'EventSettingsService', '$scope', HousingLocation
    ]
})

function HousingLocation ($stateParams, geoService, rosterTeamService, $q, EventSettingsService, $scope) {
    let self            = this;
    let countriesLoaded = false;

    this.$onInit = function () {
        this.clubIsLocalChanged = false;
        this.teamIsLocalChanged = false;

        this.countries      = {};
        this.locationData   = { comment: null };

        this.locationData.club_is_local = Boolean(this.team.club_is_local);
        this.originalClubIsLocal        = Boolean(this.team.club_is_local);

        this.locationData.team_is_local = this.team.team_is_local;
        this.originalTeamIsLocal        = this.team.team_is_local;

        this.clubInfo = {
            address : this.team.address,
            city    : this.team.city,
            state   : this.team.state,
            country : this.team.country,
            zip     : this.team.zip,
        };
    };

    this.toggleMap = function () {
        if (!countriesLoaded) {
            geoService.getCountries()
                .then(countryArray => {
                    self.countries = countryArray.reduce((acc, cur) => Object.assign(acc, {
                        [cur.code]: cur.name
                    }), {});
                    countriesLoaded = true;
                    self.showMap = true;
                })
        } else {
            self.showMap = !self.showMap;
        }
    };

    this.save = function() {
        let teamID = this.team.roster_team_id;

        let dataToSave = {
            club_is_local: this.clubIsLocalChanged ? this.locationData.club_is_local : undefined,
            team_is_local: this.teamIsLocalChanged ? this.locationData.team_is_local : undefined,
            comment      : this.locationData.comment
        };

        rosterTeamService.event_owner.housing(teamID, $stateParams.event, dataToSave, (result) => {
            this.originalClubIsLocal    = this.locationData.club_is_local;
            this.clubIsLocalChanged     = false;

            this.originalTeamIsLocal    = this.locationData.team_is_local;
            this.teamIsLocalChanged     = false;

            $scope.$emit('team.info.data-changed', result && result.updated_roster_teams);
        });
    };

    this.clubIsLocalOnChange = function() {
        let ch = (this.locationData.club_is_local !== this.originalClubIsLocal);
        this.clubIsLocalChanged = ch;
        if (!ch) this.locationData.comment = null;
    };

    this.teamIsLocalOnChange = function (value) {
        this.locationData.team_is_local = value;

        let ch = (this.locationData.team_is_local !== this.originalTeamIsLocal);
        this.teamIsLocalChanged = ch;
        if (!ch) this.locationData.comment = null;
    };

    this.getClubLocation = function () {
        return $q.when(this.clubInfo)
    };

    this.getEventLocation = function () {
        return EventSettingsService.loadLocations($stateParams.event)
            .then(function (resp) {
                return _.find(resp.data.locations, { number: 1 });
            });
    };
}

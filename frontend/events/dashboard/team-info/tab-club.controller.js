angular.module('SportWrench').controller('Events.TeamInfo.TabClubController', TabClubController);

function TabClubController($scope, $http, toastr, ConfirmationService, RosterTeamMembersService, $window) {
    const STAFF = 'staff';

    getStaffers();

    $scope.loading = {
        data_loaded: false,
        error: false,
        errMsg: ''
    };

    $scope.data = {
        teams: [],
        club_staff: [],
        sort: 'organization_code'
    };

    $scope.removeStaffer = function(id, additionalAskText, team) {
        const { roster_team_id, team_name } = team;

        ConfirmationService.ask(
            `${additionalAskText ? additionalAskText : ''}
            Do you really want to remove this Member from "${team_name}"?`
        ).then(function (resp) {
            return (resp === ConfirmationService.YES_RESP)
                ? _removeStaffer(id, roster_team_id)
                : false
        });
    };

    $scope.openStaffQRCodePage = function (s) {
        $window.open(s.checkin_description_link, '_blank');
    }

    $scope.updateStaffer = function (stafferId, staffer, team) {
        const { roster_team_id }    = team;
        const eventId               = $scope.$parent.modalParams.event_id;

        return RosterTeamMembersService.updateStaffer(
            eventId, roster_team_id, stafferId, staffer
        );
    };

    function getStaffers() {
        $http.get(
            '/api/v2/event/' + $scope.$parent.modalParams.event_id +
            '/club/' + $scope.$parent.tabs.team.roster_club_id + '/teams/staff')
        .then(function (response) {
            var data = response.data;
            $scope.data.teams = data.teams;
            $scope.data.club_staff = data.club_staff;
            $scope.loading.data_loaded = true;
        })
        .catch(function (err) {
            $scope.loading.data_loaded = true;
            $scope.loading.error = true;

            err.data && err.data.validation
                ? $scope.loading.errMsg = err.data.validation
                : $scope.loading.errMsg = 'Internal Server Error.'

        });
    }

    function onStafferRemoved() {
        toastr.success('Successfully removed!');
        getStaffers();
    }

    function _removeStaffer(id, roster_team_id) {
        RosterTeamMembersService.removeMember(
            $scope.$parent.modalParams.event_id,
            roster_team_id, id, STAFF
        ).then(onStafferRemoved)
    }
}

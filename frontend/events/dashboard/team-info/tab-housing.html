<spinner active="!loading.data_loaded"></spinner>
<div ng-if="loading.data_loaded">

    <housing-location team="team"></housing-location>

    <hr/>

    <housing-notes ng-if="loading.data_loaded" team="housingTeamInfo"></housing-notes>

    <hr ng-if="isUsingThs()" />

    <travel-coordinator-info data="team" ng-if="isUsingThs()"></travel-coordinator-info>

    <hr ng-show="!clubReservationsEmpty() && !isBookingEmpty()"/>

    <housing-reservations data="team"
                          is-booking-empty="isBookingEmpty()"
                          hide-reservations="clubReservationsEmpty()"
    ></housing-reservations>

    <hr ng-show="isBookingEmpty() && isUsingThs()"/>

    <housing-booking team-id="team.roster_team_id" booking="booking" ng-if="isUsingThs()"></housing-booking>
</div>



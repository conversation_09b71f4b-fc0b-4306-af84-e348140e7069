angular.module('SportWrench').controller('EventOfficialsController', EventOfficialsController);

function EventOfficialsController ($scope, $stateParams, $http, $state, APP_ROUTES, officialService) {
    var event_id  = $stateParams.event;

    $scope.officials = []; 
    $scope.event_days = [];
    $scope.event_use_clinic = false;
    $scope.event_official_additional_role_enabled = false;

    loadOfficials();

    $scope.show_info = function (id, filters) {
        $state.go(APP_ROUTES.EO.OFFICIALS_INFO, {
            official: id, filters: filters
        }, { notify: false });
    };

    $scope.getEventEmail = function () {
        return $scope.$parent.event.email;
    };

    $scope.reload = function () {
        loadOfficials();
    };

    function loadOfficials() {
        officialService.getOfficials(event_id)
        .then(function (resp) {
            var data = resp.data;

            $scope.officials                                = data.officials;
            $scope.event_days                               = data.event && data.event.days;
            $scope.event_use_clinic                         = data.event && data.event.use_clinic;
            $scope.link                                     = data.link;
            $scope.event_official_additional_role_enabled   = data.event.official_additional_role_enable;
            $scope.event_sport_sanctioning_id               = data.event.sport_sanctioning_id;
        });
    }

    $scope.export_to_xslx = function (officials, cb) {
        officialService.excelExport(event_id, officials);
        cb();       
    };

    $scope.groupUpd = function (officials, data) {
        return officialService.updOfficials(event_id, officials, data);
    };
}

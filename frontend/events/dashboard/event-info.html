<div class="row data-loading" ng-class="{'done': opts.loading_finished}">
    <div class="col-md-6">
        <h4>{{event.long_name}}</h4>
        <p>
            <span ng-class="{'label': true, 'label-success': event.live_to_public, 'label-danger': !event.live_to_public}">
                {{event.live_to_public?'Event Published':'Event Not Published'}}
            </span>
            &nbsp;
            <span ng-if="event.live_to_public" ng-class="{'label': true, 'label-success': event.published && event.teams_fee, 'label-default badge-dark': !event.published || event.teams_fee === null}">
                {{getTeamsRegistrationStatus()}}
            </span>
            &nbsp;
            <span ng-if="event.live_to_public && event.allow_teams_registration" ng-class="{'label': true, 'label-success': event.score_entry_allowed, 'label-danger': !event.score_entry_allowed}">
                {{event.score_entry_allowed?'Score Entry Allowed':'Score Entry Not Allowed'}} 
            </span>
            &nbsp;
            <span ng-if="event.live_to_public && event.allow_teams_registration" ng-class="{'label': true, 'label-success': event.schedule_published, 'label-danger': !event.schedule_published}">
                {{event.schedule_published?'Schedule Published':'Schedule Not Published'}} 
            </span>
            &nbsp;
            <span ng-if="event.allow_ticket_sales && event.live_to_public" ng-class="{'label': true, 'label-success': (event.tickets_published || tickets_visible), 'label-danger': !(event.tickets_published || tickets_visible)}">
                {{(event.tickets_published || tickets_visible)?'Tickets Published':'Tickets Not Published'}} 
            </span>
        </p>        
        <dl class="dl-event-horizontal">
            <dt>Event Date Start</dt><dd>{{event.date_start}}</dd>
            <dt>Event Date End</dt><dd>{{event.date_end}}</dd>
            <dt ng-if="event.allow_teams_registration">Allowed Team Genders</dt>
            <dd ng-if="event.allow_teams_registration">{{getGenders(event)}}</dd>

            <dt>Website</dt>
            <dd><short-link href="event.website"></short-link></dd>
            <div ng-if="event.allow_teams_registration">
                <dt>Registration Fee</dt>
                <dd><span ng-repeat="fee in event.all_fees">{{fee | currency:'$'}}{{$last ? '' : ', '}}</span></dd>

                <dt>Registration Open</dt>
                <dd>{{event.date_reg_open}}</dd>

                <dt>Registration Close</dt>
                <dd>{{event.date_reg_close}}</dd>

                <dt>SW Teams fee</dt>
                <dd ng-if="event.teams_fee">{{event.teams_fee | currency:'$'}}</dd>
                <dd ng-if="event.teams_fee === null">Pending admin approve</dd>
            </div>
            <dt ng-if="event.tickets_fee">SW Tickets fee</dt>
            <dd ng-if="event.tickets_fee">{{event.tickets_fee | currency:'$'}}</dd>
            <hr/>
            <div ng-if="event.allow_teams_registration">
                <dt>Divisions:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.divisions_count}}</span></dd>

                <dt>Clubs:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.clubs_count}}</span></dd>

                <dt>Teams:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.teams_count}}</span></dd>

                <dt>Athletes:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.athletes_count}}</span></dd>

                <br/>
                <dt>Paid Credit:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.paid_credit | currency:'$'}}</span></dd>

                <dt>Pending:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.pending_amount | currency:'$'}}</span></dd>

                <dt>Paid Check:</dt>
                <dd><span class="badge badge-dark">{{event_statistics.paid_check | currency:'$'}}</span></dd>

                <dt>Total:</dt>
                <dd><span class="badge badge-dark">{{total() | currency:'$'}}</span></dd>
            </div>
        </dl>        
    </div>
    <div class="col-md-6" ng-if="event.allow_teams_registration">
        <h4>Export Results</h4>
        <div>
            <div class="row">
                <div class="col-xs-4">
                    <a 
                        href="{{::data.intraReport}}?region={{data.intraRegion}}" 
                        class="btn btn-primary"
                    >Intra region Report</a>
                </div>
                <div class="col-xs-6">
                    <select 
                        class="form-control"
                        ng-model="data.intraRegion"
                        ng-options="r.region as r.name for r in data.regions">
                        <option value="">All Regions</option>
                    </select>
                </div>
            </div>
            <p class="help-block">This report only exports matches between opponents within the same specified region</p>
        </div>
        <div class="mt1">
            <div class="row">
                <div class="col-xs-4">
                    <a 
                        href="{{::data.gevaReport}}?region={{data.gevaRegion}}"
                        class="btn btn-primary"
                    >Full results Report</a>
                </div>
                <div class="col-xs-6">
                    <select 
                        class="form-control"
                        ng-model="data.gevaRegion"
                        ng-options="r.region as r.name for r in data.regions">
                        <option value="">All Regions</option>
                    </select>
                </div>
            </div>
            <p class="help-block">This report exports complete match results with team name & codes as well as scores</p>
        </div>
        <div class="mt1">
            <a 
                href="{{::data.finalReport}}"
                class="btn btn-primary">Final finish Report</a>
            <p class="help-block">This report exports a list of teams in order of final rank finish with team codes</p>
        </div>
        <div class="mt1">
            <a
                href="{{::data.finishersReport}}"
                class="btn btn-primary">Division Final Finish Report</a>
            <p class="help-block">For USA Volleyball Seeding</p>
        </div>
        <div class="mt1">
            <a
                href="{{::data.headToHeadReport}}"
                class="btn btn-primary">Head to Head Report</a>
            <p class="help-block">For USA Volleyball Seeding</p>
        </div>
        <div class="mt1" ng-if="isRegionFinishesExportAvailable()">
            <a
                href="{{::data.regionFinishesReport}}"
                class="btn btn-primary">Region Finishes Export</a>
            <p class="help-block">Uses the event sanction region to produce a region specific result file for region rankings upload</p>
        </div>
        <div class="mt1" ng-if="isUSAVEvent()">
            <a
                href="{{::data.usavNationalReport}}"
                class="btn btn-primary">USAV National Ranking System Export</a>
            <p class="help-block">Head to Head result report formatted for the USAV National Ranking System</p>
        </div>
    </div>
</div>
<spinner active="opts.loading_started && !opts.loading_finished"></spinner>


class Controller {
    $onInit () {
        this.variations = ['default', 'phone', 'email', 'url'];
    }

    onUpdate (variation) {
        this.variation = variation;

        this.onSave({ variation });
    }
}


angular.module('SportWrench').component('customFormTextVariationDropdown', {
    templateUrl: 'events/dashboard/custom-forms-builder/custom-form-editor/custom-form-fields-editor/custom-form-field-editor/custom-form-text-variations-dropdown/custom-form-text-variations-dropdown.template.html',
    bindings: {
        variation: '=',
        areaId: '<',
        onSave: '&'
    },
    controller: Controller
});

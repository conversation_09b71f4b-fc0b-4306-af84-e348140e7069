angular.module('SportWrench').component('payoutsDetails', {
    templateUrl: 'events/dashboard/accounting/common/payout-details/payout-details.html',
    bindings: {
        type: '@'
    },
    controller: ['PayoutDetailsService', '$stateParams', 'UtilsService', payoutsDetails]
});

function payoutsDetails(PayoutDetailsService, $stateParams, UtilsService) {
    let self = this;

    this.payoutsData    = {};
    this.loading        = true;

     this.titleAddon = UtilsService.capitalizeFirstLetter(this.type);

    this.$onInit = function () {
        __loadData();
    };

    function __loadData() {
        return PayoutDetailsService.getPayouts($stateParams.event, self.type)
            .then(data => {
                self.payoutsData    = data;
                self.loading        = false;
            })
    };
}

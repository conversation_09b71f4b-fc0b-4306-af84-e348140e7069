<div class="row" ng-if="!$ctrl.loading">
    <div class="col-lg-12">
        <div class="panel panel-default" ng-if="$ctrl.refundsData && $ctrl.refundsData.refunds.length">
            <div class="panel-heading">
                <h3 class="panel-title">Refund Details</h3>
            </div>
            <div class="panel-body">
                <table class="table table-condensed refund-details">
                    <thead>
                    <tr>
                        <th class="title">Customer</th>
                        <th>Refund Amt</th>
                        <th>SW Fee Adjustment</th>
                        <th>Stripe Adjustment</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="refund in $ctrl.refundsData.refunds">
                        <td class="title">
                            <a href="" ng-click="$ctrl.goToRefund(refund)" ng-bind="refund.name + ' ('+ refund.items_count + ')'"></a>
                        </td>
                        <td ng-bind="refund.amount | currency"></td>
                        <td ng-bind="refund.collected_sw_fee | currency"></td>
                        <td ng-bind="refund.stripeAdjustment | currency"></td>
                    </tr>
                    <tr>
                        <td class="title-bold">Sub Totals</td>
                        <td><b ng-bind="$ctrl.refundsData.amountTotal | currency"></b></td>
                        <td><b ng-bind="$ctrl.refundsData.swFeeTotal | currency"></b></td>
                        <td><b ng-bind="$ctrl.refundsData.subTotal | currency"></b></td>
                    </tr>
                    <tr class="bg-warning">
                        <td class="title-bold">TOTAL COST OF REFUNDS</td>
                        <td></td>
                        <td></td>
                        <td><b ng-bind="$ctrl.refundsData.totalCost | currency"></b></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <uib-alert type="warning" ng-if="!$ctrl.refundsData || !$ctrl.refundsData.refunds.length" align="center">
            There is no {{$ctrl.type}} refunds for event
        </uib-alert>
    </div>
</div>

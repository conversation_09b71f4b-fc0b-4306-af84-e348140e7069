angular.module('SportWrench').component('accountingStatisticsSection', {
    templateUrl: 'events/dashboard/accounting/common/totals-and-net-profit/statistics-section/statistics-section.html',
    bindings: {
        // TODO: describe properties
        section: '<'
    },
    require: {
        'totalsCtrl': '^totalsAndNetProfit'
    },
    controller: [
        '_',
        AccountingStatisticsSectionController
    ]
});

const CAPTION = 'caption';

function AccountingStatisticsSectionController (_) {
    this.rows       = [];

    this.$onChanges = function (changes) {
        let section = changes && changes.section && changes.section.currentValue;

        if (section) {
            this.groups =  _.groupBy(section.rows,  CAPTION);
        }
    }

    this.sectionCls = function () {
        return { 
            'table table-condensed statistics-section'  : true, 
            'drop-caption-border'                       : !this.section.label,
            [`bg-${this.section.bg}`]                   : Boolean(this.section.bg)
        }
    }

    this.getSectionLabel = function (section) {
        if (section.show_items_qty) {
            return section.label ? `${section.label} (${section.items_qty})` : '';
        } else {
            return section.label;
        }
    }
}

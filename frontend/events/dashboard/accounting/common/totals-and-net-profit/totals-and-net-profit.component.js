angular.module('SportWrench').component('totalsAndNetProfit', {
    templateUrl: 'events/dashboard/accounting/common/totals-and-net-profit/totals-and-net-profit.html',
    bindings: {
        type: '@'
    },
    controller: ['$stateParams', 'TotalsAndNetProfitService', TotalsAndNetProfitController]
});

function TotalsAndNetProfitController ($stateParams, TotalsAndNetProfitService) {
    this.sections   = [];
    this.columns    = [];
    this.section    = {};

    TotalsAndNetProfitService.getTotals($stateParams.event, this.type)
    .then(data => {
        this.data       = data;
        this.sections   = data.stats;
        this.columns    = initColumns(data.columns);
    });

    let initColumns = columns => columns
                                    .filter(c => Boolean(c.name))
                                    .sort((c1, c2) => 
                                        (c1.order - c2.order)
                                    );
}

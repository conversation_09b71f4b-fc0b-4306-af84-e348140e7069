angular.module('SportWrench').component('accountingEntryFees', {
    templateUrl 	: 'events/dashboard/accounting/entry-fees/entry-fees.html',
    controller 		: [
        'EventTransfersService', 'eventDashboardService', 'UtilsService', '$timeout', '$rootScope',
        AccountingEntryFeesController
    ]
})

function AccountingEntryFeesController (
    EventTransfersService, eventDashboardService, UtilsService, $timeout, $rootScope
) {
    var _self 	= this;
    var _event = eventDashboardService.getEvent();

    var eventID = _event.event_id;

    this.stats 					= {};
    this.usdAvailable 			= 0;
    this.transfers 		        = [];
    this.teamsCardsAllowed 		= _event.teams_cards_allowed;
    this.teamsACHAllowed        = _event.teams_ach_allowed;

    this.initLoad 				= true;
    this.applyAnimatedShowing 	= false;

    loadData();

    function loadData () {
        return EventTransfersService.loadTransfersData(eventID, 'teams').then((result) => {
            _self.teams = result;

            _self.accountID = result.account && result.account.id;

            _self.initLoad = false;

            $timeout(() => _self.applyAnimatedShowing = true);
        })
    }

    this.getAmount = function (cur, val) {
        return UtilsService.getAmount(cur, val);
    }

    this.panelBodyCls = function () {
        return {
            'panel-body panel-smooth': true,
            'showed': this.applyAnimatedShowing
        }
    }

    $rootScope.$on('reloadMerchantStatisticsData', function () {
        loadData();
    })
}

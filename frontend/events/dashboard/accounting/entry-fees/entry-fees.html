<div class="row-space"></div>
<spinner active="$ctrl.initLoad"></spinner>
<div class="panel panel-default" ng-if="!$ctrl.initLoad">
    <div ng-class="$ctrl.panelBodyCls()">
        <div class="row">
            <div class="col-sm-6">
                <accounting-general-stats
                    title="Teams"
                    payment-for="teams"
                    stats="$ctrl.teams.stats.general"
                    sw-item-fee="$ctrl.teams.stats.event_fees.teams_entry_sw_fee"
                    card-allowed="$ctrl.teams.teamsCardsAllowed"
                    ach-allowed="$ctrl.teams.teamsACHAllowed"
                ></accounting-general-stats>

                <div class="row-space"></div>

                <div ng-if="$ctrl.teams.transfers.length">
                    <p class="lead">Sent Teams Payouts:</p>
                    <event-transfers-list list="$ctrl.teams.transfers"></event-transfers-list>
                </div>
                
            </div>
            <div class="col-sm-6">
                <accounting-stripe-stats
                    ng-if="$ctrl.teams.cardsAllowed"
                    title="Teams Entry"
                    payment-for="teams"
                    stats="$ctrl.teams.stats.stripe"
                    balance="$ctrl.teams.balance"
                    account="$ctrl.accountID"
                ></accounting-stripe-stats>
            </div>
        </div>
    </div>
</div>

<h2>New Statistics</h2>

<totals-and-net-profit type="teams"></totals-and-net-profit>
<div class="row">
    <div class="col-md-6">
        <stripe-details type="teams"></stripe-details>
    </div>
</div>
<payouts-details type="teams"></payouts-details>
<refund-details type="teams"></refund-details>


<div class="row">
    <div class="col-sm-offset-1 col-sm-10">
        <form name="$ctrl.checkinForm" ng-submit="$ctrl.submit()">
            <div class="form-group well well-checkin">
                <label class="radio-inline">
                    <input type="radio" name="status" ng-model="$ctrl.data.status" value="checkedin">
                    <span class="gl-success fa fa-check"></span> Checked In
                </label>
                <label class="radio-inline">
                    <input type="radio" name="status" ng-model="$ctrl.data.status" value="notcheckedin">
                    <span class="gl-danger fa fa-times text-danger"></span> Not Checked In
                </label>
                <label class="radio-inline">
                    <input type="radio" name="status" ng-model="$ctrl.data.status" value="pending">
                    <span class="gl-info fa fa-clock-o text-primary"></span> Pending
                </label>
                <label class="radio-inline">
                    <input type="radio" name="status" ng-model="$ctrl.data.status" value="alert">
                    <span class="gl-danger fa fa-exclamation-circle text-primary"></span> Alert
                </label>
            </div>
            <div class="form-group" ng-if="$ctrl.data.status === 'alert'">
                <label>Alert Note:<span style="color: red">*</span></label>
                <textarea name="alert_notes"
                          class="form-control"
                          ng-model="$ctrl.data.team_alert_note"
                          placeholder="Enter a note..."
                          required
                ></textarea>
            </div>
            <div class="form-group add-change-note">
                <label class="control-label" ng-click="showAddnote=!showAddnote">
                    <i ng-class="{'fa': true,
                                  'fa-caret-square-o-right': !showAddnote,
                                  'fa-caret-square-o-down' : showAddnote }"
                       aria-hidden="true">
                    </i>
                    <span ng-if="!showAddnote">Add note for this change</span>
                    <span ng-if="showAddnote">Note for this change <i>(only visible for you)</i>:</span>
                </label>
                <textarea ng-if="showAddnote" name="notes" class="form-control"
                          ng-model="$ctrl.data.notes" placeholder="Enter a note..."></textarea>
            </div>
            <div class="form-group">
                <label>Selected {{$ctrl.teams.length}} {{($ctrl.teams.length > 1)?'teams':'team'}}:</label>
                <ul class="list-group">
                    <checkin-team ng-repeat="t in ::$ctrl.limitedTeams" team="t"></checkin-team>
                </ul>
                <p ng-if="$ctrl.hiddenTeamsQty > 0" class="text-info">+{{$ctrl.hiddenTeamsQty}} more</p>
            </div>
            <div class="form-group">
                <button ng-if="$ctrl.thereIsTeamsWithValidationErrors()"
                        sw-confirm="Do you really want to check in team with roster validation errors?"
                        sw-confirm-do="$ctrl.submit"
                        sw-confirm-hide-no
                        class="btn btn-primary"
                        ng-disabled="$ctrl.checkinForm.$submitted"
                        type="button">Save</button>

                <button ng-if="!$ctrl.thereIsTeamsWithValidationErrors()"
                        class="btn btn-primary"
                        type="submit"
                        ng-disabled="$ctrl.checkinForm.$submitted">Save</button>
            </div>
        </form>
    </div>
</div>

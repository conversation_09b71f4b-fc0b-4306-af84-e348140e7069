angular.module('SportWrench').component('teamsActions', {
	templateUrl: 'events/dashboard/teams/actions/actions.html',
	bindings: {
        eventHasRosters     : '<eventHasRosters',
        eventHasStatusHousing    : '<',
		eventID 			: '<event',
		reloadTeamsList 	: '&reload',
		isDoublesEvent 		: '<isDoubles',
		getFiltersValues 	: '&getParams',
		eventEmail 			: '<email',
		hasClubs 			: '<',
		pickedTeamsQty 		: '@picked',
		maxTeamsQty 		: '<teamsQty',
		divisions 			: '<',
        canAddTeamsManually : '<',
        getTeams            : '&',
        hasManualClubNames  : '<',
        hasCoupons          : '<',
	},
	controller: ['$uibModal', 'AllTeamsService', 'AEMSendDialogService', 'rosterTeamService', 'CustomFormService',
        'eventDashboardService', 'CUSTOM_FORM_TYPE',
        function ($uibModal, AllTeamsService, AEMSendDialogService, rosterTeamService, CustomFormService,
                  eventDashboardService, CUSTOM_FORM_TYPE
    ) {
		var self = this;

		var reload = function () {
			self.reloadTeamsList();
		};

		this.showAddTeams = (!self.isDoublesEvent && self.hasClubs);

		this.openAddTeamModal = function () {

	        $uibModal.open({
	            template: '<assign-team event-id="' + self.eventID + '"></assign-team>'
	        })
	        .result
	        .then(reload, reload);

		};

		this.teamsXLSXExport = function () {
			AllTeamsService.exportTeams(self.eventID, self.getFiltersValues());
		};

		this.printRosters = function () {
			AllTeamsService.printRosters(self.eventID, self.getFiltersValues());
		};

		this.openEmailModal = function () {
			AEMSendDialogService.openDialog({
				eventID: self.eventID,
				filters: self.getFiltersValues(),
				replyTo: self.eventEmail,
				group  : 'clubs',
				showRecipients : true,
			}).catch(function (err) {
				if (err instanceof Error) {
					console.error(err);
				}
			})
		};

		this.exportStaff = function () {
			AllTeamsService.exportStaff(self.eventID, self.getFiltersValues());
		};

		this.exportAthletes = function () {
			AllTeamsService.exportAthletes(self.eventID, self.getFiltersValues());
		};

        this.lockRosters = function () {
            AllTeamsService.lockRosters(self.eventID, self.getTeams()).then(reload);
        };

        this.unlockRosters = function () {
            AllTeamsService.unlockRosters(self.eventID, self.getTeams()).then(reload);
        };

		this.showEntryStatusModal = function () {
			$uibModal.open({
				template: [
					'<modal-wrapper>', 
					'<change-entry-form on-submit="submit(data)" disable-save="formSaveBtnDisabled"></change-entry-form>',
					'</modal-wrapper>'
				].join(''),
				controller: ['$scope', function ($scope) {
					$scope.modalTitle = '<h4>Change Entry Status</h4>';
                    $scope.formSaveBtnDisabled = false;

					$scope.submit = function (data) {
						var body = angular.extend({}, data, self.getFiltersValues());
                        $scope.formSaveBtnDisabled = true;

						return AllTeamsService.changeEntryStatus(self.eventID, body)
                            .then(reload)
                            .then($scope.$close)
                            .catch(() => {
                                $scope.formSaveBtnDisabled = false
                            });
					};
				}]
			});
		};

		this.showPaymentStatusModal = function () {
			$uibModal.open({
				template: [
					'<modal-wrapper>', 
					'<change-payment-status-form on-submit="submit(data)" disable-save="formSaveBtnDisabled"></change-payment-status-form>',
					'</modal-wrapper>'
				].join(''),
				controller: ['$scope', function ($scope) {
					$scope.modalTitle = '<h4>Change Payment Status</h4>';
                    $scope.formSaveBtnDisabled = false;

					$scope.submit = function (data) {
						const body = angular.extend({}, data, self.getFiltersValues());
                        $scope.formSaveBtnDisabled = true;

						return AllTeamsService.changePaymentStatus(self.eventID, body)
                            .then(reload)
                            .then($scope.$close)
                            .catch(() => {
                                $scope.formSaveBtnDisabled = false
                            });
					};
				}]
			});
		};

		this.showHousingStatusModal = function() {
            $uibModal.open({
                template: [
                    '<modal-wrapper>',
                    '<change-housing-form on-submit="submit(data)" disable-save="formSaveBtnDisabled"></change-housing-form>',
                    '</modal-wrapper>'
                ].join(''),
                controller: ['$scope', function ($scope) {
                    $scope.modalTitle = '<h4>Change Housing Status</h4>';
                    $scope.formSaveBtnDisabled = false;

                    $scope.submit = function (data) {
                        var body = angular.extend({}, data, self.getFiltersValues());
                        $scope.formSaveBtnDisabled = true;

                        return AllTeamsService.changeHousingStatus(self.eventID, body)
                            .then(reload)
                            .then($scope.$close)
                            .catch(() => {
                                $scope.formSaveBtnDisabled = false
                            });
                    };
                }]
            });
        };

		this.openManualTeamsAdditionModal = function () {
            $uibModal.open({
                template    : `<manual-add-team-modal divisions="divisions"  
                                                      close="close()"
                                                      on-save="submit(data)"
                                                      has-manual-club-names="hasManualClubNames">
                                </manual-add-team-modal>`,
                backdrop: 'static',
                controller  : ['$scope', function ($scope) {
                    $scope.close = function () {
                        $scope.$close();
                    };
                    $scope.divisions = self.divisions;

                    $scope.hasManualClubNames = self.hasManualClubNames;

                    $scope.submit = function (data) {
                        return rosterTeamService.addTeamsManually(self.eventID, data).then(reload).then($scope.$close);
                    }
                }]
            });
        };

		this.openImportMembersModal = function () {
            $uibModal.open({
                template: `<manual-import-members-modal close="close()" on-save="submit(data)">
                           </manual-import-members-modal>`,
                controller: ['$scope', function ($scope) {
                    $scope.close = function () {
                        $scope.$close();
                    };

                    $scope.submit = function (data) {
                        const formData = new FormData();
                        formData.append('file', data.file);

                        return rosterTeamService.importMembersManually(self.eventID, formData)
                            .then((response) => {
                                reload();
                                $scope.$close();
                                return response.data;
                            })
                    }
                }]
            });
        };

		this.openImportTeamsModal = function () {
            $uibModal.open({
                template: `<manual-import-teams-modal close="close()" on-save="submit(data)">
                           </manual-import-teams-modal>`,
                controller: ['$scope', function ($scope) {
                    $scope.close = function () {
                        $scope.$close();
                    };

                    $scope.submit = function (data) {
                        const formData = new FormData();
                        formData.append('statusEntry', data.statusEntry);
                        formData.append('file', data.file);

                        return rosterTeamService.importTeamsManually(self.eventID, formData)
                            .then((response) => {
                                reload();
                                $scope.$close();
                                return response.data;
                            })
                    }
                }]
            });
        };

		this.allTeamsPicked = function () {
			return +self.pickedTeamsQty === +self.maxTeamsQty;
		};

		this.changeDivision = function () {
			$uibModal.open({
				template: [
					'<modal-wrapper>',
					'<change-division-form on-submit="submit(data)" disable-save="formSaveBtnDisabled" divisions="divisions">' +
                    '</change-division-form>',
					'</modal-wrapper>'
				].join(''),
				controller: ['$scope', function ($scope) {
					$scope.modalTitle = '<h4>Change Division</h4>';

					$scope.divisions            = self.divisions;
                    $scope.formSaveBtnDisabled  = false;

					$scope.submit = function (data) {
						var body = angular.extend({}, data, self.getFiltersValues());

                        $scope.formSaveBtnDisabled = true;

						return AllTeamsService.changeDivision(self.eventID, body)
                            .then(reload)
                            .then($scope.$close)
                            .catch(() => {
                                $scope.formSaveBtnDisabled = false
                            });
					};
				}]
			});
		};

        this.showCouponsGenerationModal = function() {
            const filters =  this.getFiltersValues();
            $uibModal.open({
                size: 'sm',
                template:
                    `<modal-wrapper>
                        <coupon-generation-form close="$close()" filters="filters">
                        </coupon-generation-form>
                    </modal-wrapper>`,
                controller: ['$scope', function ($scope) {
                    $scope.modalTitle = '<h4>Coupons Settings</h4>';

                    $scope.filters = filters;
                }]
            });
        };

	}]
});

class CouponGenerationFormComponent {
    constructor($stateParams, CouponService, toastr, eventDashboardService) {
        this.$stateParams = $stateParams;
        this.CouponService = CouponService;
        this.toastr = toastr;
        this.eventDashboardService = eventDashboardService;

        this.isSubmitting = false;
        this.load().catch(console.error);
    }

    $onInit () {
        this.EMAIL_RECIPIENT_TYPES = [
            {
                id: 'club_director',
                title: 'Club Director',
                is_visible: !this.eventDashboardService.getEvent().is_with_manual_teams_addition
            },
            {
                id: 'head_coach',
                title: 'Head Coach',
                is_visible: true
            },
        ];
    }

    get EVENT_ID() {
        return this.$stateParams.event;
    }

    get DEFAULT_QUANTITY() { return 10; }

    async load() {
        this.loading = true;
        this.loaded = false;
        try {
            this.ticketTypes = await this.CouponService.getTicketTypes(this.EVENT_ID);
            this.settings = this._initializeSettingsObject();
            this.loaded = true;
        }
        catch (err) {
            this.close();
        }
        finally {
            this.loading = false;
        }
    }

    _initializeSettingsObject() {
        const settings = {
            ticket_types: {},
            email_recipient_types: [],
        };
        for(const ticketType of this.ticketTypes) {
            settings.ticket_types[ticketType.event_ticket_id] = {
                enabled: false,
                quantity: this.DEFAULT_QUANTITY,
            };
        }
        return settings;
    }

    _formatSettingsObject(settings) {
        const ticket_types = Object.keys(settings.ticket_types).map(
            event_ticket_id => Object.assign({event_ticket_id}, settings.ticket_types[event_ticket_id])
        )
            .filter(v => v.enabled)
            .map(v => _.omit(v, 'enabled'));
        const {email_recipient_types} = this.settings;
        return Object.assign({}, settings, {ticket_types, email_recipient_types});
    }

    getFieldId(ticketType, fieldName) {
        return `ticket-${ticketType.event_ticket_id}-${fieldName}`;
    }

    async submit() {
        this.isSubmitting = true;
        const {settings, filters} = this;
        try {
            const statistics = await this.CouponService.createCouponGenerationTask(this.EVENT_ID, {
                for: 'teams',
                mode: 'append',
                filters,
                settings: this._formatSettingsObject(settings),
            });
            this.CouponService.showSuccessCouponCreationMessage(statistics);
            this.close();
        }
        finally {
            this.isSubmitting = false;
        }
    }
}

angular.module('SportWrench').component('couponGenerationForm', {
    templateUrl: 'events/dashboard/teams/actions/coupon-generation-form/coupon-generation-form.html',
    bindings: {
        close: '&',
        filters: '<',
    },
    controller: ['$stateParams', 'CouponService', 'toastr', 'eventDashboardService', CouponGenerationFormComponent],
})

angular.module('SportWrench').component('pickerHeaderItem', {
	template: '<small><i class="{{$ctrl.item.class}}"></i> {{$ctrl.getName()}}</small>',
	bindings: {
		item 	: '<',
		nameLen : '@len'
	},
	require: {
		pickerCtrl: '^teamStatusPicker'
	},
	controller: [function () {
		var self = this;

		this.getName = function () {
			if(self.nameLen === 'short') {
				return self.pickerCtrl.getHeaderItemTitle(self.item);
			} else {
				return self.pickerCtrl.getItemName(self.item.name);
			}
		};
	}]
});
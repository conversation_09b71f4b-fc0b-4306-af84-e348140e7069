angular.module('SportWrench').controller('EventDivisionsController', EventDivisionsController);

function EventDivisionsController (
    $scope, $stateParams, eventsService, $state, divisionsService, $q, toastr, APP_ROUTES, ngTableParams,
    EventACLService, EVENT_OPERATIONS, EventDashboardTabsService
) {
    var eventId                 = $stateParams.event;

    $scope.reverseSort          = false;
    $scope.divisions            = [];
    $scope.editMode             = false;
    $scope.allCheckbox          = { checked: false };
    $scope.selected_divisions   = 0;
    $scope.search               = {};   
    $scope.totals               = {};
    $scope.settings             = { disableNoBtn: true };

    $scope.defaultSort = false;
    $scope.search = '';

    $scope.filters = {
        page    : 1
    };

    let getData = function ($defer, params) {

        var orderBy = params.orderBy();

        var urlQuery = {};

        var filter = params.filter();

        if (filter) {
            urlQuery = _.clone(filter);
        }

        if(orderBy && orderBy.length && !$scope.defaultSort) {
            urlQuery.order      = orderBy[0].substr(1);
            urlQuery.direction  = (orderBy[0].charAt(0) === '-')? 'asc':'desc';
        }

        divisionsService.getDivisions(eventId, urlQuery)
            .then(function (divisions) {
                $scope.selected_divisions   = 0;
                $scope.defaultSort          = false;
                $scope.allCheckbox.checked  = false;

                if (divisions) {
                    $scope.divisions = divisions;
                    $defer.resolve(divisions);
                } else {
                    $scope.divisions = [];
                    $defer.resolve([]);
                }


            }, function () {
                params.total(0);
                $scope.divisions           = [];
                $scope.allCheckbox.checked = false;
                $scope.selected_divisions  = 0;
                $defer.resolve([]);
            });

    };

    $scope.tableParams = new ngTableParams({
        page        : 1,
        filter      : $scope.filters,
        count       : 1,
        sorting     : {}
    }, {
        filterDelay : 0,
        getData     : getData
    });

    var divisionsTable = $scope.tableParams;

    $scope.filterSearch     = function () {
        if($scope.tableParams.settings().$loading) return;

        $scope.filters.search   = $scope.search;
        $scope.filters.page     = 1;
    };

    $scope.columnClass = function (colName) {
        var stylesClasses = {
            'text-center sortable'  : colName?true:false,
            'sort-asc'              : divisionsTable.isSortBy(colName, 'asc'),
            'sort-desc'             : divisionsTable.isSortBy(colName, 'desc')
        };

        return stylesClasses;
    };

    $scope.sort = function (column) {
        if (column) {
            $scope.filters.page = 1;
            divisionsTable.sorting(column, divisionsTable.isSortBy(column, 'asc') ? 'desc'
                : divisionsTable.isSortBy(column, 'desc') ? setSortDefault() : 'asc');
        }
    };

    function setSortDefault() {
        $scope.defaultSort = true;
        return 'text-center sortable';
    };

    var reloadData = function () {
        if($scope.filters.page !== 1) {
            $scope.filters.page = 1;
        } else {
            $scope.tableParams.reload();
        }
    };

    function calculateTotals () {
        $scope.totals = {
            entered     : 0,
            maxTeams    : 0,
            accepted    : 0,
            wait        : 0,
            unpaid      : 0,
            housing     : 0
        };
        angular.forEach($scope.divisions, function(div) {
            $scope.totals.entered   += div.entered;
            $scope.totals.maxTeams  += div.max_teams;
            $scope.totals.accepted  += div.accepted;
            $scope.totals.wait      += div.wait;
            $scope.totals.unpaid    += div.unpaid;
            $scope.totals.housing   += div.housing;
        });
    }

    $scope.$watch('divisions', function() {
        calculateTotals();
    });

    $scope.show_reg_fee = function (division) {
        if(division.reg_fee) {
            return division.reg_fee;
        }
        return $scope.event.reg_fee;
    };

    $scope.get_title = function (division) {
        if(division.reg_fee && division.reg_fee > 0) {
            return 'Division Entry Fee';
        }
        return 'Event Default Entry Fee';
    };

    $scope.selectAll = function() {
        let isSelected = $scope.allCheckbox.checked;

        if(!isSelected) {
            $scope.selected_divisions = 0;
        } else {
            $scope.selected_divisions = $scope.divisions.length;
        }

        for(var i = 0 ; i < $scope.divisions.length; ++i) {
            this.$data[i].selected = isSelected;
        }
    };

    $scope.$watch('search', function (newVal, oldVal) {
        if (newVal != oldVal && newVal.name && angular.isString(newVal.name)) {
            $scope.search.name = newVal.name.toLowerCase();
        }
    }, true);

    $scope.checkDivision = function(division) {
        if(division.selected) {
            $scope.selected_divisions++;
        } else {
            $scope.selected_divisions--;
        }
    };

    $scope.goToCreateMenu = function() {
        $state.go(APP_ROUTES.EO.DIVISIONS_NEW, {
            event: eventId
        });
    };

    $scope.goToGroupCreation = function() {
        $state.go(APP_ROUTES.EO.DIVISIONS_CREATE_GR, {
            event: eventId
        });
    };

    $scope.editDivision = function(division_id) {
        $state.go(APP_ROUTES.EO.DIVISIONS_EDIT, {
            division: division_id
        });
    };

    $scope.markFull = function () {
        var choosenDivisions = __getChoosenDivisions();

        if(!choosenDivisions.length)
            return;

        $q.all(
            _.map(choosenDivisions, function (division) {
                return divisionsService.markFull(eventId, division.division_id)
            })
        ).then(reloadData)
    };

    $scope.remove = function () {
        var choosenDivisions    = __getChoosenDivisions();

        if(!choosenDivisions.length) {
            return;
        }

        $q.all(
            _.map(choosenDivisions, function (division) {
                return divisionsService.remove(eventId, division.division_id)
                .then(null, function (resp) {
                    if(resp && resp.data && resp.data.validation) {
                        toastr.warning(resp.data.validation);
                    } else {
                        throw resp
                    }
                })
            })
        ).then(reloadData)
    };

    $scope.openTeams = function (division, filter_name) {
        if(!__teamsTabAvailable()) {
            return;
        }

        $state.go(APP_ROUTES.EO.TEAMS, {
            mode    : filter_name,
            value   : (filter_name === 'division')?division.division_id:true
        })
    };

    function __getChoosenDivisions () {
        return _.filter($scope.divisions, function (division) {
            return division.selected;
        })
    }

    function __teamsTabAvailable () {
        return EventDashboardTabsService.isAllowedByAcl(EVENT_OPERATIONS.TEAMS_TAB)
    }
}

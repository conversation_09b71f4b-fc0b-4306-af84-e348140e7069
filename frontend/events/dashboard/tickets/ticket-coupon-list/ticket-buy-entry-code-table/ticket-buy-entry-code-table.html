<div class="ticket-coupon-table">
    <div class="row spacer-md-b">
        <div class="col-sm-4 col-md-3 col-lg-2">
            <sw-searchbox
                css="search_teams white-ro"
                reload="$ctrl.filterSearch()"
                input-model="$ctrl.search"
                placeholder="Filter Coupons ..."
                is-readonly="$ctrl.codesTable.settings().$loading"
                reload-time="1000"
            ></sw-searchbox>
        </div>

        <div class="col-sm-2 pull-right actions-section">
            <button class="btn btn-default"
                    ng-if="$ctrl.showCouponCreationButton"
                    ng-click="$ctrl.openCustomTicketBuyEntryCodeCreationModal()"
            >Create Coupon</button>
        </div>
    </div>
    <div class="row">
        <div  class="col-xs-12" loading-container="$ctrl.codesTable.settings().$loading">
            <table  class="table sw-adaptive-grid table-condensed event-all-teams-table ng-table"
                    ng-table="$ctrl.codesTable"
                    sticky-header
            >
                <thead>
                <tr>
                    <th
                        ng-repeat="c in ::$ctrl.dynamic_cols"
                        ng-if="::c.visible"
                        ng-class="$ctrl.columnClass(c)"
                        ng-click="$ctrl.sort(c)">
                        <div class="sort-indicator">{{c.title}}</div>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="code in $ctrl.codes track by $index">
                    <td class="pointer {{c.selectors}}"
                        ng-repeat="c in ::$ctrl.dynamic_cols"
                        ng-if="c.visible"
                        sortable="c.sortable"
                    >
                        <span ng-if="!c.is_additional && !c.is_custom" ng-bind="code[c.name]"></span>

                        <span
                            ng-if="c.name === 'ticket_buy_entry_code'"
                            ng-bind="code[c.name]"
                            ng-click="$ctrl.openTicketsModal(code)"
                        ></span>

                        <span ng-repeat="fields in code.additional_fields">
                            <span ng-if="c.is_additional" ng-bind="fields[c.name]"></span>
                        </span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <pagination
        total="$ctrl.utils.totalRows"
        limit="$ctrl.filters.limit"
        page="$ctrl.filters.page"
        change-page="$ctrl.changePage(page)"
        current="$ctrl.codes.length"
        loading="$ctrl.codesTable.settings().$loading"
    ></pagination>
</div>


class Controller {

    $onInit () {
        this.isAdditionMenuOpen = false;

        this.receivers = [];
    }

    toggleAdditionMenu () {
        this.isAdditionMenuOpen = !this.isAdditionMenuOpen;
    }

    getToggleButtonClass () {
        return this.isAdditionMenuOpen
            ? 'fa fa-caret-square-o-right'
            : 'fa fa-caret-square-o-down';
    }

    removeR<PERSON><PERSON>ver (receiver) {
        this.receivers = this.receivers.filter(r => r.email !== receiver.email);

        this.onReceiversUpdate({ receivers: this.receivers });
    }

    async addReceiver (receiver) {
        if(this.receivers.length && this.__receiverExists(receiver.email)) {
            throw { validation: { field: 'email', message: 'Duplicate email' } };
        }

        this.receivers.push(receiver);

        this.onReceiversUpdate({ receivers: this.receivers });
        this.toggleAdditionMenu();
    }

    __receiverExists (email) {
        let [existReceiver] = this.receivers.filter(r => r.email === email);

        return !_.isEmpty(existReceiver);
    }
}

angular.module('SportWrench').component('ticketCouponCreationReceivers', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-creation/ticket-coupon-creation-receivers/template.html',
    bindings: {
        onReceiversUpdate: '&'
    },
    controller: Controller
});

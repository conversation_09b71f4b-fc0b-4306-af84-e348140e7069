
class TicketCouponCreationService {
    constructor ($uibModal) {
        this.$uibModal = $uibModal;
    }

    openCouponCreationModal () {
        return this.$uibModal.open({
            template: `<ticket-coupon-creation-form
                            on-save="onSave()"
                            close="close()"
                       ></ticket-coupon-creation-form>`,
            controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                $scope.onSave = function () {
                    $uibModalInstance.close();
                }

                $scope.close = function () {
                    $uibModalInstance.dismiss();
                };
            }]
        }).result;
    }
}

TicketCouponCreationService.$inject = ['$uibModal'];

angular.module('SportWrench').service('TicketCouponCreationService', TicketCouponCreationService);

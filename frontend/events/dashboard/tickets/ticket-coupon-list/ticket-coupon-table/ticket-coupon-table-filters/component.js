
const ACTIVATION_STATUS = {
    ACTIVATED: 'activated',
    DEACTIVATED: 'deactivated'
};

const EMAIL_SENDING_STATUS = {
    SENT: 'sent',
    NOT_SENT: 'not_sent'
}

class Controller {
    constructor (CouponService, $stateParams, moment) {
        this.CouponService = CouponService;
        this.$stateParams = $stateParams;
        this.moment = moment;
    }

    $onInit () {
        this.ticketNames = [];
        this.validDates = [];
        this.emailStatuses = [
            { id: EMAIL_SENDING_STATUS.SENT, name: 'Sent' },
            { id: EMAIL_SENDING_STATUS.NOT_SENT, name: 'Not Sent' }
        ];
        this.activationStatuses = [
            { id: ACTIVATION_STATUS.ACTIVATED, name: 'Activated' },
            { id: ACTIVATION_STATUS.DEACTIVATED, name: 'Deactivated' }
        ]

        this._loadFiltersData();
    }

    filterTicketNames (filters) {
        this.onChanges({filters: { ticket_names: filters }});
    }

    filterValidDates (filters) {
        this.onChanges({filters: { valid_dates: filters }});
    }

    filterActivationStatus (filters) {
        this.onChanges({filters: { activation_statuses: filters }});
    }

    filterEmailStatuses (filters) {
        this.onChanges({filters: { email_statuses: filters }});
    }

    _loadFiltersData () {
        return this.CouponService.getTicketTypes(this.$stateParams.event)
            .then(ticketTypes => {
                if(!Array.isArray(ticketTypes) || !ticketTypes.length) {
                    return;
                }

                this.ticketNames = this._formatTicketNamesList(ticketTypes);
                this.validDates = this._formatTicketValidDates(ticketTypes)
            })
    }

    _formatTicketNamesList (ticketTypes) {
        return ticketTypes.map(ticketType => {
            return {
                id: ticketType.event_ticket_id,
                name: ticketType.label
            }
        })
    }

    _formatTicketValidDates (ticketTypes) {
        let validDates = ticketTypes.reduce((all, ticketType) => {
            if(ticketType.valid_dates) {
                all = all.concat(ticketType.valid_dates)
            }

            return all;
        }, []);

        if(!validDates.length) {
            return [];
        }

        let result = Array.from(new Set(validDates));

        let sortedResult = result.sort(
            (a, b) => this.moment(a, 'YYYY-MM-DD').valueOf() - this.moment(b, 'YYYY-MM-DD').valueOf()
        );

        return sortedResult.map(date => {
            return { id: date,  name: this.moment(date, 'YYYY-MM-DD').format('ddd, MMM DD') }
        });
    }
}

Controller.$inject = ['CouponService', '$stateParams', 'moment'];

angular.module('SportWrench').component('ticketCouponTableFilters', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-table/ticket-coupon-table-filters/template.html',
    bindings: {
        onChanges: '&'
    },
    controller: Controller,
});

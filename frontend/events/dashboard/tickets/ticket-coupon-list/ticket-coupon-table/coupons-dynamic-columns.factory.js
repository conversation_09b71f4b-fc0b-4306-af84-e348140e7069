angular.module('SportWrench').factory('CouponsDynamicColsFactory', [function () {
	return {
		getColumns: function () {
			return [
		        {
		            title           : 'Email Status',
		            name            : 'email_status',
                    sortable        : false,
		            visible         : true,
                    is_custom       : true
		        }, {
		            title           : 'Team Name',
		            name            : 'team_name',
                    sortable        : true,
		            visible         : true,
                    is_custom       : false
		        }, {
		            title           : 'Coupon Code',
		            name            : 'coupon_code',
                    sortable        : false,
		            visible         : true,
                    is_custom       : true
		        },{     
		            title           : 'Available for days',
		            name            : 'valid_dates',
                    sortable        : true,
		            visible         : true,
                    selectors       : 'text-left',
                    is_custom       : false
		        }, {     
		            title           : 'Ticket Quantity',
		            name            : 'quantity',
                    sortable        : true,
                    visible         : true,
                    is_custom       : true
		        }, {     
		            title           : 'Number Purchased Tickets',
		            name            : 'bought_qty',
                    sortable        : true,
                    visible         : true,
                    is_custom       : false
                }, {
                    title           : 'Deactivated',
                    name            : 'deactivated',
                    sortable        : false,
                    visible         : true,
                    is_custom       : true,
                }
		    ];
		}
	};
}]);

angular.module('SportWrench').component('ticketCouponTable', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-table/ticket-coupon-table.html',
    controller: TicketCouponTableController,
});

TicketCouponTableController.$inject = [
    'ngTableParams', 'CouponService', '$stateParams', 'CouponsDynamicColsFactory', 'toastr',
    'TicketCouponCreationService', 'moment'
];

function TicketCouponTableController(
    ngTableParams, CouponService, $stateParams, CouponsDynamicColsFactory, toastr, TicketCouponCreationService,
    moment
) {
    let self = this;

    this.couponContactsTemplate
        = 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-table/coupon-contacts.html';

    this.filters = {
        page: 1,
        limit: 100
    };

    this.defaultSort = false;

    this.utils = {
        allSelected: false,
        selectedCouponsCount: 0,
        totalRows: 0
    }

    this.coupons = [];
    this.search = '';

    let getData = function ($q, params) {
        let _params = {};
        let filter  = params.filter();
        let orderBy = params.orderBy();

        if (filter) {
            _params = _.clone(filter);
        }

        if(orderBy && orderBy.length && !self.defaultSort) {
            _params.order  = orderBy[0].substr(1);
            _params.revert = orderBy[0].charAt(0) === '-';
        }

        return CouponService.getCouponsList($stateParams.event, _params)
            .then(function(data) {
                self.coupons = data.coupons;

                self.utils.selectedCouponsCount = 0;
                self.utils.allSelected = false;

                self.defaultSort     = false;
                self.utils.totalRows = self.coupons[0] && self.coupons[0].total_rows || 0;

                return data.coupons;
            })
            .catch(err => console.error(err));
    }

    this.filterSearch = () => {
        if(this.couponsTable.settings().$loading) return;

        this.filters.search   = this.search;
        this.filters.page     = 1;
    };

    this.openCouponTicketsModal = function (coupon) {
        if(coupon.bought_qty > 0) {
            return CouponService.openCouponTicketsModal(coupon.coupon_code);
        }
    }

    this.openCouponCreationModal = function () {
        return TicketCouponCreationService.openCouponCreationModal()
            .then(() => this.reloadCoupons());
    }

    this.couponsTable = new ngTableParams({
        page        : 1,
        count       : 100,
        sorting     : {},
        filter      : self.filters
    }, {
        total       : 0,
        counts      : [],
        filterDelay : 0,
        getData    : getData
    });

    this.dynamic_cols = CouponsDynamicColsFactory.getColumns();

    this.formatValue = (value, field) => {
        if(field === 'valid_dates') {
            return _.isEmpty(value) ? 'All Days' : getValidDates(value)
        }

        return value;
    }

    let getValidDates = (dates) => {
        return dates.map(date => {
            return `<span class="mr-3 label label-success">${moment(date, 'YYYY ddd, MMM DD').format('ddd, MMM DD')}</span>`;
        }).join('');
    }

    this.updateTicketsCount = function (cb, coupon) {
        if(Number(coupon.quantity) < Number(coupon.bought_qty)) {
            toastr.warning('Tickets Quantity should be larger Purchased Quantity');
            return;
        }

        return CouponService.updateTicketsCount($stateParams.event, { coupons: [coupon.id] }, coupon.quantity)
            .then(() => toastr.success('Updated'))
            .catch((err) => {
                console.error(err);
                toastr.warning('Not Updated');
            })
            .finally(() => cb());
    }

    this.updateActiveStatus = function(event, coupon) {
        event.preventDefault();
        const receiver = coupon.team_name === '-' ? null : coupon.team_name;
        CouponService.openCouponActiveStatusChangeModal(coupon.id, coupon.active, receiver)
            .then(({active}) => {
                coupon.active = active;
            })
    };

    this.columnClass = (c) => {
        let stylesClasses = {
            'text-center sortable': c.sortable,
            'sort-asc'            : self.couponsTable.isSortBy(c.name, 'asc'),
            'sort-desc'           : self.couponsTable.isSortBy(c.name, 'desc')
        };

        if (c.selectors) {
            stylesClasses[c.selectors] = true;
        }
        return stylesClasses;
    }

    this.sort = (column) => {
        if(!column.sortable) {
           return;
        }

        if (column) {
            self.filters.page = 1;

            let direction =  self.couponsTable.isSortBy(column.name, 'asc')
                ? 'desc'
                : self.couponsTable.isSortBy(column.name, 'desc') ? setSortDefault() : 'asc';

            self.couponsTable.sorting(column.name, direction);
        }
    };

    this.couponContacts = (emails) => {
        if(_.isEmpty(emails)) {
            return 'No data';
        }

        let template = '';

        for(let contact of emails) {
            template += `${contact.name}<br/>`;

            if(contact.is_club) {
                template += `<b>${contact.is_staff ? 'Head Coach' : 'Club Director'}</b><br/>`;
            }

            template += `<a href="mailto:${contact.email}">${contact.email}</a><br/>`;

            if(emails.indexOf(contact) !== (emails.length - 1)) {
                template += '<hr/>'
            }
        }

        return template;
    }

    this.resend = function (coupon, email) {
        let isResend = true;

        CouponService.sendCoupons($stateParams.event, { coupons: [coupon.id], email }, isResend)
            .then(response => {
                toastr.success(`Sent ${response.sentEmails} emails`);
            });
    }

    function setSortDefault() {
        self.defaultSort = true;
        return 'text-center sortable';
    }

    this.reloadCoupons = function () {
        this.couponsTable.reload();
    }

    this.getFilters = function () {
        let params = _.omit(this.filters, 'limit', 'page');

        if(this.utils.selectedCouponsCount > 0 && !this.utils.allSelected) {
            params.coupons = getPickedCoupons(this.coupons);
        }

        return params;
    }

    function getPickedCoupons (coupons) {
        return _.filter(coupons, coupon => !!coupon.checked).map(coupon => coupon.id);
    }

    this.filterTickets = function (filters) {
        if(!_.isEmpty(filters)) {
           this.filters = Object.assign(this.filters, filters);
        }
    };

    this.selectAll = () => {
        if(!this.utils.allSelected){
            this.utils.selectedCouponsCount = 0;
        } else {
            this.utils.selectedCouponsCount = this.coupons.length;
        }

        for(let i = 0; i < this.coupons.length; ++i) {
            this.coupons[i].checked = this.utils.allSelected;
        }
    }

    this.selectCoupon = (coupon) => {
        if(coupon.checked)
            this.utils.selectedCouponsCount++;
        else {
            if (this.utils.selectedCouponsCount > 0) {
                this.utils.selectedCouponsCount--;
            }
        }

        if(this.utils.selectedCouponsCount === this.coupons.length)
            this.utils.allSelected = true;
        else if (this.utils.selectedCouponsCount === 0)
            this.utils.allSelected = false;
    };

    this.changePage = (page) => {
        this.filters.page = page;
    }
}

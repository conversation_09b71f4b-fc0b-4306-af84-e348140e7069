class TicketCouponListActionsService {
    constructor (CouponService, $uibModal, toastr, $stateParams) {
        this.CouponService = CouponService;
        this.$uibModal = $uibModal;
        this.toastr = toastr;

        this.eventID = $stateParams.event;
    }

    openChangeTicketsCountModal (filters) {
        let self = this;

        return this.$uibModal.open({
            template  : `<coupon-change-tickets-count 
                            filters="filters" 
                            on-save="updateTicketsCount(count)"
                            close="close()"
                         ></coupon-change-tickets-count>`,
            controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                $scope.filters = filters;

                $scope.close = function () {
                    $uibModalInstance.dismiss();
                };

                $scope.updateTicketsCount = function (count) {
                    return self.CouponService.updateTicketsCount(self.eventID, filters, count)
                        .then(() => {
                            self.toastr.success('Success');

                            $uibModalInstance.close();
                        })
                        .catch(err => self.toastr.warning(err));
                }
            }]
        }).result;
    }

    openSendingCouponsModal (filters) {
        let self = this;

        return this.$uibModal.open({
            template  : `<ticket-coupon-sending 
                            send-coupons="sendCoupons(isResend, emailRecipientTypes)"
                            close="close()"
                         ></ticket-coupon-sending>`,
            controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                $scope.close = function () {
                    $uibModalInstance.dismiss();
                };

                $scope.sendCoupons = function (isResend, emailRecipientTypes) {
                    return self.CouponService.sendCoupons(self.eventID, filters, isResend, emailRecipientTypes)
                        .then(response => self.toastr.success(`Sent ${response.sentEmails} emails`));
                }
            }]
        }).result;
    }
}

TicketCouponListActionsService.$inject = ['CouponService', '$uibModal', 'toastr', '$stateParams'];

angular.module('SportWrench').service('TicketCouponListActionsService', TicketCouponListActionsService);

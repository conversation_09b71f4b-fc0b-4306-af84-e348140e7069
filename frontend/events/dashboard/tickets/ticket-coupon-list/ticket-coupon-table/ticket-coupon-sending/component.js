
class Controller {
    constructor ($scope) {
        this.$scope = $scope;
    }

    $onInit () {
        this.$scope.modalTitle = '<h4>Send coupons</h4>';

        this.sendingOptions = {
            isResend: false,
            emailRecipientTypes: [],
        }
    }

    send () {
        this.sendCoupons({
            isResend: this.sendingOptions.isResend,
            emailRecipientTypes: this.sendingOptions.emailRecipientTypes,
        })
            .then(() => this.close());
    }

}

angular.module('SportWrench').component('ticketCouponSending', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-table/ticket-coupon-sending/template.html',
    bindings: {
        sendCoupons: '&',
        close: '&'
    },
    controller: Controller,
});

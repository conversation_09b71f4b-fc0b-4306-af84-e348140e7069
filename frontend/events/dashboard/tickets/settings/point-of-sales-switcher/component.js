
class Controller {
    $onInit () {
        this.allow_point_of_sales = this.event.allow_point_of_sales;
    }

    updateSettings () {
        this.updateTicketsSettings({
            settings: { allow_point_of_sales: this.allow_point_of_sales }
        })
    }
}

angular.module('SportWrench').component('pointOfSalesSwitcher', {
    templateUrl: 'events/dashboard/tickets/settings/point-of-sales-switcher/template.html',
    bindings: {
        event: '=',
        updateTicketsSettings: '&',
    },
    controller: Controller,
});

angular.module('SportWrench').component('verticalInsuranceSettings', {
    templateUrl: 'events/dashboard/tickets/settings/vertical-insurance-settings.html',
    bindings: {
        event: '<',
        updateSettings: '&',
    },
    controller: [VerticalInsuranceSettings]
});

function VerticalInsuranceSettings () {
    this.checked = this.event && this.event.use_vertical_insurance || false;
    this.saveInProgress = false;

    this.onChange = () => {
        if(!this.saveInProgress) {
            this.saveInProgress = true;

            let settings = {};

            settings.use_vertical_insurance = this.checked;

            return this.updateSettings({settings})
                .finally(() => this.saveInProgress = false);
        }
    };
}

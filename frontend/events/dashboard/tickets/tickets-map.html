<div class="container row-space tickets-on-map">
    <h3>Map</h3>
    <ui-gmap-google-map 
        center="mapData.map.center"
        zoom="mapData.map.zoom" 
        draggable="true" 
        options="mapData.mapOptions" 
        bounds="map.bounds">
        <ui-gmap-markers
            models="mapData.markers" 
            coords="'self'" 
            icon="'icon'" 
            doCluster="'true'"
            clusterOptions="mapData.clusterOptions"
            options="'opts'">
        </ui-gmap-markers>
    </ui-gmap-google-map>
</div>

class FreeTicketComponent {
    constructor() {
        // Empty constructor - no dependencies needed for this component
    }

    $onInit() {
        this.tabs = {
            active: 0
        }
    }
}

// Add $inject annotation for minification safety
FreeTicketComponent.$inject = [];

angular.module('SportWrench').component('freeTicket', {
    templateUrl: 'events/dashboard/tickets/free-ticket/free-ticket.html',
    bindings: {
        onClose: '&',
        ticketType: '<',
    },
    controller: FreeTicketComponent,
})

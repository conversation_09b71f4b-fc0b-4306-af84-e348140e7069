class ExhibitorTicketComponent {
    constructor() {
        // Empty constructor - no dependencies needed for this component
    }

    $onInit() {
        this.tabs = {
            active: 0
        }
    }
}

// Add $inject annotation for minification safety
ExhibitorTicketComponent.$inject = [];

angular.module('SportWrench').component('exhibitorTicket', {
    templateUrl: 'events/dashboard/tickets/exhibitor-ticket/exhibitor-ticket.html',
    bindings: {
        onClose: '&',
        ticketType: '<',
    },
    controller: ExhibitorTicketComponent,
})

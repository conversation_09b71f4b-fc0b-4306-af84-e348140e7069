<inline-edit
    edit-value="{{$ctrl.getValue() || 'N/A'}}">
    <input
        ng-show="$ctrl.isTextField"
        type="text"
        class="form-control form-control-input-small"
        ng-model="$ctrl.additionalField.value"
        ng-blur="$ctrl.update($ctrl.additionalField.field, $ctrl.additionalField.value)"
        ng-focus="$ctrl.focusField()"
        ng-keypress="$ctrl.onKeypress($event, $ctrl.additionalField.value)"
    >
    <select
        ng-show="$ctrl.isSelectField"
        ng-model="$ctrl.additionalField.value"
        ng-options="key as value for (key, value) in $ctrl.inlineEditingField.options"
        class="form-control form-control-select-small"
        ng-change="$ctrl.update($ctrl.additionalField.field, $ctrl.additionalField.value)"
    ></select>
</inline-edit>

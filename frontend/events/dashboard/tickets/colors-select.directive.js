angular.module('SportWrench').directive('colorSelect', function () {
    return {
        restrict: 'E',
        scope: {
            val: '='
        }, 
        template: 
            '<select ' + 
                'class="form-control form-control-select-small--90" ' +
                'ng-model="val" ' +
                'ng-options="c.hex as c.name for c in utils.colorsList"> ' +
                '<option value="">Choose ...</option> ' +
            '</select>',
        replace: true,
        link: function (scope) {
            scope.utils = {
                colorsList: [
                    {   hex: '#ff0000', name: 'Red'       },
                    {   hex: '#ff7f00', name: 'Orange'    },
                    {   hex: '#ffff00', name: 'Yellow'    },
                    {   hex: '#00ff00', name: '<PERSON>'     },
                    {   hex: '#0000ff', name: 'Blue'      },
                    {   hex: '#4b0082', name: 'Indigo'    },
                    {   hex: '#8b00ff', name: 'Violet'    },
                ]
            }
        }
    }
})

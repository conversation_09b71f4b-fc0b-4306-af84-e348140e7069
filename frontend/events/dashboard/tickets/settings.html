<div class="row">
    <div class="col-xs-6">
        <h3 style="display: inline-block;">Settings</h3>

        <point-of-sales-switcher event="event"
                                 ng-if="showSalesHubModeSwitcher()"
                                 update-tickets-settings="updateTicketsSettings(settings)"
        ></point-of-sales-switcher>

    </div>
</div>
<spinner active="!utils.allDataLoaded"></spinner>
<settings-errors-list errors="data.errors"></settings-errors-list>
<div class="row row-space rowm0">
    <div class="tickets-settings-form col-xs-12 col-sm-4">
        <div class="form-group">
            <label>
            <input type="checkbox" ng-model="event.tickets_published" ng-change="setTicketsPublished()">
            Published
            </label>
            <p class="help-block">Tickets are visible at the public tickets list</p>
        </div>
        <div class="form-group" ng-if="!pointOfSalesModeEnabled()">
            <label>
                <input type="checkbox" ng-model="event.tickets_visible" ng-change="setTicketsPublished()">
                Private Access
            </label>
            <p class="help-block">Tickets are visible only by the direct link</p>
        </div>
        <div class="input-group col-md-10 col-sm-12 form-group" ng-if="!event.camps_sales && !pointOfSalesModeEnabled()">
            <label>Ticketing Mode</label>
            <div class="row">
                <div class="col-xs-10">
                    <select class="form-control"
                            ng-model="event.ticketing_mode"
                            ng-options="opt.value as opt.label for opt in ticketingModeOptions">
                    </select>
                </div>
                <div class="col-xs-2" ng-if="showTicketingModeSaveButton()">
                    <button
                        class="btn btn-success"
                        ng-click="saveTicketingMode()"
                    >Save</button>
                </div>
            </div>
        </div>
        <p class="lead">{{ (event.camps_sales? "Camps" : "Tickets") +" Purchase Available"}}</p>
        <div class="input-group date-time-control col-md-8 col-sm-10">
            <label>From</label>
            <input
                type="text" 
                class="white-ro form-control pointer"
                datetime-picker="MM/dd yyyy, hh:mm a"
                is-open="utils.pickerStartOpened"
                uib-timepicker-options="utils.pickerDateStart"
                ng-click="utils.pickerStartOpened = !utils.pickerStartOpened"
                ng-model="utils.start"
                readonly>
        </div>
        <div class="input-group date-time-control col-md-8 col-sm-10">
            <label>To</label>
            <input 
                type="text" 
                class="white-ro form-control pointer"
                datetime-picker="MM/dd yyyy, hh:mm a"
                is-open="utils.pickerEndOpened"
                uib-timepicker-options="utils.pickerDateEnd"
                ng-click="utils.pickerEndOpened = !utils.pickerEndOpened"
                ng-model="utils.end"
                readonly>
        </div>
        <button ng-class="{ 'btn btn-success': true,  'vis-hidden': hideSalesSaveButton() }"
                ng-click="saveSalesDates(utils.start, utils.end)">Save</button>

        <uib-alert ng-if="utils.pageErrors.purchaseDates.length"
                   type="danger"
                   ng-repeat="e in utils.pageErrors.purchaseDates">
            <span ng-bind-html="errorText(e.text)"></span>
        </uib-alert>

        <div class="form-group" ng-if="utils.link">
            <label>Go To Tickets Purchase Page 
                <a href="{{utils.link}}" target="_blank">
                    <span class="glyphicon glyphicon-new-window"></span>
                </a>
            </label>
        </div>

        <coupon-settings event="event"
                         ng-if="isAssignedTicketsMode()"
                         update-settings="updateCouponSettings(settings)"
        ></coupon-settings>

        <vertical-insurance-settings event="event"
                         ng-if="showVerticalInsuranceSettings()"
                         update-settings="updateTicketsSettings(settings)"
        ></vertical-insurance-settings>

        <covid-settings event="event"
                        ng-if="isAssignedTicketsMode() && !pointOfSalesModeEnabled()"
                        update-tickets-settings="updateTicketsSettings(settings)"
        ></covid-settings>

        <div ng-if="showFreeTicketButton()">
            <button ng-click="openGenerateFreeTicketModal()" class="btn btn-primary">Generate VIP ticket</button>
        </div>
    </div>
    <div class="col-xs-12 col-sm-offset-1 col-sm-7">
        <div class="panel panel-default" ng-if="hasAccess() && utils.allDataLoaded">
            <div class="panel-heading">
                <h3 class="panel-title">Tickets Payment Settings</h3>
            </div>
            <div class="panel-body">
                <div class="row row-space">
                    <div class="col-xs-5 col-sm-3" ng-class="{'center-form-text': !event.block_tickets_keys_edit}">
                        <strong>{{isStripeProvider() ? 'Stripe' : 'Payment'}} Account</strong>
                    </div>
                    <div class="col-sm-7">
                        <select
                            ng-if="!event.block_tickets_keys_edit && isStripeProvider()"
                            name="stripe_account"
                            class="form-control"
                            ng-model="event.account_id"
                            ng-options="acc.account_id as accLabel(acc) for acc in stripeAccounts"
                            required
                        >
                            <option value="" ng-if="!event.account_id">Choose a Payment Account ...</option>
                        </select>
                        <select
                            ng-if="!event.block_tickets_keys_edit && !isStripeProvider()"
                            name="tilled_account"
                            class="form-control"
                            ng-model="event.tilled_account_id"
                            ng-options="acc.tilled_account_id as tilledAccountLabel(acc) for acc in tilledAccounts"
                            required
                        >
                            <option value="" ng-if="!event.account_id">Choose a Payment Account ...</option>
                        </select>
                        <select
                            ng-if="!event.block_tickets_keys_edit && !isStripeProvider()"
                            name="tilled_account"
                            class="form-control"
                            ng-model="event.tilled_account_id"
                            ng-options="acc.tilled_account_id as tilledAccountLabel(acc) for acc in tilledAccounts"
                            required
                        >
                            <option value="" ng-if="!event.account_id">Choose a Payment Account ...</option>
                        </select>
                    </div>
                    <div class="col-sm-2" ng-if="!event.block_tickets_keys_edit">
                        <button class="btn btn-success"
                            ng-if="hasPaymentAccountChanged()"
                            ng-click="savePaymentAccount()"
                        >Save</button>
                    </div>
                    <div class="col-sm-9" ng-if="event.block_tickets_keys_edit && isStripeProvider()" ng-bind="stripeAccName"></div>
                    <div class="col-sm-9" ng-if="event.block_tickets_keys_edit && !isStripeProvider()" ng-bind="tilledAccountName"></div>
                </div>
                <div class="row row-space">
                    <div class="col-xs-5 col-sm-3"><strong>SW Application Fee</strong></div>
                    <div class="col-xs-7">{{getFee()}}</div>
                </div>
                <div class="row row-space">
                    <div class="col-xs-5 col-sm-3"><strong>SW Fee Payer</strong></div>
                    <div class="col-xs-7">
                        <span ng-class="{
                            'label': true,
                            'label-info': event.sw_fee_payer !== utils.buyer,
                            'label-primary': event.sw_fee_payer === utils.buyer
                        }">
                            {{event.sw_fee_payer || utils.defaultPayer}}
                        </span>
                    </div>
                </div>
                <div class="row row-space">
                    <div class="col-xs-5 col-sm-3"><strong>{{isStripeProvider() ? 'Stripe' : 'Payment'}} Fee Payer</strong></div>
                    <div class="col-xs-7">
                        <span ng-class="{
                            'label': true,
                            'label-info': getPaymentFeePayer() !== utils.buyer,
                            'label-primary': getPaymentFeePayer() === utils.buyer
                        }">
                            {{getPaymentFeePayer() || utils.defaultPayer}}
                        </span>
                    </div>
                </div>
                <div class="row row-space">
                   <form name="utils.statementDescriptorForm">
                       <statement-descriptor-input
                           ng-if="isStripeProvider()"
                           is-required="true"
                           ng-model="event.tickets_stripe_statement"
                           has-error="utils.statementDescriptorForm.stripe_statement.$dirty && utils.statementDescriptorForm.stripe_statement.$invalid"
                           max-length="stripeStatementMaxLength"
                       ></statement-descriptor-input>
                       <statement-descriptor-input
                           ng-if="!isStripeProvider()"
                           is-required="true"
                           ng-model="event.tickets_tilled_statement"
                           has-error="utils.statementDescriptorForm.stripe_statement.$dirty && utils.statementDescriptorForm.stripe_statement.$invalid"
                           max-length="tilledStatementMaxLength"
                       ></statement-descriptor-input>
                       <div class="col-sm-2">
                           <button class="btn btn-success"
                                   ng-if="hasStatementChanged()"
                                   ng-click="saveStatement()"
                                   ng-disabled="utils.statementDescriptorForm.stripe_statement.$invalid"
                                   type="submit"
                           >Save</button>
                       </div>
                   </form>
                </div>
            </div>
        </div>
        <ticket-buy-entry-code-settings ng-if="showTicketCouponsSettings()"
                                        event="event"
                                        update-settings="updateCouponSettings(settings)"
        ></ticket-buy-entry-code-settings>
        <div class="panel panel-default" ng-if="event.use_tg && !event.camps_sales">
            <div class="panel-heading">
                <h3 class="panel-title">Tickets Guru Settings</h3>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>Tickets Purchase Passcode:</label>
                            <input type="text" class="form-control" ng-model="event.purchase_passcode" maxlength="10">
                        </div>
                        <div class="form-group">
                            <label>Tickets Refund Passcode:</label>
                            <input type="text" class="form-control" ng-model="event.refund_passcode" maxlength="10">
                        </div>
                        <button class="btn btn-success pull-right" style="min-width: 70px" ng-click="savePasscodes()">
                            {{utils.guruPassCodesSaved?'Saved!':'Save'}}
                        </button>
                    </div>
                    <div class="col-sm-6">
                        <span class="col-xs-12"><strong>Event Locations:</strong></span>
                        <span class="col-xs-12">
                            <locations-edit
                                locations="infoData.locations"
                                on-save="saveInfoData(data, callback)"
                                field="locations"
                            ></locations-edit>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <security-pins event="event"></security-pins>
    </div>
</div>

<uib-alert ng-if="utils.errors.length"
           type="danger"
           ng-repeat="e in utils.errors">
    <span ng-bind-html="errorText(e.text)"></span>
</uib-alert>

<p class="lead">Ticket Types</p>
<tickets-table
    event="event"
    ng-if="utils.allDataLoaded"
    tickets="tickets"
    is-assigned-tickets-mode="isAssignedTicketsMode()"
    changes="event.changes"
    is-camps="event.camps_sales"
    camps="camps"
    update-ticket="updateTicket(ticket, cb)"
    save-ticket="saveTicket(ticket, cb)"
    initial-price="event.purchase_date_start"
    create-change="saveTicketsChange(change, cb)"
    update-change="updateTicketsChange(data)"
    remove-change="removePriceChange(change, cb)"
    use-merchandise-sales="event.use_merchandise_sales"
></tickets-table>
<purchase-page-event-descr
    save-info-data="saveInfoData(data, callback)"
    event-description="infoData.event_description"
    kiosk-description="infoData.kiosk_description"
    hide-kiosk-description="pointOfSalesModeEnabled()"
></purchase-page-event-descr>
<p class="lead">Event Disclaimer</p>
<p class="text-info">Shows on Ticket page and at Ticket Email</p>
<html-editor-panel
    info="infoData.disclaimer"
    on-info-changed="saveInfoData(data, callback)"
    modal-title="Disclaimer Edit"
    field="disclaimer"
>
</html-editor-panel>
<h3>Purchase Form Additional Fields</h3>
<p class="text-info">Collect some extra data on tickets purchase</p>
<tickets-additional
    additional="additional"
    save="updateAdditional(callback)"
    save-new-field="createAdditional(field, callback)"
    event="event"
    ng-if="utils.allDataLoaded"
></tickets-additional>

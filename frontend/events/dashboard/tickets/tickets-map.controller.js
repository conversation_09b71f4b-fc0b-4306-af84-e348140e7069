angular.module('SportWrench').controller('Event.Tabs.TicketsMapController', TicketsMapController)

function TicketsMapController (
    $scope, $stateParams, ticketsService, MAP_MARKER_IMAGE_PATH, GMAPS_CLUSTER_IMG_PATH, uiGmapGoogleMapApi
) {
    var utils = {
        ticketsCountPerStyle: 1000
    }
    $scope.mapData = {
        map: {
            center: {
                latitude: 40.1451,
                longitude: -99.6680
            },
            zoom: 4,
            bounds: {}
        },
        mapOptions: {
            scrollwheel: false
        },
        clusterOptions: {
            maxZoom: 12,
            minimumClusterSize: 1,
            averageCenter: true,
            imagePath: GMAPS_CLUSTER_IMG_PATH,
            calculator: function (markers) {
                var totalTickets = 0, index, m = markers.dict, keys = Object.keys(m);
                for(var i = 0, l = keys.length; i < l; ++i) {                    
                    totalTickets += m[keys[i]].model.tickets_count;
                }
                index = parseInt(totalTickets / utils.ticketsCountPerStyle, 10 ) + 1
                return {
                    title: 'Sum of tickets bought at this area',
                    text: totalTickets,
                    index: (index > 5)?5:index
                };
            }
        },
        markers: []
    }

    var initializeMarkers = function (zipCodes) {
        var markers = [], zipLoc, iter = 1000;        
        for(var i = 0, l = zipCodes.length; i < l; ++i) {            
            zipLoc = zipCodes[i];
            markers.push({
                id: i + '-' + zipLoc.zip,
                latitude: zipLoc.location.latitude,
                longitude: zipLoc.location.longitude, 
                icon: MAP_MARKER_IMAGE_PATH,
                opts: {
                    title: 'Zip: ' + zipLoc.zip, 
                    cursor: 'pointer',
                    labelContent: zipLoc.tickets_count,
                    labelClass: 'marker-label',               
                    labelAnchor: ((zipLoc.tickets_count < 10)?'3':(zipLoc.tickets_count < 100)?'6':'8') + ' 18'        
                },
                tickets_count: zipLoc.tickets_count
            })
        }       
        return markers;
    }

    ticketsService.paymentsZipOnMap($stateParams.event).success(function (data) {
        $scope.mapData.markers = initializeMarkers(data.payments);
    })
}



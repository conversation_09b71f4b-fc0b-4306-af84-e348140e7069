<div class="col-xs-12 payment-emails-list" ng-if="loaded">
    <uib-accordion>
        <uib-accordion-group class="panel-default" ng-repeat="email in emails track by $index" is-open="isOpened[$index]">
            <uib-accordion-heading>
                <span class="font-bold pull-right"><b>Sent:</b> {{email.created | date : 'MMM dd, yyyy'}}</span>
                <span class="font-bold"><b>Subject:</b> {{email.email_subject}}</span>
            </uib-accordion-heading>
            <div>From: {{email.email_from}}</div>
            <div ng-if="email.cc">CC: {{email.cc}}</div>
            <div ng-if="email.bcc">BCC: {{email.bcc}}</div>
            <br/>
            <div ng-bind-html="email.email_text"></div>
        </uib-accordion-group>
    </uib-accordion>
</div>
<div class="col-xs-12 payment-send-button" ng-if="emails.length">
    <button class="btn btn-default" ng-click="changeFormVisible()">
        <i ng-class="{'fa': true, 'fa-caret-square-o-right': !sendEmailOpen,
                                  'fa-caret-square-o-down' : sendEmailOpen }"
           aria-hidden="true"></i> Send Email
    </button>
</div>
<div class="col-xs-12 payment-send-email-form" ng-if="sendEmailOpen || !emails.length">
    <form name="emailData" class="send-email-stripe form-horizontal" ng-submit="sendEmail()">
        <div class="row">
            <label class="col-xs-2 font-bold control-label">From:</label>
            <div class="col-xs-4">
                <input value="<EMAIL>" name="from" type="email" disabled class="form-control input-sm"/>
            </div>
            <label class="col-xs-2 font-bold control-label">CC:</label>
            <div class="col-xs-4">
                <input ng-model="email.cc" type="email" class="form-control input-sm" email-validator/>
            </div>
        </div>
        <div class="row">
            <label class="col-xs-2 font-bold control-label">Reply To:</label>
            <div class="col-xs-4">
                <input ng-model-options="{ allowInvalid: true}"
                       ng-model="email.replyto"
                       name="replyto"
                       type="email"
                       class="form-control input-sm"
                       email-validator
                       required/>
            </div>
            <label class="col-xs-2 font-bold control-label">BCC:</label>
            <div class="col-xs-4">
                <input ng-model="email.bcc" type="email" class="form-control input-sm" email-validator/>
            </div>
        </div>
        <div class="row">
            <label class="col-xs-2 font-bold control-label">To:</label>
            <div class="col-xs-4">
                <input ng-model-options="{ allowInvalid: true}"
                       ng-model="email.email"
                       type="email"
                       class="form-control input-sm"
                       email-validator
                       required />
            </div>
        </div>
        <div class="row">
            <label class="col-xs-2 font-bold control-label">Subject:</label>
            <div class="col-xs-4">
                <input ng-model="email.subject" class="form-control input-sm" ng-required="true"/>
            </div>
        </div>
        <div class="row">
            <label class="col-xs-2 font-bold control-label">Text:</label>
            <div class="col-xs-10">
            <textarea
                ckeditor="options"
                ng-model="email.text"
                name="content"
                id="tickets-custom-email-editor"
                required
            ></textarea>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12" style="padding-bottom: 5px">
                <button class="btn btn-primary pull-right" type="submit">Send</button>
            </div>
        </div>
    </form>
</div>

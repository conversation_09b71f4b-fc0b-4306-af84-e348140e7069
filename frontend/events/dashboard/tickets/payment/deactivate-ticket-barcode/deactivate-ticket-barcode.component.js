angular.module('SportWrench').component('deactivateTicketBarcode', {
    templateUrl: 'events/dashboard/tickets/payment/deactivate-ticket-barcode/deactivate-ticket-barcode.html',
    bindings: {
        isTicketDeactivated: '<',
        reason: '<',
        onSave: '&',
    },
    controller: Component,
});

Component.$inject = ['ACTIVATE', 'DEACTIVATE', 'moment', '$filter'];

function Component(ACTIVATE, DEACTIVATE, moment, $filter) {
    this.showSaveButton = false;
    this.notes = '';
    this.saveInProgress = false;

    this.getAction = () => {
        return this.isTicketDeactivated ? ACTIVATE : DEACTIVATE;
    };

    this.getReasonPrefix = () => {
        return this.isTicketDeactivated ? 'Deactivate reason:' : 'Activate reason:';
    };

    this.onShowSaveButton = () => {
        this.showSaveButton = true;
    };

    this.save = () => {
        if (!this.saveInProgress) {
            this.saveInProgress = true;

            const data = {
                reason: this.notes,
                datetime: Date.now(),
            };

            this.onSave({ data })
                .then(() => {
                    this.showSaveButton = false;
                    this.notes = '';
                })
                .finally(() => {
                    this.saveInProgress = false;
                })
        }
    }
}

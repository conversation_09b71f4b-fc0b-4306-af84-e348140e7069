<div class="form-group">
	<ng-transclude></ng-transclude><button class="btn btn-primary" ng-click="toggleCheckForm()">{{title}}</button>
</div>
<div class="row rowm0" ng-if="checkEntryMenu.isOpen">
	<div class="col-sm-12">
		<form class="form-horizontal" name="checkEntryMenu.checkForm">
			<div ng-class="{ 'form-group validation-required': true, 'has-error': (checkEntryMenu.formSubmitted && checkEntryMenu.checkForm.received_at.$invalid) }">
				<label class="control-label col-sm-4">Received At:</label>
				<div class="col-sm-8">
					<input 
				        type="text" 
				        name="received_at"
				        class="form-control white-ro pointer" 
				        datetime-picker="MMM dd, yyyy hh:mm a"
				        placeholder="MMM dd, yyyy hh:mm a"
				        is-open="checkEntryMenu.isPickerOpen" 
				        ng-model="check.received_at"
				        ng-click="checkEntryMenu.isPickerOpen = !checkEntryMenu.isPickerOpen"
				        readonly
				        required>
				</div>
			</div>
			<div ng-class="{ 'form-group validation-required': true, 'has-error': (checkEntryMenu.formSubmitted && checkEntryMenu.checkForm.check_num.$invalid) }">
				<label class="control-label col-sm-4">Check Num:</label>
				<div class="col-sm-8">
					<input type="text" name="check_num" class="form-control" ng-model="check.check_num" required>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-sm-4">Notes:</label>
				<div class="col-sm-8">
					<textarea class="form-control" rows="2" ng-model="check.notes"></textarea>
				</div>	
			</div>
			<button class="btn btn-default pull-right" ng-click="accept()" ng-disabled="checkEntryMenu.formSubmitted">Accept</button>
		</form>
	</div>
</div>

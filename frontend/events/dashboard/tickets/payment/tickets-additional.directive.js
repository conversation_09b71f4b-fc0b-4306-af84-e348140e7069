angular.module('SportWrench').directive('ticketsAdditional', 
function ($uibModal, INTERNAL_ERROR_MSG, ticketsAdditionalService, $stateParams, toastr) {
    const CSS_CLASSES = {
        DRAGGING_OVER             : 'dragging-row-over',
        ADDITIONAL_TABLE          : 'tickets-additional-table',
        // special class added by jquery when draggable element gets dragging
        JQUI_DRAGGABLE_DRAGGING   : '.ui-draggable-dragging'
    };
    return {
        restrict: 'E',
        scope: {
            additional: '=',
            save: '&',
            saveNewField: '&',
            event: '<',
        },
        templateUrl: 'events/dashboard/tickets/payment/tickets-additional.tmpl.html',
        link: function (scope) {
            let defaultNewField = { field: '',
                                    type: 'text', 
                                    label: '', 
                                    short_label: '', 
                                    options: {}, 
                                    required: false,
                                    qr_code_border_change_rule: {
                                        option_keys: [],
                                        border_colour: '',
                                    },
                                    show_on: { receipt: false, 
                                               purchase: false, 
                                               payment_list:false,
                                               purchase_start: false,
                                            }
                                    };
            scope.utils = {
                opts: { 
                    'text': 'Text Field',
                    'select': 'Select'
                },
                newField: {}
            };

            angular.copy(defaultNewField, scope.utils.newField)

            scope.newLineEmptyOptions = function () {
                return _.isEmpty(scope.utils.newField.options);
            }

            scope.optionsLength = function (opts) {
                return Object.keys(opts).length
            }

            scope.updateFieldType = function (cb) {
                const opts = Object.keys(scope.utils.opts);
                const notValid = _.find(
                    scope.additional,
                    (a) => !opts.includes(a.type)
                );
                if (notValid) {
                    scope.utils.error_msg = `Type must be 'select' or 'text'`;
                    return;
                }

                scope.saveData(cb);
            };

            scope.saveData = function (cb) {
                scope.save({ 
                    callback: function (err, data) {
                        if(err) {
                            scope.utils.error_msg = (err.data.validation || INTERNAL_ERROR_MSG)                            
                        } else {
                            scope.utils.error_msg = false
                        }
                        if(cb) return cb(err, data);
                    } 
                });
            }

            scope.fieldWithAssignedQRCodeBorderRule = function () {
                return _.filter(scope.additional, (field) =>
                    field.qr_code_border_change_rule && !_.isEmpty(field.qr_code_border_change_rule.option_keys)
                );
            }

            scope.showQRCodeBorderChangeRule = function (f) {
                let fieldWithRule = scope.fieldWithAssignedQRCodeBorderRule();

                let rules = [
                    /* no other additional fields with rule OR */
                    _.isEmpty(fieldWithRule) ||

                    /* there is additional field with rule, and current field is that one */
                    (!_.isEmpty(fieldWithRule) && f.field === fieldWithRule[0].field),

                    /* current additional field options are not empty */
                    Object.keys(f.options).length
                ];

                return rules.every(rule => rule);
            }

            scope.openQRCodeBorderColorRuleModal = function (field, type) {
                let { options, qr_code_border_change_rule = {} } = field;

                $uibModal.open({
                    size: 'md',
                    template: `<additional-field-qr-code-border-rule 
                                options="options" 
                                rule="rule" 
                                close="close(selected, colour)"
                               ></additional-field-qr-code-border-rule>`,

                    controller: function ($scope, $uibModalInstance) {
                        $scope.options = options;
                        $scope.rule = qr_code_border_change_rule;

                        $scope.close = function (selected = null, colour = null) {
                            if(_.isNull(selected)) {
                                $uibModalInstance.close();
                            } else {
                                field.qr_code_border_change_rule = {
                                    option_keys: angular.copy(selected),
                                    border_colour: colour
                                }
                            }

                            if(type === 'edit') {
                                scope.saveData(function (err) {
                                    if(err) {
                                        scope.utils.error_msg = (err.data.validation || INTERNAL_ERROR_MSG);

                                        field.qr_code_border_change_rule = $scope.rule;
                                    } else {
                                        $uibModalInstance.close();
                                    }
                                })
                            } else {
                                $uibModalInstance.close();
                            }
                        }
                    }
                })
            }

            scope.openOptionsModal = function (line, type) {
                $uibModal.open({
                    size: 'sm',
                    templateUrl: 'options-modal-tmpl.html',

                    controller: function ($scope, $uibModalInstance) {
                        $scope.line = line;
                        $scope.addOption = function (optName) {
                            var propName = ticketsAdditionalService.generateFieldName(optName);
                            if(!$scope.line.options[propName])
                                $scope.line.options[propName] = optName;
                            $scope.new_option = null;
                        }

                        $scope.removeOption = function (key) {
                            delete line.options[key];
                        }

                        $scope.saveChanges = function (cb) {
                            cb();
                        }

                        $scope.close = function () {
                            if(type === 'edit') {
                                scope.saveData(function (err) {
                                    if(err) {   
                                        scope.utils.error_msg = (err.data.validation || INTERNAL_ERROR_MSG)
                                    } else {
                                        $uibModalInstance.close();
                                    }
                                })
                            } else {
                                $uibModalInstance.close();
                            }
                        }
                    }
                })
            }

            scope.cancelNewField = function () {
                scope.utils.error_msg = false;
                scope.utils.newLine = false;
                scope.utils.newField = {};
                angular.copy(defaultNewField, scope.utils.newField);
            }

            scope.addField = function () {
                scope.utils.newField.field = ticketsAdditionalService.generateFieldName(scope.utils.newField.label);
                 scope.saveNewField({
                    field: scope.utils.newField,
                    callback: function (err) {
                        if(err) {
                            scope.utils.error_msg = (err.data.validation || INTERNAL_ERROR_MSG)
                        } else {
                            scope.additional.push(scope.utils.newField);
                            scope.cancelNewField();
                        }
                    }
                }) 
            }

            scope.disableSave = function () {
                var ok = (scope.utils.newField.label && scope.utils.newField.type)
                if(scope.utils.newField.type === 'select') {
                    ok = !!Object.keys(scope.utils.newField.options).length
                }
                return !ok;
            }

            scope.setAdditionalDefault = function() {
                ticketsAdditionalService.setAdditionalDefaultFields($stateParams.event)
                    .then(res => {
                        scope.additional = res;
                        toastr.success('Default Additional Fields are set');
                    });
            }

            // Draggable plugin table option
            scope.dragRowOptions = function (f, $index) {
                return {
                    index: $index,
                    animate: true,
                    deepCopy: true,
                    onStart: 'onRowDragStart(f, $index)',
                    onStop:'onRowDragStop(f, $index)'
                };
            };

             // Draggable jQuery UI options
            scope.jquiDraggableOptions = {
                appendTo: "body",
                revert: 'invalid',
                helper: 'clone',
                //snap: true
            };

            scope.onRowDragStart = function (event, ui, f, $index) {
                //add necessary classes to preserve table row formating
                angular.element(CSS_CLASSES.JQUI_DRAGGABLE_DRAGGING).addClass(CSS_CLASSES.ADDITIONAL_TABLE);
                
                /* // hide dragged row
                let attr ='#row-' + $index;
                angular.element(attr).css("display","none"); */

                //keep track of dragged row data and index
                scope.dragedItem = f;
                scope.dragedIdx = $index;
            };

            scope.onRowDragStop = function (event, ui, f, $index) {
                /* //unhide dragged row
                let attr ='#row-' + $index;
                angular.element(attr).css("display",""); */
            }
            
            // Droppable plugin table options
            scope.dropRowOptions = function (f, $index) {
                return {
                    onDrop: 'onRowDrop(f, $index)',
                    onOut: 'onDragedItemOut(f, $index)',
                    onOver: 'onDragedItemOver(f, $index)',
                    multiple: true,
                    index: $index
                };
            };

            scope.onRowDrop = function (event, ui, f, $index) {
                //remove daragedItem
                scope.additional.splice(scope.dragedIdx, 1);

                //insert into new position
                scope.additional.splice($index, 0, scope.dragedItem);

                //background color adjustment
                let attr ='#row-' + $index;
                angular.element(attr).removeClass(CSS_CLASSES.DRAGGING_OVER);

                // save data
                scope.saveData();
            };

            scope.onDragedItemOut = function(event, ui, f, $index) {
                let attr ='#row-' + $index;
                angular.element(attr).removeClass(CSS_CLASSES.DRAGGING_OVER);
            }


            scope.onDragedItemOver = function(event, ui, f, $index) {
                let attr ='#row-' + $index;
                angular.element(attr).addClass(CSS_CLASSES.DRAGGING_OVER);
            }

            scope.showSetDefaultAdditionalFieldBtn = function() {
                return !scope.additional.length && scope.event.camps_sales;
            }
        }
    }
})

<td colspan="{{vm.cols}}">
    <small>
    	<span ng-if="vm.ticket.discount">
        	<strong>Discounted:</strong> {{vm.ticket.discount | currency}}
        	<a ui-state="vm.discountState" ui-state-params="{ discount_id: vm.ticket.discount_id }" ng-if="vm.ticket.discount_id">Discont Details</a>
        </span>
        <span class="padding-sm" ng-if="vm.ticket.cancellation">
        	<strong>Cancellation: </strong> {{vm.ticket.cancellation | currency}}
        </span>
    </small>
</td>
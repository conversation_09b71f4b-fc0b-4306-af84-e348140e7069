<div class="panel panel-info">
    <div class="panel-heading">Scanner Info</div>
    <div class="panel-body">
        <div class="form-group">
            <label>Scanned At</label>
            <div class="row" ng-show="isUpdateMode">
                <div class="col-sm-12">
                <input
                    type="text"
                    class="form-control white-ro pointer"
                    datetime-picker="MMM dd, yyyy hh:mm a"
                    placeholder="MMM dd, yyyy hh:mm a"
                    is-open="picker.isOpen"
                    ng-model="picker.date"
                    ng-click="picker.toggle()"
                    readonly>
                </div>
            </div>
            <span ng-if="!isUpdateMode"><p>{{scannedText()}}</p></span>
        </div>
        <div class="form-group">
            <label>Scanner Id</label>
            <input ng-show="isUpdateMode" type="text" class="form-control" ng-model="scanner">
            <span ng-if="!isUpdateMode"><p>{{scannerText()}}</p></span>
        </div>
        <div class="form-group">
            <label>Scanner Location</label>
            <input ng-show="isUpdateMode" type="text" class="form-control" ng-model="location">
            <span ng-if="!isUpdateMode"><p>{{locationText()}}</p></span>
        </div>

        <!------------------------- Main buttons ---------------------------------->
        <div ng-if="!isUpdateMode">
            <button
                ng-if="!isScannedTicket()"
                class="btn btn-success"
                ng-click="manualScan()"
            >Manual Scan</button>

            <button
                ng-if="isScannedTicket()"
                class="btn btn-primary"
                ng-click="changeUpdateMode(true)"
            >Edit Scan Info</button>

            <button
                class="btn btn-danger"
                ng-click="manualScan(true)"
                ng-if="isScannedTicket()"
            >Undo Scan</button>
        </div>
        <!-------------------------------------------------------------------------->


        <!------------------------- Update mode buttons ---------------------------->
        <div ng-if="isUpdateMode">
            <button
                ng-if="isScannedTicket()"
                class="btn btn-success"
                ng-click="update(false, picker.toTimestamp(), true)"
                ng-disabled="!picker.date"
            >Update</button>

            <button
                ng-if="isScannedTicket()"
                class="btn btn-primary"
                ng-click="changeUpdateMode(false)"
            >Cancel</button>
        </div>
        <!-------------------------------------------------------------------------->

    </div>
    <div class="alert-block">
        <result-alert 
            type="resultAlert.type" 
            msg="resultAlert.msg">
        </result-alert>
    </div>
</div>

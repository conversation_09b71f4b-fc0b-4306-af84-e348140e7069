<div class="modal-header payment-modal-header">
    <button type="button" class="close" ng-click="$parent.$close()"><span aria-hidden="true">&times;</span></button>
    <h4>Ticket: {{formatBarcode()}} <span ng-if="isWaitlisted()" class="text-warning">WAITLISTED</span></h4>    
</div>
<div ng-class="{'tickets-payment-modal': true, 'not-loaded': !utils.isLoaded }">
     <uib-tabset class="row-space">
         <uib-tab heading="General">
            <div class="row row-space rowm0" ng-if="utils.isLoaded && utils.paymentFound">
                <div class="col-sm-6">
                    <purchaser-info payment="payment" open-ticket-modal="openTicketModal(barcode)"></purchaser-info>
                    <ticket-holder-covid-test
                        ng-if="require_covid_test"
                        tickets="payment.tickets"
                        on-update="updateCovidTest(value)"
                        barcode="payment.barcode"
                    ></ticket-holder-covid-test>
                    <coupon-info
                        coupon="payment.coupon"
                        ng-if="showCouponPanel()"
                    ></coupon-info>
                </div>
                <div class="col-sm-6">
                    <tickets-refund ng-if="showTicketsRefund()" payment="payment"></tickets-refund>
                    <waitlist-items ng-if="isWaitlisted()" payment="payment"></waitlist-items>
                    <scanner-info
                        ng-if="showScannerInfo()"
                        scanner="payment.scanner_id"
                        scanned="payment.scanned_at"
                        location="payment.scanner_location"
                        barcode="barcode"
                    ></scanner-info>
                    <redeem-tickets
                        ng-if="showRedeemTickets()"
                        tickets="payment.tickets"
                        type="{{payment.sales_type}}"
                    ></redeem-tickets>
                    <deactivate-ticket-barcode
                        ng-if="showDeactivateTicketBarcode()"
                        is-ticket-deactivated="payment.is_ticket_deactivated"
                        reason="payment.deactivate_reason"
                        on-save="deactivateTicketBarcode(data)"
                    ></deactivate-ticket-barcode>
                    <request-refund
                        ng-if="showRequestRefund()"
                        refund-request-at="payment.refund_request_at"
                        refund-request-reason="payment.refund_request_reason"
                    ></request-refund>
                    <change-event-ticket-type-form
                        ng-if="showChangeEventTicketTypeForm()"
                        ticket="payment"
                        on-update="eventTicketChanged()"
                    ></change-event-ticket-type-form>
                </div>
            </div>
            <div class="row rowm0" ng-if="utils.isLoaded && utils.paymentFound && payment.history">
                <div class="col-sm-12">
                     <div class="panel panel-info">
                         <div class="panel-heading">History</div>
                         <div class="panel-body" ng-bind-html="payment.history"></div>
                     </div>
                </div>
            </div> 
            <spinner active="utils.isLoaded === false"></spinner>
         </uib-tab>
         <uib-tab heading="Types Change" ng-if="payment.sales_type === 'camps' && payment.status !== 'canceled' && !isWaitlisted()">
            <div class="row rowm0 row-space">
                <div class="col-sm-12">
                    <camp-change
                        receipt="payment.tickets"
                        price="payment.amount"
                        payment="payment"
                    ></camp-change>
                </div>
            </div>
         </uib-tab>
         <uib-tab heading="History" select="loadHistory()">
            <div class="row rowm0 row-space" ng-if="!utils.historyLoading">
                <div class="col-sm-12">
                    <ticket-payment-history history="utils.history" add-note="addNote(notes)"></ticket-payment-history>
                </div>
            </div>
            <spinner active="utils.historyLoading"></spinner>
         </uib-tab>
         <uib-tab heading="Stripe Info" ng-if="isStripe()">
             <ticket-stripe-info payment="payment"></ticket-stripe-info>
         </uib-tab>
     </uib-tabset>
</div>
<uib-alert type="{{utils.msg_type}}" ng-if="utils.msg_text && utils.msg_type">{{utils.msg_text}}</uib-alert>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="$parent.$close()">Close</button>
</div>

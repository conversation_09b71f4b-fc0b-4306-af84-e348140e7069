<div class="modal-header text-center">
    <h4>Please, select dates when ticket {{$ctrl.ticketName}} will be valid</h4>
</div>
<div class="modal-body">
    <uib-alert type="info" ng-if="$ctrl.disabled">Dates can't be changed. Ticket sales already started.</uib-alert>
    <div ng-class="{'form-group': true, 'has-error': $ctrl.error }">
        <div class="checkbox">
            <label>
                <input type="checkbox"
                       ng-model="$ctrl.allDays.selected"
                       ng-change="$ctrl.toggleAll()"
                       ng-disabled="$ctrl.disabled"
                > All Days
            </label>
        </div>
    </div>
    <div ng-class="{'form-group': true, 'has-error': $ctrl.error }">
        <div class="checkbox" ng-repeat="day in $ctrl.eventDays">
            <label>
                <input type="checkbox"
                       ng-model="$ctrl.days[day]"
                       ng-change="$ctrl.onCheckboxChange(day)"
                       ng-disabled="$ctrl.disabled"
                >
                {{day | UTCdate: 'ddd, MMM D'}}
            </label>
        </div>
    </div>
    <uib-alert type="danger" ng-if="$ctrl.error" ng-bind="$ctrl.error"></uib-alert>
</div>
<div class="modal-footer">
    <button class="btn btn-default" type="button" ng-click="$ctrl.close()">Cancel</button>
    <button class="btn btn-success" type="button" ng-click="$ctrl.submit()" ng-if="!$ctrl.disabled">Save</button>
</div>

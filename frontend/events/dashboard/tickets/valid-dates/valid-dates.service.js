angular.module('SportWrench').service('ValidDatesService', ['$uibModal', ValidDatesService]);

function ValidDatesService ($uibModal) {
    this.$uibModal = $uibModal;
}

ValidDatesService.prototype.openValidDatesModal = function (ticket, eventDates, tickets, updateTicket) {
    let self = this;

    this.$uibModal.open({
        template    : `<valid-dates 
                        ticket-name="ticketName" 
                        valid-dates="validDates"
                        event-days="eventDates"
                        on-save="onSave(validDates)"
                        disabled="isTicketTypePaid"
                        close="close()"
                        ></valid-dates>`,
        size        : 'sm',
        controller  : [
            '$scope', '$uibModalInstance', 'ConfirmationService',
            function ($scope, $uibModalInstance, ConfirmationService) {
                $scope.ticketName       = ticket.label;
                $scope.validDates       = angular.copy(ticket.valid_dates);
                $scope.isTicketTypePaid = ticket.is_ticket_purchased;
                $scope.eventDates       = eventDates;
                $scope.onSave           = async function (validDates) {
                    let duplicate = self.checkValidDatesDuplicates(tickets, validDates, ticket);

                    if(duplicate) {
                        let answer = await ConfirmationService.ask(
                            'Are you sure you want to create multiple daily tickets with the same available date for scanning?',
                            {
                                title: 'Confirm',
                                disableNoBtn: true
                            }
                        )

                        if(answer !== ConfirmationService.YES_RESP) {
                            return;
                        }
                    }

                    let temp         = _.clone(ticket);
                    temp.valid_dates = validDates;

                    self.formatValidDates(temp);

                    if(ticket.event_ticket_id) {
                        updateTicket(_.omit(temp, 'bought', 'sw_fee'), function (err) {
                            if(!err) {
                                ticket.valid_dates = validDates;
                                $uibModalInstance.close();
                            }
                        });
                    } else {
                        ticket.valid_dates = validDates;
                        $uibModalInstance.close();
                    }
                }

                $scope.close = function() {
                    $uibModalInstance.close();
                }
        }]
    })
}

ValidDatesService.prototype.checkValidDatesDuplicates = function (tickets, validDates, originTicket) {
    if(_.isEmpty(validDates)) {
        return;
    }

    let duplicate = null;

    _.forEach(tickets, ticket => {
        let isNotSameTicket = _.isUndefined(originTicket.event_ticket_id)
            ? ticket.position !== originTicket.position
            : ticket.event_ticket_id !== originTicket.event_ticket_id;

        if(isNotSameTicket && this.__validDatesAreEqual(ticket.valid_dates, validDates)) {
            // if ticket hasn't label yet, show its position in list
            duplicate = ticket.label || `ticket at position #${ticket.position + 1}`;
        }
    })

    return duplicate;
}

ValidDatesService.prototype.__validDatesAreEqual = function (validDatesOne, validDatesTwo) {
    let datesOne = validDatesOne || [];
    let datesTwo = validDatesTwo;

    if(datesOne.length === datesTwo.length) {
        let difference = datesOne.filter(date => !datesTwo.includes(date));

        if(!difference.length) {
            return true;
        }
    }

    return false;
}

ValidDatesService.prototype.formatValidDates = function (ticket) {
    if(ticket.valid_dates && ticket.valid_dates.length) {
        ticket.valid_dates = ticket.valid_dates.reduce((all, day) => {
            all[day] = true;
            return all;
        }, {});
    } else {
        ticket.valid_dates = {};
    }
}

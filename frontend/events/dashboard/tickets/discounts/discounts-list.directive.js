angular.module('SportWrench').directive('ticketsDiscountsList', function (
$uibModal, ticketsService, $stateParams, toastr, CKEDITOR_OPTIONS, SAVED_MSG, 
AEMSendDialogService, eventDashboardService) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'events/dashboard/tickets/discounts/discounts-list.html',
        link: function (scope) {
            var eventId         = $stateParams.event,
                currentEvent    = eventDashboardService.getEvent(),
                defaultLimit    = 100;

            scope.data = {
                discountsList: [],
                total: 0
            };

            scope.utils = {
                loading: false,
                selection: {
                    items: {},
                    all: false,
                    selectedItemsCount: 0
                }
            };

            scope.filters = {
                offset: 0,
                order: 'last',
                reverse: true
            };
            scope.defaultLimit  = defaultLimit;
            scope.pageNum       = parseInt((scope.filters.offset / defaultLimit), 10) + 1;

            __loadDiscountsList();

            scope.rowClass = function (item) {
                return {
                    'text-grey': (item.used === item.quantity)
                };
            };

            scope.openImportMenu = function () {
                $uibModal.open({
                    templateUrl: 'events/dashboard/tickets/discounts/import.html',

                    controller: function ($uibModalInstance) {
                        var self            = this;
                        this.ticketTypes    = [];
                        this.skip_first     = true;

                        ticketsService.ticketsListShort($stateParams.event).success(function (data) {
                            self.ticketTypes = data.tickets;
                            if(self.ticketTypes.length === 1) {
                                self.ticket = self.ticketTypes[0].id;
                            }
                        });

                        this.importDiscountsList = function () {
                            if(this.importForm.$invalid) {
                                toastr.warning('Invalid form data');
                                return;
                            }
                            var file = _.first(angular.element("[name='discounts-list']")[0].files);                   
                            if(!file) {
                                toastr.warning('Choose a file to import');
                                return;
                            }
                            ticketsService.importDiscountsList($stateParams.event, this.ticket, file, {
                                amount      : this.amount,
                                skip_first  : this.skip_first,
                                type        : this.type
                            })
                            .success(function () {
                                toastr.success('Successfully imported');
                                $uibModalInstance.close();
                            });
                        };
                    },
                    controllerAs: 'vm'
                });
            };

            scope.openEmailMenu = function () {                
                var selected    = __countSelected(),
                    qty         = (scope.utils.selection.all || (selected === 0))?scope.data.total:selected;

                if (qty === 0) {
                    toastr.warning('No Recepients');
                    return;
                }

                var discounts = (qty === scope.data.total)
                                    ? null
                                    : __getSelectedItems();

                AEMSendDialogService
                .openDialog({
                    eventID     : $stateParams.event, 
                    filters     : angular.extend({ discounts: discounts }, scope.filters),
                    replyTo     : currentEvent.email,
                    lettersQty  : qty,
                    section     : 'tickets',
                    group       : 'discounts'
                }).catch(function (err) {
                    if (err instanceof Error) {
                        console.error(err);
                    }
                });
            };

            scope.createMenu = function () {
                $uibModal.open({
                    template    : '<modal-wrapper><discount-info-form></discount-info-form></modal-wrapper>',
                    controller  : ['$scope', function ($scope) {
                        $scope.isFormCreateMode = true;
                        $scope.modalTitle       = '<h4>Create Coupon</h4>';
                        $scope.discountInfo     = {};
                        $scope.ticketTypes      = [];

                        ticketsService.ticketsListShort($stateParams.event).success(function (data) {
                            $scope.ticketTypes = data.tickets;
                            if($scope.ticketTypes.length === 1) {
                                $scope.discountInfo.ticket_id = $scope.ticketTypes[0].id;
                            }
                        });

                        $scope.sendFormData     = function () {
                            return ticketsService.createDiscount(
                                $stateParams.event, 
                                _.omit($scope.discountInfo, 'is_free')
                            ).then(function () {
                                toastr.success(SAVED_MSG);
                                $scope.$close(true);
                            });
                        };
                    }]
                }).result.then(function (reload) {
                    if(reload) {
                        __loadDiscountsList();
                    }
                });
            };

            scope.toggleAllItems = function () {
                if(scope.utils.selection.all) {
                    for(var i = 0, l = scope.data.discountsList.length; i < l; ++i) {
                        scope.utils.selection.items[scope.data.discountsList[i].id] = true;
                    }
                } else {
                    for(var id in scope.utils.selection.items) {
                        scope.utils.selection.items[id] = false;
                    }
                }
                scope.utils.selection.selectedItemsCount = scope.data.total;
            }

            scope.toggleItem = function (id) {
                var isChecked = scope.utils.selection.items[id];
                if(scope.utils.selection.all && !isChecked)
                    scope.utils.selection.all = false;
                scope.utils.selection.selectedItemsCount = __countSelected();
            }

            scope.changePage = function (page) {
                this.filters.offset = (Number(page) - 1) * defaultLimit;

                this.pageNum = page;
                __loadDiscountsList();
            }

            scope.order = function (col) {
                if(this.filters.order === col) {
                    this.filters.reverse = !this.filters.reverse;
                }
                this.filters.order = col;
                __loadDiscountsList();
            }

            scope.reloadList = function () {
                scope.utils.selection.all = false;
                scope.toggleAllItems();
                __loadDiscountsList();
            }

            scope.getInfo = function (d) {
                $uibModal.open({
                    template: 
                        '<discount-info discount="' + d.id + '" event="' + $stateParams.event + '"></discount-info>',
                    size: 'lg'
                }).result.then(function (reload) {
                    if(reload) {
                        __loadDiscountsList();
                    }
                })
            }

            if(!!$stateParams.discount_id) {
                scope.getInfo({ id: $stateParams.discount_id })
            }

            function __loadDiscountsList () {
                scope.utils.loading = true;
                ticketsService.loadDiscountsList(eventId, scope.filters).success(function (data) {
                    scope.data.discountsList = data.discounts;
                    scope.data.total = data.count;
                    scope.toggleAllItems();
                    scope.utils.selection.selectedItemsCount = data.count;
                }).finally(function () {
                    scope.utils.loading = false;
                });
            }

            function __countSelected () {
                if(scope.utils.selection.all)
                    return scope.utils.selection.selectedItemsCount;
                var selected = __getSelectedItems().length;
                return (selected === 0)?scope.data.total:selected;
            }

            function __getSelectedItems () {
                return _.keys(
                        _.pick(scope.utils.selection.items, function (value) {
                            return (value === true);
                        })
                )
            }
        }
    }
})

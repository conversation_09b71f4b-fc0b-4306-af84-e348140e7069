<form class="form-horizontal" name="discountInfoForm" ng-submit="submit()">
	<div class="form-group" ng-if="!isFormCreateMode">
		<label class="control-label col-sm-4">Notified At</label>
		<div class="col-sm-8 center-form-text">{{discountInfo.notified_at}}</div>
	</div>
	<div ng-class="{ 'form-group': true, 'has-error': discountInfoForm.first.$invalid }">
		<label class="control-label col-sm-4">First</label>
		<div class="col-sm-8">
			<input type="text" name="first" class="form-control" ng-model="discountInfo.first">
			<form-error-block message="{{errors.first}}" ng-if="discountInfoForm.first.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group': true, 'has-error': discountInfoForm.last.$invalid }">
		<label class="control-label col-sm-4">Last</label>
		<div class="col-sm-8">
			<input type="text" name="last" class="form-control" ng-model="discountInfo.last">
			<form-error-block message="{{errors.last}}" ng-if="discountInfoForm.last.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.email.$invalid }">
		<label class="control-label col-sm-4">Email</label>
		<div class="col-sm-8">
			<input type="email" name="email" class="form-control" ng-model="discountInfo.email" email-validator>
			<form-error-block message="{{errors.email}}" ng-if="discountInfoForm.email.$error.serverValidation"></form-error-block>
			<form-error-block message="Invalid Email" ng-if="discountInfoForm.email.$error.email"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.code.$invalid }">
		<label class="control-label col-sm-4">Coupon</label>
		<div class="col-sm-8">
			<input type="text" name="code" class="form-control" ng-model="discountInfo.code">
			<form-error-block message="{{errors.code}}" ng-if="discountInfoForm.code.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.max_count.$invalid }">
		<label class="control-label col-sm-4">Available</label>
		<div class="col-sm-8">
			<input type="number" name="max_count" class="form-control" ng-model="discountInfo.max_count">
			<form-error-block message="{{errors.max_count}}" ng-if="discountInfoForm.max_count.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.used_count.$invalid }" ng-if="!isFormCreateMode">
		<label class="control-label col-sm-4">Used</label>
		<div class="col-sm-8">
			<input type="number" name="used_count" class="form-control" ng-model="discountInfo.used_count">
			<form-error-block message="{{errors.used_count}}" ng-if="discountInfoForm.used_count.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.amount.$invalid }">
		<label class="control-label col-sm-4">Amount</label>
		<div class="col-sm-3">
			<label class="checkbox pull-right">
				<input type="checkbox" name="amount" ng-model="discountInfo.is_free" ng-change="freeModeChanged()" ng-disabled="!isFormCreateMode"> Is free
			</label>
		</div>
		<div class="col-sm-5" ng-if="!discountInfo.is_free">
			<input type="number" name="amount" class="form-control" min="0" ng-model="discountInfo.amount" ng-disabled="!isFormCreateMode">
		</div>
		<div class="row" ng-if="discountInfoForm.amount.$error.serverValidation">
			<div class="col-sm-offset-4 col-sm-8">
				<form-error-block message="{{errors.amount}}"></form-error-block>
			</div>
		</div>
	</div>
	<div ng-class="{ 'form-group validation-required': true, 'has-error': discountInfoForm.ticket_id.$invalid }" ng-if="ticketTypes && ticketTypes.length">
		<label class="control-label col-sm-4">Ticket Type:</label>
		<div class="col-sm-8">
			<select class="form-control"
                    ng-model="discountInfo.ticket_id"
                    name="ticket_id"
                    ng-options="t.id as t.label for t in ticketTypes"
                    >
                <option value="">Choose a ticket type</option>
            </select>
            <form-error-block message="{{errors.ticket_id}}" ng-if="discountInfoForm.ticket_id.$error.serverValidation"></form-error-block>
		</div>
	</div>
	<button class="btn btn-primary pull-right" type="submit">Save</button>
	<button class="btn btn-danger pull-right" type="button" sw-confirm="Are you sure you want to remove this discount?" sw-confirm-do="removeDiscount" sw-confirm-hide-no ng-if="showRemoveBtn()">Remove</button>
</form>
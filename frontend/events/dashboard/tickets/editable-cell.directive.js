angular.module('SportWrench').directive('editableCell', function (
    $filter, UtilsService, TICKET_BARCODE_BACKGROUND_COLOR, MERCHANDISE_TYPE
) {
    return {
        restrict    : 'E',
        scope: {
            val         : '=',
            valStr      : '@?',
            editing     : '@',
            prevPrice   : '@',
            type        : '@',
            disabled    : '@',
            ctrlType    : '@',
            elType      : '@',
        },
        templateUrl : 'events/dashboard/tickets/editable-cell.html',
        replace     : true,
        link: function (scope) {
            var currencyFilter  = $filter('currency');
            scope.isEditing     = false;
            scope.inputType     = scope.ctrlType || 'text';
            scope.ticketBarcodeBackgroundColor = TICKET_BARCODE_BACKGROUND_COLOR;
            scope.merchandiseTypes = MERCHANDISE_TYPE;

            scope.utils = {
                val: scope.val,
            }

            // set default value for sup-type select
            if (scope.elType === 'select' && scope.type === 'sub-type') {
                scope.utils.val = scope.val || 'default';
            }

            // set default value for checkbox = true
            if (scope.elType === 'checkbox' && typeof scope.utils.val === 'undefined') {
                scope.utils.val = true || scope.val;
            }

            scope.priceType = function () {
                return scope.type === 'price';
            }

            scope.priceAndWlType = function () {
                return scope.type === 'price' || scope.type === 'wl';
            }

            scope.getValue = function () {
                if(scope.valStr) {
                    return scope.valStr;
                }
                if(scope.type === 'price') {
                    var p = Number(scope.val) >= 0 ? scope.val : scope.prevPrice;
                    return currencyFilter(p, '$', 2);
                }
                if (scope.elType === 'select' && scope.val) {
                    return UtilsService.capitalizeFirstLetter(scope.val);
                }
                if (scope.elType === 'checkbox') {
                    return scope.val ? 'Yes' : 'No';
                }
                if (scope.elType === 'select' && !scope.val && scope.type === 'border-color') {
                    return 'Without border';
                }
                return scope.val;
            }

            scope.enableEditing = function () {
                if(scope.disabled == 'true') return;
                scope.isEditing = true;
            }

            scope.disableEditing = function () {
                scope.isEditing = false;
            }

            scope.getPlaceholder = function () {
                if(scope.type === 'title') return 'Ticket label ...';
                else if(scope.type === 'title-sm') return 'Short label ...';
                else if(scope.type === 'price') return '00.00';
                return '';
            }

            scope.showEditIcon = function () {
                return !!(Number(scope.val) >= 0 ? true : scope.prevPrice);

            }

            scope.isLabel = function() {
                return scope.type === 'title';
            }

            scope.isShortLabel = function() {
                return scope.type === 'title-sm';
            }

            scope.isNotLabel = function() {
                const types = ['title', 'title-sm', 'ticket-type', 'sub-type', 'checkbox', 'border-color'];

                return types.every(type => scope.type !== type);
            }

            scope.$watch('editing', function (val) {
                if(!val) return;
                if(val === 'false')
                    scope.disableEditing();
                else if(val === 'true' || val === 'create')
                    scope.enableEditing();
            });

            scope.onChange = function() {
                scope.val = scope.utils.val;
            }

            scope.isNotEmptyValue = function(value) {
                return !!value;
            }

            scope.isBorderColor = function() {
                return scope.type === 'border-color';
            }
        }
    }
})

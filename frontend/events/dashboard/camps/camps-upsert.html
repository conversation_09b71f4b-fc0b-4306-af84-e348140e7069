<div class="modal-header text-info text-center">
    <h2> {{ title }} Camp </h2>
</div>
<div class="modal-body">
   <form role="form" class="form-horizontal row-space" name="campForm">
    <!-- Visibility START -->
       <div class="form-group validation-required">
           <label class="col-sm-3 control-label">Visibility</label>
           <div class="col-sm-7">
               <label class="radio-inline">
                   <input type="radio" value="hidden" name="visibility_types" ng-model="camp.visibility">
                   Hidden
               </label>
               <label class="radio-inline">
                   <input type="radio" value="eo" name="visibility_types" ng-model="camp.visibility">
                   EO Only
               </label>
               <label class="radio-inline">
                   <input type="radio" value="published" name="visibility_types" ng-model="camp.visibility">
                   Published
               </label>
           </div>
       </div>
    <!-- Visibility END -->
    <!-- Name START -->
       <div class="form-group validation-required">
           <label for="camp_name" class="col-sm-3 control-label">Name</label>
           <div class="col-sm-7">
               <input type="text" 
                      name="camp_name" 
                      class="form-control"
                      ng-model="camp.name"
                      required
                      ng-style="campForm.camp_name.$touched 
                              && campForm.camp_name.$invalid 
                              && {'border':'solid 1px red'}"
                      >
           </div>
           <div class="col-sm-7 col-sm-offset-3" ng-if="campForm.camp_name.$touched && campForm.camp_name.$invalid">
                <p class="text-info" ng-style="{'color':'red'}"> 
                    <i class="fa fa-exclamation-triangle"></i>
                    Name is required
                </p>
           </div>         
       </div>
    <!-- Name END -->
    <!-- Description START -->
       <div class="form-group validation-required">
           <label for="camp_description" class="col-sm-3 control-label">Description</label>
           <div class="col-sm-7">
               <textarea type="text"
                      rows = "3" 
                      name="camp_description" 
                      class="form-control"
                      ng-model="camp.description"
                      required
                      ng-style="campForm.camp_description.$touched  
                              && campForm.camp_description.$invalid 
                              && {'border':'solid 1px red'}"
                      ></textarea>
           </div>
           <div class="col-sm-7 col-sm-offset-3" ng-if="campForm.camp_description.$touched  && campForm.camp_description.$invalid ">
                <p class="text-info" ng-style="{'color':'red'}"> 
                    <i class="fa fa-exclamation-triangle"></i>
                    Description is required
                </p>
           </div> 
        </div>
    <!-- Description `END -->
    <!-- Date Start START -->
        <div class="form-group validation-required">
           <label class="col-sm-3 control-label">Date Start</label>
           <div class="col-sm-5">
                <date-time-form-control 
                    date="camp.date_start" 
                    format="MM/dd/yyyy HH:mm"
                    name="date_start"
                    field-required="true"
                    required
                    ng-style="campForm.date_start.$touched  
                            && campForm.date_start.$invalid 
                            && {'border':'solid 1px red', 'border-radius':'4px'}"
                    >
                </date-time-form-control>
           </div>
          <div class="col-sm-7 col-sm-offset-3" ng-if="campForm.date_start.$touched  && campForm.date_start.$invalid ">
                <p class="text-info" ng-style="{'color':'red'}"> 
                    <i class="fa fa-exclamation-triangle"></i>
                    Date Start is required
                </p>
           </div> 
        </div>
    <!-- Date Start END -->
    <!-- Date End START -->
        <div class="form-group">
           <label class="col-sm-3 control-label">Date End</label>
           <div class="col-sm-5">
                <date-time-form-control 
                    date="camp.date_end" 
                    format="MM/dd/yyyy HH:mm"
                    deftime="23:59:59"
                    name="date_end"
                    >
                </date-time-form-control>
           </div>
        </div>
    <!-- Date End END -->
    <!-- Age From/To START -->
        <div class="form-group">
           <label class="col-sm-3 control-label">Age From</label>
           <div class="col-sm-2">
               <input type="number"
                      name="age_from" 
                      min = "0" max="99"
                      class="form-control"
                      ng-model="camp.age_from"
                      ng-style="campForm.age_from.$touched  
                            && campForm.age_from.$invalid 
                            && {'border':'solid 1px red', 'border-radius':'4px'}"
                      >
           </div>   
           <label class="col-sm-1 control-label">To</label>
           <div class="col-sm-2">
               <input type="number" 
                      name="age_to" 
                      min = "0" max="99"
                      class="form-control"
                      ng-model="camp.age_to"
                      ng-style="campForm.age_to.$touched  
                            && campForm.age_to.$invalid 
                            && {'border':'solid 1px red', 'border-radius':'4px'}"
                      >
           </div>     
       </div>
    <!-- Age From/To END -->
    <!-- Age Date START -->
        <div class="form-group">
            <label class="col-sm-3 control-label">Age Date</label>
            <div class="col-sm-5">
                <date-time-form-control date="camp.age_date" format="MM/dd/yyyy HH:mm" name="age_date">
                </date-time-form-control>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-9 col-sm-offset-3">
                <p ng-if="camp.date_start">
                    Set to:
                    <span ng-if="!isAgeDateEqualTo(camp.date_start)">
                        <a href="" style="cursor:pointer;" ng-click="setCampAgeDateTo(camp.date_start)">Date Start</a>
                    </span>
                    <span ng-if="!isAgeDateEqualTo(camp.date_start) && !isAgeDateEqualTo(null)">
                    or
                    </span>
                    <span ng-if="!isAgeDateEqualTo(null)">
                        <a href="" style="cursor:pointer;" ng-click="setCampAgeDateTo(null)">Blank to Use Event Age Date</a>
                    </span>
                </p>
            </div>
        </div>
        <div class="form-group">
            <p class="col-sm-9 col-sm-offset-3 camp-text-warning">
                This date is used to caclulate participants age. </br>
                Leave it blank to use Event's Age Date: <u>{{camp.event_age_date | date }}.</u>
            </p>
        </div>
    <!-- Age Date END -->
    </form>
</div>

<div class="modal-footer">

    <button
        class="btn btn-danger pull-left"
        ng-if="camp.id"
        sw-confirm="Do you really want to remove camp?"
        sw-confirm-do="delete"
        sw-confirm-hide-no
    >Delete</button>

    <button class="btn btn-info pull-left" ng-click="duplicate()" ng-if="camp.id">Duplicate</button>
    <a class="btn btn-default" ng-click="$dismiss()">Cancel</a>
    <a class="btn btn-primary" 
       ng-click="save()" 
       ng-disabled="campForm.$invalid"
       >Save</a>
</div>

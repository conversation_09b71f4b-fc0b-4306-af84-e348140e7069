<div ng-if="data.events.length">
    <div class="form-group" ng-if="data.events.length > 4">
        <div class="input-group">
          <div class="input-group-addon"><i class="fa fa-search"></i></div>
          <input type="text" class="form-control" ng-model="data.search" placeholder="event name, city, state abbrev"> 
        </div>
    </div>
    <table class="table table-condensed pointer events-list-table">  
        <ng-include src="data.tableHeaderTemplate" ng-if="data.tableHeaderTemplate"></ng-include>
        <thead class="unbordered">
            <tr>
                <th class="date-col">Date</th>
                <th class="name-col">Name</th>
                <th class="city-col hidden-xs">City</th>
                <th class="state-col">State</th>
                <th class="stub-col" ng-if="data.filtered.length > 4"></th>              
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan="{{data.colspan}}" class="scroll-container">
                    <div scroll="data.scrollBarConfig">
                        <table class="table table-condensed scroll-table">
                            <tbody>                        
                                <tr ng-repeat="e in data.filtered = (data.events | filter:data.eventsFilter)" 
                                    ng-click="data.open(e.event_id, e.type, e.link)"                            
                                    ng-class="data.rowClass(e)">
                                    <td class="date-col">{{data.dateFormatter(e.date_start, e.type)}}</td>
                                    <td class="name-col" ng-bind="e.long_name"></td>
                                    <td class="city-col hidden-xs" ng-bind="e.city"></td>
                                    <td class="state-col" ng-bind="e.state"></td>
                                </tr>
                            </tbody>
                        </table>                     
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<uib-alert type="info" ng-if="!data.events.length">
    <div class="row">
        <div class="col-xs-12 text-center" ng-bind="data.noEventsMessage"></div>
    </div>
</uib-alert>
<script type="text/ng-template" id="spectator-table-heading.html">
<p class="text-center"><i>Usually available 3 Days prior to the start of the event.</i></p>
<p class="text-center"><b>Please use Chrome or Firefox as your Internet Browser to purchase tickets.</b></p>
</script>


<form class="form-horizontal">    
    <fieldset ng-disabled="member.deleted_by_user" ng-if="!rosterLocked">
        <!-- <PERSON><PERSON>'s Role -->
        <div class="form-group form-group-sm row-sm-pd">
            <label class="col-sm-4 control-label">Current Season Role</label>
            <div class="col-sm-4">
                <select class="form-control"
                        ng-model="utils.defaultRole"
                        ng-options="r as r.name for r in roles"
                        ng-change="defaultRoleChange()">
                </select>
                <small class="help-block" ng-if="member.default_role_id !== initialMember.default_role_id">Initial Value: <b>{{initialMember.default_role_name}}</b></small>
            </div>
        </div>
        <div class="form-group form-group-sm row-sm-pd">
            <div class="col-sm-7">
                <label class="checkbox-inline to-right">
                    <input type="checkbox" ng-model="utils.overwrite_staff_role"> Change Role for this Event only {{eventName}}
                </label>
            </div>
            <div class="col-sm-4" ng-if="utils.overwrite_staff_role">
                <select class="form-control"
                        ng-model="utils.role"
                        ng-options="r as r.name for r in roles"
                        ng-change="roleChange()">                    
                </select>
                <small class="help-block" ng-if="member.role_id !== initialMember.role_id">Initial Value: <b>{{initialMember.role_name}}</b></small>
            </div>
        </div>
        <div class="well well-sm">Staffer's Role for this Event: <b>{{(utils.overwrite_staff_role && member.role_id)?member.role_name:member.default_role_name}}</b></div>
        <!-- Staffer's Role End -->
        <!-- Staffer's Primary Team -->
        <div class="form-group form-group-sm row-sm-pd">
            <label class="col-sm-5 control-label">Current Season Primary Team</label>
            <div class="col-sm-6">
                <select class="form-control"
                        ng-model="member.primary_master_team"
                        ng-options="t as teamLabel(t) for t in member.master_teams track by t.master_team_id"
                >
                </select>
                <small class="help-block" ng-if="member.primary_master_team.master_team_id !== initialMember.primary_master_team.master_team_id">Initial Team: <b>{{initialMember.primary_master_team.name || 'N/A'}}</b></small>
            </div>
        </div>
        <div class="form-group form-group-sm row-sm-pd">
            <div class="col-sm-offset-1 col-sm-11">
                <label class="checkbox-inline">
                    <input type="checkbox" ng-model="utils.overwrite_primary_team"> Change Primary Team for this Event only {{eventName}}
                </label>
            </div>
        </div>
        <div class="form-group form-group-sm row-sm-pd">
            <div class=" col-sm-offset-6 col-sm-6" ng-if="utils.overwrite_primary_team">
                <select class="form-control"
                        ng-model="member.primary_roster_team"
                        ng-options="t as teamLabel(t) for t in member.roster_teams track by t.roster_team_id"
                >
                </select>
                <small class="help-block" ng-if="member.primary_roster_team.roster_team_id !== initialMember.primary_roster_team.roster_team_id">Initial Team: <b>{{initialMember.primary_roster_team.name || 'N/A'}}</b></small>
            </div>
        </div>
        <div class="well well-sm">Staffer's Primary Team for this Event is: <b>{{teamLabel((utils.overwrite_primary_team && member.primary_roster_team.roster_team_id)?member.primary_roster_team:member.primary_master_team)}}</b></div>
        <!-- Staffer's Primary Team End -->
    </fieldset>  
    <fieldset ng-if="rosterLocked">
        <div class="form-group">
            <label class="control-label col-xs-3">Team:</label>
            <div class="col-xs-9 center-form-text">
                {{teamName}}
            </div>
            <label class="control-label col-xs-3">Is Primary:</label>
            <div class="col-xs-9 center-form-text">
                <span class="label label-success" ng-if="member.primary">Yes</span>
                <span class="label label-warning" ng-if="!member.primary">No</span>
            </div>
            <label class="control-label col-xs-3">Role:</label>
            <div class="col-xs-9 center-form-text">{{(member.role_id)?member.role_name:member.default_role_name}}</div>
        </div>
    </fieldset> 
    <div class="form-group" ng-if="member && member.roster_teams">
        <div class="col-sm-12">
            <h5>All Staffer's Roles For This Event</h5>
            <div class="table-wrapper">
                <table class="table table-condensed">
                    <thead>
                        <tr>
                            <th>Team Name</th>
                            <th>Code</th>
                            <th>Role</th>
                            <th>Primary</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="t in member.roster_teams" ng-class="{ 'text-grey striked-out': t.removed }">
                            <td ng-bind="t.name"></td>
                            <td ng-bind="t.usav_code"></td>
                            <td>{{t.role_name}}</td>
                            <td><i class="fa fa-check text-success" ng-if="t.result_primary"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>   
    <uib-alert type="warning">
        <h5><i class="fa fa-exclamation-triangle"></i> Important Wristband Admission Information</h5>
        <p>While one adult can have roles on multiple teams, they will only receive an admission wristband for the team on which they are assigned a PRIMARY role. Per team limits on staff wristbands still apply</p>
    </uib-alert>
</form>

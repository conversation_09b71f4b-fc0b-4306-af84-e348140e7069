<inline-edit on-toggle="aauJerseyEditToggle(isEdit, m)" block-edit="block">
    <edit-value>
        <span 
            ng-class="{ 'text-grey striked-out red-line': isOverwritten() }" 
            uib-tooltip="Master AAU Uniform Number">&nbsp;{{getDefaultAAUJersey()}}&nbsp;</span>
        <span 
            ng-bind="m.aau_jersey"
            class="font-bold" 
            ng-if="isOverwritten()"
            uib-tooltip="Event AAU Uniform Number"></span>
    </edit-value>
    <!-- Editor's markup -->
    <div class="inline-block pull-left">
        <span 
            ng-if="isOverwritten()"
            class="text-grey striked-out red-line" 
            uib-tooltip="Master AAU Uniform Number">&nbsp;{{getDefaultAAUJersey()}}&nbsp;</span>
    </div>
    <div class="inline-block pull-left">
        <div class="input-group sw-input-group-sm">
            <input 
                type="number"
                class="form-control form-control-input-small number-without-arrows"
                ng-model="inlineAAUJerseyEdit.value"
                ng-blur="saveAAUJerseyChange(m)"
                ng-keypress="onEditKeypress($event, m)"
                ng-disabled="inProgress">
            <edit-addon type="input-group-addon"></edit-addon>
        </div>
    </div>
    <div class="clearfix"></div>
    <!-- Editor's markup end -->
</inline-edit>

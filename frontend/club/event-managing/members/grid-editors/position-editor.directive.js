angular.module('SportWrench').directive('positionEditor', positionEditor);

function positionEditor (SPORT_POSITIONS, EventMembersService, toastr, OVERWRITE_VALUE_MODE, DEFAULT_VALUE_MODE) {
	return {
		restrict: 'E',
		scope: {
			m 				: '=member',
			eventId 		: '@',
			onSaved 		: '&?',
			onAddonClick 	: '&',
			block 			: '='
		},
		templateUrl: 'club/event-managing/members/grid-editors/position-editor.html',
		link: function (scope) {
			scope.positions 			= SPORT_POSITIONS;
			scope.inlinePositionEdit 	= {};

			scope.positionEditToggle = function (isEditMode) {
				if(isEditMode) {
					if(scope.isOverwritten()) {
						scope.inlinePositionEdit.value 	= scope.m.position_id;
						scope.inlinePositionEdit.type 	= OVERWRITE_VALUE_MODE;
						scope.inlinePositionEdit.name 	= scope.m.sport_position_name;
					} else {
						scope.inlinePositionEdit.value 	= scope.m.default_position_id;
						scope.inlinePositionEdit.type 	= DEFAULT_VALUE_MODE;
						scope.inlinePositionEdit.name 	= scope.m.default_position_name;
					}
				} else {
					dropInlineEditorValues();
				}
			};
			scope.savePositionChange = function (choosenPosition) {
				if(scope.inProgress) return;
				scope.inlinePositionEdit.name = choosenPosition.short_name;
				scope.inProgress = true;

				var body = {};

				if( scope.inlinePositionEdit.type === OVERWRITE_VALUE_MODE && 
					choosenPosition.id === scope.m.default_position_id) {
					body[scope.inlinePositionEdit.type] = {
						position: null
					}
				} else {
					body[scope.inlinePositionEdit.type] = {
						position: choosenPosition.id
					}
				}
				

				EventMembersService.updateAthlete(scope.m.id, scope.eventId, body)
				.then(function (resp) {
					var respData = resp.data;

					if(scope.isOverwritten()) {
						scope.m.position_id 		= body[OVERWRITE_VALUE_MODE].position;
						scope.m.sport_position_name = respData.o.position_name;
					} else {
						scope.m.default_position_id 	= body[DEFAULT_VALUE_MODE].position;
						scope.m.default_position_name 	= respData.d.position_name;
					}

					scope.$broadcast('inline-edit.close');

					if(scope.onSaved) scope.onSaved();
				}, function (resp) {
					if(resp && resp.data.validation) {
		                toastr.error(resp.data.validation)
		            }

		            scope.inlinePositionEdit.name = (scope.isOverwritten())
		            									?scope.m.sport_position_name
		            									:scope.m.default_position_name
				}).then(function () {
					scope.inProgress = false;
				})
			};
			scope.isOverwritten = function () {
				return (scope.m.position_id && (scope.m.position_id !== scope.m.default_position_id))
			};

			function dropInlineEditorValues () {
				scope.inlinePositionEdit.type 	= '';
				scope.inlinePositionEdit.value 	= undefined
				scope.inlinePositionEdit.name 	= '';
			}
		}
	}
}
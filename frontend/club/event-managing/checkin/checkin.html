<p class="lead text-center row-space" ng-if="event.online_team_checkin_available">
    <span ng-if="!event.online_checkin_over && !event.online_checkin_not_started">
        On-Line Team Check-In will only be available from {{event.online_team_checkin_start}} to {{event.online_team_checkin_end}} ({{event.timezone_abbrev}})
    </span>
    <uib-alert ng-if="event.online_checkin_not_started" type="warning">
        On-Line Team Check-In will only be available from {{event.online_team_checkin_start}} ({{event.timezone_abbrev}})
    </uib-alert>
    <uib-alert ng-if="event.online_checkin_over" type="warning">
        On Line Team Check In has expired at {{event.online_team_checkin_end}} ({{event.timezone_abbrev}})
    </uib-alert>
</p>
<p class="text-center row-space"><strong>Only Teams with a COMPLETE Roster Status will be able to be checked in On-Line</strong></p>
<h5>Review the following Steps:</h5>
<p>
- Click on each team name listed below to verify the roster is complete and accurate<br/>
- Make any necessary changes to the roster at this time<br/>
- Once you check in on line, no further changes will be allowed to the roster
</p>
<p>Select check box of teams to confirm for On Line Check in (You can return to this screen to check in additional teams at a later date)</p>
<h5>Teams to check in at this time:</h5>
<table class="table table-condensed">
	<thead>
		<tr>
			<th>
				<input type="checkbox"
                       ng-if="!utils.teamsLoading"
                       ng-disabled="checkinNotAvailableByDates"
                       ng-model="utils.chooseAll"
                       ng-change="toggleSelection()"
                >
			</th>
			<th>Team Name</th>
			<th>Team Code</th>
			<th ng-bind="staffersColumnHeading"></th>
			<th>Roster Validation</th>
            <th>Online Check in Completed</th>
		</tr>
	</thead>
	<tbody ng-if="!utils.teamsLoading">
		<tr ng-repeat="t in data.teams">
			<td>
                <span uib-tooltip="{{!t.online_checkin_date?'':'Team has been checked in online already'}}">
                    <input type="checkbox"
                           ng-model="utils.check[t.team_id]"
                           ng-disabled="disableCheckboxes(t)"
                           ng-change="toggleTeam()">
                </span>
			</td>
			<td>
				<a href="" ng-click="openRosters(t.team_id)" ng-bind="t.team_name"></a>
			</td>
			<td ng-bind="t.usav_code"></td>
			<td ng-if="!isPrimaryStaffBarcodesMode">
				<span ng-repeat="s in t.staffers">{{($index > 0)?', ':''}}<strong ng-bind="s.name"></strong></span>
				<div ng-if="!t.staffers.length"><i>Staff will populate when you complete check in</i></div>
			</td>
			<td ng-if="isPrimaryStaffBarcodesMode">
				<span ng-repeat="s in t.staffers">{{($index > 0)?', ':''}}
					<span ng-class="{'text-bold' : s.is_checked_in}">{{s.name}}</span>
                    <i
                        ng-if="!s.email"
                        class="fa fa-exclamation-triangle t-error"
                        aria-hidden="true"
                        uib-tooltip="Email is empty. Staff will not receive email with barcode after online check-in."
                    >
                    </i>
				</span>
				<div ng-if="!t.staffers.length"><i>Staff will populate when you complete check in</i></div>
			</td>
			<td ng-click="openErrorsModal(t)" class="pointer">
				<span ng-bind-html="getValidationMsg(t.team_id)"></span>
			</td>
            <td>
                <span ng-if="t.online_checkin_date" ng-bind="t.online_checkin_date"></span>
                <span ng-if="!t.online_checkin_date">-</span>

            </td>
		</tr>
		<tr no-data-row cs="5" text="No accepted Teams yet" ng-if="!data.teams"></tr>
	</tbody>
	<tbody ng-if="utils.teamsLoading">
		<tr>
			<td colspan="5">
				<spinner active="true"></spinner>
			</td>
		</tr>
	</tbody>
</table>

<div ng-bind-html="getDisclaimer()"></div>

<fieldset ng-disabled="utils.saving">

	<form class="form-inline" name="pickerForm" ng-show="!isPrimaryStaffBarcodesMode">
		<staffer-picker 
			staffers="data.staffers" 
			choosen-staffer="data.firstStaffer"
			exclude-staffer="data.secondStaffer" 
			prefix="first" 
			form-instance="pickerForm" 
			preson-label="Person Completing Online Team Check In">
		</staffer-picker>

		<div class="row-space"></div>

		<staffer-picker
			staffers="data.staffers" 
			choosen-staffer="data.secondStaffer"
			exclude-staffer="data.firstStaffer"  
			prefix="second" 
			form-instance="pickerForm" 
			preson-label="Additional Person Approved to Pick Up Team Wristbands">
		</staffer-picker>
	</form>

	<button 
		class="btn btn-primary row-space" 
		ng-if="!hideSubmitButton()"
		ng-click="save()"
	>Submit</button>

	<spinner active="utils.saving"></spinner>
</fieldset>

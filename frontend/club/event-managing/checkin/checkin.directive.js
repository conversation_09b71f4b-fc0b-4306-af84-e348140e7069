angular.module('SportWrench').directive('clubCheckin', [
	'ClubCheckinService',
	'$stateParams',
	'toastr',
	'$sce',
	'$uibModal',
	'$state',
	'APP_ROUTES',
	'_',
	'UtilsService',
	'CHECKIN_MODES',
    'PrevQualificationService',
    'BID_AGREEMENT_SENDER',
    'ONLINE_CHECKIN_MESSAGES',
	clubCheckin
]);

function getChosenTeamsIDs (pickedTeams) {
	return Object.keys(pickedTeams).reduce(function (res, teamID) {

		if (pickedTeams[teamID] === true) {
			res.push(Number(teamID));
		}

		return res;
	}, []);
}

function getStaffersList (UtilsService, firstStaffer, secondStaffer) {
	var staffers = [];

	if (firstStaffer !== null && !UtilsService.isEmpty(firstStaffer)) {
        firstStaffer.staff_type = 1;
		staffers.push(firstStaffer);
	}

	if (secondStaffer !== null && !UtilsService.isEmpty(secondStaffer)) {
        secondStaffer.staff_type = 2;
        staffers.push(secondStaffer);
	}

	return staffers;
}

function clubCheckin (
    ClubCheckinService, $stateParams, toastr, $sce, $uibModal, $state, APP_ROUTES, _, UtilsService, CHECKIN_MODES,
    PrevQualificationService, BID_AGREEMENT_SENDER, ONLINE_CHECKIN_MESSAGES
) {
	return {
		restrict: 'E',
		scope: {
			event: '='
		},
		templateUrl: 'club/event-managing/checkin/checkin.html',
		link: function (scope) {
			var eventId 			= $stateParams.event;
			var rosterValidation 	= {};

			scope.checkinNotAvailableByDates
                = (scope.event.online_checkin_over || scope.event.online_checkin_not_started);

			scope.isPrimaryStaffBarcodesMode
                = (scope.event.online_team_checkin_mode === CHECKIN_MODES.PRIMARY_STAFF_BARCODES);

			scope.utils = {
				check			: {},
				chooseAll 		: false
			};
			scope.data = {
				teams 			: [],
				staffers 		: [],
				firstStaffer	: {},
				secondStaffer 	: {},
			}

			scope.toggleSelection = function () {
				for(var i = 0, l = scope.data.teams.length; i < l; ++i) {
				    if(!scope.data.teams[i].online_checkin_date) {
                        scope.utils.check[scope.data.teams[i].team_id] = scope.utils.chooseAll
                    }
				}
			}

			scope.hideSubmitButton = function () {
			    let hideSubmitRules = [
                    !scope.data.teams.length,
                    scope.utils.saving,
                    scope.checkinNotAvailableByDates
                ];

			    return hideSubmitRules.some(rule => rule);
            };

			scope.disableCheckboxes = function (team) {
                return team.online_checkin_date || scope.checkinNotAvailableByDates;
            };

			scope.staffersColumnHeading = scope.isPrimaryStaffBarcodesMode
                                    ? 'Primary staff to receive QR entry code'
                                    : 'Person(s) Approved to Pick Up Team Wristbands';

			scope.toggleTeam = function () {
				var choosenQty = _.filter(_.values(scope.utils.check), function (val) { 
					return (val === true)
				}).length;
				scope.utils.chooseAll = (choosenQty === scope.data.teams.length)
			}

			scope.openRosters = function (teamID) {
				$state.go(APP_ROUTES.CD.MANAGE_EVENT_MEMBERS, {
            		team    : teamID,
            		event   : $stateParams.event
        		})	
			}

			scope.save = async function () {
			    let checkinParams = {};

                checkinParams.teams = getChosenTeamsIDs(scope.utils.check);

				if(!checkinParams.teams.length) {
					return toastr.warning('Choose at least one Team');
				}

				if(!scope.isPrimaryStaffBarcodesMode) {
                    if(scope.pickerForm.$invalid) {
                        return toastr.warning('Invalid Form Data')
                    }

                    if(!scope.data.firstStaffer.staff_id) {
                        return toastr.warning('Choose Person Completing Online Team Check In');
                    }

                    checkinParams.staffers = getStaffersList(
                        UtilsService, scope.data.firstStaffer, scope.data.secondStaffer
                    );

                    if(!checkinParams.staffers.length) {
                        return toastr.warning('Choose at least one Staffer')
                    }
				}

				scope.utils.saving = true;

                let validationErrors = await ClubCheckinService.validateTeams(eventId, checkinParams.teams, __getTeam);

                if(validationErrors && validationErrors.length) {
                    scope.utils.saving = false;

                    return ClubCheckinService.openValidationErrorsModal(validationErrors);
                }

                let prevQualifyingTeams = __getTeamsFromQualifyingDivisions(
                    scope.event, scope.data.teams, checkinParams.teams
                );

                let teamsAgreement;

                if(prevQualifyingTeams.length) {
                    teamsAgreement = await PrevQualificationService.openTeamsAgreementModal(prevQualifyingTeams);
                }

				await ClubCheckinService.applyOnlineCheckin(eventId, checkinParams);

                if(!_.isEmpty(teamsAgreement)) {
                    await PrevQualificationService.updateBidAgreementForTeams(
                        eventId, teamsAgreement, BID_AGREEMENT_SENDER.CLUB
                    );
                }

                scope.utils.chooseAll = false;
                scope.toggleSelection();
                toastr.success('Saved');

                __confirmationModal(checkinParams.teams, scope.isPrimaryStaffBarcodesMode, eventId);
                __loadTeams();

                scope.utils.saving = false;
			}

			scope.getDisclaimer = function () {
				return $sce.trustAsHtml(scope.event.online_team_checkin_disclaimer)
			}

            scope.getCheckinInfo = function () {
				return $sce.trustAsHtml(scope.event.online_team_checkin_info)
			}

			scope.openErrorsModal = function (team) {
				var validationData = rosterValidation[team.team_id];

				if (_.isEmpty(validationData) || (validationData.qty === 0)) {
					return;
				}

				var errObj = {
					team 	: team,
					errors 	: validationData.errors
				}

				ClubCheckinService.openValidationErrorsModal([errObj]);
			}

			scope.getValidationMsg = function (teamID) {
				var cssClass = '';
				var msg = '';

				var validationRes 	= rosterValidation[teamID];

				if (_.isEmpty(validationRes)) {
					cssClass 	= 'text-info';
					msg 		= 'Pending ...';
				} else if (validationRes.qty === 0) {
					cssClass 	= 'text-success';
					msg  		= 'Valid!';
				} else if (validationRes.qty > 0) {
					cssClass 	= 'text-danger';
					msg  		= 
						'Failed! ' + validationRes.qty + ' error' + ((validationRes.qty === 1) ? '' : 's') + '.';
				}


				return '<span class="' + cssClass + '">' + msg + '</span>';
			}

			__loadTeams();
			__loadStaffers();

			function __loadTeams () {
				scope.utils.teamsLoading = true;
				ClubCheckinService.teamsList(eventId).then(function (teams) {
					scope.data.teams = teams;

					scope.utils.teamsLoading = false;

					var teamsIDsList = teams.map(function (team) {
						return Number(team.team_id);
					})

					return teamsIDsList;
				})
				.then(function (teamsIDsList) {
					return ClubCheckinService.validateTeams(eventId, teamsIDsList)
					.then(function (errorsList) {
					    if (!_.isEmpty(errorsList)) {
                            errorsList.forEach(function (errObj, index) {
                                var teamID = teamsIDsList[index];
                                rosterValidation[teamID] = {
                                    qty 		: ClubCheckinService.getErrorsQty(errObj),
                                    errors 		: errObj
                                }
                            })
                        }
					})
				})
			}

			function __loadStaffers () {
				scope.utils.staffersLoading = true;
				ClubCheckinService.eventStaffers(eventId).then(function (staffers) {
					scope.data.staffers = staffers;
				}).then(function () {
					scope.utils.staffersLoading = false;
				})
			}

			function __confirmationModal (chosenTeamsIdentifiers, isPrimaryStaffBarcodesMode, eventID) {
                var teams = [];
				scope.data.teams.forEach(function (t) {
					if(chosenTeamsIdentifiers.indexOf(t.team_id) >= 0) {
						teams.push(t);
					}
				})

                const onlineTeamCheckinInfo = scope.getCheckinInfo();

                let primaryStaffBarcodesModeMessage = onlineTeamCheckinInfo
                    || ONLINE_CHECKIN_MESSAGES.PRIMARY_STAFF_BARCODES;

                // Hardcode for SW-2940
                if([23215, 23293].includes(Number(eventID))) {
                    primaryStaffBarcodesModeMessage = `You have successfully completed online team check-in.`;
                }

                // Hardcode for SW-3297
                if([24006].includes(Number(eventID))) {
                    primaryStaffBarcodesModeMessage = `This completes the online registration process.
                     You will need to complete in-person check-in to pick up your player and coach credentials for each
                     team for entry into the tournament. An email will be sent to each Primary Staff member
                     with further instructions for admission to the event.`;
                }

                let messageText = isPrimaryStaffBarcodesMode
                    ? primaryStaffBarcodesModeMessage : ONLINE_CHECKIN_MESSAGES.DEFAULT;

				$uibModal.open({
					templateUrl: 'club/event-managing/checkin/success-modal.html',
					controller: ['$scope', function ($scope) {
						$scope.teams    = teams;
						$scope.message  = messageText;
					}]
				})
			}

			function __getTeam (id) {
				for(var i = 0, l = scope.data.teams.length; i < l; ++i) {
					if(scope.data.teams[i].team_id === id) {
						return scope.data.teams[i];
					}
				}
			}

            function __getTeamsFromQualifyingDivisions (event, teams, selectedTeams) {
                if(!PrevQualificationService.isQualifiedEventType(event.event_type)) {
                    return [];
                }

                return teams.filter(team => team.is_qualifying && selectedTeams.includes(team.team_id));
            }
		}
	}
}

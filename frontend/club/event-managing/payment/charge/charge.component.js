angular.module('SportWrench').component('teamsCharge', {
	templateUrl : 'club/event-managing/payment/charge/charge.html',
	bindings 	: {
		teamsListPromise 	: '<teams',
		eventInfo 			: '<event',
		masterClub			: '<',
		createCharge 		: '&create',
		disableCheckboxes 	: '<',
		hasCustomDiscount 	: '<?',
		userType 			: '@?'
	},
	transclude 	: {
		checkBlock: '?checkBlock',
	},
	controller 	: ['UtilsService', '$q', '$transclude', 'purchaseService', '$state',function (UtilsService, $q, $transclude, purchaseService, $state) {
		var self = this;

		this.eventID = +$state.params.event;

		this.payment = {
			subtotal 	: 0,
			surcharge 	: 0,
			serviceFee	: 0,
			total 		: 0,
			receipt 	: []
		};

		this.teamsLoading 	= true;
		this.teamsList 		= [];

		this.onTypeChanged = function (t) {
			self.payment.type = t;
			self.recountTotal(self.payment.subtotal, t, self.payment.receipt);
		};

		this.disableSbmt = function () {
			return (self.payment.total === 0);
		};

		this.noPaymentType = function () {
			return !(self.teamsLoading || self.eventInfo.check || self.eventInfo.card || self.eventInfo.ach);
		};

		this.onAmountChanged = function (amount, receipt, discounts) {
			self.payment.subtotal 	= amount;
			self.payment.receipt 	= receipt;
			self.payment.discounts 	= discounts;

			if (!!self.payment.type) {
				self.recountTotal(amount, self.payment.type, receipt);
			}
		};

		this.recountTotal = function (subtotal, type, receipt) {
            let totals = purchaseService.recountTotal(subtotal, type, receipt, self.eventInfo, self.teamsList);

            this.payment = angular.extend(this.payment, totals);
		};

		this.pay = function (token) {
			self.payment.inProgress = true;

			if(token) {
			    self.payment.stripe_token = token;
            }

			return self.createCharge({
				payment: self.payment
			}).then(function (id) {
				self.payment.id = id;
				self.payment.chargeCompleted = true;
				self.payment.inProgress = false;
			}, function () {
				self.payment.inProgress = false;
			});
		};

		this.checkBlockExists = function () {
			return $transclude.isSlotFilled('checkBlock');
		}

		$q.when(this.teamsListPromise).then(function (teamsList) {
			self.teamsList 		= teamsList;
			self.teamsLoading 	= false;
		});
	}]
});

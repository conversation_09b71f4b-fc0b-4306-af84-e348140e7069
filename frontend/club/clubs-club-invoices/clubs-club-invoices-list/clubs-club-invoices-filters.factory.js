angular.module('SportWrench').factory('ClubsClubInvoicesFiltersFactory', function () {
    return {
        getStatuses: function () {
            return [
                {
                    id: 'paid',
                    name: 'Paid',
                    class: 'glyphicon glyphicon-ok-sign green'
                }, {
                    id: 'refunded',
                    name: 'Refunded',
                    class: 'glyphicon glyphicon-repeat red'
                }, {
                    id: 'pending',
                    name: 'Pending',
                    class: 'glyphicon glyphicon-minus-sign blue'
                }, {
                    id: 'canceled',
                    name: 'Canceled',
                    class: 'glyphicon glyphicon-remove-circle red'
                }, {
                    id: 'disputed',
                    name: 'Disputed',
                    class: 'glyphicon glyphicon-minus-sign red'
                }, {
                    id: 'not_paid',
                    name: 'Not Paid',
                    class: 'glyphicon glyphicon-minus-sign red'
                }
            ]
        }
    }
});

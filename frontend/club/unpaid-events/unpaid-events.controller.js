angular
    .module('SportWrench')
    .controller('UnpaidEventsController', UnpaidEventsController);
function UnpaidEventsController($scope, $state, $http, APP_ROUTES) {
    $scope.loaded = false;

    _loadEvents();

    $scope.$on('club.entry.paid', function () {
        _loadEvents();
    });

    $scope.$on('club.entry.payment.failed', function () {
        _loadEvents();
    });

    function _loadEvents() {
        $http.get('/api/club/unpaid-events').success(function (data) {
            $scope.events = data.events;
            $scope.loaded = true;
        });
    }

    $scope.paymentMenu = function (event) {
        if (event.teams_entry_sw_fee === null) {
            return;
        }

        $state.go(APP_ROUTES.CD.CLUB_UNPAID_EVENTS_PAY, {
            event: event.event_id,
        });
    };
}

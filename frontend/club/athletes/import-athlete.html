<div class="modal-header">
    <h2 class="text-info">Import from Webpoint</h2>
</div>
<div class="modal-body">
    <form name="importAthleteForm" role="form" class="form-horizontal row-space" autocomplete="off">
        <div class="form-group" ng-class="importAthleteForm.username.$invalid && importAthleteForm.username.$dirty ? 'has-error' : ''">
            <label class="col-sm-4 control-label">Webpoint username</label>
            <div class="col-xs-7">
                <input type="text" style="display:none;" name="username">
                <input ng-model="athleteData.username"
                       required
                       name="username" 
                       type="text"
                       class="form-control"
                       placeholder="Webpoint club username"
                       autocomplete="off">                    
            </div>
        </div>
        <div class="form-group" ng-class="importAthleteForm.password.$invalid && importAthleteForm.password.$dirty ? 'has-error' : ''">
            <label class="col-sm-4 control-label">Webpoint password</label>
            <div class="col-xs-7">
                <input style="display:none" type="password" name="password"/>
                <input ng-model="athleteData.password"                            
                       required
                       name="password"
                       type="password"
                       class="form-control"
                       placeholder="Webpoint club password"
                       autocomplete="off">
            </div>
        </div>
        <div class="form-group save-time-text bg-warning">
            <div class="col-sm-10 col-sm-offset-1">
              <b>Save Time!:</b>
              <p>
                USA Volleyball does not provide basic contact information without your permission.
                If you give your permission below, SportWrench will import the USAV club download file and update your club member’s contact information.<br/>
                This may take up to 30 minutes to complete, but it will save you from having to enter email and phone and address information for all of your club members.<br/>
              </p>
            </div>              
        </div>
        <div class="form-group">
          <div class="col-sm-offset-1 checkbox">
              <label>
                <input type="radio" 
                       required
                       ng-model="athleteData.agree" 
                       name="webpoint_import_agree"
                       ng-value="true"> I agree
              </label>
          </div>
        </div>
        <div class="form-group" ng-if="athleteData.agree">
            <div class="well col-sm-offset-1 col-sm-10">
                <p>Choose the most suitable import option:</p>
                <label class="col-sm-offset-1 radio">
                    <input type="radio" name="webpoint_import_option" ng-model="athleteData.option" value="insert"> Import New Athlete/Staff Only:
                    <p class="help-block">I want to update the list of currently available athletes and staff by importing members from Webpoint that are not currently in SportWrench.  New athletes/staff will be added to existing event rosters. Current athlete/staff in SportWrench will not be updated.  </p>
                </label>
                <label class="col-sm-offset-1 radio">
                    <input type="radio" name="webpoint_import_option" ng-model="athleteData.option" value="default"> Import New Athlete/Staff and Update Current Athlete/Staff:
                    <p class="help-block">I want to update the list of currently available athletes and staff by importing members from Webpoint that are not currently in SportWrench.  I also want to update the current athlete/staff in SportWrench with values from Webpoint (i.e, update uniform number and/or team).  New athletes/staff will be added to existing event rosters and updated athlete/staff values will be added to existing rosters.</p>
                    <p class="help-block">THIS WILL REPLACE INFORMATION IN SPORTWRENCH WITH WEBPOINT DATA IF IT EXISTS</p>
                </label>
            </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-1 checkbox">
            <label>
              <input type="radio"
                     required 
                     ng-model="athleteData.agree" 
                     name="webpoint_import_agree"
                     ng-value="false"> I don’t agree
            </label>
          </div>
        </div>
        <div class="col-sm-10 col-md-offset-1">
            <h3 ng-if="queueData.requested" class="text-danger">
                Import is still in process. Requested at {{queueData.requested}}. Please wait.
            </h3>
        </div>        
    </form>
    <blocked-events-list events="blockedEvents"></blocked-events-list>
    <uib-alert type="danger" ng-if="error">{{error}}</uib-alert>
</div>
<div class="modal-footer">
    <button ng-disabled="importAthleteForm.$invalid || queueData.requested || isImportProcessing" type="button" class="btn btn-primary" ng-click="submit()">Start Import</button>
    <button type="button" class="btn btn-default" ng-click="$dismiss()">Close</button>
</div>

<form class="form-horizontal row-space">
    <div class="col-md-2 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <label class="control-label">Type</label>
        <select
            class="form-control"
            ng-options="t.id as t.name for t in $ctrl.filterValues.typeFilters"
            ng-model="$ctrl.filters.event_type"
            ng-change="$ctrl.onFilterChange()">
        </select>
    </div>

    <div class="col-md-2 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <label class="control-label">State</label>
        <select
            class="form-control"
            ng-options="t.state as t.name for t in $ctrl.filterValues.stateFilters"
            ng-model="$ctrl.filters.event_state"
            ng-change="$ctrl.onFilterChange()">
        </select>
    </div>

    <div class="col-md-2 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <label class="control-label">Region</label>
        <select
            class="form-control"
            ng-options="t.region as t.name for t in $ctrl.filterValues.regionFilters"
            ng-model="$ctrl.filters.event_region"
            ng-change="$ctrl.onFilterChange()">
        </select>
    </div>

    <div class="col-md-3 col-sm-6 col-xs-12 form-group date-time-control">
        <label class="control-label">From</label>
        <div class="input-group date-time-control"
             ng-click="$ctrl.datePickerUtils.fromPicker.isOpen = !$ctrl.datePickerUtils.fromPicker.isOpen">
            <input type="text"
                   ng-model="$ctrl.filters.event_date_start"
                   class="form-control white-ro pointer"
                   uib-datepicker-popup="{{$ctrl.datePickerUtils.format}}"
                   max-date="$ctrl.filters.event_date_end"
                   is-open="$ctrl.datePickerUtils.fromPicker.isOpen"
                   ng-change="$ctrl.onFilterChange()"
                   placeholder="{{::$ctrl.datePickerUtils.placeholder}}"
                   readonly>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 col-xs-12 form-group date-time-control" style="margin-right: 10px">
        <label class="control-label">To</label>
        <div class="input-group" ng-click="$ctrl.datePickerUtils.toPicker.isOpen = !$ctrl.datePickerUtils.toPicker.isOpen">
            <input type="text"
                   ng-model="$ctrl.filters.event_date_end"
                   class="form-control white-ro pointer"
                   uib-datepicker-popup="{{$ctrl.datePickerUtils.format}}"
                   min-date="$ctrl.filters.event_date_start"
                   is-open="$ctrl.datePickerUtils.toPicker.isOpen"
                   ng-change="$ctrl.onFilterChange()"
                   placeholder="{{::$ctrl.datePickerUtils.placeholder}}"
                   readonly>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
            </span>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <div class="search-box search-input">
            <label class="control-label">Search</label>
            <sw-searchbox
                reload="$ctrl.onFilterChange()"
                css="search_teams white-ro"
                input-model="$ctrl.filters.event_name_search"
                placeholder="Event Name"
            reload-time="500"
            ></sw-searchbox>
        </div>
    </div>

    <div class="col-md-2 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <label class="control-label">Gender</label>
        <select
            class="form-control"
            ng-options="t.id as t.name for t in $ctrl.filterValues.genderFilters"
            ng-model="$ctrl.filters.event_gender"
            ng-change="$ctrl.onFilterChange()">
        </select>
    </div>

    <div class="col-md-2 col-sm-6 col-xs-12 form-group" style="margin-right: 10px">
        <label class="control-label">Events Visibility</label>
        <select
            class="form-control"
            ng-model="$ctrl.filters.event_visibility"
            ng-change="$ctrl.onFilterChange()">
            <option value="available" selected>Available Only</option>
            <option value="">All</option>
        </select>
    </div>

    <div class="col-md-3 col-sm-6 col-xs-12 form-group">
        <label class="control-label">Divisions Ages</label>
        <div
            ng-dropdown-multiselect=""
            options="$ctrl.filterValues.ageFilters"
            selected-model="$ctrl.divisionAges"
            events="$ctrl.divisionAgesEventHandlers"
            translation-texts="{buttonDefaultText: 'Select Division Ages'}"
        >
        </div>
    </div>

</form>

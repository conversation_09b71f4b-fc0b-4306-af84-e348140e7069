<div ng-form name="clubRegionComponent">
    <div ng-class="{ 'form-group': true, 'validation-required': $ctrl.isRequired, 'has-error': ($ctrl.parentForm.$submitted && clubRegionComponent.region.$invalid || $ctrl.hasError({ fieldName: 'region' }))}">
        <label for="region" class="col-sm-4 control-label">Region</label>
        <div class="col-sm-5">
            <select 
                name="region" 
                class="form-control"
                ng-model="$ctrl.region"
                ng-options="r.region as r.name for r in $ctrl.countryRegions | orderBy:'name'"
                ng-required="$ctrl.isRequired"
                ng-disabled="$ctrl.isDisabled"
                ng-change="$ctrl.onChange()"
            >
                <option value="" selected>Select...</option>
            </select>
            <!-- ng-change="createMCForm.usav_code.$setValidity('unique', true)" -->
            <p class="help-block" ng-if="createMCForm.$submitted && createMCForm.region.$invalid" ng-bind="utils.required_error"></p>
        </div>
    </div>
</div>

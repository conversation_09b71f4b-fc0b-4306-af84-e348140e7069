<div ng-form="aauPrimaryMembershipComponent">
    <div ng-class="{
     'form-group validation-required': true,
     'has-error': ($ctrl.parentForm.$submitted && aauPrimaryMembershipComponent.aau_primary_membership_id.$invalid
      || $ctrl.hasError({ fieldName: 'aau_primary_membership_id' }))
    }">
        <label for="aau_primary_membership_id" class="col-sm-4 control-label">
            AAU Representative Membership Id
        </label>
        <div class="col-sm-5">
            <input
                type="text"
                name="aau_primary_membership_id"
                class="form-control"
                ng-model="$ctrl.aauPrimaryMembershipId"
                ng-required="true"
                ng-change="$ctrl.onAAUPrimaryMembershipIdChange()">
        </div>
    </div>
</div>

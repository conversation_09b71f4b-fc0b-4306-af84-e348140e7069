angular.module('SportWrench')
.controller('EditTeamRosterController', EditTeamRosterController);

// TODO Add injection

function EditTeamRosterController($scope, $rootScope, $stateParams, athletesService, $uibModalInstance, club, team) {
    // $scope.roles = [ {id: 0, name: '(Title not set...)'} ];

    $scope.tab = 0;
    $scope.club = club;
    $scope.master_team_id = team;
    $scope.data = {};

    if(!club || !team) $uibModalInstance.dismiss();

    $scope.close = function() {
        $uibModalInstance.close();
    };

    $scope.$on('club.team.updated', function (e, team) {
        $uibModalInstance.dismiss(team)
    });  
}

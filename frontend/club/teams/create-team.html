<div class="modal-header text-info text-center">
    <h3>Create new Team</h3>
    <p>You can modify the team name at any time from year to year. You CANNOT change your USAV Code from year to year. If you do, all previous tournaments and records will not be associated with this team.</p>
</div>
<uib-alert type="danger" ng-if="prefixNotEntered">You have to update your club info. Short Club Name is not entered!</uib-alert>
<master-team-form
    team="team"
    club="club"
    disable-edit="prefixNotEntered"
    watch-team-name="true"
></master-team-form>
<uib-alert type="danger" ng-if="error">{{error}}</uib-alert>
<div class="modal-footer" ng-if="!prefixNotEntered">
    <button type="button" class="btn btn-default" ng-click="$dismiss()">Close</button>
    <button type="submit" class="btn btn-primary" ng-disabled="disableSubmit()" ng-click="create()">Submit</button>
</div>
<div class="modal-footer" ng-if="prefixNotEntered">
    <a class="btn btn-primary" ui-state="states.update">Update Club Info</a>
</div>



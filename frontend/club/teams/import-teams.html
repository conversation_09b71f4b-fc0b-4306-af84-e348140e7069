<div class="modal-header">
    <h2 class="text-info">Import Teams</h2>
</div>
<div class="modal-body">
    <div ng-hide="importErrors">
        <form name="teamsImportForm">
            <div class="well" nv-file-drop uploader="uploader">
                Drop MS Excel (.xls or .xlsx) file here
                <input type="file" nv-file-select uploader="uploader"/>
                <a href="/data/import/teams_demo.xlsx">Download a sample teams file</a>
            </div>
            <div ng-show="opts">
                <div class="row">
                    <label class="col-xs-6 control-label">Sheet
                        <select class="form-control"
                            ng-model="importOpts.sheet"
                            ng-selected="0"
                            required>
                            <option ng-repeat="s in opts.sheets" value="{{$index}}">
                                {{s}}
                            </option>
                        </select>
                    </label>
                    <label class="col-xs-3 control-label">
                        <br/>
                        <input ng-disabled="!importOpts.sheet"
                            ng-model="importOpts.skipFirstRow"
                            type="checkbox"
                            checked="true">
                        Skip first row
                    </label>
                </div>
                <div class="row">
                    <label class="col-xs-3 control-label">Team name *
                        <select ng-disabled="!importOpts.sheet" class="form-control"
                            ng-model="importOpts.team_name"
                            required>
                            <option value="" selected> — </option>
                            <option ng-repeat="c in opts.cols[importOpts.sheet]" value="{{$index}}">
                                Column {{c}}
                            </option>
                        </select>
                    </label>
                    <label class="col-xs-3 control-label">Age
                        <select ng-disabled="!importOpts.sheet" class="form-control"
                            ng-model="importOpts.age">
                            <option value="" selected> — </option>
                            <option ng-repeat="c in opts.cols[importOpts.sheet]" value="{{$index}}">
                                Column {{c}}
                            </option>
                        </select>
                    </label>
                    <label class="col-xs-3 control-label">Rank in club
                        <select ng-disabled="!importOpts.sheet" class="form-control"
                            ng-model="importOpts.rank">
                            <option value="" selected> — </option>
                            <option ng-repeat="c in opts.cols[importOpts.sheet]" value="{{$index}}">
                                Column {{c}}
                            </option>
                        </select>
                    </label>
                    <label class="col-xs-3 control-label">Gender *
                        <select ng-disabled="!importOpts.sheet" class="form-control"
                            ng-model="importOpts.gender"
                            required>
                            <option value="" selected> — </option>
                            <option value="male"> Male </option>
                            <option value="female"> Female </option>
                            <option value="coed"> Coed </option>
                            <option ng-repeat="c in opts.cols[importOpts.sheet]" value="{{$index}}">
                                Column {{c}}
                            </option>
                        </select>
                    </label>
                    <label class="col-xs-4 control-label">Sport Variation *
                        <select ng-disabled="!importOpts.sheet" class="form-control"
                            ng-model="importOpts.sportVariation"
                            required>
                            <option value="" selected> — </option>
                            <option ng-repeat="v in sportVariations" value="{{v.id}}">
                                {{v.name}}
                            </option>                           
                        </select>
                    </label>
                </div>
            </div>
        </form>
        <div spreadsheet-preview="sheets"></div>
    </div>
    <div ng-show="validationErrs && validationErrs.length">
        <h3>{{validationErrs.length}} teams have next errors:</h3>
        <table class="table">
            <tr>
                <th>Row</th>
                <th>Team Name</th>
                <th>Field</th>
                <th>Error</th>
            </tr>
            <tr ng-repeat="err in validationErrs | orderBy: 'row'">
                <td>{{err.row}}</td>
                <td ng-class="{ danger: err.teamName }">
                    {{err.teamName}}
                </td>
                <td>
                    {{err.field}}
                </td>
                <td>
                    {{err.msg}}
                </td>
            </tr>
        </table>
    </div>
    <div ng-show="importErrors">
        <h3>{{importErrors.length}} of {{teamsSent}} teams have not been imported:</h3>
        <table class="table">
            <tr>
                <th>Row</th>
                <th>Team Name</th>
                <th>Team Code</th>
                <th>Error</th>
            </tr>
            <tr ng-repeat="e in importErrors">
                <td>{{e.data.row}}</td>
                <td ng-class="{ danger: e.invalidAttributes.team_name }">
                    {{e.data.team_name}}
                </td>
                <td ng-class="{ danger: e.invalidAttributes.organization_code }">
                    {{e.data.organization_code}}
                </td>
                <td>
                    {{e.invalidAttributes.team_name}}
                    {{e.invalidAttributes.organization_code}}
                    {{e.status == 500 ? 'Internal Server Error' : null}}
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="modal-footer">
    <button ng-disabled="teamsImportForm.$invalid" ng-hide="importErrors"
        type="button" class="btn btn-primary" ng-click="submit()">Submit</button>
    <button type="button" class="btn btn-default" ng-click="$dismiss()">Close</button>
</div>

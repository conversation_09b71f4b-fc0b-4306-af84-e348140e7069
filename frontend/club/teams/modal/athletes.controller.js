angular.module('SportWrench').controller('Club.Teams.Modal.AthletesController', AthletesController);

function AthletesController ($scope, athletesService, $rootScope, $filter, toastr, DONE_MSG, ROSTER_CHANGE_WARNING_TEXT,
                             SANCTIONING_BODY, masterClubService) {
    $scope.athletes         = [];
    $scope.positions        = [];
    $scope.heights          = generateHeightArray();
    $scope.loaded           = false;
    $scope.warningText      = ROSTER_CHANGE_WARNING_TEXT;
    $scope.SANCTIONING_BODY = SANCTIONING_BODY;
    $scope.filteredAthletes = undefined;

    $scope.filters = {
        sanctionedBody: {},
    };

    athletesService.getTeamAthletes($scope.$parent.master_team_id, function (data) {
        $scope.athletes     = data.athletes;
        $scope.positions    = data.positions;
        $scope.loaded       = true;
    }); 

    $scope.updateAthlete = function (id, data) {
        if(data.gradyear) data.gradyear = +data.gradyear;
        if(data.height) data.height = +data.height;

        return athletesService.updateAthlete(id, data).then(function() {
            toastr.success(DONE_MSG);
        })
    };

    $scope.updateJersey = function (a) {
        if(!_.isNull(a.jersey) && a.jersey <= 0) {
            toastr.warning('Uniform must be a positive number');
            return;
        }
        if(a.temp_jersey === a.jersey) {
            delete a.temp_jersey;
            return;
        }
        $scope.updateAthlete(a.master_athlete_id, {jersey: a.jersey}).then(function () {
            delete a.temp_jersey;
        })
    };

    $scope.updateAAUJersey = function (a) {
        if(!_.isNull(a.aau_jersey) && a.aau_jersey <= 0) {
            toastr.warning('AAU Uniform must be a positive number');
            return;
        }
        if(a.temp_aau_jersey === a.aau_jersey) {
            delete a.temp_aau_jersey;
            return;
        }
        $scope.updateAthlete(a.master_athlete_id, {aau_jersey: a.aau_jersey}).then(function () {
            delete a.temp_aau_jersey;
        })
    };

    $scope.jerseyFocus = function (a) {
        a.temp_jersey = a.jersey;
    };

    $scope.aauJerseyFocus = function (a) {
        a.temp_aau_jersey = a.aau_jersey;
    };

    function generateHeightArray() {
        var heights = [{
                "v": "0",
                "t": "N/A"
            }],
            max = 90;
        for (var i = 54; i <= max; i++) {
            var k = {
                "v": '' + i,
                "t": (parseInt(i / 12) + '\'' + ('0' + (i % 12)).slice(-2) + '\"')
            };
            heights.push(k);
        }
        return heights;
    }

    $scope.$watch('[athletes, filters]', function (newVal) {
        if (newVal[0] && newVal[0].length) {
            $scope.filteredAthletes = $filter('filter')(newVal[0], function (value) {
                return masterClubService.sanctionedBodyFilter(value, $scope.filters.sanctionedBody)
            });
        }
    }, true);

    $scope.clubHasUsavAndAauSanctioning = masterClubService.clubHasUsavAndAauSanctioning($scope.club);
    $scope.clubHasAauSanctioning = masterClubService.clubHasAauSanctioning($scope.club);
}

AthletesController.$inject = ['$scope', 'athletesService', '$rootScope', '$filter', 'toastr', 'DONE_MSG', 'ROSTER_CHANGE_WARNING_TEXT', 'SANCTIONING_BODY', 'masterClubService'];

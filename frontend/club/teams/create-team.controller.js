angular.module('SportWrench')

.controller('CreateTeamController', CreateTeamController);

function CreateTeamController($scope, $uibModalInstance, $state, ClubTeamsService, club, INTERNAL_ERROR_MSG, APP_ROUTES) {
    $scope.team = {
        organization_code: ''
    };

    $scope.states = {
        update: APP_ROUTES.CD.INFO_UPDATE
    }

    $scope.club = club;

    $scope.disableSubmit = function () {
        const seasonality = $scope.team.has_usav_sanctioning ? $scope.team.seasonality : true;

        return !(!_.isUndefined($scope.team.age) && $scope.team.rank && $scope.team.team_name && $scope.team.gender
            && $scope.team.sport_variation_id && $scope.team.organization_code
            && seasonality);
    }

    $scope.create = function() {
        $scope.error = '';

        var team = angular.copy($scope.team);

        team.age = parseInt(team.age, 10);
        team.sport_variation_id = parseInt(team.sport_variation_id, 10);
        team.sport_id = 2;

        ClubTeamsService.createTeam(team).success(function (data) {
            var createdTeam = data.team;
            createdTeam.id  = createdTeam.master_team_id;

            $uibModalInstance.dismiss(createdTeam);
        }).error(function (data) {
            $scope.error = data.validation || INTERNAL_ERROR_MSG;
        })
    }
}

CreateTeamController.$inject = ['$scope', '$uibModalInstance', '$state', 'ClubTeamsService', 'club', 'INTERNAL_ERROR_MSG', 'APP_ROUTES'];

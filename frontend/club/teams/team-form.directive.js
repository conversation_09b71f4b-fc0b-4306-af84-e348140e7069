angular.module('SportWrench').directive('masterTeamForm', masterTeamForm);

function masterTeamForm (masterClubService, masterTeamService) {
    return {
        restrict            : 'E',
        scope               : {
            team                : '=',
            club                : '=',
            accepted            : '=',
            hasAthletes         : '<',
            entered             : '=',
            disableEdit         : '=',
            watchTeamName       : '@'
        },
        replace             : true,
        templateUrl         : 'club/teams/team-form.html',
        link: function (scope) {
            scope.data = {
                ranks                   : masterTeamService.getRanks(),
                spVars                  : masterTeamService.getSportVariations(),
                ages                    : masterTeamService.getAges(),
                upcomingEvents          : null,
                upcomingAcceptedEvents  : null
            };

            scope.hasUsavSanctioning = masterClubService.clubHasUsavSanctioning(scope.club);

            _filterClubSportVariations();

            scope.team.has_usav_sanctioning = scope.hasUsavSanctioning;

            // Auto select radio button if only one option is available for Gender
            if ((scope.club.has_female_teams + scope.club.has_male_teams + scope.club.has_coed_teams) == 1) {
                if (scope.club.has_female_teams) scope.team.gender = 'female';
                if (scope.club.has_male_teams) scope.team.gender = 'male';
                if (scope.club.has_coed_teams) scope.team.gender = 'coed';
            }

            // Auto select Sport Variation if only one option is available
            if(scope.club.sport_variations.length == 1) {
                scope.team.sport_variation_id = scope.club.sport_variations[0];
            }

            let _runTeamNameWatcher = function () {
                scope.$watchCollection('[team.age, team.rank]', function (values) {
                    let age = values[0];
                    let rank = values[1];

                    if(!scope.club || !scope.club.team_prefix || _.isUndefined(age) || !rank) return;

                    let prefix = scope.club.team_prefix.replace(/(\r\n|\n|\r)/gm, '');
                    scope.team.team_name = age > 0 ? `${prefix} ${age} ${rank}` : `${prefix} ${rank}`;
                });
            }

            if(scope.watchTeamName)
                _runTeamNameWatcher();

            scope.isAllAccepted = function () {
                return (this.entered > 0) && ((this.entered - this.accepted) === 0)
            }

            scope.hasAccepted = function () {
                return this.accepted > 0
            }

            scope.hasEntered = function () {
                return this.entered > 0
            }

            scope.teamNameChanged = function () {
                if((this.team.master_team_id) && (this.entered > 0)) {
                    masterTeamService.upcomingEvents(this.team.master_team_id)
                    .success(function (data) {
                        scope.data.upcomingEvents = data.events;
                    })
                }
            }

            scope.rankChanged = function () {
                if((this.team.master_team_id) && (this.entered > 0)) {
                    masterTeamService.upcomingEvents(this.team.master_team_id, { accepted: false })
                    .success(function (data) {
                        scope.data.upcomingAcceptedEvents = data.events;
                    })
                }
            }

            scope.agesOrder = function (age) {
                const id = age.id;
                return id > 0 ? id : id + 100;
            }

            scope.$watch('team', function (val) {
                if(!val) return;

                scope.team.organization_code =
                    masterClubService.getTeamcode(
                        scope.club, scope.team.gender, scope.team.age, scope.team.rank
                    );
            }, true);

            function _filterClubSportVariations() {
                scope.data.spVars = scope.data.spVars.filter(sp => {
                    return _.includes(scope.club.sport_variations, sp.id)
                });
            }
        }
    }
}

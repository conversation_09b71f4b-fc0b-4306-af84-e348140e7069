<div uib-dropdown>
    <button type="button" class="btn btn-default events-dropdown_btn" uib-dropdown-toggle ng-click="$ctrl.onDropdownOpen()">
        {{$ctrl.label}}
        <span class="caret"></span>
    </button>
    <ul class="dropdown-menu events-dropdown_menu" role="menu" ng-click="$event.stopPropagation()">
        <li class="row rowm0" ng-if="$ctrl.events.length">
            <div class="col-sm-12">
                <div class="checkbox">
                    <label>
                        <input ng-change="$ctrl.toggleSelectAll()" type="checkbox" ng-model="$ctrl.isSelectedAllEvents">
                        All Events
                    </label>
                </div>
            </div>
        </li>
        <li class="row rowm0" ng-if="!$ctrl.events.length">
            <div class="col-sm-12">
                <div class="checkbox">
                    <label>
                        No events available
                    </label>
                </div>
            </div>
        </li>
        <li class="row rowm0" ng-repeat="event in $ctrl.events">
            <div class="col-sm-12">
                <div class="checkbox">
                    <label>
                        <input
                            type="checkbox"
                            ng-model="event.selected"
                            ng-change="$ctrl.toggleSelectEvent(event)"
                        >
                        {{event.long_name}}, {{event.date_start}}
                    </label>
                </div>
            </div>
        </li>
    </ul>
</div>

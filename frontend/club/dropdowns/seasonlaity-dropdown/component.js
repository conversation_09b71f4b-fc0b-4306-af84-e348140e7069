class Component {
    constructor (USAV_SEASONALITY, UtilsService) {
        this.USAV_SEASONALITY = USAV_SEASONALITY;
        this.UtilsService = UtilsService
    }

    $onInit () {
        this.seasonalityData = [
            {
                value: this.USAV_SEASONALITY.LOCAL,
                label: this.UtilsService.capitalizeFirstLetter(this.USAV_SEASONALITY.LOCAL)
            },
            {
                value: this.USAV_SEASONALITY.FULL,
                label: this.UtilsService.capitalizeFirstLetter(this.USAV_SEASONALITY.FULL)
            }
        ];
    }

    setSeasonality (value = undefined) {
        this.seasonality = value;
    }

}

Component.$inject = ['USAV_SEASONALITY', 'UtilsService'];

angular.module('SportWrench').component('seasonalityDropdown', {
    templateUrl: 'club/dropdowns/seasonlaity-dropdown/template.html',
    controller: Component,
    bindings: {
        seasonality: '='
    }
});

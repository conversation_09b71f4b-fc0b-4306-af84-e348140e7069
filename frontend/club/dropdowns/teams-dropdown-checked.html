<div class="btn-group" uib-dropdown>
    <dd-btn></dd-btn>
    <div class="dropdown-menu large-drop-down-menu dropdown-team-filter" role="menu" ng-click="$event.stopPropagation()">
        <dd-ctrls></dd-ctrls>                       
        <ul class="list-group" ng-if="teams.length">
            <li ng-class="{'list-group-item': true, 'clearfix': true, 'pointer': true, 'font-bold': (assigned === true) }" ng-click="setAssign(true)">Assigned
            </li>
            <li ng-class="{'list-group-item': true, 'clearfix': true, 'pointer': true, 'font-bold': (assigned === false) }" ng-click="setAssign(false)">Unassigned                                                      
            </li>
            <li class="list-group-item clearfix" ng-repeat="item in teams">
                <div class="col-1">
                    <input type="checkbox" ng-model="selection[item.master_team_id]" ng-change="disableAssigned()">
                </div>
                <div class="col-2">
                    <genders
                    m="item.gender !== 'female'"
                    f="item.gender !== 'male'"
                    ></genders>
                </div>
                <div class="col-3" ng-bind="item.code"></div>
                <div class="col-4" ng-bind="item.name"></div>
            </li> 
        </ul>
    </div>
</div>

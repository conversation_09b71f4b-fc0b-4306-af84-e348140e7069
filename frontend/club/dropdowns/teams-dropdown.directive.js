angular.module('SportWrench').directive('teamsDropdown', function (ClubTeamsService) {
    return {
        restrict: 'E',
        scope: {
            title: '@',
            onOpen: '&',
            btnClass: '@',
            selection: '=', // object
            onSelect: '&',
            assigned: '=' // boolead
        },
        templateUrl: function (elem, attr) {
            return (attr.checking)
                        ?'club/dropdowns/teams-dropdown-checked.html'
                        :'club/dropdowns/teams-dropdown.html'
        },
        replace: true,
        link: function (scope) {
            scope.teams = [];
            scope.utils = {
                loading: false
            }

            scope.setAssign = function (val) {
                if(val === scope.assigned) 
                    scope.assigned = undefined;
                else 
                    scope.assigned = val;
                scope.selection = {};
            }

            scope.disableAssigned = function () {
                scope.assigned = undefined;
            }
        },
        controller: function ($scope) {
            this.loadTeamsList = function () {
                $scope.utils.loading = true;
                ClubTeamsService.shortSeasonList().success(function (data) {
                    $scope.teams = data.teams;
                }).finally(function () {
                    $scope.utils.loading = false;
                })
            }
        }
    }
})

angular.module('SportWrench')

.controller('OfficialInfoController', OfficialInfoController);

function OfficialInfoController ($scope, $rootScope, $state, userService, APP_ROUTES, officialService, toastr) {
    $scope.user             = {};
    $scope.states 			= {
    	create: APP_ROUTES.OF.INFO_NEW,
    	update: APP_ROUTES.OF.INFO_UPDATE
    };

    userService.getUser().then(function (user) {
        $scope.user = user;
    });

    $scope.updateWebpointInfo = function() {
        officialService.updateWebpointInfo().then(() => {
            $rootScope.$broadcast('off.reload');
        })
    };

    $scope.$parent.$watch('official', function (val) {
    	$scope.officialExists = !_.isEmpty(val);

        $scope.isStaffOnly = function () {
            return !$scope.official.is_official && $scope.official.is_staff;
        };

        $scope.getRole = function () {
            let role = '';

            if($scope.official.is_official) {
                role = 'Official'
            }

            if($scope.official.is_staff) {
                role += role ? ', Staff' : 'Staff';
            }

            return role;
        };
    });
}

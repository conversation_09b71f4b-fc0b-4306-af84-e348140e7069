<div ng-if="loaded && showProfileRestoreAlert">
    <profile-restore-alert handler="restoreProfileData()"></profile-restore-alert>
</div>
<form name="profileForm" ng-submit="submit()" >
    <!-- Ugly hack to disable chrome autofill -->
    <div class="hide">
        <input type="text">
        <input type="password">
    </div>
    <div class="modal-header">
        <h3 class="text-info text-center">Staff/Official Profile</h3>
    </div>
    <div class="modal-body official-edit">
        <spinner active="!loaded"></spinner>
        <div class="form form-horizontal data-loading" ng-class="{'done': loaded}">
            <div class="text-info text-center">Choose all that apply</div>
            <div class="{{$root.getControlClass(profileForm, 'role', true)}}">
                <label class="col-sm-4 control-label">Role</label>
                <div class="col-sm-8">
                    <div class="btn-group btn-group-justified">
                        <div class="col-md-4">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="role" value="is_official" ng-model="details.is_official" ng-required="!details.is_staff"> Official
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="role" value="is_staff" ng-model="details.is_staff" ng-required="!details.is_official"> Staff
                                </label>
                            </div>
                        </div>                        
                    </div>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'sanctioned_by', false)}}">
                <label class="col-sm-4 control-label">Sanctioned By</label>
                <div class="col-sm-8">
                    <div class="btn-group btn-group-justified">
                        <div class="col-md-4">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sanctioned_by" value="sanctioned_by_usav" ng-model="details.sanctioned_by_usav"> USAV
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sanctioned_by" value="sanctioned_by_aau" ng-model="details.sanctioned_by_aau"> AAU
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div ng-if="hasUSAVSanctioning()"
                class="{{$root.getControlClass(profileForm, 'region', true)}}">
                <label for="region" class="col-sm-4 control-label">USAV Region</label>
                <div class="col-sm-8">
                    <select 
                        name="region" class="form-control"
                        ng-model="details.region"
                        ng-options="region.region as region.name for region in geo_data.regions | orderBy:'name'"
                        ng-required="true"
                    >
                        <option value="" selected>Select Region</option>
                    </select>
                </div>
            </div>
            <div ng-if="hasUSAVSanctioning()"
                class="{{$root.getControlClass(profileForm, 'usav_num', isUS())}}">
                <label class="col-sm-4 control-label">USAV Membership #</label>
                <div class="col-sm-8">
                    <input type="text" name="usav_num"
                           class="form-control" ng-model="details.usav_num"
                           ng-required="isUS()" usav-validator>
                </div>
            </div>
            <div ng-if="hasAAUSanctioning()"
                class="{{$root.getControlClass(profileForm, 'aau_region', true)}}">
                <label for="aau_region" class="col-sm-4 control-label">AAU Region</label>
                <div class="col-sm-8">
                    <input
                        id="aau_region"
                        type="text"
                        name="aau_region"
                        class="form-control"
                        ng-model="details.aau_region"
                        ng-required="true"
                    >
                </div>
            </div>
            <div ng-if="hasAAUSanctioning()"
                class="{{$root.getControlClass(profileForm, 'aau_number', true)}}">
                <label for="aau_number" class="col-sm-4 control-label">AAU Membership #</label>
                <div class="col-sm-8">
                    <input
                        id="aau_number"
                        type="text"
                        name="aau_number"
                        class="form-control"
                        ng-model="details.aau_number"
                        ng-required="true"
                    >
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'arbiter_pay_username', false)}} mt-30">
                <label for="arbiter_pay_username" class="col-sm-4 control-label">ArbiterPay Username</label>
                <div class="col-sm-8">
                    <input id="arbiter_pay_username"
                           type="text"
                           name="arbiter_pay_username"
                           class="form-control"
                           ng-model="details.arbiter_pay_username">
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'arbiter_pay_account_number', false)}}">
                <label for="arbiter_pay_account_number" class="col-sm-4 control-label">ArbiterPay Acc. Number</label>
                <div class="col-sm-8">
                    <input id="arbiter_pay_account_number"
                           type="text" name="arbiter_pay_account_number"
                           class="form-control"
                           ng-model="details.arbiter_pay_account_number">
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'rq_pay_username', false)}}">
                <label for="rq_pay_username" class="col-sm-4 control-label">ArbiterPay Username</label>
                <div class="col-sm-8">
                    <input id="rq_pay_username"
                           type="text"
                           name="rq_pay_username"
                           class="form-control"
                           ng-model="details.rq_pay_username">
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'rank', true)}} mt-30" ng-if="!isStaffOnly()">
                <label for="rank" class="col-sm-4 control-label">Officiating Cert. Level</label>
                <div class="col-sm-8">
                    <select name="rank" class="form-control" 
                            ng-model="details.rank" required
                            id="rank"
                            ng-options="r.id as r.name for r in ranks"
                             >
                        <option value="" selected>Select Rank</option>
                    </select>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'address', true)}}">
                <label class="col-sm-4 control-label">Address</label>
                <div class="col-sm-8">
                    <textarea type="text" name="address" class="form-control"
                        ng-model="details.address"
                        required>
                    </textarea>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'city', true)}}">
                <label class="col-sm-4 control-label">City</label>
                <div class="col-sm-8">
                    <input type="text" name="city" class="form-control"
                        ng-model="details.city" required>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'state', true)}}" ng-if="$root.countryHasState(user.country)">
                <label for="state" class="col-sm-4 control-label">{{getStateLabel()}}</label>
                <div class="col-sm-8">
                    <select name="state" class="form-control" required
                            ng-model="details.state"
                            id="state"
                            ng-options="state.state as state.name for state in geo_data.states | orderBy:'name'"
                             >
                        <option value="" selected>Select {{getStateLabel()}}</option>
                    </select>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'zip', true)}}">
                <label class="col-sm-4 control-label">ZIP</label>
                <div class="col-sm-8">
                    <input type="text" name="zip" class="form-control"
                        ng-model="details.zip" required zip-validator>
                </div>
            </div>
            <div class="form-group validation-required" ng-if="!isStaffOnly()" ng-init="details.advancement=true">
                <label class="col-sm-4 control-label">Candidate for Advancement</label>
                <div class="col-sm-8">
                    <div class="help-block">Would you like to be evaluated at an event</div>
                    <div class="radio-inline">
                        <label>
                            <input type="radio" name="advancement" ng-value="true" ng-model="details.advancement"> Yes
                        </label>
                    </div>
                    <div class="radio-inline">
                        <label>
                            <input type="radio" name="advancement" ng-value="false" ng-model="details.advancement"> No
                        </label>
                    </div>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'special_sizing_requests', false)}}">
                <label for="special_sizing_requests" class="col-sm-4 control-label"> Special Sizing Requests</label>
                <div class="col-sm-8">
                    <input id="special_sizing_requests" type="text"
                           name="special_sizing_requests" class="form-control"
                           ng-model="details.special_sizing_requests">
                </div>
            </div>

            <div
                ng-repeat="clothing in details.clothes track by $index"
                class="{{$root.getControlClass(profileForm, clothing.common_item_id, clothing.required)}}"
            >
                <label class="col-sm-4 control-label">
                    <span class="text-capitalize">{{getAlias(user.gender)}}</span>
                    {{clothing.title}}
                </label>
                <div class="col-sm-8">
                    <clothing-size-select
                        size="clothing.size"
                        type="clothing.size_type"
                        gender-size="clothing.gender_size"
                        clothing-title="clothing.title"
                        is-required="clothing.required"
                        default-gender="user.gender"
                    >
                    </clothing-size-select>
                </div>
            </div>

            <div class="{{$root.getControlClass(profileForm, 'birthdate', true)}}">
                <label class="col-sm-4 control-label" for="birthdate"> Date of Birth</label>
                <div class="col-sm-8">
                    <input
                        type="text"
                        class="white-ro form-control pointer"
                        ng-model-options="{timezone: 'utc'}"
                        datetime-picker="MM/dd/yyyy"
                        is-open="pickerStartOpened"
                        enable-time="false"
                        ng-click="pickerStartOpened = !pickerStartOpened"
                        ng-model="details.birthdate"
                        readonly required>
                </div>
            </div>
            <hr/>
            <div class="form-group">
                <p class="text-center"><b>Emergency Contact</b></p>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'emergency_contact_name', true)}}">
                <label for="emergency_contact_name" class="col-sm-4 control-label"> Name</label>
                <div class="col-sm-8">
                    <input id="emergency_contact_name" type="text"
                           name="emergency_contact_name" class="form-control"
                           ng-model="details.emergency_contact_name" required>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'emergency_phone', true)}}">
                <label for="emergency_phone" class="col-sm-4 control-label"> Mobile Phone</label>
                <div class="col-sm-8">
                    <input id="emergency_phone" ui-mask="(*************"
                           type="tel" phone-validator
                           name="emergency_phone" class="form-control"
                           ng-model="details.emergency_phone" required>
                </div>
            </div>
            <div class="{{$root.getControlClass(profileForm, 'emergency_contact_relationship', true)}}">
                <label for="emergency_contact_relationship" class="col-sm-4 control-label"> Relationship</label>
                <div class="col-sm-8">
                    <input id="emergency_contact_relationship" type="text"
                           name="emergency_contact_relationship" class="form-control"
                           ng-model="details.emergency_contact_relationship" required>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="$dismiss()">Close</button>
        <button type="submit" class="btn btn-primary">Save</button>
    </div>
</form>

angular.module('SportWrench').component('staffEvents', {
    templateUrl: 'official/staff-events/staff-events.html',
    bindings: {},
    controller: ['officialService', 'INTERNAL_ERROR_MSG', 'toastr', Component],
});

function Component(officialService) {
    this.events = null;

    this.loading = {
        success         : false,
        error           : false,
    };

    this.$onInit = () => {
        officialService.getStaffEvents()
            .then(onGetStaffEventsSuccess)
            .catch(onError);
    };

    const onGetStaffEventsSuccess = (response) => {
        this.loading.success = true;

        this.events = response && response.events || [];
    };

    const onError = () => {
        this.loading.error = true;
        this.loading.success = false;
    }
}

angular.module('SportWrench').controller('OfficialEventInfoController', OfficialEventInfoController);
function OfficialEventInfoController ($scope, $state, $stateParams, $http, $uibModalInstance, EventOfficialService, $q) {
    var eventId = $stateParams.event,
        onClose = function () {
            $state.go('^');
        };

    $scope.data = {
        event       : {},
        assignments : {}    
    };

    $scope.utils = {
        loading             : false,
        hasAssignments      : false
    };

    function loadData () {
        $scope.utils.loading = true;
        $q.all([
            EventOfficialService.regData(eventId),
            EventOfficialService.assignments(eventId)
        ]).then(function (results) {
            $scope.data.event           = results[0].data.event;
            $scope.data.eventMatches    = results[1].data.event;

            $scope.utils.hasAssignments = (assignedMatchesLength($scope.data.eventMatches) > 0);

            $scope.utils.loading = false;
        }, function () {
            $scope.utils.loading = false;
        });
    }

    function assignedMatchesLength (eventMatches) {
        var total = 0;
        Object.keys(eventMatches.assignments).forEach(function (eventDay) {
            total += eventMatches.assignments[eventDay].length;
        });
        return total;
    }

    $uibModalInstance.result.then(onClose, onClose);

    loadData();
}

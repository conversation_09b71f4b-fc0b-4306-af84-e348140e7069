<form 
    name="$ctrl.officialEventForm" 
    class="form form-horizontal" 
    ng-submit="$ctrl.submit()"
    novalidate 
    >

    <fieldset ng-disabled="$ctrl.isFieldsDisabled()">

    <div ng-class="$ctrl.controlClass(['is_official', 'is_staff'], true)">
        <label class="col-sm-4 control-label">Role at this event</label>
        <div class="col-sm-8">
            <div class="checkbox-inline" ng-show="!$ctrl.hideRoleCheckbox($ctrl.OFFICIAL_MEMBER_TYPE)">
                <label>
                    <input 
                        type="checkbox" 
                        name="is_official" 
                        ng-model="$ctrl.data.is_official" 
                        ng-required="$ctrl.isRoleRequired()"
                        ng-change="$ctrl.roleChanged()"
                        ng-disabled="$ctrl.doesOfficialCheckboxDisabled()"
                    > Official
                </label>
            </div>
            <div class="checkbox-inline" ng-show="!$ctrl.hideRoleCheckbox($ctrl.STAFF_MEMBER_TYPE)">
                <label>
                    <input 
                        type="checkbox" 
                        name="is_staff" 
                        ng-model="$ctrl.data.is_staff" 
                        ng-required="$ctrl.isRoleRequired()"
                        ng-change="$ctrl.roleChanged()"
                        ng-disabled="$ctrl.doesStaffCheckboxDisabled()"
                    > Staff
                </label>
            </div>
        </div>
    </div>

    <div ng-show="$ctrl.doesRoleChecked()">
        <div ng-if="$ctrl.event.official_additional_role_enable && $ctrl.data.is_official" ng-class="$ctrl.controlClass('offificial_additional_role', true)">
            <label class="col-sm-4 control-label">What role are you applying for</label>
            <div class="col-sm-8">
                <select
                    name="offificial_additional_role"
                    class="form-control"
                    ng-model="$ctrl.data.official_additional_role_id"
                    id="add_role"
                    ng-options="ar.id as ar.name for ar in $ctrl.officialAdditionalRoles"
                    ng-disabled="$ctrl.isOfficialApproved()"
                    required>
                    <option value="" selected>Select Option</option>
                </select>
            </div>
        </div>
        <payment-options
            c-class="$ctrl.controlClass(fieldName, isFieldRequired)"
            member-data="$ctrl.data"
            payment-options="$ctrl.event.payment_options_for_officials"
            payment-option="$ctrl.data.payment_option"
            member="$ctrl.OFFICIAL_MEMBER_TYPE"
            is-member-checked="$ctrl.data.is_official"
            hide-required-fields="$ctrl.hidePaymentRequiredFields()"
        >
        </payment-options>
        <payment-options
            c-class="$ctrl.controlClass(fieldName, isFieldRequired)"
            member-data="$ctrl.data"
            payment-options="$ctrl.event.payment_options_for_staffers"
            payment-option="$ctrl.data.staff_payment_option"
            member="$ctrl.STAFF_MEMBER_TYPE"
            is-member-checked="$ctrl.data.is_staff"
        >
        </payment-options>
        <fieldset>

            <div ng-class="$ctrl.controlClass('travel_method', true)">
                <label class="col-sm-4 control-label">Travel method</label>
                <div class="col-sm-8">
                    <select
                        class="form-control"
                        name="travel_method"
                        ng-model="$ctrl.data.travel_method"
                        required>
                        <option value="" selected>Select</option>
                        <option value="car">Car</option>
                        <option value="air">Air</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <div class="officials-hotel-form" ng-show="$ctrl.hotel_nights_enabled()">
                <div class="form-group">
                    <label class="col-sm-4 control-label">I am requesting a hotel room</label>
                    <div class="col-sm-4">
                        <label>
                            <input
                                type="checkbox"
                                ng-model="$ctrl.data.need_hotel_room"
                                name="need_hotel_room"
                            >
                        </label>
                    </div>
                </div>

                <div ng-class="$ctrl.controlClass('hotel_nights', true)" ng-if="$ctrl.data.need_hotel_room"
                     ng-init="$ctrl.initDates()">
                    <label class="col-sm-4 control-label">Nights required</label>
                    <ng-form name="hotel_nights">
                        <div ng-repeat="hotelDay in $ctrl.hotelNights track by $index"
                             ng-class="{'col-sm-8': true, 'col-sm-offset-4': $index !== 0 }">
                            <div class="col-sm-1" ng-show="!$ctrl.hotelNightsAreComp()">
                                <span ng-if="!hotelDay.paid" class="glyphicon glyphicon-usd" aria-hidden="true"></span>
                            </div>
                            <div ng-class="{'col-sm-11': !$ctrl.hotelNightsAreComp()}">
                                <label>
                                    <input
                                        type="checkbox"
                                        ng-model="$ctrl.hotelDates[hotelDay.date]"
                                        ng-change="$ctrl.selectDate(hotelDay.date, $ctrl.hotelDates[hotelDay.date])"
                                        name="hotelDay.date"
                                        ng-required="!$ctrl.checkCheckboxes()"
                                    >
                                    {{hotelDay.text}}
                                </label>
                            </div>
                        </div>
                    </ng-form>
                </div>
                <div class="form-group" ng-show="$ctrl.data.need_hotel_room && !$ctrl.hotelNightsAreComp()">
                    <label class="col-sm-6 col-sm-offset-3">
                        <span class="glyphicon glyphicon-usd" aria-hidden="true"></span>
                        Indicates nights are pay-on-your-own
                    </label>
                </div>

                <div ng-class="$ctrl.controlClass('rommate_pref')" ng-if="$ctrl.data.need_hotel_room">
                    <label class="col-sm-4 control-label">Roommate Preference</label>
                    <div class="col-sm-8">
                        <input
                            type="text"
                            class="form-control"
                            name="rommate_pref"
                            ng-model="$ctrl.data.roommate_preference"
                        >
                    </div>
                </div>
            </div>

        </fieldset>

        <div ng-class="$ctrl.controlClass('days_attend', true)" ng-if="$ctrl.data.is_official">
            <label class="col-sm-4 control-label">Schedule availability</label>
            <div class="col-sm-8">
                <ng-form name="days_attend">
                    <days-attendance
                        ng-repeat="day in $ctrl.event.days_dates track by day"
                        day="day"
                        control-name="{{$ctrl.getSchFieldName($index)}}"
                        model="$ctrl.data.schedule_availability[$ctrl.getSchPropName($index)]"
                        on-toggle="$ctrl.onAvailabilityChange()"
                    ></days-attendance>
                </ng-form>
            </div>
        </div>
        <div ng-class="$ctrl.controlClass('staff_arrival_datetime', true)" ng-if="$ctrl.data.is_staff">
            <label class="col-sm-4 control-label">Arrival Day/Time</label>
            <div class="col-sm-8">
                <input
                    type="text"
                    name="staff_arrival_datetime"
                    class="white-ro form-control pointer"
                    datetime-picker="MMM dd, yyyy hh:mm a"
                    is-open="$ctrl.pickerStartOpened"
                    ng-click="$ctrl.pickerStartOpened = !$ctrl.pickerStartOpened"
                    ng-model="$ctrl.data.staff_arrival_datetime"
                    required readonly>
            </div>
        </div>
        <fieldset>

            <div ng-class="$ctrl.controlClass('departure_datetime', true)">
                <label class="col-sm-4 control-label no-pd-top">Departure date and time Required from the Facility on the last day</label>
                <div class="col-sm-8">
                    <input
                        type="text"
                        name="departure_datetime"
                        class="white-ro form-control pointer"
                        datetime-picker="MMM dd, yyyy hh:mm a"
                        is-open="$ctrl.departurePickerStartOpened"
                        ng-click="$ctrl.departurePickerStartOpened = !$ctrl.departurePickerStartOpened"
                        ng-model="$ctrl.data.departure_datetime"
                        required readonly>
                </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-4 control-label">Conflicts and additional schedule restrictions.</label>
                <div class="col-sm-8">
                    <div class="btn-group btn-group-justified">
                     <textarea
                         style="resize: vertical"
                         class="form-control"
                         name="additional_restrictions"
                         rows="3"
                         ng-model="$ctrl.data.additional_restrictions">
                    </textarea>
                    </div>
                </div>
            </div>

        <div class="form-group"
             ng-class="$ctrl.controlClass(clothing.common_item_id, true)"
             ng-repeat="clothing in $ctrl.clothes | filter:$ctrl.filterByMemberType"
        >
            <label class="col-sm-4 control-label">{{clothing.title}}</label>
            <div class="col-sm-8">
                <div class="btn-group btn-group-justified">
                     <clothing-size-select
                         size="clothing.size"
                         gender-size="clothing.gender_size"
                         type="clothing.size_type"
                         genders="clothing.genders"
                         clothing-title="clothing.title"
                         name="clothing.common_item_id"
                         is-required="true"
                         default-gender="$ctrl.userGender"
                     >
                     </clothing-size-select>
                </div>
            </div>
        </div>

        </fieldset>

        <div class="form-group" ng-if="!$ctrl.event.finished_at">
            <div class="col-xs-offset-1 col-xs-5">
                <button
                    type="button"
                    class="btn btn-danger btn-outline"
                    ng-if="$ctrl.isWithdrawOfficialButtonVisible()"
                    sw-confirm="Are you sure you want to withdraw your application as Official from {{$ctrl.event.name}}?"
                    sw-confirm-do="$ctrl.exitEvent"
                    sw-confirm-args="'official'"
                    sw-partial-template="<withdrawal-reason-form></withdrawal-reason-form>"
                    sw-confirm-hide-no
                >Withdraw official application</button>
            </div>
            <div class="col-xs-5">
                <button
                    type="button"
                    ng-if="$ctrl.isWithdrawStaffButtonVisible()"
                    class="btn btn-danger btn-outline"
                    sw-confirm="Are you sure you want to withdraw your application as Staff from {{$ctrl.event.name}}?"
                    sw-confirm-do="$ctrl.exitEvent"
                    sw-confirm-args="'staff'"
                    sw-partial-template="<withdrawal-reason-form></withdrawal-reason-form>"
                    sw-confirm-hide-no
                >Withdraw staff application</button>
            </div>
        </div>

    </div>

    </fieldset>

</form>

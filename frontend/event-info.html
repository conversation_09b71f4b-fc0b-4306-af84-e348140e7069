<div class="container container--toll-mg-b container-fluid--toll">
    <div class="event-info-container">
        <a ui-state="states.index"><i class="fa fa-arrow-left"></i> Back to Homepage</a>
        <h4 class="text-center">{{event_info.long_name}}</h4>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">Event Info</h4>
            </div>
            <div class="panel-body">
                <event-info info="event_info"></event-info>           
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">Entered Teams</h4>
            </div>
            <div class="panel-body">
                <spinner active="event_divisions == undefined"></spinner>
                <table class="table table-condensed events-table">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Gender</th>
                            <th>Division name</th>
                            <th>Registered</th>
                            <th>Accepted</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="division-line" ng-repeat-start="division in event_divisions" ng-click="checkDivision(division)">
                            <td>
                                <i class="fa fa-angle-double-down" ng-show="division.checked"></i>
                                <i class="fa fa-angle-double-right" ng-show="!division.checked"></i>
                            </td>
                            <td>
                                <genders 
                                m="division.gender !== 'female'"
                                f="division.gender !== 'male'"
                                ></genders>
                            </td>
                            <td>
                                <a href="" ng-if="division.teams.length" ng-bind="division.division_name"></a>
                                <span ng-if="!division.teams.length" ng-bind="division.division_name"></span>
                            </td>
                            <td>{{division.entered_teams_count}}</td>
                            <td>{{division.accepted_teams_count}}</td>
                        </tr>
                        <tr ng-repeat-end ng-show="division.checked">
                            <td colspan="6" class="teams-in-division">
                                <div>
                                    <table class="table table-condensed" ng-show="division.teams.length > 0">
                                        <thead>
                                            <tr>
                                                <th>Team name</th>
                                                <th>Club name</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="team in division.teams">
                                                <td>
                                                    <b ng-if="team.status_entry == 12">{{team.team_name}}</b>
                                                    <span ng-if="team.status_entry != 12">{{team.team_name}}</span>
                                                </td>
                                                <td>{{team.club_name}}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <p ng-show="!division.teams || !division.teams.length" class="text-danger text-center">There is no teams in this division yet</p>

                                    <!-- division.teams != undefined -->
                                    <!-- <p ng-show="division.teams == undefined" class="text-danger text-center">Loading. Please wait...</p> -->
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>

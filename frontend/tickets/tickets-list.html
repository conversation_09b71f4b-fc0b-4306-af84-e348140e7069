<h3 class="h_title">Your purchases:</h3>    
<table class="table table-condensed">
    <thead>
        <tr>
            <th>Ticket Code</th>
            <th>Event Name</th>
            <th>Name</th>
            <th>Tickets Count</th>
            <th>Price</th>
            <th>Purchase Date</th>
            <th>Type</th>
            <th>Refunded</th>
        </tr>
    </thead>
    <tbody ng-if="!utils.loading">
        <tr ng-repeat="p in purchaseData.purchases" ng-class="{ 'bg-warning': isWaitlisted(p) }">
            <td>
                <a href="tickets/receipt/{{p.receipt}}" ng-if="!isWaitlisted(p)" target="_blank">{{p.ticket_barcode}}</a>
                <span ng-if="isWaitlisted(p)">N/A</span>
            </td>
            <td>{{p.event_name}}</td>
            <td>{{p.first}} {{p.last}}</td>
            <td>
                <div ng-repeat="t in ::p.tickets">{{p.is_camps? (t.camp_name + ':') : t.quantity}} {{t.label}}</div>
            </td>
            <td>{{p.amount | currency:'$'}}</td>
            <td>{{p.created}}</td>
            <td>
                <div>{{p.type}}</div>
                <a href="{{p.typeChangeLink}}" 
                    target="_blank"
                    rel="nofollow noopener" 
                    class="pointer" 
                    ng-if="canChangeType(p)"
                >
                    Pay by Card
                </a>
            </td>
            <td>{{p.date_refunded?p.date_refunded:'No'}}</td>
        </tr>
        <tr 
            ng-if="!purchaseData.purchases.length"
            no-data-row 
            cs="7" 
            text="No Tickets purchased yet."
        ></tr>          
    </tbody>
    <tbody ng-if="utils.loading">
        <tr>
            <td colspan="6" class="text-center">
                <spinner active="utils.loading"></spinner>
            </td>
        </tr>
    </tbody>
</table>        


angular.module('SportWrench').directive('noZero', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attr, ngModelCtrl) {
            function checkForZero(value) {
                if (value === '0') {
                    ngModelCtrl.$setViewValue('');
                    ngModelCtrl.$render();
                }

                return value;

            }
            ngModelCtrl.$parsers.unshift(checkForZero);
        }
    };
});

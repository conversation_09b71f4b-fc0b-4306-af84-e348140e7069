<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-info">
            <div class="panel-heading">
                {{payment.is_ticket ? 'Ticket Info' : 'Purchaser info'}}
            </div>
            <div class="panel-body form-horizontal">
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Purchased</label>
                    <div class="col-sm-8 center-text" >
                        <span ng-bind="payment.purchased"></span>
                        <span ng-if="payment.source === 'api'" class="text-danger">via API</span>
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Ticket code</label>
                    <div class="col-sm-3 center-text pr-0">
                        <receipt-link
                            show-link="{{payment.type !== 'cash' && !$parent.isWaitlisted() && payment.source !== 'api' || payment.purchaser_info.is_pending_payment}}"
                            barcode-vis="{{::payment.ticket_barcode}}"
                            barcode="{{::payment.receipt_hash}}"
                        ></receipt-link>
                    </div>
                    <div ng-if="payment.wristband_serial" class="col-sm-5 center-text">
                        <b>Wristband:</b> {{payment.wristband_serial}}
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">First</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" ng-model="payment.first" maxlength="100">
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Last</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" ng-model="payment.last" maxlength="100">
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Email</label>
                    <div class="col-sm-8">
                        <input type="email" class="form-control" email-validator ng-model="payment.email" maxlength="100">
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Phone</label>
                    <div class="col-sm-8">
                        <input type="tel" ui-mask="{{getPhoneMask()}}" class="form-control" ng-model="payment.phone" maxlength="20">
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-sm-3">Zip</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" ng-model="payment.zip" maxlength="10">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <!-- for custom fields -->
        <div class="panel panel-info" ng-if="hasAdditionalFields()">
            <div class="panel-heading">Additional (Player) Info</div>
            <div class="panel-body form-horizontal">
                <div ng-repeat="f in payment.event_fields">
                    <div class="col-sm-12">
                        <label class="control-label" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" title="{{f.label}}" ng-bind="::f.label"></label>
                    </div>
                    <div class="col-sm-12">
                        <input  type="{{field.type}}"
                                class="form-control"
                                ng-model="payment.additional_fields[f.field]"
                                ng-if="f.type !== 'select'"
                                ng-keypress="onKeypress($event)"
                        >
                        <select class="form-control"
                                ng-options="k as v for (k, v) in f.options"
                                ng-model="payment.additional_fields[f.field]"
                                ng-if="f.type === 'select'"
                                ng-keypress="onKeypress($event)"
                        >
                            <option value="">Choose a value</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="col-lg-12 text-right spacer-lg-b">
        <button class="btn btn-primary" ng-click="update()">Update Info</button>
        <div id="update-info" ng-if="!$parent.isWaitlisted()" class="btn-group" uib-dropdown>
            <button type="button" class="btn btn-info" uib-dropdown-toggle>
                Resend Ticket <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu" aria-labelledby="update-info">
                <li role="menuitem" ng-class="{'disabled': !payment.email}">
                    <a href="" ng-click="resend('email')">By Email</a>
                </li>
                <li role="menuitem" ng-class="{'disabled': !payment.phone}">
                    <a href="" ng-click="resend('phone')">By Text message</a>
                </li>
                <li role="menuitem" ng-class="{'disabled': !(payment.phone && payment.email)}">
                    <a href="" ng-click="resend()">Email & Text</a>
                </li>
            </ul>
        </div>
        <result-alert type="resultAlert.type" msg="resultAlert.msg" dismiss-time="resultAlert.dismissTime">
        </result-alert>
    </div>
</div>

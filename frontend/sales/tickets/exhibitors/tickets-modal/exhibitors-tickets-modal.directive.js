angular.module('SportWrench').directive('exhibitorsTicketsModal',
function (
    $stateParams, salesTicketsService, INTERNAL_ERROR_MSG, $filter, DateService, PAYMENT_TYPE, eventDashboardService,
    $rootScope, $uibModal, ACTIVATE, DEACTIVATE, PAYMENT_STATUS
) {
	return {
		restrict: 'E',
		scope: {
			barcode: '@'
		},
		templateUrl: 'sales/tickets/exhibitors/tickets-modal/exhibitors-tickets-modal.html',
		link: function (scope, elem, attrs, ctrl) {
			scope.payment = {};
            scope.utils = { 
                isLoaded 					: false, 
                paymentFound 				: false,
                isPickerOpened 				: false,
                alertDismissTime   			: 20000,
                partialRefundModeEnabled 	: false,
                paymentDataChanged          : false,
                partialRefund 				: {},
                eventId 					: $stateParams.event,
                history                     : []
            };
            scope.messages = {
                paymentBlock 	: {},
                scannerBlock 	: {}
            };

            ctrl.reloadPayment();

            scope.updatePaymentsList = function (barcode) {
                $rootScope.$emit('reload.payments', barcode);
            };

            scope.formatBarcode = function () {
                return scope.utils.formattedBarcode 
                    || (
                        scope.utils.formattedBarcode 
                            = scope.barcode && scope.barcode.replace(/(\d{3})(\d{3})(\d{3})/g, '$1-$2-$3')
                    );
            };

            scope.openTicketModal = function (barcode) {
                scope.$parent.$close();

                $uibModal.open({
                    template: '<exhibitors-tickets-modal barcode="' + barcode + '"></exhibitors-tickets-modal>',
                    size: 'lg'
                })
            };

            scope.deactivateTicketBarcode = (data) => {
                return salesTicketsService.deactivateExhibitorsTicketBarcode({
                    eventID: $stateParams.event,
                    ticketBarcode: scope.barcode,
                    action: scope.payment.is_ticket_deactivated ? ACTIVATE : DEACTIVATE,
                    data: data
                }).then(() => {
                    ctrl.reloadPayment(true);
                })
            };

            scope.showDeactivateTicketBarcode = () => {
                const rules = [
                    scope.payment.barcode,
                    scope.payment.status === PAYMENT_STATUS.PAID,
                ]

                return rules.every(rule => rule);
            };

            scope.isWaitlisted  = ctrl.isWaitlisted;
		},
		controller: function ($scope) {
            const self = this;

            this.resendReceipt = function (type) {
                return salesTicketsService.resendTicket($stateParams.event, $scope.barcode, {
                    type: type
                });
            };

            this.reloadPayment = function (paymentDataChanged) {
            	return salesTicketsService.paymentInfo($stateParams.event, $scope.barcode)
	            .then((response) => {
	                let data = response.data;

	                if(_.isEmpty(data.payment)) {
	                    $scope.utils.msg_type = 'info';
	                    $scope.utils.msg_text = 'No payment found.';
	                } else {
	                    $scope.payment = data.payment;
	                    $scope.utils.paymentFound       = true;

                        if($scope.utils.paymentDataChanged || paymentDataChanged) {
                            $scope.updatePaymentsList($scope.payment.barcode);
                            $scope.utils.paymentDataChanged = false;
                        }
	                }
	            }).catch(function (err) {
	                $scope.utils.msg_type = 'danger';
	                $scope.utils.msg_text = err.validation || INTERNAL_ERROR_MSG;
	            }).finally(function () {                    
	                $scope.utils.isLoaded = true;                         
	            });
            };

            this.updatePayerData = function (data) {
                return salesTicketsService.updatePayment($stateParams.event, $scope.barcode, data)
                    .then(() => {
                        $scope.utils.paymentDataChanged = true;
                        self.reloadPayment();
                    });
            };

            this.isWaitlisted = function () {
                return ($scope.payment.type === PAYMENT_TYPE.WAITLIST);
            };
        }
	}
})

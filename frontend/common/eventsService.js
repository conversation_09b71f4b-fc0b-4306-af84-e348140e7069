angular.module('SportWrench')

.factory('eventsService', function($http, INTERNAL_ERROR_MSG, moment, APP_ROUTES, $uibModal, $state) {
    var getData = function() {
        return $http.get('/events');
    };
    var createNewEvent = function(data) {
        return $http.post('/api/event', data);
    };
    var eventById = function(id, params) {
        return $http.get('/api/event/' + id, {
            params: params
        });
    };
    var getGender = function(id) {
        return $http.get('/api/event/' + id + '/genders');
    };

    var _get = function(filter_params) {
        return $http.get('api/events', {
            params: filter_params
        })
    };
    var _getEventInfo = function (id) {
        return $http.get('api/event/' + id + '/info');
    };
    var clubDates       = ['from_date', 'to_date'],
        clubDateFormat  = 'MM/DD/YYYY';
    var _getForClub = function (params) {
        var query = _.clone(params);
        if(params) {
            var dateFieldVal;
            clubDates.forEach(function (d) {
                dateFieldVal = query[d];
                if(dateFieldVal) {
                    query[d] = moment(dateFieldVal).format(clubDateFormat);
                }
            });
        }
        return $http.get('/api/club/events', {
            params: query
        });
    }; 
    var _getTackedEvent = function(id) {
        return $http.get('api/club/event/' + id)
    };
    var _getEventDivisions = function(id) {
        return $http.get('/api/club/event/' + id + '/divisions')
    };

    let copyEvent = function (id, data) {
        return $http.post('/api/event/' + id + '/copy', data);
    };

    return {    
        copy: function (id, data) {
            return copyEvent(id, data);
        },
        getTackedEvent: function(id, callback) {
            _getTackedEvent(id).then(function(resp) {
                callback(resp);
            })
        },  
        getEventDivisions: function(id, callback) {
            _getEventDivisions(id).success(function(resp) {
                callback(resp);
            })
        },
        getForClub: function(params, callback) {
            _getForClub(params).then(function(response) {
                callback(response);
            })
        },
        getEventInfo: function (event_id, callBack) {
            _getEventInfo(event_id).then(function (resp) {
                callBack(resp);
            })
        },
        get: function(params, callBack) {
            _get(params).then(function(response) {
                callBack(response);
            })
        },
        getAllEvents: function ({season, owner_type, favorite}) {
            return $http.get('/api/events', { params: { season, owner_type, favorite } });
        },
        getSeasons: function (type) {
            return $http.get('/api/seasons', {params: {type}}).then(function (response) {
                return response.data;
            })
        },
        getEvents: function(callBack) {
            getData().then(function(resp) {
                callBack(resp);
            });
        },
        createEvent: function(data, callBack) {
            createNewEvent(data).then(function (response) {
                callBack(response);
            }, function (response) {
                callBack(response);
            })
        },
        getEventById: function(id, callBack) {
            eventById(id).then(function(response) {
                callBack(response);
            })
        },
        getEvent: function (id, params) {
            return $http.get('/api/event/' + id, {
                params: params
            })
        },
        getEventByIdWithLocation:function(id, callBack) {
            eventById(id, {loc: 1}).then(function(response) {
                callBack( null, response.data );
            }, function (response) {
                callBack( response.data || INTERNAL_ERROR_MSG);
            })
        },
        deleteEvent: function (id) {
            return $http.delete('/api/event/' + id);
        },
        getEventGender: function(id, callBack) {
            getGender(id).then(function(response) {
                callBack(response);
            })
        }, 
        save_note: function (data, cb) {
            $http.post('/api/event_note', data)
            .success(function (data, status) {
                cb(data, status);
            }).error(function (data, status) {
                cb(data, status);
            })
        },
        history: function (id, params, cb) {
            $http.get('/api/event/' + id + '/history', {
                params: params
            }).success(function (data, status) {
                return cb(data, status)
            }).error(function (data, status) {
                return cb(data, status)
            })
        },
        email_info: function (data) {
            return $http.get('/api/event/' + data.id + '/history/' + data.email_id)
                .then(response => response.data)
        },
        club_owner: {
            getEventByIdWithLocation: function(id, callBack) { 
                return $http.get('/api/club/event/' + id + '/info?loc=1')
                .then(function (response) {
                    callBack(response);
                })
            }            
        },
        getEmailPreviewLink: function (eventID, emailID) {
            return '/api/event/' + eventID + '/email/' + emailID + '/preview';
        },
        duplicateEvent: function (eventID) {
            $uibModal.open({
                size        : 'sm',
                templateUrl : 'events/duplicate-event-modal.html',

                controller: function ($scope) {
                    let copyInProcess = false;

                    $scope.data = {
                        event_id: eventID,
                        duplicate_divisions: false,
                        duplicate_locations: false
                    };
                    $scope.copy = function () {
                        if (copyInProcess) {
                            return;
                        }

                        copyInProcess = true;

                        copyEvent(eventID, $scope.data)
                            .then(function (resp) {
                                $state.go(APP_ROUTES.EO.UPDATE_EVENT, {event: resp.data.event_id});
                            })
                            .finally(() => {
                                copyInProcess = false;
                            });
                    };
                }
            });
        }
    }
});

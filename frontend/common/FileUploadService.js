angular.module('SportWrench').factory('fileUploadService', FileUploadService);

function FileUploadService($http, UtilsService) {
    const ALLOWED_SHEET_FILE_TYPES = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
    ];
    const ALLOWED_XML_FILE_TYPE = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    const MAX_TEXT_FILE_SIZE_KB = 5 * 1024;

    return {
        uploadImage(eventId, file) {
            return $http.post(`/api/event/${eventId}/image`, file, {
                withCredentials: true,
                headers: { 'Content-Type': undefined },
                transformRequest: angular.identity
            })
        },
        removeImage(eventId, imageId) {
            return $http.delete(`/api/event/${eventId}/image`, { data: { id: imageId }});
        },
        getImages(eventId) {
            return $http.get(`/api/event/${eventId}/image`).then(response => response.data);
        },
        textFileUploadValidation (file) {
            const isAllowType   = ALLOWED_SHEET_FILE_TYPES.some(type => type === file.type);
            const fileSizeInKB  = UtilsService.getNextBinaryPrefixValue(file.size);

            if (!isAllowType) {
                return 'Allowable XLSX or CSV types';
            }

            if (fileSizeInKB > MAX_TEXT_FILE_SIZE_KB) {
                return `Maximum size for this file type is ${
                    UtilsService.getNextBinaryPrefixValue(MAX_TEXT_FILE_SIZE_KB)
                }MB`;
            }
        },
        xmlFileUploadValidation (file) {
            const isAllowType   = ALLOWED_XML_FILE_TYPE.some(type => type === file.type);
            const fileSizeInKB  = UtilsService.getNextBinaryPrefixValue(file.size);

            if (!isAllowType) {
                return 'Allowable only XLSX type';
            }

            if (fileSizeInKB > MAX_TEXT_FILE_SIZE_KB) {
                return `Maximum size for this file type is ${
                    UtilsService.getNextBinaryPrefixValue(MAX_TEXT_FILE_SIZE_KB)
                }MB`;
            }
        }
    }
}

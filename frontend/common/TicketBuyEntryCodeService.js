angular.module('SportWrench').service('TicketBuyEntryCodeService', [
    '$http', 'TEAM_PAYMENT_STATUS_ARRAY', 'TEAM_STATUS', 'ENTRY_STATUS', 'TEAM_ENTRY_STATUS', '$uibModal',
    TicketBuyEntryCodeService
]);

function TicketBuyEntryCodeService (
    $http, TEAM_PAYMENT_STATUS_ARRAY, TEAM_STATUS, ENTRY_STATUS, TEAM_ENTRY_STATUS, $uibModal
) {
    this.http = $http;

    this.TEAM_PAYMENT_STATUS_ARRAY = TEAM_PAYMENT_STATUS_ARRAY;
    this.TEAM_STATUS = TEAM_STATUS;
    this.ENTRY_STATUS = ENTRY_STATUS;
    this.TEAM_ENTRY_STATUS = TEAM_ENTRY_STATUS;
    this.$uibModal = $uibModal;
}

TicketBuyEntryCodeService.prototype.upsert = function (eventID, settings) {
    return this.http.post(`/api/ticket-entry-code/event/${eventID}/settings`, { settings });
}

TicketBuyEntryCodeService.prototype.getDefaultCouponSettings = function () {
    const settings = {
        team_code_settings: {
            entry_statuses: this.ENTRY_STATUS.filter(item => item.id === this.TEAM_ENTRY_STATUS.ACCEPTED),
            payment_statuses: this.TEAM_PAYMENT_STATUS_ARRAY.filter(item => item.id === this.TEAM_STATUS.PAYMENT.PAID),
        },
        team_code_source_enabled: true,
        custom_code_source_enabled: false,
        is_active: true
    }

    return settings;
}

TicketBuyEntryCodeService.prototype.getCodesList = function (eventID, filters) {
    return this.http.get(
        `/api/ticket-entry-code/event/${eventID}/list`,
        { params: filters, paramSerializer: '$httpParamSerializerJQLike' }
    ).then(response => response.data);
}

TicketBuyEntryCodeService.prototype.couponTicketsList = function (eventID, code) {
    return this.http.get(`/api/ticket-entry-code/event/${eventID}/code/${code}/tickets`)
        .then(response => response.data);
}

TicketBuyEntryCodeService.prototype.createCustomCode = function (eventID, code) {
    return this.http.post(`/api/ticket-entry-code/event/${eventID}/code/custom`, { code });
}

TicketBuyEntryCodeService.prototype.openTicketBuyEntryCodeCreationModal = function () {
    return this.$uibModal.open({
        size: 'sm',
        template: `
            <ticket-buy-entry-code-creation-form on-save="close(code)" close="close()">
            </ticket-buy-entry-code-creation-form>
        `,
        controller: ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
            scope.close = function (code) {
                $uibModalInstance.close(code);
            }
        }]
    }).result
}

TicketBuyEntryCodeService.prototype.openCouponTicketsModal = function (code) {
    let self = this;

    return this.$uibModal.open({
        template: `
                <modal-wrapper>
                    <coupon-bought-tickets-list code="couponCode" get-tickets-fn="boughtTicketsList(eventID, code)">
                    </coupon-bought-tickets-list>
                </modal-wrapper>
        `,
        controller: ['$scope', function (scope) {
            scope.couponCode = code;
            scope.modalTitle = `<h4>Coupon ${code}</h4>`;
            scope.modalShowClose = true;
            scope.boughtTicketsList = self.couponTicketsList.bind(self);
        }]
    })
}

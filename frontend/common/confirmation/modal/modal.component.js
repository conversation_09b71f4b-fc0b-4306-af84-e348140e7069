angular.module('SportWrench').directive('confirmModal', confirmModal);

function confirmModal () {
    return {
        restrict    : 'E',
        templateUrl : 'common/confirmation/modal/modal.html',
        transclude  : true,
        controller: ['$scope', function ($scope) {
            $scope.onYesResp = function () {
                $scope.$close($scope.yesResp);
            }

            $scope.onNoResp = function () {
                $scope.$dismiss($scope.noResp);
            }

            $scope.onCancelResp = function () {
                $scope.$dismiss($scope.cancelResp);
            }
        }]
    }
}

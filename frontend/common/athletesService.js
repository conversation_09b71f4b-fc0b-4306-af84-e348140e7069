angular.module('SportWrench')

.factory('athletesService', function($http) {
    var _getAthletes = function() {
        return $http.get('/master_athlete');
    };
    var _getMasterTeamAthletes = function (master_team_id) {
        return $http.get('/master_athlete', {
            params: {
                master_team_id: master_team_id
            }
        });
    };
    var _getAthletesByGender = function (master_team_id, gender) {
        return $http.get('/master_athlete', {
            params: {
                master_team_id  : master_team_id,
                gender          : gender
            }
        });
    };

    var _getAthlete = function(id) {
        return $http.get('/api/v2/club/master_athlete/' + id);
    };

    var _addToTeam = function(dataObj) {
        return $http.post('/api/master_athlete/move', dataObj);
    };

    var _getTeamAthletes = function(id) {
        return $http.get('/api/master_athlete/team/' + id);
    };

    var _getTeamMembers = function(id) {
        return $http.get('/api/master_athlete/team/' + id + '/members');
    };

    return {
        getAthlete: function(id) {
            return $http.get('/api/v2/club/master_athlete/' + id)
                .then(response => response.data);
        },
        getAthletes: function(callBack) {
            _getAthletes().then(function(response) {
                callBack(response);
            });
        },
        getMasterTeamAthletes: function(master_team_id, callBack) {
            _getMasterTeamAthletes(master_team_id).then(function(response) {
                callBack(response);
            });
        },
        updateAthlete: function(id, athlete) {
            return $http.put( '/api/v2/club/master_athlete/' + id + '/update', {
                athlete: athlete
            });
        },
        getAvailableAthletes: function(gender, callBack) {
            _getAthletesByGender('null', gender).then(function(response) {
                callBack(response);
            });
        },
        addToTeam: function(dataObj, callback) {
            _addToTeam(dataObj).then(function(response) {
                callback(response);
            });
        },
        getTeamAthletes: function(id, callBack) {
            _getTeamAthletes(id).success(function(resp) {
                callBack(resp);
            });
        },
        getTeamMembers: function(id, callBack) {
            _getTeamMembers(id).success(function(resp) {
                callBack(resp);
            });
        },
        removeAthletesFromClub: function (athletes) {
            return $http.delete('/api/v2/club/master_athlete', { data: { athletes } })
        },
    };
});

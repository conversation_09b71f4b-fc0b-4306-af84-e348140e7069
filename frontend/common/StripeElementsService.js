angular.module('SportWrench').service('StripeElementsService', ['StripeV3', 'UtilsService', '$q', StripeElementsService]);

function StripeElementsService (StripeV3, UtilsService, $q) {
    this._stripeV3  = StripeV3;
    this._utils     = UtilsService;
    this.$q         = $q;
}

StripeElementsService.prototype.initStripeElements = function (key) {
    if(!key) {
        throw new Error('Undefined stripe key');
    }

    this.stripeInstance = this._stripeV3(key);
};

StripeElementsService.prototype.getCardElement = function (style = {}) {
    let elements = this.stripeInstance.elements();
    return elements.create('card', { style });
};

StripeElementsService.prototype.collectBankAccountToken = async function (clientSecret) {
    return this.stripeInstance.collectBankAccountToken({
        clientSecret,
    })
}

StripeElementsService.prototype.createStripeElementsToken = function (card) {
    if(_.isEmpty(card)) {
        return Promise.reject({ message: 'Card object is empty' });
    }

    return this.stripeInstance.createToken(card).then((response) => {
        if (response.error) {
            throw response.error;
        } else {
            return response.token && response.token.id;
        }
    })
};

StripeElementsService.prototype.createPaymentRequestButton = function (total, country = 'US', currency = 'usd') {
    return this.$q((resolve, reject) => {
        let paymentRequest = this.stripeInstance.paymentRequest({
            country : country,
            currency: currency,
            total: {
                amount : this._utils.approxNumber(total * 100),
                label  : 'Teams',
                pending: true
            }
        });

        return paymentRequest.canMakePayment().then(result => {
            if(result) {
                let elements = this.stripeInstance.elements();
                let button   = elements.create('paymentRequestButton', { paymentRequest });

                resolve({ button, paymentRequest });
            }

            resolve(null);
        })
    })
};

StripeElementsService.prototype.confirmCardSetup = function (clientSecret, cardElement) {
    return this.$q((resolve, reject) => {
        return this.stripeInstance.confirmCardSetup(
            clientSecret,
            {
                payment_method: {
                    card: cardElement
                },
            }
        ).then(result => {
            if(!result) {
                reject({message: 'Internal Stripe Error'});
            }

            if(result.error) {
                reject(result.error);
            }

            resolve(result)
        });
    });
}

angular.module('SportWrench').factory('StripeService', ['$q', '$http', '$window', function ($q, $http, $window) {
    var _CONNECT_URL = 'https://connect.stripe.com/oauth/authorize';
    const STRIPE_PLATFORM_REGISTRATION_URL = 'https://dashboard.stripe.com/account/applications/settings';
    const PLATFORM_CLIENT_ID_PATTERN = /[a-z]{2}_[a-zA-Z0-9]{32}/g;
    const STRIPE_ACCOUNT_TYPES = {
                STANDARD: 'standard',
                EXPRESS: 'express',
                CUSTOM: 'custom'
    };
	return {
		setPublishableKey: function (key) {
            if(!key) {
            	throw new Error('Undefined publishable key');
            }

            Stripe.setPublishableKey(key);
        },
        createCardToken: function (card) {
            if(_.isEmpty(card)) {
            	throw new Error('Card object is empty');
            }

            var defer = $q.defer();

            Stripe.card.createToken(card, function (status, resp) {
                if(resp && resp.error) {
                    defer.reject(resp.error);
                } else {
                    defer.resolve(resp.id);
                }
            });

            return defer.promise;
        },
        getPlatformClientID: function () {
            return $http.get('/api/platform/client-id').then(function (resp) {
                return resp.data;
            });
        },
        connect: function (clientID, isTest, stripeUserEmail) {
            var url = _CONNECT_URL + '?response_type=code&client_id=' + clientID + '&scope=read_write&state=';

            url += isTest?'test':'live';

            if (stripeUserEmail) {
                url += ('&stripe_user[email]=' + stripeUserEmail);
            }

            var w = $window.open(url, '_blank');
            
            if (!w) {
                window.location = url;
            }
        },
        getPlatformRegistrationURL : function () {
            return STRIPE_PLATFORM_REGISTRATION_URL;
        },
        platformClientIdPattern : function () {
            return PLATFORM_CLIENT_ID_PATTERN;
        },
        savePlatformClientId : function(account_id, platform_client_id) {
            return $http.put('/api/eo/stripe-acc/save_platform_client_id', { account_id, platform_client_id });
        },
        toggleStripeAccountVisibility : function(id, visibility) {
            return $http.put('/api/eo/stripe-acc/account/' + id + '/visibility/' + visibility);
        },
        platformStripeAccountType: function() {
            return STRIPE_ACCOUNT_TYPES.STANDARD;
        },
        hasEventPlatform: function (event_id) {
            return $http.get('/api/eo/' + event_id + '/stripe-accounts/has_platform')
                .then(res => res.data.has_platform);
        },
        makePayout: function(eventId, officialId, amount) {
            return $http.put('/api/event/' + eventId + '/officials/' + officialId + '/payout',
                                        { amount });
        },
        getPayoutsHistory: function(eventId, officialId) {
            return $http.get('/api/event/' + eventId + '/officials/payouts/history', { params : { officialId }})
                .then(res => res.data);
        },
        getOfficialStripeDashboardURL: function(eventId) {
            return $http.get('/api/event/' + eventId + '/officials/payouts/dashboard_url')
                .then(res => res.data);
        },
        generateExpressAcccountCreationLink: function (eventId) {
            return $http.get('/api/event/' + eventId + '/officials/payouts/create_express_account_url')
                .then(res => res.data);
        }
	};
}]);
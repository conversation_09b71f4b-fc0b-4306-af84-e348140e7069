angular.module('SportWrench').service('StripePaymentElementService',
    ['StripeV3', 'UtilsService', '$q', StripePaymentElementService]
);

function StripePaymentElementService (StripeV3, UtilsService, $q) {
    this._stripeV3  = StripeV3;
    this._utils     = UtilsService;
    this.$q         = $q;
}

StripePaymentElementService.prototype.init = function (key) {
    if(!key) {
        throw new Error('Undefined stripe key');
    }

    this.stripeInstance = this._stripeV3(key);
};

StripePaymentElementService.prototype.getPaymentElement = function (secret, params) {
    let elements = this.stripeInstance.elements({clientSecret: secret});

    let paymentElement = elements.create('payment', params);

    return { elements, paymentElement };
}

StripePaymentElementService.prototype.confirmPayment = function (elements, returnUrl) {
    return this.stripeInstance.confirmPayment({
        elements,
        confirmParams: { return_url: returnUrl },
        redirect: 'if_required'
    });
}

StripePaymentElementService.prototype.confirmCardPayment = function (secret, payment_method, return_url) {
    return this.stripeInstance.confirmCardPayment(secret, {
        payment_method,
        return_url,
    });
}
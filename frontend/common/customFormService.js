
class Service {
    constructor ($http, $window) {
        this.http = $http;
        this.window = $window;
    }

    async getForm (eventID, formID, submitter) {
        const { data } = await this.http.get(
            `/api/custom-form/event/${eventID}/form/${formID}`,
            { params: { submitter }}
        );

        return data;
    }

    submitForm (eventID, formID, data, submitter) {
        return this.http.post(`/api/custom-form/event/${eventID}/form/${formID}`, { values: data, submitter });
    }

    async exportFormResults (eventID, formID) {
        const url = `/api/custom-form/event/${eventID}/form/${formID}/export`;

        const w = this.window.open(url, '_blank');

        if (!w) {
            window.location = url;
        }
    }
}

Service.$inject = ['$http', '$window'];

angular.module('SportWrench').service('CustomFormService', Service);

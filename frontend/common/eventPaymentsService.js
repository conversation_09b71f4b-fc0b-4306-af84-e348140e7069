angular.module('SportWrench').factory('eventPaymentsService', ['$http', function ($http) {
    var urlPrefix = '/api/event/';
    return {
        eventPayments: function (event_id, params) {
            return $http.get(urlPrefix + event_id + '/payments', {
                params: params
            });
        },
        paymentTeams: function (event_id, purchase_id, callback) {
            $http.get(urlPrefix + event_id + '/payment/' + purchase_id + '/teams')
            .then(function(resp) {
                callback(resp);
            });
        },
        receive: function (event_id, purchase_id, date_paid, check_num, received_amount) {
            return $http.put(urlPrefix + event_id + '/payment/' + purchase_id + '/receive', {
                date_paid       : date_paid,
                check_num       : check_num,
                received_amount : received_amount
            });
        },
        save_note: function (event_id, purchase_id, note) {
            var url = urlPrefix + event_id + '/payment/' + purchase_id + '/notes';

            return $http.post(url, { notes: note });
        },
        fingerprintCharges: function (event_id, fingerprint) {
            return $http.get(urlPrefix + event_id + '/payments/fingerprint/' + fingerprint);
        },
        sendEmailToCustomer: function (event_id, purchase_id, email) {
            return $http.post(urlPrefix + event_id + '/ticket/'+purchase_id+'/send-email', { email: email });
        },
        getPurchaseEmails: function (event_id, purchase_id) {
            return $http.get(urlPrefix + event_id + '/ticket/'+purchase_id+'/emails');
        },
        getPaymentState: function (event_id, purchase_id) {
            return $http.get(urlPrefix + event_id + '/payment/' + purchase_id + '/state')   
            .then(function (res) {
                return res.data && res.data.payment || {};
            })         
        }
    };
}]);

angular.module('SportWrench')

    .factory('teamHousingService', function ($http, $window) {
        return {
            getBookings: function (eventID, rosterTeamID) {
                return $http.get('/api/housing/events/' + eventID + '/teams/' + rosterTeamID + '/bookings')
                    .then(response => response.data && response.data.team);
            },
            loadHistory: function (rosterTeamID, thsID) {
                return $http.get('/api/housing/events/teams/' + rosterTeamID + '/history/' + thsID + '/ths')
                    .then(response => response.data && response.data.history);
            },
            loadContacts: function (eventID, rosterTeamID) {
                return $http.get('/api/housing/events/' + eventID + '/teams/' + rosterTeamID + '/contacts')
                    .then(response => response.data && response.data.contacts);
            },
            changeTeamStatus: function (eventID, rosterTeamID, housingStatus, notes) {
                return $http.put(
                    '/api/housing/events/' + eventID + '/teams/' + rosterTeamID + '/status',
                    {
                        status_housing: housingStatus,
                        notes,
                    }
                )
            },
            export: function (eventID) {
                $window.location = '/api/housing/events/' + eventID + '/teams/export';
            },
        };
    });

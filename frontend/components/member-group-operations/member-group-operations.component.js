angular.module('SportWrench').component('memberGroupOperations', {
    templateUrl: 'components/member-group-operations/member-group-operations.html',
    bindings: {
        totalCount      : '=',
        selectedCount   : '=',
        showWithdrawn   : '=',
        groupWorkStatus : '=',
        onSave          : '&',
        onSendEmail     : '&',
        onExport        : '&',
    },
    controller: Component
});

Component.$inject = [];

function Component() {
    this.getFormClass = () => ({
        'form form-inline invisible-fields': true,
        'make-invisible': this.selectedCount > 0
    })
}

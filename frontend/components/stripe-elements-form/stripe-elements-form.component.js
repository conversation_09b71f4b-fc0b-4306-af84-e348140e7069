angular.module('SportWrench').component('stripeElementsForm', {
    templateUrl: 'components/stripe-elements-form/stripe-elements-form.html',
    bindings: {
        payment: '<',
        pay    : '&'
    },
    controller: StripeElementsFormController
});

StripeElementsFormController.$inject = [
    'StripeElementsService', '$stateParams', 'ClubPaymentsService', '$rootScope', 'toastr', '$scope'
];

function StripeElementsFormController(
    StripeElementsService, $stateParams, ClubPaymentsService, $rootScope, toastr, scope
) {
    let self = this;

    let initAmount, cardDataIsFilled;

    this.$onInit = function () {
        initCardForm();
        initPaymentRequestButton();

        initAmount = self.payment.total;
    };

    this.utils = {
        hidePaymentRequestButton: false,
        paymentBtnIsLoading     : true,
        payButtonPressed        : false
    };

    this.submit = function () {
        this.utils.payButtonPressed = true;

        if(!cardDataIsFilled) {
            this.utils.payButtonPressed = false;
            toastr.warning('Invalid card data');
            return;
        }

        return StripeElementsService.createStripeElementsToken(self.card)
            .then(token =>
                self.pay({token})
                    .then(() => this.utils.payButtonPressed = false)
                    .catch((err) => {
                        this.utils.payButtonPressed = false;
                        console.error(err);
                    })
            ).catch(error => {
                showError(error);

                self.utils.payButtonPressed = false;

                scope.$digest();
            })
    };

    this.disableSubmit = function () {
        return Number(self.payment.total) === 0 || self.payment.inProgress || this.utils.payButtonPressed;
    };

    this.$doCheck = function () {

        // need to reload Payment Request Button after amount change
        if(initAmount !== self.payment.total) {
            initPaymentRequestButton();

            initAmount = self.payment.total;
        }

        if(self.payment.total === 0 && !self.utils.hidePaymentRequestButton) {
            self.utils.hidePaymentRequestButton = true;
        }
    };

    function initCardForm() {
        self.card = StripeElementsService.getCardElement();

        // Add an instance of the card Element into the `card-element` <div>.
        // https://stripe.com/docs/stripe-js/reference#element-mount
        self.card.mount('#card-element');

        // https://stripe.com/docs/stripe-js/reference#element-on
        self.card.addEventListener('change', ({error, complete}) => {
            showError(error);

            // complete = true if the value is well-formed and potentially complete.
            cardDataIsFilled = complete;
        });
    }

    function showError (error) {
        const displayError = document.getElementById('card-errors');

        // current validation error
        if (error) {
            displayError.textContent = error.message;
        } else {
            displayError.textContent = '';
        }
    }

    function initPaymentRequestButton() {
        self.utils.paymentBtnIsLoading = true;
        return StripeElementsService.createPaymentRequestButton(self.payment.total)
            .then(result => {
                if (result) {
                    let {button, paymentRequest} = result;

                    if(self.utils.hidePaymentRequestButton) {
                        self.utils.hidePaymentRequestButton = false;
                    }

                    // Add an instance of the card Element into the `card-element` <div>.
                    // https://stripe.com/docs/stripe-js/reference#element-mount
                    button.mount('#payment-request-button');

                    self.utils.paymentBtnIsLoading = false;

                    paymentRequest.on('token', function (ev) {
                        self.pay({token: ev.token.id})
                            .then(() => {
                                if (self.payment.chargeCompleted) {
                                    // Report to the browser that the payment was successful, prompting
                                    // it to close the browser payment interface.
                                    ev.complete('success');
                                } else {
                                    // Report to the browser that the payment failed, prompting it to
                                    // re-show the payment interface, or show an error message and close
                                    // the payment interface.
                                    ev.complete('fail');
                                }
                            });
                    });
                } else {
                    self.utils.hidePaymentRequestButton = true;
                }

            })
            .catch(err => {
                console.error(err);
                self.utils.paymentBtnIsLoading = false;
            });
    }
}

<div ng-bind-html="info.event_notes | unsafe"></div><br/>

<div class="row">
    <div class="col-sm-6">        
        <div><b>Event Dates:</b></div>
        <div>Start Date: {{info.date_start | date: "MM/dd/yyyy"}}</div>
        <div>End Date: {{info.date_end | date: "MM/dd/yyyy"}}</div>        
        <div ng-if="locationsExists()" ng-repeat="location in info.locations">
            <br/>
            <div><b ng-bind="getLocationTitle(location.number)"></b></div>
            <div ng-if="location.state">State: {{location.state}}</div>
            <div ng-if="location.city">City: {{location.city}}</div>
            <div ng-if="location.address">Address: {{location.address}}</div>
            <div ng-if="location.location_name">Location: {{location.location_name}}</div>
        </div>
    </div>
    <div class="col-sm-6">        
        <div ng-if="hasTeams()">
            <div><b>Registration Info:</b></div>
            <div>Open Date: {{info.date_reg_open | date: "MM/dd/yyyy"}}</div>
            <div>Close Date: {{info.date_reg_close | date: "MM/dd/yyyy"}}</div>
            <div>Registration Fee: <span ng-repeat="fee in info.all_fees">{{fee | currency:'$'}}{{$last ? '' : ', '}}</span></div>
            <div ng-if="!isDoubles() && info.has_rosters">Roster Deadline: {{info.roster_deadline | UTCdate: "MM/DD/YYYY"}}</div>
            <div ng-if="info.sport_sanctioning_name">Sanctioning Body: {{info.sport_sanctioning_name}}</div>
            <div ng-if="(info.mincount_enter > 0 && !isDoubles())">Roster Count Required to Enter Event: {{info.mincount_enter}}</div>
            <div ng-if="(info.mincount_accept > 0 && !isDoubles())">Roster Count Required for Acceptance: {{info.mincount_accept}}</div>
            <br/>
            <div><b>Team Genders:</b></div>
            <div>
                <span ng-if="info.has_male_teams === true">Male</span>
                <span ng-if="info.has_female_teams === true">Female</span>
                <span ng-if="info.has_coed_teams === true">Coed</span>
            </div>
            <br/>
        </div>        
        <div><b>Contact Info:</b></div>
        <div class="link-block">
            <span>Website:</span>
            <short-link href="info.website"></short-link>
        </div>
        <div class="link-block">
            <span>Email:</span>
            <a ng-href="mailto:{{info.email}}">{{info.email}}</a>
        </div>
        <br/>
        <div ng-if="hasSocialLinks()"><b>Follow us:</b></div>
        <div>
            <social-icons icons="info.social_links"></social-icons>
        </div>        
        <div class="row-space">
            <button class="btn btn-success" ng-if="info.doubles_reg_available" ng-click="openDoublesRegForm()">Open Registration Page</button>
        </div>     
    </div>
</div>
<br/>
<div class="row-space" ng-if="showSWTDate()">
    <i class="fa fa-ticket"></i> Tickets Purchase will be available on {{info.tickets_purchase_date_start}}
</div>
<div class="row-space" ng-if="showSWTLink()">
    <i class="fa fa-ticket"></i> Tickets Purchase available <a href="" ng-click="openSWT();">here</a>
</div>

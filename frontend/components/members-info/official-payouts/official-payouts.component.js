angular.module('SportWrench').component('officialPayouts', {
    templateUrl: 'components/members-info/official-payouts/official-payouts.html',
    bindings: {
        official    : '<',
        eventId     : '<',
        officialId  : '<',
    },
    controller: ['StripeService',  Component],
});

function Component(StripeService) {
    this.payouts = {};

    this.loading = {
        success         : false,
        error           : false,
        errorMessage    : '',
    };

    this.$onInit = () => {
        this.getPayoutsHistory();
    };

    this.getPayoutsHistory = function () {
        return StripeService.getPayoutsHistory(this.eventId, this.officialId)
            .then(response => {
                this.payouts.history    = response.payouts_history;
                this.payouts.total      = response.total;

                this.loading.success = true;
            });
    };
}

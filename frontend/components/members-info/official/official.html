<uib-alert type="danger" ng-if="$ctrl.loading.error">{{ $ctrl.loading.errorMessage }}</uib-alert>
<spinner active="!$ctrl.loading.success"></spinner>

<div ng-if="$ctrl.loading.success">
    <div class="modal-body">
        <official-reg-info-credentials
            official="$ctrl.official"
            on-submit="$ctrl.updateOfficial(data)"
            on-changed="$ctrl.changesListener(isChanged)"
            event-official-additional-role-enabled="$ctrl.event.official_additional_role_enable"
            official-additional-roles="$ctrl.officialAdditionalRoles">
        </official-reg-info-credentials>
        <email-notifications-receiver
            official="$ctrl.official"
            email-contact-provider="$ctrl.emailContactProvider">
        </email-notifications-receiver>
        <member-info
            member="$ctrl.official"
            payment-methods="$ctrl.event.payment_methods"
            event-dates="$ctrl.event.days_dates"
            clothes="$ctrl.clothes"
            show-scan-history="true"
        >
        </member-info>
    </div>

    <div class="modal-footer">
        <a class="btn btn-info spacer-sm-r pointer-events-auto"
                ng-click="$ctrl.openEntryQRCodePage()"
                ng-if="$ctrl.showEntryQRCodeBtn()"
                uib-tooltip="{{$ctrl.disableShowQRCodeBtn ? 'QR code is not generated before sending. Send QR code first.' : ''}}"
                tooltip-append-to-body="true"
                ng-disabled="$ctrl.disableShowQRCodeBtn">Show QR Code</a>
        <button class="btn btn-info spacer-sm-r"
                ng-click="$ctrl.onSendQR()"
                ng-if="$ctrl.showSendQRBtn()">Send QR Code</button>
        <button class="btn btn-success spacer-sm-r"
                ng-click="$ctrl.onMakeHead()"
                ng-if="$ctrl.showMakeHeadBtn()"
                ng-disabled="$ctrl.disableMakeRemoveHeadBtn">Make Head</button>
        <button class="btn btn-warning spacer-sm-r"
                ng-click="$ctrl.onRemoveHead()"
                ng-if="$ctrl.showRemoveHeadBtn() && !$ctrl.official.deleted"
                ng-disabled="$ctrl.disableMakeRemoveHeadBtn">Remove Head</button>
        <button class="btn btn-default" ng-click="$ctrl.onClose()">Close</button>
    </div>
</div>

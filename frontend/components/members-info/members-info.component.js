angular.module('SportWrench').component('membersInfo', {
    templateUrl: 'components/members-info/members-info.html',
    bindings: {
        memberId    : '<',
        eventId     : '<',
        memberLabel : '<',
        operationService: '<',
    },
    controller: [
        '$scope',
        'ConfirmationService',
        'EventACLService',
        'MembersInfoService',
        'STAFF_MEMBER_TYPE',
        'OFFICIAL_MEMBER_TYPE',
        'EVENT_OPERATIONS',
        'StripeService',
        'userService',
        Component
    ]
});

function Component(
    $scope,
    ConfirmationService,
    EventACLService,
    MembersInfoService,
    STAFF_MEMBER_TYPE,
    OFFICIAL_MEMBER_TYPE,
    EVENT_OPERATIONS,
    StripeService,
    userService
) {
    this.INTERNAL_SERVER_ERROR = 'Internal Server Error.';

    this.loading = {
        childLoaded : false,
        error: false,
        errorMessage: '',
    };

    this.isStaff    = false;
    this.isOfficial = false;

    this.INDEX_TABS = {
        OFFICIAL_INFO   : 'official_info',
        STAFF_INFO      : 'staff_info',
        PAYOUTS         : 'payouts'
    };

    this.MEMBER_TABS = {
        [STAFF_MEMBER_TYPE]: this.INDEX_TABS.STAFF_INFO,
        [OFFICIAL_MEMBER_TYPE]: this.INDEX_TABS.OFFICIAL_INFO,
    };

    this.hasUnsavedChanges  = false;
    this.hasChangesNotSet   = true;
    this.closingConfirmed   = false;

    this.isOfficialChanged  = false;
    this.isStaffChanged     = false;

    this.userAcl        = null;
    this.isStaff        = false;
    this.isOfficial     = false;

    this.official        = {};
    this.isShowPayoutTab = false;

    this.onTabSelect = (indexTab) => {
        this.currentTab = indexTab;
    };

    this.initTab = (tab) => {
        return this.currentTab === tab;
    };

    this.$onInit = () => {
        this.loadingModalTitle();

        this.userAcl    = EventACLService.getUserAcl();
        this.currentTab = this.getDefaultTab();

        $scope.modalSkipFooter = true;

        if (userService.hasEORole()) {
            StripeService.hasEventPlatform(this.eventId)
                .then(this.onHasEventPlatformSuccess, this.onError);
        }
    };

    this.onHasEventPlatformSuccess = (hasPlatform) => {
        this.isShowPayoutTab = hasPlatform && this.official.work_status === 'approved';
    };

    this.onError = (error) => {
        this.loading.error = true;

        this.loading.errorMessage = (error.data && error.data.validation)
            ? error.data.validation
            : this.INTERNAL_SERVER_ERROR
    };

    this.loadingModalTitle = () => {
        $scope.modalTitle = '<h4>Loading...</h4>';
    };

    this.setStaffModalTitle = ({ first, last }) => {
        $scope.modalTitle = `<h4>${first} ${last} Entry </h4>`;
    };

    this.setModalTitle = function(member, isHead) {
        const { first, last, reason, deleted, withdrawal_date } = member;

        $scope.modalTitle = `<h4>${first} ${last} Entry </h4>`;

        if (isHead) {
            $scope.modalTitle += '<span class="text-success">(IS HEAD OFFICIAL)</span>';
        }

        if (reason && deleted) {
            $scope.modalTitle += `<span>(Withdrawn ${withdrawal_date})</span>`
        }
    };

    this.onOfficialChanged = (isChanged) => {
        this.hasUnsavedChanges = isChanged;
    };

    this.close = (result) => {
        $scope.$parent.$close(result);
    };

    this.resetUnsavedChanges = () => {
        this.hasUnsavedChanges = false;
    };

    this.isMemberChanged = () => {
        return this.isOfficialChanged || this.isStaffChanged;
    };

    this.getDefaultTab = () => {
        return this.MEMBER_TABS[this.memberLabel];
    };

    this.showStaffTab = () => {
        if (this.memberLabel === STAFF_MEMBER_TYPE) {
            return true;
        }

        return this.userAcl ? this.isStaff && this.userAcl[EVENT_OPERATIONS.STAFF_TAB] : this.isStaff;
    };

    this.showOfficialTab = () => {
        if (this.memberLabel === OFFICIAL_MEMBER_TYPE) {
            return true;
        }

        return this.userAcl ? this.isOfficial && this.userAcl[EVENT_OPERATIONS.OFFICIALS_TAB] : this.isOfficial;
    };

    this.showPayoutTab =() => {
        return this.showOfficialTab() && this.isShowPayoutTab;
    };

    this.childIsLoaded = () => {
        this.loading.childLoaded = true;
    };

    this.isLoaded = () => {
        return this.loading.childLoaded;
    };

    this.getMemberStatuses = (member) => {
        this.isStaff = member.is_staff;
        this.isOfficial = member.is_official;
    };

    $scope.$on('modal.closing', ($event) => {
        if (this.hasUnsavedChanges && !this.closingConfirmed) {
            $event.preventDefault();

            ConfirmationService.ask(
                'There are unsaved changes. Do you want to discard them and close?',
                {
                    disableNoBtn: true,
                    title: 'Unsaved Changes'
                }
            ).then(answer => {
                if (answer === ConfirmationService.YES_RESP) {
                    this.closingConfirmed = true;

                    this.close(this.isMemberChanged());
                }
            });
        } else if (this.hasChangesNotSet) {
            $event.preventDefault();

            this.hasChangesNotSet = false;

            this.close(this.isMemberChanged());
        }
    })
}

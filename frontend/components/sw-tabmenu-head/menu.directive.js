angular.module('SportWrench').directive('swTabmenuHead', ['$log', '$window', '$timeout', '$state', swTabmenuHead]);

function swTabmenuHead ($log, $window, $timeout, $state) {
    return {
        restrict: 'E',
        scope: {
            tabs: '=',
            headerClass: '@'
        },
        transclude: true,
        templateUrl: 'components/sw-tabmenu-head/menu.html',   
        link: function (scope, elem) {
            scope.activeStateName   = '';
            scope.menuCollapsed     = false;
            scope.utils             = { smallDevice: false };            

            scope.toggleBtnClick = function () {            
                $timeout(function() {
                    var toggleBtn = elem.find('.sw-menu-toggle-btn')[0];

                    if (toggleBtn) {
                        toggleBtn.click(); 
                    }
                }, 0);
            }           

            function mediaListener (mQ) {
                scope.utils.smallDevice = mQ.matches;   
                        
                if( ( mQ.matches && !scope.menuCollapsed ) || 
                    ( !mQ.matches && scope.menuCollapsed ) ) {
                    // bicycle: collapse does not close/open if change trigger variable programmatically 
                    scope.toggleBtnClick();
                }
            }

            var mql = $window.matchMedia('(max-width: 480px)');
            mql.addListener(mediaListener);
            mediaListener(mql);             

            scope.collapseToggle = function () {
                scope.menuCollapsed = !scope.menuCollapsed;
            }

            scope.isCollapsed = function () {
                return scope.menuCollapsed;
            }           

            scope.isActiveTab = function (t) {
                var _active;

                if (t.state) {
                    _active = $state.is(t.state) || _.includes($state, t.state);

                    if(_active) scope.activeStateName = t.name;

                    return _active;
                } else if (!_.isEmpty(t.states)) {
                    for (var i = 0, l = t.states.length; i < l; ++i) {

                        _active = $state.is(t.states[i].state);

                        if(_active) {
                            scope.activeStateName = (t.name + ': ' + t.states[i].name);
                            
                            return _active;
                        }
                    }
                }

                return false;                
            }

            scope.isActiveState = function (stateName) {
                return $state.is(stateName);
            }

            scope.isDisabledTab = function (tab) {
                return ( tab.disabled && tab.disabled() )
            }

            scope.openTab = function (state, params) {
                $state.go(state, params);
                if(scope.utils.smallDevice) {
                    scope.toggleBtnClick();
                }
            }  

            scope.getStateParams = function (tab) {
                return ( tab.stateParams && tab.stateParams() )
            }

            scope.getTabName = function (tab) {
                if(!tab.name) return null;
                return ( typeof tab.name === 'function' )?tab.name():tab.name;
            }

            scope.dropdownTab = function (tab) {
                return !_.isEmpty(tab.states);
            }   

            scope.getTabType = function (tab) {
                var isDropDown = scope.dropdownTab(tab);
                if(!isDropDown)
                    return (tab.name)?'name':'template';
                return 'dropdown';
            }

            scope.$on('$destroy', function () {        
                mql.removeListener(mediaListener);
            });
        }
    }
}

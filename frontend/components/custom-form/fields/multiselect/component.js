
class Controller {
    $onInit () {
        this.formSettings = this.getMultiSelectSettings();
        this.value = [];
        this.internalValue = [];

        this.multiselectEventHandlers = {
            onItemSelect: this.onItemSelect.bind(this),
            onItemDeselect: this.onItemDeselect.bind(this),
        }
    }

    getMultiSelectSettings () {
        let settings = {
            showCheckAll: false,
            showUncheckAll: false,
            buttonClasses: 'btn btn-default full-width',
            smartButtonTextProvider(selectionArray) {
                let selectedString = selectionArray.map(arr => arr.label).join(', ');

                if(selectedString.length > 30) {
                    selectedString = selectedString.slice(0, 30);
                }

                return selectedString + '...';
            }
        }

        if(!_.isEmpty(this.field.settings)) {
            if(this.field.settings.maxQtySelected > 0) {
                settings.selectionLimit = this.field.settings.maxQtySelected;
            }
        }

        return settings;
    }

    onItemSelect (item) {
        this.value.push(item.id);
    }

    onItemDeselect (item) {
        this.value = this.value.filter(v => v === item.id);
    }
}

angular.module('SportWrench').component('customFormFieldMultiselect', {
    templateUrl: 'components/custom-form/fields/multiselect/template.html',
    bindings: {
        value: '=',
        field: '<',
        fieldHasError: '&'
    },
    controller: Controller
});


<div class="row">
    <div class="col-xs-12 text-center" ng-if="$ctrl.formDataLoading">
        <i class="fa fa-spinner fa-pulse fa-2x text-center"></i>
    </div>
    <div class="col-xs-12" ng-if="!$ctrl.formDataLoading">
        <div class="row">
            <div class="col-xs-12 text-center">
                <h3 ng-bind="$ctrl.form.header_text"></h3>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <form class="form form-horizontal custom-form" name="$ctrl.customForm">
                    <div class="row" data-ng-repeat="section in $ctrl.form.sections track by $index">
                        <div class="col-xs-12">
                            <div class="row" data-ng-repeat="field in section">

                                <custom-form-field-signature
                                        ng-if="field.type === 'signature'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-signature>

                                <custom-form-field-date
                                        ng-if="field.type === 'date'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-date>

                                <custom-form-field-paragraph
                                        class="col-xs-12"
                                        ng-if="field.type === 'paragraph'"
                                        field="field"
                                ></custom-form-field-paragraph>

                                <custom-form-field-signature-checkbox
                                        ng-if="field.type === 'signature_checkbox'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-signature-checkbox>

                                <custom-form-field-text
                                        ng-if="field.type === 'text'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-text>

                                <custom-form-field-checkbox
                                        ng-if="field.type === 'checkbox'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-checkbox>

                                <custom-form-field-multiselect
                                        ng-if="field.type === 'multiselect'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-multiselect>

                                <custom-form-field-select
                                        ng-if="field.type === 'select'"
                                        value="$ctrl.values[field.id]"
                                        field="field"
                                        field-has-error="$ctrl.fieldHasError(field)"
                                ></custom-form-field-select>

                            </div>
                        </div>
                        <div class="col-xs-12" ng-if="$ctrl.showSectionLine($index)">
                            <hr/>
                        </div>
                    </div>
                    <button class="btn btn-success pull-right" type="button" ng-click="$ctrl.onSubmit()">Submit</button>
                    <button class="btn btn-default pull-right" type="button" ng-click="$ctrl.cancel()">Cancel</button>
                </form>
            </div>
        </div>
    </div>
</div>

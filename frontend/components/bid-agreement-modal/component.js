

class Controller {
    constructor ($scope, MALE_CHAR, FEMALE_CHAR, toastr, TEAM_AGREEMENT_VALUE) {
        this.$scope = $scope;
        this.toastr = toastr;

        this.MALE_CHAR = MALE_CHAR;
        this.FEMALE_CHAR = FEMALE_CHAR;
        this.TEAM_AGREEMENT_VALUE = TEAM_AGREEMENT_VALUE;
    }

    $onInit () {
        this.$scope.modalTitle
            = '<h4>Will you accept or decline the bid if this team is in a position to earn the bid?</h4>';

        this.teamsList = angular.copy(this.teams);
    }

    save () {
        let error = false;

        let result = this.teamsList.reduce((all, team) => {
            if(team.agreed === 'true') {
                all[this.TEAM_AGREEMENT_VALUE.ACCEPTED].push(team.team_id);
            } else if(team.agreed === 'false'){
                all[this.TEAM_AGREEMENT_VALUE.DECLINED].push(team.team_id);
            }

            if(_.isUndefined(team.agreed)) {
                error = true;
            }

            return all;
        }, { [this.TEAM_AGREEMENT_VALUE.ACCEPTED]: [], [this.TEAM_AGREEMENT_VALUE.DECLINED]: [] });

        if(error) {
            this.toastr.warning('Bid not selected for all teams');
            return;
        }

        this.close({ teams: result });
    }

    declineAll () {
        let result = this.teamsList = this.teamsList.reduce(
            (all, team) => {
                all[this.TEAM_AGREEMENT_VALUE.DECLINED].push(team.team_id);

                return all;
            },
            { [this.TEAM_AGREEMENT_VALUE.ACCEPTED]: [], [this.TEAM_AGREEMENT_VALUE.DECLINED]: [] }
        );

        this.close({ teams: result });
    }

    getDivisionLabel (team) {
        let genderChar = team.division_gender === 'male'
            ? this.MALE_CHAR
            : this.FEMALE_CHAR;

        return `${genderChar} ${team.division_name}`;
    }
}

Controller.$inject = ['$scope', 'MALE_CHAR', 'FEMALE_CHAR', 'toastr', 'TEAM_AGREEMENT_VALUE'];

angular.module('SportWrench').component('bidAgreementModal', {
    templateUrl: 'components/bid-agreement-modal/template.html',
    bindings: {
        teams: '<',
        close: '&'
    },
    controller: Controller
});

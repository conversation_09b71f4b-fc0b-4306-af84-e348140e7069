angular.module('SportWrench').directive('reverseArrow', function () {
    return {
        restrict: 'E',
        scope: {
            reverse: '=',
            show: '@'
        },
        template: "<span ng-if=\"show === 'true'\">" +
                  "     <i ng-class=\"{'fa': true,  'fa-sort-asc': !reverse, 'fa-sort-desc': reverse}\"></i>" +
                  "</span>",
        controller: function () {}
    }
});

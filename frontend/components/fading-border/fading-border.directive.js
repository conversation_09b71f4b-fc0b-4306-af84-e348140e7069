angular.module('SportWrench').directive('fadingBorder', fadingBorder);

function fadingBorder ($timeout) {
	return {
		restrict 	: 'A',
		link 		: function (scope, elem, attrs) {
			elem.addClass('fading-elem');
			scope.$watch(attrs.fadingBorder, function (n, o) {
				if(n !== o) {
					if(elem.hasClass('fading-border')) {
						elem.removeClass('fading-border');
					}
					$timeout(function () {
						elem.addClass('fading-border');
					});
				}
			});
		}
	};
}
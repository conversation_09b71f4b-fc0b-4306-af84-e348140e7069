<form class="form form-horizontal" name="$ctrl.form" novalidate>
    <div class="modal-body">
        <div class="form-group vendor-type-wrapper">
            <label class="col-xs-4 vendor-type-label">Vendor type:</label>
            <label class="col-xs-4 vendor-type-item">
                <input type="checkbox" ng-model="$ctrl.data.is_exhibitor"> Exhibitor
            </label>
            <label class="col-xs-4 vendor-type-item">
                <input type="checkbox" ng-model="$ctrl.data.is_sponsor"> Sponsor
            </label>
        </div>
        <div class="form-group text-center">
            <label class="col-xs-offset-4 col-xs-4 vendor-type-item">
                <input type="checkbox" ng-model="$ctrl.data.is_non_profit"> Non Profit
            </label>
            <label class="col-xs-4 vendor-type-item">
                <input type="checkbox" ng-model="$ctrl.data.is_other"> Other
            </label>
        </div>
        <div class="{{$ctrl.getControlClass('company', true)}}">
            <label class="col-sm-4 control-label">Company Name:</label>
            <div class="col-sm-8">
                <input type="text" name="company" class="form-control" ng-model="$ctrl.data.company_name" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-4 control-label">Company Description:</label>
            <div class="col-sm-8">
                <textarea class="form-control" ng-model="$ctrl.data.company_description" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-4 control-label">Has Samples:</label>
                <div class="col-sm-8">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" ng-model="$ctrl.data.company_samples"> &nbsp;                  
                        </label>
                    </div>               
                </div>                 
            </div>
            <div ng-class="{'form-group': true, 'has-error': $ctrl.form.$submitted && ($ctrl.form.drink.$invalid || $ctrl.form.food.$invalid) }" ng-if="$ctrl.data.company_samples">
                <label class="col-sm-4 control-label">Company Samples:</label>
                <div class="col-sm-8">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="drink" ng-model="$ctrl.data.samples_food" ng-required="!$ctrl.data.samples_beverages"> Food
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="food" ng-model="$ctrl.data.samples_beverages" ng-required="!$ctrl.data.samples_food"> Beverages
                        </label>
                    </div>                
                </div>                 
            </div>
            <div class="form-group">
                <label class="col-sm-4 control-label">Badge Names:</label>
                <div class="col-sm-8">
                    <textarea class="form-control" ng-model="$ctrl.data.badge_names"></textarea>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('first', true)}}">
            <label class="col-sm-4 control-label">First Name:</label>
            <div class="col-sm-8">
                <input type="text" name="first" class="form-control" ng-model="$ctrl.data.first" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('last', true)}}">
            <label class="col-sm-4 control-label">Last Name:</label>
            <div class="col-sm-8">
                <input type="text" name="last" class="form-control" ng-model="$ctrl.data.last" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-4 control-label">Title:</label>
            <div class="col-sm-8">
                <input type="text" class="form-control" ng-model="$ctrl.data.sponsor_title">
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('email', true)}}">
            <label class="col-sm-4 control-label">Email:</label>
            <div class="col-sm-8">
                <input type="email" name="email" class="form-control" email-validator ng-model="$ctrl.data.email" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('website_url', false)}}">
            <label class="col-sm-4 control-label">Website address:</label>
            <div class="col-sm-8">
                <input type="url" name="website_url" class="form-control" ng-model="$ctrl.data.website_url" url-validator>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('mhone', true)}}">
            <label class="col-sm-4 control-label">Mobile Phone:</label>
            <div class="col-sm-8">
                <input type="tel" name="mhone" ui-mask="(*************" class="form-control"
                    ng-model="$ctrl.data.mobile_phone" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('ophone', true)}}">
            <label class="col-sm-4 control-label">Office Phone:</label>
            <div class="col-sm-8">
                <input type="tel" name="ophone" ui-mask="(*************" class="form-control"
                    ng-model="$ctrl.data.office_phone" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('street', true)}}">
            <label class="col-sm-4 control-label">Street:</label>
            <div class="col-sm-8">
                <input type="text" name="street" class="form-control" ng-model="$ctrl.data.street" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('city', true)}}">
            <label class="col-sm-4 control-label">City:</label>
            <div class="col-sm-8">
                <input type="text" name="city" class="form-control" ng-model="$ctrl.data.city" required>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('state', true)}}">
            <label class="col-sm-4 control-label">State:</label>
            <div class="col-sm-8">
                <select class="form-control" ng-model="$ctrl.data.state" name="state" required
                    ng-options="st.state as st.name for st in $ctrl.states | orderBy:'name'">
                    <option value="" selected>Select...</option>
                </select>
            </div>
        </div>
        <div class="{{$ctrl.getControlClass('zip', true)}}">
            <label class="col-sm-4 control-label">ZIP:</label>
            <div class="col-sm-8">
                <input type="text" name="zip" class="form-control" ng-model="$ctrl.data.zip" zip-validator required>
            </div>
        </div>
    </div>
</form>

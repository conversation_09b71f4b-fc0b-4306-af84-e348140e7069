angular.module('SportWrench').component('manualAddTeamModal', {
    templateUrl: 'components/manual-add-team/manual-add-team-modal.html',
    bindings: {
        divisions           : '<',
        close               : '&',
        onSave              : '&',
        hasManualClubNames  : '<'
    },
    controller: ManualAddTeamModal
});

ManualAddTeamModal.$inject = ['$scope'];

function ManualAddTeamModal($scope) {
    let self = this;

    this.submitForm = function () {
        $scope.$broadcast('ManualAddTeamFormSubmitted');
    };

    this.save = function (data) {
        return self.onSave({data});
    };
}

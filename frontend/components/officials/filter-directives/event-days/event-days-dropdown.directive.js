angular.module('SportWrench').directive('eventDaysDd', function () {
    return {
        restrict: 'E',
        scope: {
            selection: '=',
            eventDays: '=',
            listTitle: '@'
        },
        templateUrl: 'components/officials/filter-directives/event-days/event-days-dropdown.html',
        require: '^officials',
        replace: true,
        link: function (scope, attrs, elem, ctrl) {
            scope.toggle = function (id) {
                this.selection[id] = !this.selection[id];
            }
        }
    }
})

<div class="panel panel-default" ng-if="$ctrl.isApprovedHeadReferee()">
    <div class="panel-heading">Email Notifications</div>
    <div class="panel-body">
        <label class="pointer">
            <input  type="checkbox" 
                    ng-model="$ctrl.official.is_email_notifications_receiver" 
                    ng-change="$ctrl.onEmailNotificationsReceiverChange()"
                    ng-disabled="$ctrl.updateInProgress">
            Receive Email Notifications about officials
        </label>
        <br>
        <label class="pointer">
            <input  type="checkbox" 
                    ng-model="$ctrl.official.is_email_contact_provider"
                    ng-change="$ctrl.onEmailContactProviderChange()"
                    ng-disabled="$ctrl.updateInProgress">
            Put <u>{{$ctrl.official.first}} {{$ctrl.official.last}}'s</u> name and contacts to emails sent to the officials                        
        </label>
    </div>
</div>

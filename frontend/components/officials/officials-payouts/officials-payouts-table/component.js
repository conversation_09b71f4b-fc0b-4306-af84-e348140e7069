class Component {
    constructor(
        $stateParams, OfficialsPayoutsService, $document, LOAD_PAYOUTS_ACTION, $rootScope, OFFICIALS_PAYOUT_MEMBER_TYPE
    ) {
        this.matchTypes = [];
        
        this.$stateParams = $stateParams;
        this.service = OfficialsPayoutsService;
        this.$document = $document;
        this.initialAdditionalPayment = {};
        this.initialPayouts = [];
        this.parentTr = null;
        this.parentTd = null;
        this.eventID = $stateParams.event;
        this.LOAD_PAYOUTS_ACTION = LOAD_PAYOUTS_ACTION;
        this.$rootScope = $rootScope;

        this.markPayoutInfoLoading = {
            inProcess: false,
            error: ''
        }

        this.OFFICIALS_PAYOUT_MEMBER_TYPE = OFFICIALS_PAYOUT_MEMBER_TYPE;
    }

    $onChanges(changes) {
        if (changes.payouts) {
            if (_.isEmpty(changes.payouts.previousValue) || _.isEqual(changes.payouts.currentValue, changes.payouts.previousValue)) {
                return;
            }

            this.initData(changes.payouts.currentValue);
        }
    }

    $onInit() {
        this.initData();
    }

    isOfficialsType() {
        return this.type === this.OFFICIALS_PAYOUT_MEMBER_TYPE;
    }

    initData(payouts = this.payouts) {
        this.initialPayouts = JSON.parse(JSON.stringify(payouts));

        if(this.isOfficialsType()) {
            this.matchTypes = this.getMatchTypes(payouts);
        }
    }

    getMatchTypes(payouts) {
        if(!this.isOfficialsType()) {
            return [];
        }

        return payouts[0].official_matches.map(({ type }) => type);
    }

    // TODO Create directive or component and use it in all places
    formatAmount(amount, useEmpty = false) {
        if (!amount && !useEmpty) {
            return '';
        } else if (amount < 0) {
            return `-$${Math.abs(amount)}`
        } else {
            return `$${amount}`;
        }
    }

    formatAdditionalTotalAmount({ event_official_id,  totalAdditional}) {
        if (totalAdditional !== 0) {
            return this.formatAmount(totalAdditional);
        }

        const isAdditionalPaymentsZero = this.initialPayouts
            .find(({ event_official_id: id }) => id === event_official_id)
            .additional_payments
            .every(({ amount }) => amount === 0);

        if (isAdditionalPaymentsZero) {
            return this.formatAmount(totalAdditional);
        } else {
            return `$${totalAdditional}`;
        }
    }

    formatCategoryTitle({ category }) {
        if (category === 'Miscellaneous') {
            return 'Misc';
        }

        return category;
    }

    formatPaymentMethod({ payment_method }) {
        const methodLabels = {
            'on_site': 'On Site',
            'mailed': 'Mailed',
            'arbiterpay': 'ArbiterPay',
            'direct_deposit': 'Direct Deposit',
            'no_payment_required': 'No Payment Required',
        };

        return  methodLabels[payment_method];
    }

    onFocusAdditionalPaymentCell($event, payment, event_official_id) {
        this.highlightSelectedRow($event);

        this.initialAdditionalPayment = Object.assign({}, payment, { event_official_id });
    }

    updateAdditionalPayment(payment, event_official_id) {
        const _payment = Object.assign({}, payment, { event_official_id });

        this.removeHighlightSelectedRow();

        if (_.isEqual(_payment, this.initialAdditionalPayment)) {
            return;
        }

        this.onUpdateAdditionalPayment({ 
            eventID: this.$stateParams.event, 
            payment: _payment,
            type: this.type
        });
    }

    addClassToElement(element, className) {
        element.addClass(className);
    }

    removeClassFromElement(element, className) {
        element.removeClass(className);
    }

    highlightSelectedRow($event) {
        const element = angular.element($event.target)[0];

        this.parentTd = angular.element(element.parentNode);
        this.parentTr = angular.element(element.parentNode.parentNode);

        this.addClassToElement(this.parentTd, 'clear-background');
        this.addClassToElement(this.parentTr, 'highlight-row');
    }

    removeHighlightSelectedRow() {
        this.removeClassFromElement(this.parentTr, 'highlight-row');
        this.removeClassFromElement(this.parentTd, 'clear-background');
    }

    setMarkPayoutInfoLoadingStatus(inProcess) {
        this.markPayoutInfoLoading.inProcess = inProcess;
    }

    setBodyCursor(value) {
        this.$document[0].body.style.cursor = value;
    }

    openMarkPayoutModal({ event_official_id }) {
        if (this.markPayoutInfoLoading.inProcess) {
            return;
        }

        this.setMarkPayoutInfoLoadingStatus(true);
        this.setBodyCursor('wait');

        this.service.getMarkPayoutInfo(this.eventID, event_official_id, this.type)
            .then(response => {
                this.service.openMarkPayoutModal(
                    this.eventID, event_official_id, response, this.type, this.reloadTable.bind(this)
                );
            })
            .finally(() => {
                this.setMarkPayoutInfoLoadingStatus(false);
                this.setBodyCursor('default');
            }) 
    }

    reloadTable() {
        this.$rootScope.$broadcast(this.LOAD_PAYOUTS_ACTION);
    }

    getNegativeNumberClass(value) {
        return {
            'negative-number': value < 0,
        }
    }
} 

angular.module('SportWrench').component('officialsPayoutsTable', {
    templateUrl: 'components/officials/officials-payouts/officials-payouts-table/template.html',
    bindings: {
        payouts: '<',
        total: '<',
        additionalCategories: '<',
        onUpdateAdditionalPayment: '&?updateAdditionalPayment',
        canUpdatePayment: '<',
        type: '<'
    },
    controller: [
        '$stateParams','OfficialsPayoutsService', '$document', 'LOAD_PAYOUTS_ACTION', '$rootScope',
        'OFFICIALS_PAYOUT_MEMBER_TYPE',
        Component
    ]
})

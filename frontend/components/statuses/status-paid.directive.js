angular.module('SportWrench').directive('statusPaid', function () {
    return {
        restrict: 'E',
        scope: {
            status: '@'
        },
        templateUrl: 'components/statuses/status-paid.html'
    }
})

angular.module('SportWrench').directive('statusEntry', function () {
    return {
        restrict: 'E',
        scope: {
            status: '@'
        },
        templateUrl: 'components/statuses/status-entry.html'
    }
});

angular.module('SportWrench').directive('statusHousing', function () {
    return {
        restrict: 'E',
        scope: {
            status: '@'
        },
        templateUrl: 'components/statuses/status-housing.html'
    }
})

angular.module('SportWrench').component('sizingChart', {
    templateUrl: 'components/sizing-chart/sizing-chart.html',
    bindings: {
        officialGroups: '<',
        role: '<',
        filters: '<',
        closeModal: '&',
    },
    controller : Sizing<PERSON>hartController
});

SizingChartController.$inject = ['UtilsService', 'EventStaffService', '$stateParams'];

function SizingChartController (UtilsService, EventStaffService, $stateParams) {
   this.title = UtilsService.capitalizeFirstLetter(this.role) + ' Sizing Chart';
   this.groups = ['male', 'female'].map(gender => {
       const {
           eventClothes,
           officials,
           totals,
       } = this.officialGroups[gender];

       return {
           gender,
           eventClothes,
           officials,
           totals,
       };
   });
    this.isSubmitting = false;

   this.export = () => {
       if(this.isSubmitting) {
           return;
       }
       this.isSubmitting = true;
       EventStaffService.exportClothingList($stateParams.event, this.role, this.filters)
           .then(()=>{
               this.isSubmitting = false;
           });
   };
}

angular.module('SportWrench').directive('sizingChartTotals',
    ['UtilsService', function (UtilsService) {
        return {
            restrict: 'E',
            scope: {
                gender: '<',
                eventClothes: '<',
                totals: '<',
            },
            templateUrl: 'components/sizing-chart/sizing-chart-totals/sizing-chart-totals.html',
            link: function (scope, elem, attrs, ctrl) {
                scope.title = scope.gender === 'male' ? 'Mens' : 'Womens';
            }
        }
    }]
);

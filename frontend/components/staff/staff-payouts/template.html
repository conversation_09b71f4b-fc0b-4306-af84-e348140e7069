<div class="row row-space">
    <div class="col-sm-12">
        <spinner active="!$ctrl.loading.loaded"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div class="div" ng-if="$ctrl.loading.loaded">
        <div class="col-sm-12" ng-if="!$ctrl.loading.error">
            <button ng-if="$ctrl.showDefaultAdditionalPayments()" ng-click="$ctrl.openDefaultAdditionalPayments()" class="btn btn-primary">Default Additional Payments</button>
            <button ng-if="$ctrl.showExportButton()" ng-click="$ctrl.export()" class="btn btn-default to-right">Export Staffs Payments</button>
        </div>
        <div class="col-sm-12 officials-payouts-table_wrapper" ng-if="!$ctrl.loading.error">
            <div class="row">
                <div class="col-sm-5" ng-if="$ctrl.payoutsAreEmpty.errorMessage">
                    <uib-alert type="danger text-center">{{$ctrl.payoutsAreEmpty.errorMessage}}</uib-alert>
                </div>
            </div>
            <officials-payouts-table
                ng-if="!$ctrl.payoutsAreEmpty.errorMessage"
                additional-categories="$ctrl.additionalCategories"
                payouts="$ctrl.payouts"
                total = "$ctrl.total"
                type="$ctrl.STAFF_MEMBER_TYPE"
                update-additional-payment="$ctrl.onUpdateAdditionalPayment(eventID, payment, type)"
                can-update-payment="$ctrl.canUpdatePayment"
            ></officials-payouts-table>
        </div>
    </div>
</div>

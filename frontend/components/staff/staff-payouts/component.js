class Component {
    constructor(
        OfficialsPayoutsService,
        $rootScope,
        SHOW_STAFF_PAYOUTS_TAB_ACTION,
        APP_ROUTES,
        $stateParams,
        INTERNAL_ERROR_MSG,
        LOAD_PAYOUTS_ACTION,
        PAYMENT_OPTIONS,
        STAFF_MEMBER_TYPE
    ) {
        this.service = OfficialsPayoutsService;
        this.$rootScope = $rootScope;
        this.SHOW_STAFF_PAYOUTS_TAB_ACTION = SHOW_STAFF_PAYOUTS_TAB_ACTION;
        this.routes = APP_ROUTES;
        this.$stateParams = $stateParams;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.LOAD_PAYOUTS_ACTION = LOAD_PAYOUTS_ACTION;
        this.PAYMENT_OPTIONS = PAYMENT_OPTIONS;
        this.STAFF_MEMBER_TYPE = STAFF_MEMBER_TYPE;

        this.payouts = [];
        this.total = {};

        this.loading = {
            loaded: false,
            error: false,
        }

        this.payoutsAreEmpty = {
            errorMessage: '',
        }

        this.canUpdatePayment = true;
    }

    $onInit() {
        this.$rootScope.$broadcast(this.SHOW_STAFF_PAYOUTS_TAB_ACTION);
        this.loadPayouts();

        this.$rootScope.$on(this.LOAD_PAYOUTS_ACTION, () => {
            this.loadPayouts();
        })
    };

    loadPayouts() {
        this.service.getPayouts(this.$stateParams.event, this.STAFF_MEMBER_TYPE)
            .then(this.onGetPayoutsSuccess.bind(this))
            .catch(this.onGetPayoutsFail.bind(this))
            .finally(() => {
                this.canUpdatePayment = true;
            })
    }

    onGetPayoutsSuccess(response) {
        const {  payouts, total, additionalCategories, paymentMethodsAllowed } = response;

        this.payoutsAreEmpty.errorMessage = '';

        if (!payouts.length) {
            this.payoutsAreEmpty.errorMessage = 'Payouts list is empty. Fill officials rates first';
        }

        this.payouts = payouts;
        this.total = total;
        this.additionalCategories = additionalCategories;
        this.allowedPaymentMethodTypes = paymentMethodsAllowed || {};

        this.loading.error = '';
        this.loading.loaded = true;
    }

    onGetPayoutsFail(error) {
        this.loading.loaded = true;

        this.loading.error = error && error.validation
            ? error.validation
            : this.INTERNAL_ERROR_MSG
    }

    showDefaultAdditionalPayments() {
        return this.payouts.length;
    }

    openDefaultAdditionalPayments() {
        this.service.showDefaultAdditionalPayments(this.additionalCategories, this.STAFF_MEMBER_TYPE);
    }

    onUpdateAdditionalPayment(eventID, payment, type) {
        this.canUpdatePayment = false;

        return this.service.updateAdditionalPayment(eventID, payment, type)
            .finally(() => this.loadPayouts());
    }

    showExportButton() {
        return this.payouts.length;
    }

    export() {
        this.service.openExportModal(this.allowedPaymentMethodTypes, this.STAFF_MEMBER_TYPE);
    }
}

angular.module('SportWrench').component('staffPayouts', {
    templateUrl: 'components/staff/staff-payouts/template.html',
    controller: [
        'OfficialsPayoutsService',
        '$rootScope',
        'SHOW_STAFF_PAYOUTS_TAB_ACTION',
        'APP_ROUTES',
        '$stateParams',
        'INTERNAL_ERROR_MSG',
        'LOAD_PAYOUTS_ACTION',
        'PAYMENT_OPTIONS',
        'STAFF_MEMBER_TYPE',
        Component
    ],
});

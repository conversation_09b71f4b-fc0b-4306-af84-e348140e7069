<div class="member-info_block-wrapper">
    <div class="row">
        <info-block-header label="Event Application"></info-block-header>

    </div>
    <div class="row" ng-if="$ctrl.member.payment_option">
        <div class="col-xs-6">
            <span class="font-bold">Payment option:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.paymentMethods[$ctrl.member.payment_option].label}}</span>
            <span
                ng-if="!$ctrl.paymentMethods[$ctrl.member.payment_option].available"
                class="text-danger font-bold"
            >
            (Not Available Option)
        </span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showBankAccountNumber()">
        <div class="col-xs-6">
            <span class="font-bold">Bank Account Number:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.bank_account_number}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showBankAccountRouting()">
        <div class="col-xs-6">
            <span class="font-bold">Bank Account Routing#:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.bank_account_routing}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showArbiterpayUsername()">
        <div class="col-xs-6">
            <span class="font-bold">ArbiterPay Username:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.arbiterpay_username}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showArbiterpayAccountNumber()">
        <div class="col-xs-6">
            <span class="font-bold">ArbiterPay Account Number:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.arbiterpay_account_number}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showRqPayUsername()">
        <div class="col-xs-6">
            <span class="font-bold">RQ Pay Username:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.rq_pay_username}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.member.travel_method">
        <div class="col-xs-6">
            <span class="font-bold">Travel method:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.getTravelMethodLabel()}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showNeedHotelRoom()">
        <div class="col-xs-6">
            <span class="font-bold">Need hotel room:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.getNeedHotelRoomLabel()}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showNeedHotelNights()"
         ng-init="$ctrl.getDates()">
        <div class="col-xs-6">
            <span class="font-bold">Need hotel nights:</span>
        </div>
        <div class="col-xs-6">
            <div class="row" ng-repeat="date in $ctrl.dates">
                <div class="col-xs-6">
                    <span class="text-grey">{{date.date}}</span>
                </div>
                <div class="col-xs-6">
                    <span>{{date.decision}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showRoommatePreferences()">
        <div class="col-xs-6">
            <span class="font-bold">Roommate preferences:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.roommate_preference}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showScheduleAvailability()">
        <div class="col-xs-6">
            <span class="font-bold">Schedule availability:</span>
        </div>
        <div class="col-xs-6">
            <official-reg-schedule
                dates="$ctrl.eventDates"
                schedule-availability="$ctrl.member.schedule_availability"
            >
            </official-reg-schedule>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showArrivalDatetime()">
        <div class="col-xs-6"><span class="font-bold">Arrival datetime:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.getDate($ctrl.member.arrival_datetime) | date: 'MM/dd hh:mm a'}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.showDepartureDatetime()">
        <div class="col-xs-6"><span class="font-bold">Departure datetime:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.getDate($ctrl.member.departure_datetime) | date: 'MM/dd hh:mm a'}}</span>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.member.additional_restrictions">
        <div class="col-xs-6">
            <span class="font-bold">Additional restrictions:</span>
        </div>
        <div class="col-xs-6">
            <span>{{$ctrl.member.additional_restrictions}}</span>
        </div>
    </div>

</div>

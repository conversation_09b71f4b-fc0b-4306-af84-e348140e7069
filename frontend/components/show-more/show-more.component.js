angular.module('SportWrench').component('showMore', {
    templateUrl : 'components/show-more/show-more.html',
    bindings    : {
        itemsList       : '<',
        maxItemsActive  : '@'
    },
    controller: function () {
        this.isShortList = function () {
            return this.itemsList.length <= Number(this.maxItemsActive);
        };

        this.isShortListItem = function (index) {
            return (index + 1) <= Number(this.maxItemsActive); // array index need +1
        };

        this.isLongListItem = function (index) {
            return (index + 1) > Number(this.maxItemsActive); // array index need +1
        };

        this.toggleBtnText = function () {
            return `Show ${!this.isOpen ? 'more' : 'less'}`;
        };

        this.toggleListItem = (e) => {
            e.stopPropagation();

            this.isOpen = !this.isOpen
        }
    }
});

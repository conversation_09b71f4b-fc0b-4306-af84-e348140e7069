angular.module('SportWrench').component('receiveCheckForm', {
	templateUrl: 'components/event-payments/check-form/check-form.html',
	bindings: {
		payment 	: '<',
		onSave  	: '&?',
		onChanges 	: '&?'
	},
	controller: ['DateService', 'moment', 'toastr', '$scope', ReceiveCheckFormController]
})

function ReceiveCheckFormController (DateService, moment, toastr, $scope) {

	this.enableCheckForm 	= !!(this.payment.check_num || this.payment.received_at);
	this.showOKBtn 			= angular.isFunction(this.onSave);
	this.datePicker 		= {
		isOpen: false
	};

	this.saveInProcess = false;

	this.toggleDatePicker = function () {
		this.datePicker.isOpen = !this.datePicker.isOpen;
	}

	this.onDataChanged = function () {
		if (angular.isFunction(this.onChanges)) {
			this.onChanges({
				data: {
					check_num 	: this.payment.check_num,
					received_at : __prepareReceivedAt__(this.payment.received_at)
				}
			})
		}
	}

	this.onOKClick = function () {
		if (this.saveInProcess) {
			return;
		}

		this.saveInProcess = true;

		if (this.receiveForm.$invalid) {
            if (this.receiveForm['amount'].$invalid) {
                toastr.warning('Amount is required and should be greater than 0');
            }

            if (this.receiveForm.$error.required) {
			    toastr.warning('Fill in all the fields!');
            }

            this.saveInProcess = false;

			return;
		}

		this.onSave({
			data: {
				check_num 		: this.payment.check_num,
				received_at 	: __prepareReceivedAt__(this.payment.received_at),
				received_amount : Number(this.receivedAmount)
			}
		})
		.finally(() => {
			this.saveInProcess = false;
		})
	}

	this.showErrorCls = function (ctrlName) {
		var formControl = this.receiveForm[ctrlName]
		return (
			this.receiveForm.$submitted && formControl && formControl.$invalid
		);
	}

	this.receivedPickerToggle = function () {
		if (this.enableCheckForm && !this.payment.received_at) {
			this.payment.received_at = DateService.normalize(new Date());
		}
	}


	this.$onInit = function () {
        this.receivedAmount = Number(this.payment.received_amount) || Number(this.payment.amount);
	}

	$scope.$watch('$ctrl.payment.amount', function (currentAmount) {
		if (!this.payment.received_amount) {
			this.receivedAmount = Number(currentAmount);
		}
	}.bind(this));



	function __prepareReceivedAt__ (receivedAt) {
		return receivedAt
			?moment(receivedAt).format('YYYY-MM-DD')
			:null
	}

	if (this.payment.received_at && !(this.payment.received_at instanceof Date)) {
		this.payment.received_at = DateService.parseUnix(this.payment.received_at);
	}
}

angular.module('SportWrench').directive('partialRefund', ['UtilsService', 'toastr', 'StripeFeeService', 'FEE_PAYER', 'PAYMENT_PROVIDER',
    function (UtilsService, toastr, StripeFeeService, FEE_PAYER, PAYMENT_PROVIDER
    ) {
    return {
        restrict: 'E',
        scope: {
            payment: '<',
            teams: '&',
            save: '&'
        },
        templateUrl: 'components/refunds/partial/partial-refund.html',        
        controller: ['$scope', function ($scope) {
            $scope.p                    = $scope.payment;
            $scope.p.afterRefund        = $scope.p.amount;
            $scope.refund_teams         = prepareTeams();

            const refundedTeamAmountIsValid = new Map();
            let refundIsInProgress = false;

            function prepareTeams () {
                return _.map($scope.teams(), function (team) {
                    return _.extend({ is_refunded: !!team.canceled }, team)
                });
            }

            function recountAmount () {
                let amount = 0;
                const teams  = $scope.refund_teams;

                teams.forEach(function (team) {
                    if (!team.is_refunded) {
                        const teamDiscount    = parseFloat(team.discount) || 0;
                        const regFee          = parseFloat(team.reg_fee) || 0;
                        const surcharge       = parseFloat(team.surcharge) || 0;

                        if($scope.p.teams_sw_fee_payer === FEE_PAYER.BUYER) {
                            amount += parseFloat(team.sw_fee);
                        }

                        amount += (regFee + surcharge - teamDiscount);
                    }
                });

                if(amount > 0) {
                    if(buyerPaysProviderFeePayer()) {
                        amount += calculateProviderFee(amount);
                    }
                }

                return UtilsService.approxNumber(amount);
            }

            function calculateProviderFee(amount) {
                // todo: add payment hub fee service
                return calculateStripeFee(amount);
            }

            function calculateStripeFee(amount) {
                const isDefaultFeePayer = false;

                if($scope.p.type === 'card') {
                    return StripeFeeService.countCardFeeAmount(
                        amount,
                        $scope.p.percent,
                        $scope.p.stripe_fixed,
                        isDefaultFeePayer
                    );
                } else if ($scope.p.type === 'ach') {
                    return StripeFeeService.countACHFeeAmount(
                        amount,
                        $scope.p.percent,
                        isDefaultFeePayer
                    );
                }

                return 0;
            }

            function buyerPaysProviderFeePayer() {
                const feePayer = $scope.p.payment_provider === PAYMENT_PROVIDER.PAYMENT_HUB ? $scope.p.payment_hub_teams_fee_payer : $scope.p.stripe_teams_fee_payer;

                return feePayer === FEE_PAYER.BUYER
            }

            function findTeamByTeamPurchaseId(id) {
                return $scope.teams().find(({ purchase_team_id }) => purchase_team_id === id) || {};
            }

            function refundedTeamsAmountIsValid() {
                for (let value of refundedTeamAmountIsValid.values()) {
                    if (!value) {
                        return false;
                    }
                }

                return true;
            }

            function validateRefundedAmount(team) {
                const { discount: initialDiscount } = findTeamByTeamPurchaseId(team.purchase_team_id);
                const { discount, reg_fee, canceled } = team;

                if (canceled) {
                    return true;
                }

                return (reg_fee - discount) >= 0 && discount >= initialDiscount;
            }

            $scope.recountTotals = function () {
                $scope.p.afterRefund = recountAmount();
            };

            $scope.onRefundedAmountChange = function(team) {
                $scope.p.afterRefund = recountAmount();

                refundedTeamAmountIsValid.set(team.roster_team_id, validateRefundedAmount(team));
            }

            $scope.onSurchargeChange = function(team) {
                const { surcharge: initialSurcharge } = findTeamByTeamPurchaseId(team.purchase_team_id);

                if (team.surcharge > initialSurcharge) {
                    team.surcharge = initialSurcharge;
                }

                $scope.p.afterRefund = recountAmount();
            }

            $scope.teamClass = function  (team) {
                return {
                    'text-grey crossed': team.is_refunded
                };
            };

            $scope.amountToRefund = function () {
                return UtilsService.approxNumber($scope.p.amount - $scope.p.afterRefund);
            };

            $scope.isRefundDisabled = function () {
                if (!refundedTeamsAmountIsValid()) {
                    return true;
                }

                return (($scope.p.afterRefund === Number($scope.p.amount)) ||
                    ($scope.amountToRefund() <= 0)) || refundIsInProgress;
            };

            $scope.makeRefund = function () {
                var __teams = [];

                $scope.refund_teams.forEach(function (team) {
                    if (!team.canceled) {
                        __teams.push({
                            roster_team_id  : team.roster_team_id,
                            discount        : +team.discount || 0,
                            canceled        : team.is_refunded,
                            surcharge       : +team.surcharge || 0
                        });
                    }
                });

                if(__teams.length === 0) {
                    toastr.warning('Nothing changed');
                    return;
                }

                refundIsInProgress = true;

                $scope.save({
                    payment: {
                        amount      : $scope.p.afterRefund,
                        purchase_id :  $scope.p.purchase_id
                    },
                    refunded_teams: __teams
                }).finally(() => refundIsInProgress = false);
            };   
            
            
        }]
    };
}]);

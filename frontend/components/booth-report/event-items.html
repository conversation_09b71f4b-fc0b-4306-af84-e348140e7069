<p class="event-title pointer" ng-if="report.long_name" ng-click="details && details({ id: report.event_id })">
    {{report.date_start | UTCdate: 'MM/DD'}}&nbsp;<span class="lead">{{report.long_name}}</span>&nbsp;<i class="fa fa-info-circle"></i>
</p>
<table class="table table-condensed" ng-if="exhibitors.length">
    <thead>
        <tr>
            <th>Company Name</th>
            <th>Booth Title</th>
            <th>Date</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Type</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="ex in exhibitors">
            <td>{{ex.company_name}}</td>
            <td>{{ex.booth_title}}</td>
            <td>{{ex.created}}</td>
            <td>{{ex.amount | currency:'$':2}}</td>
            <td>{{ex.payment_status}}</td>
            <td>{{ex.type}}</td>
        </tr>
    </tbody>
</table>

<!-- This will be moved to Accounting tab -->
<eo-exhibitors-stats
    ng-if="report.exhibitors.length && exhibitors.length"
    report="report"
></eo-exhibitors-stats>

<uib-alert type="warning text-center" ng-if="!report.exhibitors.length">No Booth bought</uib-alert>
<uib-alert type="warning text-center" ng-if="report.exhibitors.length && !exhibitors.length">No exhibitors found</uib-alert>

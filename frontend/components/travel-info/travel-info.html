<div ng-if="$ctrl.members.length">
    <div class="row-space text-right">
        <div class="col12">
            <button class="btn btn-primary" ng-disabled="$ctrl.isSubmitting" ng-click="$ctrl.export()">
                <span>Export</span>
                <spinner is-inline="true" size="1" pos="left" active="$ctrl.isSubmitting"></spinner>
            </button>
        </div>
    </div>
    <table class="table table-bordered travel-info-list">
        <thead>
        <tr>
            <th>First</th>
            <th>Last</th>
            <th>Travel Method</th>
            <th>Hotel</th>
            <th>Nights Required</th>
            <th>Roommate</th>
            <th ng-if="$ctrl.isStaff()">Arrival Date/Time</th>
            <th >Departure Date/Time</th>
            <th>Conflicts</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="member in $ctrl.members">
            <td class="travel-info-list-overflow-sm" ng-bind="member.first"></td>
            <td class="travel-info-list-overflow-sm" ng-bind="member.last"></td>
            <td class="travel-info-list-overflow-xs" ng-bind="$ctrl.travelMethods[member.travel_method]"></td>
            <td class="travel-info-list-overflow-xs" ng-bind="$ctrl.formatHotelField(member.need_hotel_room)"></td>
            <td class="travel-info-list-overflow-sm">
                <span ng-if="member.hotel_nights_required && member.need_hotel_room" ng-bind="member.hotel_nights_required.dates"></span>
            </td>
            <td class="travel-info-list-overflow-md" ng-bind="member.roommate_preference"></td>
            <td ng-if="$ctrl.isStaff()" ng-bind="$ctrl.formatTime(member.arrival_time, false)"></td>
            <td ng-bind="$ctrl.formatTime(member.departure_datetime, false)"></td>
            <td class="travel-info-list-overflow-lg" ng-bind="member.additional_restrictions"></td>
        </tr>
        </tbody>
    </table>
</div>

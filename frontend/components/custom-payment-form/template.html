<modal-wrapper>
    <div class="row" loading-container="$ctrl.showIncompletePaymentAlert">
        <div class="col-xs-12" align="center">
            <form class="form-horizontal">
                <div class="form-group">
                    <label class="col-sm-3 control-label">Payment Type</label>
                    <div class="col-sm-7">
                        <input
                            class="form-control"
                            name="payment_type"
                            ng-model="$ctrl.paymentTypeTitle"
                            disabled
                        />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">Card</label>
                    <div class="col-sm-7">
                        <select class="form-control" disabled>
                            <option value="" ng-bind="$ctrl.cardLabel()"></option>
                        </select>
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': !!$ctrl.amountErrorMessage }">
                    <label class="col-sm-3 control-label">Amount</label>
                    <div class="col-sm-7">
                        <input
                            class="form-control"
                            name="amount"
                            ng-model="$ctrl.amount"
                            ng-change="$ctrl.amountChanged()"
                            float-number
                        />
                    </div>
                    <div class="has-error-div col-xs-offset-3 col-sm-7 mt-5"
                         ng-if="!!$ctrl.amountErrorMessage"
                         ng-bind="$ctrl.amountErrorMessage"
                    ></div>
                </div>

                <div class="row">
                    <div class="col-xs-3 col-xs-offset-2">
                        <dl class="dl-horizontal">
                            <dt>Merchant fee: </dt>
                            <dd ng-bind="$ctrl.merchantFee | currency"></dd>
                            <dt>Total:</dt>
                            <dd ng-bind="$ctrl.total | currency"></dd>
                        </dl>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-3 control-label">Description</label>
                    <div class="col-sm-7">
                        <textarea class="form-control" ng-model="$ctrl.description"></textarea>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <uib-alert type="danger" class="text-center" ng-if="$ctrl.showIncompletePaymentAlert">
        Payment requires authentication. <a href ng-click="$ctrl.goToIncompletePayments()">Go to incomplete payments</a>
    </uib-alert>

    <external-button
        ng-disabled="$ctrl.payButtonDisabled()"
        class="btn btn-primary"
        ng-click="$ctrl.pay()"
    >Pay</external-button>

    <external-button class="btn btn-default pull-right" ng-click="$ctrl.closeModal()">Cancel</external-button>
</modal-wrapper>

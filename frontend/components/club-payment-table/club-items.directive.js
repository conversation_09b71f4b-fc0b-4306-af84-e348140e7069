angular.module('SportWrench').component('clubItems', {
	templateUrl : 'components/club-payment-table/club-items.html',
	bindings	: {
		teams 	 	 		: '=',
		eventFee 	 		: '=fee',
		surcharge 	 		: '=?',
		hidePicker 	 		: '<',
		hasCustomDiscount 	: '<?',
        onAmountChanged     : '&'
	},
	controllerAs: 'vm',
	controller 	: ['$filter', 'purchaseService', clubItemsController]
});

function clubItemsController ($filter, purchaseService) {
	let self = this;

    let currencyFilter 	= $filter('currency');

    let amount = 0;

    let receipt = [];

	this.pick = {
		all 	: (this.teams.length > 0),
		items 	: {}
	};

	this.receipt = {};

	self.declinedTeams 	= __getDeclinedTeams__(self.teams);
	this.discounts 		= purchaseService.initTeamsDiscounts(self.teams);

	this.teams = filterTeams(this.teams);

	this.pickAll = function () {
        let items 		= self.pick.items,
			isAllPicked = self.pick.all;

		receipt.length 	= 0;
		amount 			= 0;

		self.teams.forEach(function (team) {
			if (team.is_declined) {
				return;
			}

			items[team.roster_team_id] = isAllPicked;

			if(isAllPicked) {
				receipt.push(team.roster_team_id);
				amount += __getDue__(team);
			}
		});

		this.onAmountChanged({amount, receipt, discounts: self.discounts});
	};

	this.toggleTeam = function (team) {
		if (self.pick.items[team.roster_team_id]) {
			receipt.push(team.roster_team_id);
			amount += __getDue__(team);
		} else {
			receipt.splice(receipt.indexOf(team.roster_team_id), 1);
			amount -= __getDue__(team);
		}

		self.pick.all = (receipt.length === self.teams.length);

        this.onAmountChanged({amount, receipt, discounts: self.discounts});
	};

	this.onTeamDiscountChange = function () {
		amount = 0;

		self.teams.forEach(function (team) {
			if (team.is_declined || !self.pick.items[team.roster_team_id]) {
				return;
			}

			amount += __getDue__(team);
		});

        this.onAmountChanged({amount, receipt, discounts: self.discounts});
	};

	this.teamLabel = function (team) {
		return '(' + team.division_name + ') ' + team.team_name;
	};

	this.getFee = function (team) {
		return currencyFilter(__getFee__(team.div_reg_fee, self.eventFee));
	};

	this.getDue = function (team) {
		return currencyFilter(__getDue__(team));
	};

	this.rowClass = function (team) {
		return {
			'danger': team.is_declined
		}
	}

	this.getDeclinedTeamNames = function () {
		return this.declinedTeams.join(', ');
	}

	this.getTeamRegFee = function (team) {
		return __getFee__(team.div_reg_fee, self.eventFee);
	}

	if(this.teams.length > 0) {
		this.pickAll();
	}

	function __getDue__ (team) {
		/* NOTE: surcharge is counted in the parent component */
		return purchaseService.getTeamDue(
				team.div_reg_fee, self.eventFee, self.discounts[team.roster_team_id], team.paid);
	}

	function __getFee__ (divisionFee, eventFee) {
		return purchaseService.getTeamRegistrationFee(divisionFee, eventFee);
	}

	function __getDeclinedTeams__ (teams) {
		return _.reduce(teams, function (res, team) {
			if (team.is_declined) {
				res.push(team.team_name)
			}
			return res;
		}, []);
	}

	function filterTeams(teams) {
	    return teams.filter(team => {
	        const { div_reg_fee } = team;

	        if (div_reg_fee === null) {
	            return true;
            }

            return Number(div_reg_fee) !== 0;
        })
    }
}

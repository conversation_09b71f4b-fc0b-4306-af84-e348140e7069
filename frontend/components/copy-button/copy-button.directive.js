angular.module('SportWrench').directive('copyButton', ['toastr', 'clipboard', function (toastr, clipboard) {
    return {
        restrict: 'A',
        scope: {
            text: '<',
            title: '<'
        },
        templateUrl: 'components/copy-button/copy-button.html',
        link: function (scope, attrs, elem, ctrl) {
            scope.showButton = clipboard.supported;
            scope.titleText = scope.title || 'Copy';

            scope.onSuccess = function () {
                toastr.success('Copied to clipboard');
            };

            scope.onError = function (err) {
                toastr.error('Error: ' + err);
            };
        }
    }
}]);

angular.module('SportWrench').component('webpointSyncAdult', {
    templateUrl: 'components/webpoint-sync-adult/webpoint-sync-adult.html',
    bindings: {
        adult   : '<',
        onUpdate: '&'
    },
    controller: WebpointSyncAdultController
});

WebpointSyncAdultController.$inject = [
    'WebpointSyncAdultService', 'SAFESPORT_NOT_VALID_STATUS', 'SAFESPORT_VALID_STATUS', '$stateParams'
];

function WebpointSyncAdultController(
    WebpointSyncAdultService, SAFESPORT_NOT_VALID_STATUS, SAFESPORT_VALID_STATUS, $stateParams
) {

    let eventID = $stateParams.event;

    this.syncAthlete = function () {
        return WebpointSyncAdultService.syncAthlete(this.adult.master_athlete_id, eventID, this.adult.master_club_id)
            .then(response => {

                if(response) {
                    this.onUpdate({
                        id      : this.adult.id,
                        status  : response.safesport_status ? SAFESPORT_VALID_STATUS : SAFESPORT_NOT_VALID_STATUS,
                        lastSync: response.last_webpoint_sync
                    });
                }
            })
    }
}

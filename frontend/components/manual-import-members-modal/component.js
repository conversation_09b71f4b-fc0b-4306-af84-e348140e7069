

class Controller {

    constructor (toastr, fileUploadService) {
        this.toastr = toastr;
        this.fileUploadService = fileUploadService;
    }

    $onInit () {
        this.submitting = false;
        this.error = null;
        this.file = null;
    }

    save () {
        this.onChanges();

        if(this.form.$invalid || this.error !== null) {
            this.toastr.warning('Not all data filled');
            return;
        }
        this.submitting = true;

        const data = { file: this.file };

        this.onSave({ data }).then((response) => {
            this.toastr.success(`${response.parsed} member(s) created`);
        }).finally(() => {
            this.submitting = false;
        });
    }

    isSubmitDisabled () {
        return this.submitting;
    }

    getFileErrorMessage () {
        return this.fileUploadService.textFileUploadValidation(this.file);
    }

    onChanges () {
        if(this.form.file.$invalid) {
            this.error = 'File is required';
        }

        let fileErrorMessage = this.getFileErrorMessage();

        if(fileErrorMessage) {
            this.error = fileErrorMessage;
        } else {
            this.error = null;
        }
    }

    isErrorVisible () {
        return this.error !== null && this.form.$submitted;
    }
}

Controller.$inject = ['toastr', 'fileUploadService'];

angular.module('SportWrench').component('manualImportMembersModal', {
    templateUrl: 'components/manual-import-members-modal/template.html',
    bindings: {
        close: '&',
        onSave: '&',
    },
    controller: Controller
});

<div class="container">
    <div class="panel panel-default" ng-show="!isSent">
        <div class="panel-heading">
            <h2 class="panel-title">Send a Message to SportWrench Support</h2>
        </div>
        <div class="panel-body">
            <form name="contactform" class="form-horizontal">
                <div ng-class="{ 'form-group validation-required': true, 'has-error': contactform.reason.$invalid && submitted }">
                    <label for="reason" class="col-lg-2 control-label">Reason</label>
                    <div class="col-lg-10">
                        <select name="reason" id="reason" class="form-control"
                                ng-model="feedbackData.reason"
                                ng-options="r.id as r.title for r in reasons"
                                ng-required="true">
                            <option value="">Choose a reason ...</option>
                        </select>
                    </div>
                </div>
                <!--feedbackData.reason !== 'camps'-->
                <div ng-if="feedbackData.reason !== 'coalition'" ng-class="{ 'form-group': true, 'validation-required': isTournamentRequired(),  'has-error': contactform.tournament.$invalid && submitted }">
                    <label for="tournament" 
                           class="col-lg-2 control-label" 
                           ng-if="feedbackData.reason !== 'camps'">Tournament</label>
                    <label for="tournament" 
                           class="col-lg-2 control-label" 
                           ng-if="feedbackData.reason === 'camps'">Camp</label>
                    <div class="col-lg-10">
                        <select name="tournament" id="tournament" class="form-control"
                                ng-model="feedbackData.tournament"
                                ng-options="e as e.long_name for e in events | showCamp : feedbackData.reason"
                                ng-required="isTournamentRequired()">
                            <option value="">Choose a tournament</option>
                        </select>
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': contactform.name.$invalid && submitted }">
                    <label for="name" class="col-lg-2 control-label">Name</label>
                    <div class="col-lg-10">
                        <input ng-model="feedbackData.name" type="text" class="form-control" id="name" name="name" placeholder="Your Name" ng-required="true">
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': contactform.email.$invalid && submitted }">
                    <label for="email" class="col-lg-2 control-label">Email</label>
                    <div class="col-lg-10">
                        <input ng-model="feedbackData.email" type="email" class="form-control" email-validator id="email" name="email" placeholder="Your Email" autofill-sync ng-required="true">
                    </div>
                </div>
                <div class="form-group">
                    <label for="phone" class="col-lg-2 control-label">Phone</label>
                    <div class="col-lg-10">
                        <input type="tel" class="form-control" name="phone" ng-model="feedbackData.phone">
                    </div>
                </div>                
                <div ng-class="{ 'form-group validation-required': true, 'has-error': contactform.subject.$invalid && submitted }">
                    <label for="subject" class="col-lg-2 control-label">Subject</label>
                    <div class="col-lg-10">
                        <input ng-model="feedbackData.subject" type="text" class="form-control" id="subject" name="subject" placeholder="Message Subject" ng-required="true">
                    </div>
                </div>
                <div ng-class="{ 'form-group validation-required': true, 'has-error': contactform.message.$invalid && submitted }">
                    <label for="message" class="col-lg-2 control-label">Message</label>
                    <div class="col-lg-10">
                        <textarea ng-model="feedbackData.message" class="form-control no-resize" rows="4" id="message" name="message" placeholder="Your message..." ng-required="true"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-lg-offset-2 col-lg-10">
                        <button class="btn btn-default" ng-disabled="submitButtonDisabled" ng-click="submit(contactform)">
                            Send a Message
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="alert alert-success text-center" ng-show="isSent">
        Your Message has been sent to SportWrench Support. Thank you!    
    </div>
</div>

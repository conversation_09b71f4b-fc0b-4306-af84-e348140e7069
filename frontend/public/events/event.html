<table class="table table-condensed">
    <thead>
        <tr>
            <th>Dates</th>
            <th>Event Name</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat-start="e in events" class="bg-info" ng-if="showYear(e, $index, events)">
            <td style="width: 25%" class="font-bold">{{e.year}} Events:</td>
            <td></td>
        </tr>
        <tr ng-repeat-end ng-class="{ 'font-bold': e.upcoming }">
            <td style="width: 25%">{{e.date_start + ' - ' + e.date_end}}</td>
            <td class="pointer">
                <a ng-click="open_teams(e.event_id)">{{e.long_name}}</a>
            </td>            
        </tr>
    </tbody>
</table>

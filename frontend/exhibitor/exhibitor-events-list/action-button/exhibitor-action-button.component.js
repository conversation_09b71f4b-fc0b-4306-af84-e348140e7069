class Component {
    constructor() {}

    showBtn() {
        return !(!this.status && !this.exhibitorsRegistrationIsOpened);
    }

    getClass() {
        return {
            'btn-success': this.isApplyMode,
            'btn-warning': this.isEditMode,
            'btn-info-grey': this.isViewMode,
        }
    }

    getName() {
        if (this.isApplyMode) {
            return 'Apply';
        }

        if (this.isEditMode) {
            return 'Edit Info';
        }

        if (this.isViewMode) {
            return 'View Info';
        }
    }
}

angular.module('SportWrench').component('exhibitorActionButton', {
    templateUrl: 'exhibitor/exhibitor-events-list/action-button/exhibitor-action-button.html',
    bindings: {
        isApplyMode: '<',
        isEditMode: '<',
        isViewMode: '<',
        status: '<',
        eventIsOver: '<',
        onClick: '&',
        exhibitorsRegistrationIsOpened: '<'
    },
    controller: [Component]
});

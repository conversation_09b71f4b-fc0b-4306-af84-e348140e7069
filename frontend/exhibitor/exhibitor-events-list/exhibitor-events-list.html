<div class="container">
    <div class="row justify-content-sm-center">
        <div class="col-sm-12">
            <spinner active="$ctrl.loading.inProcess"></spinner>
            <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
        </div>
        <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error" class="col-sm-12">
            <table class="table table-condensed">
                <thead>
                <tr>
                    <th>Status</th>
                    <th>Name</th>
                    <th>Region</th>
                    <th>State</th>
                    <th>Date Start</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                    <tr ng-repeat-start="event in $ctrl.events track by $index" class="bg-info font-bold" ng-if="$ctrl.showYear(event, $index)">
                        <td colspan="8">{{event.year}} Events:</td>
                    </tr>
                    <tr ng-repeat-end>
                        <td><exhibitor-status-column status="event.status"></exhibitor-status-column></td>
                        <td><a href="" ng-click="$ctrl.openModal(event, $ctrl.tabs.EVENT_INFO)">{{event.name}}</a></td>
                        <td>{{event.region}}</td>
                        <td>{{event.state}}</td>
                        <td>{{event.date_start | UTCdate: 'MMM DD, YYYY'}}</td>
                        <td>
                            <div class="row exhibitor_actions">
                                <div class="col-sm-5">
                                    <exhibitor-action-button
                                            status="event.status"
                                            on-click="$ctrl.openModal(event, $ctrl.tabs.REGISTRATION_INFO)"
                                            exhibitors-registration-is_opened="event.exhibitors_registration_is_opened"
                                            is-apply-mode="$ctrl.isApplyMode(event)"
                                            is-edit-mode="$ctrl.isEditMode(event)"
                                            is-view-mode="$ctrl.isViewMode(event)"

                                    >
                                    </exhibitor-action-button>
                                </div>
                                <div class="col-sm-5 exhibitor_actions_invoices">
                                    <button
                                        ng-if="$ctrl.showInvoicesBtn(event)"
                                        class="btn btn-primary btn-xs"
                                        ng-click="$ctrl.goToInvoices(event)"
                                    >
                                        Invoices
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>

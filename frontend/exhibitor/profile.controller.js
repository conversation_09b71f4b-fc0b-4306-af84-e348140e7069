angular.module('SportWrench')

.controller('Exhibitor.ProfileController', function($scope, $http, $rootScope, geoService, $state, APP_ROUTES) {

    $scope.states = [];

    let routes = {
    	create: APP_ROUTES.EX.PROFILE_CREATE,
    	update: APP_ROUTES.EX.PROFILE_UPDATE
    };

    geoService.getStates('US', function(res) {
        $scope.states = res.data;
    });

    $scope.state = $scope.profileExists ? routes.update : routes.create;
});

class Component {
    constructor() {
        // Empty constructor - no dependencies needed for this component
    }

    showStatus() {
        return this.status;
    }
}

// Add $inject annotation for minification safety
Component.$inject = [];

angular.module('SportWrench').component('exhibitorStatusColumn', {
    templateUrl: 'exhibitor/status-column/status-column.html',
    bindings: {
        status: '<',
    },
    controller: Component
});

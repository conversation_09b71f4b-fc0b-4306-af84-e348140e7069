angular.module('SportWrench')

.controller('PasswordRecoveryController', PasswordRecoveryController);

function PasswordRecoveryController($scope, $http, $state, $stateParams, INTERNAL_ERROR_MSG, APP_ROUTES) {
    var code = $stateParams.code;
    $scope.error = null;

    $http.get('/api/recover/check_code', {
        params: { code: code }
    })
    .error(function (resp, status) {
        $scope.hideForm = true;
        if (status === 500)
            $scope.error = 'An error has occurred. Please retry later.';
        if (status === 400)
            $scope.error = 'Incorrect recovery code.';
    });

    $scope.data = {};

    $scope.$watchCollection('data', function(d) {
        if (d.password !== d.confirm)
            $scope.recoveryForm.new_password_confirm.$setValidity('match', false);
        else
            $scope.recoveryForm.new_password_confirm.$setValidity('match', true);
    });

    $scope.submit = function() {
        $scope.error = null;
        $scope.success = null;
        var d = {};
        d.password = $scope.data.password;
        d.code = code;
        $http.post('/api/recover/reset', d)
        .success(function () {
            $scope.hideForm = true;
            $state.go(APP_ROUTES.LOGIN);
        }).error(function (resp, status) {
            if (status === 500)
                $scope.error = INTERNAL_ERROR_MSG;
            if (status === 403)
                $scope.error = 'Incorrect recovery code.';
        });
    };
}

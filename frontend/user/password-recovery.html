<div class="row">
    <div class="col-sm-4 col-sm-offset-4 col-xs-8 col-xs-offset-2">
        <div ng-hide="hideForm">
            <form name="recoveryForm" ng-submit="submit()">
                <div class="form-group">
                    <label class="control-label">New password: </label>
                    <input type="password" name="new_password" class="form-control" ng-model="data.password" autofill-sync required ng-minlength="6">
                    <span class="text-danger">Minimum 6 characters</span>
                </div>
                <div class="form-group">
                    <label class="control-label">Confirm:</label>
                    <input type="password" name="new_password_confirm" class="form-control" ng-model="data.confirm" autofill-sync required>
                    <span class="text-danger" ng-show="recoveryForm.new_password_confirm.$error.match">Passwords doesn't match</span>
                </div>
                <div class="form-group">
                    <input type="submit" class="btn btn-primary" value="Submit"
                        ng-disabled="recoveryForm.$invalid">
                </div>
            </form>
        </div>
        <div class="label label-warning" ng-show="!!error">{{error}}</div>
    </div>
</div>
<div class="pass center-block">
    <div class="center-block label label-success"
    ng-show="success">Your password was changed. You can log in now.</div>
</div>
<div ui-view="login"></div>

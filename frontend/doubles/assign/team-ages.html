<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title" ng-if="data.event.name">Event: {{$parent.data.event.name}}</h3>
        <i class="fa fa-spinner fa-pulse" ng-if="!data.event.name && !error.text"></i>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-sm-6">
                <b>Event Dates:</b><br/>
                Start Date: {{$parent.data.event.date_start | date: 'MM/DD/YYYY'}}<br/>
                End Date: {{$parent.data.event.date_end | date: 'MM/DD/YYYY'}}<br/><br/>
                <b ng-if="$parent.data.event.state || $parent.data.event.city || $parent.data.event.address">Event Location:</b><br/>
                <span ng-if="$parent.data.event.state" ng-bind="$parent.data.event.state"></span><br/>
                <span ng-if="$parent.data.event.city" ng-bind="$parent.data.event.city"></span><br/>
                <span ng-if="$parent.data.event.address" ng-bind="$parent.data.event.address"></span>
            </div>
            <div class="col-sm-6">
                <b>Contact Info:</b><br/>
                Website: <short-link href="$parent.data.event.website" ng-if="$parent.data.event.website"></short-link><span ng-if="!$parent.data.event.website">N/A</span><br/>
                Email:  <a href="mailto:{{$parent.data.event.email}}" ng-if="$parent.data.event.email">{{$parent.data.event.email}}</a><span ng-if="!$parent.data.event.email">N/A</span><br/><br/>
                <b>Team Genders:</b>
                <span ng-if="$parent.data.event.has_male_teams === true">Male</span>
                <span ng-if="$parent.data.event.has_female_teams === true">Female</span>
                <span ng-if="$parent.data.event.has_coed_teams === true">Coed</span>
            </div>
        </div>
    </div>
</div>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">Event: {{$parent.data.event.name}}</h3>
    </div>
    <div class="panel-body">        
        <div class="row">
            <div class="col-sm-offset-2 col-sm-4">
                <p class="lead">Choose a team type:</p>
                <div class="form-group">
                    <label>Type:</label>
                    <select 
                        ng-model="$parent.data.team_type" 
                        class="form-control"   
                        ng-options="t.id as t.name for t in types"
                    >
                        <option value=""> --- </option>
                    </select>
                </div>
                <button class="btn btn-success" ng-disabled="!$parent.data.team_type" ng-click="next()">Next</button>
            </div>
        </div>
    </div>
</div>
